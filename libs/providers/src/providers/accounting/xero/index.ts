import { Region } from '@datagatherers/datagatherers'
import { configBrowser, snapshot } from '@datagatherers/worker-utils'

import { extractApps, extractAdvisorsTotals, scheduleAdvisorsTotals } from '../../../lib/accounting/xero'
import { isos } from '../../../lib/accounting/xero/constants'
import { trackerXeroNewsWW, trackerXeroPageStateAU, trackerXeroPageStateGB } from '../../non_standard/trackers'

import type { Provider, JobInstance, Iso3166Alpha2 } from '@datagatherers/datagatherers'

const config = {
  concurrency: 1,
}

const provider: Provider = [
  {
    launchXero: {
      cron: '0 17 5 * *',
      region: Region.eu_west_1,
      function: () => [],
      scheduleAfterFinished: (config) => {
        return {
          project: config.project,
          provider: config.provider,
          type: `extractAdvisors${isos[0]}`,
        }
      },
      config,
    },
    snapXero: {
      region: Region.eu_west_1,
      function: () => [],
      snapshot: async (config, runtime) => {
        await Promise.all(
          isos.map(async (iso: Iso3166Alpha2) => {
            await snapshot(iso, config, runtime)
          }),
        )
      },
      config,
    },
  },

  ...isos.map((iso) => ({
    // [`extractAdvisors${iso}`]: {
    //   region: Region.eu_west_1,
    //   schedule: async (job, runtime) => scheduleAdvisors(job, runtime, iso),
    //   function: extractAdvisorsTotals,
    //   scheduleAfterFinished: (config) => {
    //     const nextIso = isos[isos.indexOf(iso) + 1] ?? undefined
    //     const type = nextIso ? `extractAdvisorsTotals${nextIso}` : `extractApps${isos[0]}`
    //     return {
    //       project: config.project,
    //       provider: config.provider,
    //       type,
    //     }
    //   },
    //   config: {
    //     ...configBrowser,
    //     //constrains: { countries: [iso] },
    //   },
    // },
    [`extractApps${iso}`]: {
      region: Region.eu_west_1,
      function: async (job: JobInstance) => {
        return extractApps(job, iso)
      },
      scheduleAfterFinished: (config) => {
        const nextIso = isos[isos.indexOf(iso) + 1] ?? undefined
        const type = nextIso ? `extractApps${nextIso}` : `snapXero`
        return {
          project: config.project,
          provider: config.provider,
          type,
        }
      },
      config,
    },
    trackerXeroPageStateGB,
    trackerXeroPageStateAU,
    trackerXeroNewsWW,
    extractAdvisorsTotals: {
      region: Region.eu_west_1,
      schedule: scheduleAdvisorsTotals,
      function: extractAdvisorsTotals,
      scheduleAfterFinished: (config) => {
        const type = `extractApps${isos[0]}`
        return {
          project: config.project,
          provider: config.provider,
          type,
        }
      },
      config: {
        ...configBrowser,
        concurrency: 5,
        //constrains: { countries: [iso] },
      },
    },
  })),
].reduce((acc, val) => Object.assign(acc, val), {})

export default provider
