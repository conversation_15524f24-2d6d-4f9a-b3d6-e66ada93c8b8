import { CancelError } from 'got'
import { <PERSON>SD<PERSON> } from 'jsdom'
import UserAgent from 'user-agents'

import { Iso3166Alpha2, Region, ProjectType, DataType, ResultType } from '@datagatherers/datagatherers'
import { getItemId, snapshot, wait } from '@datagatherers/worker-utils'

import type { JobInstance, JobDefinition, Provider, ControllerRuntime, ResultItem } from '@datagatherers/datagatherers'

const isoList = [Iso3166Alpha2.BR, Iso3166Alpha2.WW]

const LIMIT = 50
const provider: Provider = {
  extractIntegrations: {
    cron: '30 8 7 * *',
    region: Region.eu_west_1,
    function: getIntegrations,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractPartners',
      }
    },
    config: {
      browser: true,
      concurrency: 1,
      constrains: { countries: [Iso3166Alpha2.BR] },
    },
  },
  extractPartners: {
    region: Region.eu_west_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractPartners(job, runtime, Iso3166Alpha2.BR),
    function: getPartners,
    snapshot: async (config, runtime) => {
      for (const iso of isoList) {
        await snapshot(iso, config, runtime)
      }
    },
    config: {
      concurrency: 10,
      constrains: { countries: [Iso3166Alpha2.BR] },
    },
  },
}

async function getIntegrations(job: JobInstance) {
  try {
    await job.runtime.page.goto('https://contaazul.com/integracoes/', { waitUntil: 'networkidle2', timeout: 240000 })
  } catch (e) {
    console.error(e)
    throw e
  }

  // Get all child elements from the grid container
  const childElements = await job.runtime.page.$$(
    'div[class="grid flex-1 grid-cols-[repeat(auto-fill,minmax(100px,1fr))] justify-items-center gap-4 md:gap-[30px]"] > *',
  )

  const apps = await Promise.all(
    childElements.map(async (element) => {
      // Get text content from the element
      const name = await element.evaluate((node) => (node as HTMLElement).innerText?.trim())

      // Try to get URL from href attribute (if it's a link) or from any child link
      const url = await element.evaluate((node) => {
        // Check if the element itself is a link
        if (node.tagName === 'A') {
          return node.getAttribute('href')
        }
      })

      return {
        name,
        url,
      }
    }),
  )

  return apps?.flatMap((item) => {
    if (!item.name) return []

    return isoList.map((iso) => {
      const data = {
        provider: job.provider,
        project: ProjectType.accounting,
        itemId: item.name,
        iso,
        appName: item.name,
        ...(item.url && { url: item.url }),
        dataType: DataType.app,
        data: item,
      }
      return {
        type: ResultType.item,
        data,
      }
    })
  })
}

async function scheduleExtractPartners(job: JobDefinition, runtime: ControllerRuntime, iso: Iso3166Alpha2) {
  const url = 'https://contaazul.com/encontre-contador/lista/'
  const { document } = new JSDOM().window

  const stateOptions = await runtime
    .got(url, {
      headers: {
        'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
      },
      retry: {
        limit: 5,
        errorCodes: ['ERR_CANCELED'],
      },
    })
    .text()
    .then((res) => {
      document.body.innerHTML = res
      return document?.querySelectorAll('#filter_state > option:not(:first-child)')
    })

  if (typeof stateOptions === 'undefined' || !stateOptions?.length) return []
  const stateItems: { slug: string | null }[] = []
  stateOptions.forEach((element) => {
    if (element?.getAttribute('value')) {
      stateItems.push({
        slug: element.getAttribute('value'),
      })
    }
  })

  const cityItems: { slug: string | null }[] = []
  for (const option of stateItems) {
    await wait(200)
    const cityOptions = await runtime
      .got(`${url}${option.slug}`, {
        headers: {
          'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
        },
        retry: {
          limit: 5,
          errorCodes: ['ERR_CANCELED'],
        },
      })
      .text()
      .then((res) => {
        document.body.innerHTML = res
        return document?.querySelectorAll('#filter_city > option:not(:first-child)')
      })
      .catch((error) => console.error(error))
    if (typeof cityOptions === 'undefined' || !cityOptions?.length) continue
    cityOptions.forEach((element) => {
      if (element.getAttribute('value')) {
        cityItems.push({
          slug: `${url}${option.slug}/${element.getAttribute('value')}`,
        })
      }
    })
  }

  const jobs: JobDefinition[] = cityItems
    .filter((a) => a?.slug)
    .map((item) => {
      const res = {
        ...job,
        data: {
          iso,
          url: item.slug,
        },
      }
      return res
    })

  return jobs
}

function getPartnersCardsUrl(url: string, pageNr: number) {
  return `${url}/${pageNr}/`
}

async function getPartners(job: JobInstance) {
  const url = job.data?.url
  const { document } = new JSDOM().window

  let pageNr = 1

  return await job.runtime.got.paginate.all<ResultItem, string>(getPartnersCardsUrl(url, pageNr), {
    retry: {
      limit: 5,
      errorCodes: ['ERR_CANCELED'],
    },
    responseType: 'text',
    hooks: {
      afterResponse: [
        (response) => {
          if (typeof response.body !== 'string') throw new Error('Malformed response body')
          if (!document.querySelectorAll('#contadores-parceiros .card')?.length && pageNr > 1) {
            throw new CancelError(response.request)
          }

          return response
        },
      ],
    },
    pagination: {
      paginate: () => {
        if (pageNr === LIMIT) throw new Error(`Reached page Limit at: ${url}/${pageNr}/`)

        const len = document?.querySelectorAll('#contadores-parceiros .card')?.length
        if (!len || len < 6) return false

        pageNr++

        return {
          url: new URL(getPartnersCardsUrl(url, pageNr)),
        }
      },
      transform: ({ body }) => {
        document.body.innerHTML = body

        return Array.from(document?.querySelectorAll('#contadores-parceiros .card') ?? [])?.flatMap(
          (element: Element) => {
            const info = element.querySelector('.card-button > .btn-message')
            if (!info) return []

            const id = info?.getAttribute('data-accountid')?.trim()
            if (!id) return []

            const email = info?.getAttribute('data-email')?.trim()
            const name = info?.getAttribute('data-name')?.trim()

            return isoList.map((iso) => {
              const data = {
                provider: job.provider,
                project: ProjectType.accounting,
                itemId: getItemId(DataType.partner, job.project, id),
                iso,
                companyName: name,
                dataType: DataType.partner,
                data: {
                  email: email,
                },
              }
              return {
                type: ResultType.item,
                data,
              }
            })
          },
        )
      },
    },
  })
}

export default provider
