import { DataType, Iso3166Alpha2, ProjectType, Region, ResultType } from '@datagatherers/datagatherers'
import { getItemId, snapshot } from '@datagatherers/worker-utils'

import { operationName, query, variables } from './constants'

import type { PartnersData } from './types'
import type { JobInstance, Provider, Result } from '@datagatherers/datagatherers'

const isoList = [Iso3166Alpha2.TR, Iso3166Alpha2.WW]

const provider: Provider = {
  extractAdvisorsTR: {
    cron: '0 7 5 * *',
    region: Region.us_east_1,
    function: async (job: JobInstance) => {
      return job.runtime.got.paginate.all<Result, PartnersData>(
        'https://api-client-production.logo.com.tr/graphql/integration',
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
            'x-application-token':
              'ot+6OfiZbex2+GRqXt3joTpY2gOgwDt4avIp+3Q0OB3oPUH+y+Tzmlv7fwPu6eJ2pZa8E5jJuoS+*******************************=',
          },
          retry: {
            limit: 3,
            errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
          },
          responseType: 'json',
          json: { query, variables, operationName },
          pagination: {
            backoff: 500,
            paginate: ({ response }) => {
              if (
                !response?.request?.options?.body ||
                response?.body?.data?.partners?.pagination?.pages.length <=
                  response?.body?.data?.partners?.pagination?.current ||
                !response.body.data?.partners?.items?.length
              ) {
                return false
              }

              return {
                json: {
                  query,
                  variables: {
                    ...variables,
                    page: response?.body?.data?.partners?.pagination?.current + 1,
                  },
                  operationName,
                },
              }
            },
            transform: ({ body }) => {
              if (!body?.data?.partners?.items?.length) {
                return []
              }

              return body.data.partners.items.flatMap((item) => {
                if (!item?.id) return []

                return isoList.map((iso) => ({
                  type: ResultType.item,
                  data: {
                    project: ProjectType.accounting,
                    itemId: getItemId(DataType.partner, ProjectType.accounting, item.id.toString()),
                    dataType: DataType.partner,
                    iso,
                    data: {
                      ...item,
                    },
                  },
                }))
              })
            },
          },
        },
      )
    },
    snapshot: async (config, runtime) => {
      for (const iso of isoList) {
        await snapshot(iso, config, runtime)
      }
    },
    config: {
      concurrency: 1,
    },
  },
}

export default provider
