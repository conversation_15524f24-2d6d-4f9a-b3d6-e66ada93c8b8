import { <PERSON>SD<PERSON> } from 'jsdom'

import { Region, ResultType, DataType, Iso3166Alpha2, ProjectType } from '@datagatherers/datagatherers'
import { getItemId, snapshot } from '@datagatherers/worker-utils'

import type { Provider, JobDefinition, JobInstance, ControllerRuntime } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractAdvisors: {
    cron: '30 12 3 * *', // Every 3rd day of month at 12:30
    region: Region.us_east_1,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => {
      const jobs: JobDefinition[] = []

      return await runtime
        .got('https://www.e-conomic.dk/revisorliste')
        .text()
        .then((html) => {
          const { document } = new JSDOM(html).window
          const anchors = document.querySelectorAll('.accountants-list > li a[data-city]')
          const cities: string[] = []

          anchors.forEach((anchor) => {
            const city = anchor.getAttribute('data-city')
            if (city) {
              cities.push(city)
            }
          })

          for (const city of cities) {
            jobs.push({
              ...job,
              data: {
                iso: Iso3166Alpha2.DK,
                city,
              },
            })
          }

          return jobs
        })
    },
    function: async (job: JobInstance) => {
      return await job.runtime
        .got(`https://www.e-conomic.dk/ajax/accountants-list?category=city&city=${job.data?.city}`)
        .json<{ list: string }>()
        .then((response) => {
          const html = response.list

          const { document } = new JSDOM(html).window
          const items = document.querySelectorAll('.accountants-list > li')
          const results = []

          items.forEach((item) => {
            const nameElement = item.querySelector('.accountant-info--name')
            const companyName = nameElement?.textContent?.trim()
            if (!companyName) return

            const phoneLink = item.querySelector('a[href^="tel:"]')
            const phone = phoneLink?.getAttribute('href')?.replace('tel:', '') || ''

            results.push(
              ...[
                {
                  type: ResultType.item,
                  data: {
                    dataType: DataType.partner,
                    provider: job.provider,
                    project: ProjectType.accounting,
                    itemId: getItemId(DataType.partner, job.project, phone),
                    iso: job.data?.iso,
                    companyName,
                  },
                },
                {
                  type: ResultType.item,
                  data: {
                    dataType: DataType.partner,
                    provider: job.provider,
                    project: ProjectType.accounting,
                    itemId: getItemId(DataType.partner, job.project, phone),
                    iso: Iso3166Alpha2.WW,
                    companyName,
                  },
                },
              ],
            )
          })

          return results
        })
    },
    scheduleAfterFinished: async (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: `extract`,
      }
    },
    config: {
      concurrency: 10,
    },
  },
  extractApps: {
    region: Region.eu_north_1,
    function: async (job: JobInstance) => {
      const { document } = new JSDOM().window
      return job.runtime
        .got(`https://www.e-conomic.dk/apps-og-udvidelser`)
        .text()
        .then((html) => {
          if (!html) {
            return []
          }
          document.body.innerHTML = html

          const listings = document.querySelectorAll('.apps__card')

          if (!listings?.length) {
            return []
          }

          return Array.from(listings).flatMap((element) => {
            const name = element?.getAttribute('data-name')
            const id = element?.getAttribute('data-app-id')
            if (!id || !name) return []

            return [Iso3166Alpha2.DK, Iso3166Alpha2.WW].map((iso) => ({
              type: ResultType.item,
              data: {
                project: ProjectType.accounting,
                dataType: DataType.app,
                itemId: getItemId(DataType.app, job.project, id),
                iso,
                appName: name,
              },
            }))
          })
        })
    },
    snapshot: async (config, runtime) => {
      for (const iso of [Iso3166Alpha2.WW, Iso3166Alpha2.DK]) {
        await snapshot(iso, config, runtime)
      }
    },
    config: {
      concurrency: 1,
    },
  },
}

export default provider
