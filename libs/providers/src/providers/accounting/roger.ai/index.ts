import { JSD<PERSON> } from 'jsdom'

import { Region, ResultType, DataType, Iso3166Alpha2, ProjectType } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import type { Provider, JobDefinition, JobInstance } from '@datagatherers/datagatherers'

const provider: Provider = {
  extract: {
    cron: '30 8 3 * *', // Every 3rd day of month at 12:30
    region: Region.us_east_1,
    function: async (job: JobInstance) => {
      const { document } = new JSDOM().window

      return await job.runtime
        .got(`https://cp.corpayone.com/integrations/`)
        .text()
        .then((res) => {
          document.body.innerHTML = res

          const listingsCards = document?.querySelectorAll('.card-small')
          if (!listingsCards?.length) return []

          const items: { name?: string; url: string; id: string }[] = []
          listingsCards.forEach((element) => {
            const id = element.querySelector('a')?.getAttribute('href') as string
            const name = element.querySelector('.heading-small')?.textContent?.trim()
            if (id && name && id !== '' && name !== '')
              items.push({
                name,
                id,
                url: `https://cp.corpayone.com${id}`,
              })
          })
          return items.flatMap((item) => {
            const data = {
              project: ProjectType.accounting,
              dataType: DataType.app,
              itemId: `app_${item.id}`,
              iso: job.data?.urlCountry,
              url: item.url,
              provider: job.provider,
              appName: item.name,
              data: item,
            }
            return [
              {
                type: ResultType.item,
                data,
              },
              {
                type: ResultType.item,
                data: {
                  ...data,
                  iso: Iso3166Alpha2.WW,
                },
              },
            ]
          })
        })
    },
    schedule: (job) => {
      const jobs: JobDefinition[] = [Iso3166Alpha2.US].map((country) => {
        return {
          ...job,
          data: {
            urlCountry: country,
            _id: country,
            ...(job.data?.parameters && { parameters: job.data?.parameters }),
          },
        }
      })
      return jobs
    },
    config: {
      concurrency: 1,
    },
    snapshot: async (config, runtime) => {
      await snapshot(Iso3166Alpha2.US, config, runtime)
      await snapshot(Iso3166Alpha2.WW, config, runtime)
    },
  },
}

export default provider
