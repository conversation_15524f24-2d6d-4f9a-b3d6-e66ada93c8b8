import { Region } from '@datagatherers/datagatherers'
import { wait, getIp } from '@datagatherers/worker-utils'

import type { Iso3166Alpha2, JobDefinition, JobInstance } from '@datagatherers/datagatherers'

const provider = {
  // ./dg worker tools proxyIpTests testEUW1
  testEUW1: {
    region: Region.eu_west_1,
    schedule: scheduleTestProxy,
    function: testProxy,
    config: {
      browser: true,
      concurrency: 1,
    },
  },
  // ./dg worker tools proxyIpTests testAPN2
  testAPN2: {
    region: Region.ap_northeast_2,
    schedule: scheduleTestProxy,
    function: testProxy,
    config: {
      browser: true,
      concurrency: 1,
    },
  },
  // ./dg worker tools proxyIpTests testUSE1
  testUSE1: {
    region: Region.us_east_1,
    schedule: scheduleTestProxy,
    function: testProxy,
    config: {
      browser: true,
      concurrency: 1,
    },
  },
  // ./dg worker tools proxyIpTests testSAE1
  testSAE1: {
    region: Region.sa_east_1,
    schedule: scheduleTestProxy,
    function: testProxy,
    config: {
      browser: true,
      concurrency: 1,
    },
  },
}

async function scheduleTestProxy(jobDefinition: JobDefinition) {
  // SET TEST VARIABLES HERE
  const TEST_CALLS = 2
  const JOBS = 2
  const MS = 1000
  const gotTests = true
  const puppeteerTests = true

  const jobs = []

  for (let job = 0; job < JOBS; job++) {
    jobs.push({
      ...jobDefinition,
      data: {
        gotTests,
        puppeteerTests,
        MS,
        TEST_CALLS,
      },
    })
  }
  return jobs
}

async function testProxy(job: JobInstance) {
  const gotErrors: string[] = []
  const gotIpTestGroup: { ip: string; country: { name: string; code: Iso3166Alpha2 } }[] = []

  const puppeteerErrors: string[] = []
  const puppeteerIpTestGroup: { ip: string; country: { name: string; code: Iso3166Alpha2 } }[] = []

  if (job.data?.gotTests) {
    for (let i = 0; i < job.data?.TEST_CALLS; i++) {
      try {
        const response = await getIp(job.runtime)
        gotIpTestGroup.push(response)
      } catch (error) {
        gotErrors.push(error.message)
      }
      await wait(job.data?.MS)
    }
  }

  if (job.data?.puppeteerTests) {
    for (let i = 0; i < job.data?.TEST_CALLS; i++) {
      try {
        const responseRaw = await job.runtime.page.goto('https://api.my-ip.io/v2/ip.json')
        const response = JSON.parse(await responseRaw.text())
        puppeteerIpTestGroup.push(response)
      } catch (error) {
        puppeteerErrors.push(error.message)
      }
      await wait(job.data?.MS)
    }
  }

  return []
}

export default provider
