import { format, startOfISOWeek, subDays } from 'date-fns'
import { json2csv, csv2json } from 'json-2-csv'

import { ProjectType, Region, TranferFeeTimeUnit, WiseFeeType } from '@datagatherers/datagatherers'
import { accountConfigs, GDrive, round } from '@datagatherers/worker-utils'

import { extractFeesBrlTax, getGbpUnits, scheduleFeesBrlTax } from '../../../lib/payments/wise'
import { moveModifiedBefore } from '../../../lib/tools/csvExportsArchiving/index'

import type { ControllerConfig, ControllerRuntime, Provider } from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'

const provider: Provider = {
  extractWeeklyTransferFees: {
    region: Region.eu_north_1,
    schedule: async (job, runtime) => {
      const gbpEquivalentArray = getGbpUnits(job.provider)
      job.data = { gbpEquivalentArray, payInMethods: ['BALANCE', 'BANK_TRANSFER', 'BANKGIRO', 'PISP'] }
      return scheduleFeesBrlTax(job, runtime, 'GBP')
    },
    function: extractFeesBrlTax,
    afterControllerFinished: async (config, runtime) => {
      if (!runtime.collection) return
      const gDrive = await GDrive(gDriveSettings.account)

      const gbpEquivalentArray = getGbpUnits(config.provider)
      for (const gbpEquivalent of gbpEquivalentArray) {
        for (const transferType of ['BALANCE', 'BANK_TRANSFER']) {
          await exportToGDrive(config, runtime, gDrive, transferType, gbpEquivalent)
        }
      }
    },
    scheduleAfterFinished: () => {
      return [
        {
          project: ProjectType.payments,
          provider: 'wise_unified',
          type: 'csvFile',
        },
      ]
    },
    config: {
      constrains: { countries: ['GB', 'US', 'DE'] },
    },
  },
  csvFile: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (config, runtime) => {
      if (!runtime.collection) return

      const gDrive = await GDrive(gDriveSettings.account)

      const gbpEquivalentArray = getGbpUnits(config.provider)
      for (const gbpEquivalent of gbpEquivalentArray) {
        for (const transferType of ['BALANCE', 'BANK_TRANSFER']) {
          await exportToGDrive(config, runtime, gDrive, transferType, gbpEquivalent)
        }
      }
    },
    config: {
      concurrency: 1,
    },
  },
}

export default provider

const gDriveSettings = {
  account: 'vor',
  emptyField: '-',
  preFileName: (provider: string, gbpEquivalent?: number, prefix?: string) =>
    `${prefix ? prefix : 'Transfer Fees'}_${provider}_${gbpEquivalent ? `${gbpEquivalent} GBP_` : ''}`,
  fileName: (preFileName: string, date: Date) => `${preFileName}Week_${format(date, 'RRRR_II')}.csv`,
}

async function exportToGDrive(
  config: ControllerConfig,
  runtime: ControllerRuntime,
  gDrive: GDriveUtil,
  transferType: string,
  //isOnlyRoutes: boolean,
  gbpEquivalent?: number,
) {
  const date = startOfISOWeek(new Date())

  // const addDefaultCurrencies = false //['BALANCE', 'BANK_TRANSFER'].includes(transferType)

  const regexMatchArray = ['Wise_v4', 'Wise_v5']
  let preFileName
  let filename
  let csv
  let jsonFromCsv
  let withEmpty: object[]

  const feeTime = format(date, 'RRRR/II')
  const feeTimeUnit = TranferFeeTimeUnit.week
  const transferTypes = transferType === 'BANK_TRANSFER' ? ['BANK_TRANSFER', 'BANKGIRO', 'PISP'] : [transferType]

  const fees = await runtime
    .collection('transferFees')
    .aggregate([
      {
        $match: {
          provider: config.provider,
          feeTimeUnit,
          feeTime,
          transferType: { $in: transferTypes },
          ...(gbpEquivalent && { gbpEquivalent }),
        },
      },
      {
        $sort: { updatedAt: -1 }, // Sort by currency and latest updatedAt
      },
      {
        $group: {
          _id: {
            currency: '$currency',
            feeTime: '$feeTime',
            feeType: '$feeType',
            gbpEquivalent: '$gbpEquivalent',
            transferType: '$transferType',
            productType: '$productType',
          },
          doc: { $first: '$$ROOT' }, // Take the latest document for each currency
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' }, // Flatten the result
      },
      {
        $project: {
          _id: 0,
          project: 0,
          provider: 0,
          updatedAt: 0,
          createdAt: 0,
          itemId: 0,
          providerIsoItemId: 0,
          iso: 0,
          productType: 0,
          // transferType: 0,
          ...(gbpEquivalent && { gbpEquivalent: 0 }),
        },
      },
      {
        $sort: { gbpEquivalent: 1, transferType: 1, feeType: 1, currency: 1, amount: 1 },
      },
    ])
    .toArray()

  if (!fees?.length) {
    await gDrive.notifyDevs(
      `${config.provider} Transfer Fees - Error report`,
      `ControllerId: ${config.id}

           Aggregation empty/failed`,
    )
  } else {
    for (const isOnlyRoutes of [false, true]) {
      const feesSorted = fees.map((item) => {
        const ob = {}
        const keysToSort = []
        for (const key of Object.keys(item)) {
          if (key === 'transferType') continue
          if (key.toUpperCase() !== key) ob[key] = item[key].value ?? item[key]
          else keysToSort.push(key)
        }

        const targetCurrencies = [...keysToSort]

        const sortedTargetCurrencies = [...new Set(targetCurrencies)]
          .sort((a, b) => {
            return a.localeCompare(b)
          })
          .reduce((obj, key) => {
            const val = item[key]

            if (val === 'N/A') {
              obj[key] = val
              return obj
            }

            if (isOnlyRoutes) {
              obj[key] = !item[key].isTargetCurrency
                ? 'N/A'
                : ['BANK_TRANSFER', 'BANKGIRO', 'PISP'].includes(item.transferType)
                  ? val.hasBankTransferPayIn || val.hasBankgiroPayIn || val.hasPISPPayIn
                    ? item.transferType
                    : 'FALSE'
                  : item.transferType === 'BALANCE'
                    ? val.hasBalancePayIn
                    : gDriveSettings.emptyField
            } else {
              obj[key] =
                (item.transferType === 'BANK_TRANSFER' && !val.hasBankTransferPayIn) ||
                (item.transferType === 'BANKGIRO' && !val.hasBankgiroPayIn) ||
                (item.transferType === 'PISP' && !val.hasPISPPayIn)
                  ? '0'
                  : item.feeType === WiseFeeType.variable || item.feeType === 'tax' || item.feeType === 'taxFree'
                    ? round(val.value / item.amount, 5)
                    : val.value
            }

            return obj
          }, {})

        return {
          ...ob,
          ...sortedTargetCurrencies,
        }
      })

      const providerName =
        transferType === 'BALANCE'
          ? `Wise Account${isOnlyRoutes ? ' Routes' : ''} BRL Tax Old`
          : transferType === 'BANK_TRANSFER'
            ? `Wise Bank${isOnlyRoutes ? ' Routes' : ''} BRL Tax Old`
            : config.provider.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))

      preFileName = gDriveSettings.preFileName(providerName, gbpEquivalent)
      if (preFileName) {
        regexMatchArray.push(preFileName)
        filename = gDriveSettings.fileName(preFileName, date)
        if (filename) {
          csv = json2csv(feesSorted, { emptyFieldValue: gDriveSettings.emptyField })
          jsonFromCsv = csv2json(csv)

          withEmpty = []
          for (const row of jsonFromCsv) {
            let countEmpty = 0
            for (const rowElKey in row) {
              if (row[rowElKey] === gDriveSettings.emptyField) {
                countEmpty++
              }
            }
            if (countEmpty > 0) withEmpty.push(row)
          }
          if (withEmpty.length) {
            const withEmptyCsv = json2csv(withEmpty, { emptyFieldValue: gDriveSettings.emptyField })
            const message = `ControllerId: ${config.id}

           ${filename}

            --------------------------------
            Rows - Empty
            --------------------------------

            ${withEmptyCsv}

            --------------------------------

            This is an automated message`
            await gDrive.notifyDevs(`${providerName} Transfer Fees - Error report`, message)
          } else {
            await gDrive.notifyDevs(`${providerName} Transfer Fees - Error report`, `No errors\n${filename}`)
          }
          await gDrive.upsert(
            csv,
            filename,
            accountConfigs[gDrive.account].defaultFileType,
            filename.includes('Routes')
              ? accountConfigs[gDriveSettings.account].folders.wiseOldRoutes
              : accountConfigs[gDriveSettings.account].folders.wiseOldBreakdown,
          )
        }
      }
    }
  }

  await moveModifiedBefore(
    gDrive,
    subDays(new Date(), 1),
    accountConfigs[gDrive.account].folders.archive,
    [
      accountConfigs[gDriveSettings.account].folders.wiseOldRoutes,
      accountConfigs[gDriveSettings.account].folders.wiseOldBreakdown,
    ],
    regexMatchArray,
  )
}
