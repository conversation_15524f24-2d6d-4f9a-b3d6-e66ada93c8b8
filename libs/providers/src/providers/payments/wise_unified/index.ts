import { format, startOfISOWeek, subDays } from 'date-fns'
import { json2csv, csv2json } from 'json-2-csv'

import { ProjectType, Region, TranferFeeTimeUnit, WiseFeeType } from '@datagatherers/datagatherers'
import { accountConfigs, GDrive, round } from '@datagatherers/worker-utils'

import { extractFeesUnified, getGbpUnits, scheduleFeesUnified } from '../../../lib/payments/wise'
import { moveModifiedBefore } from '../../../lib/tools/csvExportsArchiving/index'
import { trackerWiseCurrenciesWW } from '../../non_standard/trackers'

import type { ControllerConfig, ControllerRuntime, Provider } from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'
import type { Document } from 'mongodb'

const provider: Provider = {
  extractWeeklyTransferFees: {
    region: Region.eu_north_1,
    schedule: async (job, runtime) => {
      const gbpEquivalentArray = getGbpUnits(job.provider)
      job.data = { gbpEquivalentArray, payInMethods: ['BALANCE', 'BANK_TRANSFER', 'BANKGIRO', 'PISP'] }
      return scheduleFeesUnified(job, runtime, 'GBP')
    },
    function: extractFeesUnified,
    scheduleAfterFinished: () => {
      return [
        {
          project: ProjectType.payments,
          provider: 'wise_ach',
          type: 'extractWeeklyTransferFees',
        },
      ]
    },
    config: {
      constrains: { countries: ['GB', 'US', 'DE'] },
    },
  },
  csvFile: {
    region: Region.eu_west_1,
    function: () => [],
    afterControllerFinished: async (config, runtime) => {
      if (!runtime.collection) return
      const gDrive = await GDrive(gDriveSettings.account)

      const gbpEquivalentArray = getGbpUnits(config.provider)
      for (const gbpEquivalent of gbpEquivalentArray) {
        for (const transferType of ['BALANCE', 'BANK_TRANSFER']) {
          await exportToGDrive(config, runtime, gDrive, transferType, gbpEquivalent)
        }
      }
    },
    config: {
      concurrency: 1,
    },
  },
  trackerWiseCurrenciesWW: {
    ...trackerWiseCurrenciesWW,
    region: Region.eu_west_3,
  },
}

export default provider

const gDriveSettings = {
  account: 'vor',
  emptyField: '-',
  preFileName: (provider: string, gbpEquivalent?: number, prefix?: string) =>
    `${prefix ? prefix : 'Transfer Fees'}_${provider}_${gbpEquivalent ? `${gbpEquivalent} GBP_` : ''}`,
  fileName: (preFileName: string, date: Date) => `${preFileName}Week_${format(date, 'RRRR_II')}.csv`,
}

function escapeRegExp(input: string) {
  return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

function weekCsvPattern(preFileName: string) {
  return new RegExp(`^${escapeRegExp(preFileName)}Week_\\d{4}_\\d{2}\\.csv$`, 'i')
}

async function exportToGDrive(
  config: ControllerConfig,
  runtime: ControllerRuntime,
  gDrive: GDriveUtil,
  transferType: string,
  gbpEquivalent?: number,
) {
  const date = startOfISOWeek(new Date())

  const addDefaultCurrencies = ['BALANCE', 'BANK_TRANSFER'].includes(transferType)

  const filesToMove: Array<RegExp | string> = [/Wise_v4/i, /Wise_v5/i, /Bank Transfer Fees/i]
  const feeTime = format(date, 'RRRR/II')
  const feeTimeUnit = TranferFeeTimeUnit.week
  const transferTypes = transferType === 'BANK_TRANSFER' ? ['BANK_TRANSFER', 'BANKGIRO', 'PISP'] : [transferType]

  const wiseFees = await getWiseFees(runtime, config, feeTimeUnit, feeTime, transferTypes, gbpEquivalent)
  const brlTaxData = await getBrlTaxData(runtime, feeTimeUnit, feeTime, transferTypes)
  const usdAchFees = await getUsdAchFees(runtime, feeTimeUnit, feeTime, gbpEquivalent)

  if (!wiseFees?.length || !brlTaxData?.length || !usdAchFees?.length) {
    await gDrive.notifyDevs(
      `${config.provider} Transfer Fees - Error report`,
      `ControllerId: ${config.id} Aggregation empty/failed`,
    )
    throw new Error(
      `Controller: ${config.id}, Provider: ${config.provider} Transfer Fees, Error: aggregations empty/failed`,
    )
  }

  let preProcessedFees = wiseFees

  if (addDefaultCurrencies) preProcessedFees = preProcessFees(wiseFees, feeTimeUnit, feeTime)

  for (const isOnlyRoutes of [false, true]) {
    const feesSorted = preProcessedFees.map((item) => {
      const ob = {}
      const keysToSort = []
      for (const key of Object.keys(item)) {
        if (key === 'transferType') continue
        if (key.toUpperCase() !== key) ob[key] = item[key].value ?? item[key]
        else keysToSort.push(key)
      }

      const targetCurrencies = [...keysToSort]
      if (addDefaultCurrencies) targetCurrencies.push(...defaultTargetCurrencies)

      const sortedTargetCurrencies = [...new Set(targetCurrencies)]
        .sort((a, b) => a.localeCompare(b))
        .reduce((obj, key) => {
          const val = item[key]

          if (addDefaultCurrencies && !val) {
            obj[key] = 'N/A'
            return obj
          }

          if (val === 'N/A') {
            obj[key] = val
            return obj
          }

          if (item.currency === 'USD' && key === 'USD') {
            obj[key] = 'N/A'
            return obj
          }

          if (isOnlyRoutes) {
            obj[key] = !item[key].isTargetCurrency
              ? 'N/A'
              : ['BANK_TRANSFER', 'BANKGIRO', 'PISP'].includes(item.transferType)
                ? val.hasBankTransferPayIn || val.hasBankgiroPayIn || val.hasPISPPayIn
                  ? item.transferType
                  : 'FALSE'
                : item.transferType === 'BALANCE'
                  ? val.hasBalancePayIn
                  : gDriveSettings.emptyField
          } else {
            if (item.currency === 'USD' && val && val.value !== undefined && transferType === 'BANK_TRANSFER') {
              const achFee = usdAchFees.find(
                (f) =>
                  f.feeType === item.feeType &&
                  Math.trunc(f.amount) === Math.trunc(item.amount) &&
                  f.gbpEquivalent === gbpEquivalent &&
                  f[key]?.value !== undefined,
              )

              if (achFee && achFee[key]) {
                const avgValue = (achFee[key].value + val.value) / 2

                obj[key] =
                  (item.transferType === 'BANK_TRANSFER' && !val.hasBankTransferPayIn) ||
                  (item.transferType === 'BANKGIRO' && !val.hasBankgiroPayIn) ||
                  (item.transferType === 'PISP' && !val.hasPISPPayIn)
                    ? '0'
                    : item.feeType === WiseFeeType.variable
                      ? round(avgValue / item.amount, 5)
                      : avgValue

                return obj
              }
            }

            if (item.currency === 'BRL' && item.feeType === WiseFeeType.variable && val && val.value !== undefined) {
              const matchingTaxData = brlTaxData.find(
                (taxItem) =>
                  taxItem.transferType === item.transferType &&
                  taxItem.amount === item.amount &&
                  taxItem.gbpEquivalent === gbpEquivalent &&
                  taxItem[key]?.value !== undefined,
              )

              if (matchingTaxData && matchingTaxData[key]?.value) {
                const brlTaxFreeAmount = matchingTaxData[key].value
                obj[key] =
                  (item.transferType === 'BANK_TRANSFER' && !val.hasBankTransferPayIn) ||
                  (item.transferType === 'BANKGIRO' && !val.hasBankgiroPayIn) ||
                  (item.transferType === 'PISP' && !val.hasPISPPayIn)
                    ? '0'
                    : round(brlTaxFreeAmount / item.amount, 5)

                return obj
              }
            }

            obj[key] =
              (item.transferType === 'BANK_TRANSFER' && !val.hasBankTransferPayIn) ||
              (item.transferType === 'BANKGIRO' && !val.hasBankgiroPayIn) ||
              (item.transferType === 'PISP' && !val.hasPISPPayIn)
                ? '0'
                : item.feeType === WiseFeeType.variable
                  ? round(val.value / item.amount, 5)
                  : val.value
          }

          return obj
        }, {})

      return {
        ...ob,
        ...sortedTargetCurrencies,
      }
    })
    if (config.provider === 'wise_unified' && transferType === 'BANK_TRANSFER' && !isOnlyRoutes) {
      const usdBankFees = wiseFees.filter((item) => item.currency === 'USD' && item.transferType === 'BANK_TRANSFER')

      const usdBankFeesProcessed = usdBankFees.map((item) => {
        const obj = {
          amount: item.amount,
          currency: item.currency,
          feeTime: item.feeTime,
          feeTimeUnit: item.feeTimeUnit,
          feeType: item.feeType,
        }

        const targetCurrencies = [
          ...new Set([...Object.keys(item).filter((key) => key.toUpperCase() === key), ...defaultTargetCurrencies]),
        ].sort((a, b) => a.localeCompare(b))

        for (const key of targetCurrencies) {
          const val = item[key]

          if (!val) {
            obj[key] = 'N/A'
          } else if (item.currency === 'USD' && key === 'USD') {
            obj[key] = 'N/A'
          } else if (val === 'N/A') {
            obj[key] = val
          } else if (val && val.value !== undefined) {
            obj[key] =
              (item.transferType === 'BANK_TRANSFER' && !val.hasBankTransferPayIn) ||
              (item.transferType === 'BANKGIRO' && !val.hasBankgiroPayIn) ||
              (item.transferType === 'PISP' && !val.hasPISPPayIn)
                ? '0'
                : item.feeType === WiseFeeType.variable
                  ? round(val.value / item.amount, 5)
                  : val.value
          } else {
            obj[key] = gDriveSettings.emptyField
          }
        }

        return obj
      })

      const usdBankFeesSorted = usdBankFeesProcessed.sort((a, b) => {
        if (a.feeType !== b.feeType) {
          return a.feeType === WiseFeeType.fixed ? -1 : 1
        }
        return a.amount - b.amount
      })

      const usdBankProviderName = `Wise USD Bank Transfer Fees`
      const usdBankPreFileName = gDriveSettings.preFileName(usdBankProviderName, gbpEquivalent)
      const usdBankFilename = gDriveSettings.fileName(usdBankPreFileName, date)

      filesToMove.push(weekCsvPattern(usdBankPreFileName))

      const usdBankCsv = json2csv(usdBankFeesSorted, { emptyFieldValue: gDriveSettings.emptyField })
      const jsonFromCsv = csv2json(usdBankCsv)

      const withEmpty = []
      for (const row of jsonFromCsv) {
        let countEmpty = 0
        for (const rowElKey in row) {
          if (row[rowElKey] === gDriveSettings.emptyField) {
            countEmpty++
          }
        }
        if (countEmpty > 0) withEmpty.push(row)
      }
      if (withEmpty.length) {
        const withEmptyCsv = json2csv(withEmpty, { emptyFieldValue: gDriveSettings.emptyField })
        const message = `ControllerId: ${config.id}

               ${usdBankFilename}

                --------------------------------
                Rows - Empty
                --------------------------------

                ${withEmptyCsv}

                --------------------------------

                This is an automated message`
        await gDrive.notifyDevs(`${usdBankProviderName} Transfer Fees - Error report`, message)
      } else {
        await gDrive.notifyDevs(`${usdBankProviderName} Transfer Fees - Error report`, `No errors\n${usdBankFilename}`)
      }
      await gDrive.upsert(
        usdBankCsv,
        usdBankFilename,
        accountConfigs[gDrive.account].defaultFileType,
        routeToOutputFolder(usdBankFilename),
      )
    }

    const providerName =
      transferType === 'BALANCE'
        ? `Wise Account${isOnlyRoutes ? ' Routes' : ''} Old`
        : transferType === 'BANK_TRANSFER'
          ? `Wise Bank${isOnlyRoutes ? ' Routes' : ''} Old`
          : config.provider.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))

    const preFileName = gDriveSettings.preFileName(providerName, gbpEquivalent)

    const filename = gDriveSettings.fileName(preFileName, date)

    filesToMove.push(weekCsvPattern(preFileName))

    const csv = json2csv(feesSorted, { emptyFieldValue: gDriveSettings.emptyField })
    const jsonFromCsv = csv2json(csv)

    const withEmpty = []
    for (const row of jsonFromCsv) {
      let countEmpty = 0
      for (const rowElKey in row) {
        if (row[rowElKey] === gDriveSettings.emptyField) {
          countEmpty++
        }
      }
      if (countEmpty > 0) withEmpty.push(row)
    }
    if (withEmpty.length) {
      const withEmptyCsv = json2csv(withEmpty, { emptyFieldValue: gDriveSettings.emptyField })
      const message = `ControllerId: ${config.id}

           ${filename}

            --------------------------------
            Rows - Empty
            --------------------------------

            ${withEmptyCsv}

            --------------------------------

            This is an automated message`
      await gDrive.notifyDevs(`${providerName} Transfer Fees - Error report`, message)
    } else {
      await gDrive.notifyDevs(`${providerName} Transfer Fees - Error report`, `No errors\n${filename}`)
    }
    await gDrive.upsert(csv, filename, accountConfigs[gDrive.account].defaultFileType, routeToOutputFolder(filename))
  }
  for (const folder of [
    accountConfigs[gDrive.account].folders.wiseOldTFees ?? accountConfigs[gDriveSettings.account].folders.wiseTFees,
    accountConfigs[gDriveSettings.account].folders.wiseOldRoutes,
    accountConfigs[gDriveSettings.account].folders.wiseOldBreakdown,
  ]) {
    try {
      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[gDrive.account].folders.archive,
        [folder],
        filesToMove,
      )
    } catch (error) {
      const message = `ControllerId: ${config.id}
     Error moving files to archive
     --------------------------------

     ${error.message}

     --------------------------------

     This is an automated message`
      await gDrive.notifyDevs(`MoveModifiedBefore Folder: ${folder}`, message)
    }
  }
}

function routeToOutputFolder(fileName: string) {
  if (fileName.includes('Wise Bank Old_1300 GBP') || fileName.includes('Wise Account Old_1300 GBP'))
    return accountConfigs[gDriveSettings.account].folders.wiseOldTFees

  return fileName.includes('Routes')
    ? accountConfigs[gDriveSettings.account].folders.wiseOldRoutes
    : accountConfigs[gDriveSettings.account].folders.wiseOldBreakdown
}

async function getBrlTaxData(
  runtime: ControllerRuntime,
  feeTimeUnit: TranferFeeTimeUnit,
  feeTime: string,
  transferTypes: string[],
) {
  return runtime
    .collection('transferFees')
    .aggregate([
      {
        $match: {
          provider: 'wise_brl_tax',
          currency: 'BRL',
          feeTimeUnit,
          feeTime,
          feeType: 'taxFree',
          transferType: { $in: transferTypes },
        },
      },
      {
        $sort: { updatedAt: -1 },
      },
      {
        $group: {
          _id: {
            currency: '$currency',
            feeType: '$feeType',
            gbpEquivalent: '$gbpEquivalent',
            transferType: '$transferType',
            amount: '$amount',
          },
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' },
      },
    ])
    .toArray()
}

async function getUsdAchFees(
  runtime: ControllerRuntime,
  feeTimeUnit: TranferFeeTimeUnit,
  feeTime: string,
  gbpEquivalent?: number,
) {
  return runtime
    .collection('transferFees')
    .aggregate([
      {
        $match: {
          provider: 'wise_ach',
          currency: 'USD',
          feeTimeUnit,
          feeTime,
          transferType: 'ACH',
          feeType: { $in: [WiseFeeType.fixed, WiseFeeType.variable] },
          ...(gbpEquivalent && { gbpEquivalent }),
        },
      },
      {
        $sort: { updatedAt: -1 },
      },
      {
        $group: {
          _id: {
            currency: '$currency',
            feeTime: '$feeTime',
            feeType: '$feeType',
            gbpEquivalent: '$gbpEquivalent',
            transferType: '$transferType',
            amount: '$amount',
          },
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' },
      },
    ])
    .toArray()
}

async function getWiseFees(
  runtime: ControllerRuntime,
  config: ControllerConfig,
  feeTimeUnit: TranferFeeTimeUnit,
  feeTime: string,
  transferTypes: string[],
  gbpEquivalent?: number,
) {
  return runtime
    .collection('transferFees')
    .aggregate([
      {
        $match: {
          provider: config.provider,
          feeTimeUnit,
          feeTime,
          transferType: { $in: transferTypes },
          ...(gbpEquivalent && { gbpEquivalent }),
        },
      },
      {
        $sort: { updatedAt: -1 },
      },
      {
        $group: {
          _id: {
            currency: '$currency',
            feeTime: '$feeTime',
            feeType: '$feeType',
            gbpEquivalent: '$gbpEquivalent',
            transferType: '$transferType',
            productType: '$productType',
          },
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' },
      },
      {
        $project: {
          _id: 0,
          project: 0,
          provider: 0,
          updatedAt: 0,
          createdAt: 0,
          itemId: 0,
          providerIsoItemId: 0,
          iso: 0,
          productType: 0,
          ...(gbpEquivalent && { gbpEquivalent: 0 }),
        },
      },
      {
        $sort: { gbpEquivalent: 1, transferType: 1, feeType: 1, currency: 1, amount: 1 },
      },
    ])
    .toArray()
}

function preProcessFees(wiseFees: Document[] = [], feeTimeUnit: TranferFeeTimeUnit, feeTime: string) {
  const sourceCurrenciesArray = [...new Set([...wiseFees.map((fee) => fee.currency), ...defaultSourceCurrencies])].sort(
    (a, b) => a.localeCompare(b),
  )

  return sourceCurrenciesArray
    .flatMap((sourceCurrency) => {
      const feeFromTracker = wiseFees.filter((fee) => fee.currency === sourceCurrency)
      if (feeFromTracker?.length) return feeFromTracker

      return [WiseFeeType.fixed, WiseFeeType.variable].map((feeType) => ({
        amount: 'N/A',
        currency: sourceCurrency,
        feeTime,
        feeTimeUnit,
        feeType,
      }))
    })
    .sort((a, b) => {
      const cmp1 = a.feeType.localeCompare(b.feeType)
      if (cmp1 !== 0) return cmp1

      return a.currency.localeCompare(b.currency)
    })
}

const defaultSourceCurrencies = [
  'AED',
  'AUD',
  'BDT',
  'BGN',
  'BRL',
  'CAD',
  'CHF',
  'CLP',
  'CNY',
  'CRC',
  'CZK',
  'DKK',
  'EGP',
  'EUR',
  'GBP',
  'GEL',
  'HKD',
  'HUF',
  'IDR',
  'ILS',
  'INR',
  'JPY',
  'KES',
  'KRW',
  'LKR',
  'MAD',
  'MXN',
  'MYR',
  'NGN',
  'NOK',
  'NPR',
  'NZD',
  'PHP',
  'PKR',
  'PLN',
  'RON',
  'SEK',
  'SGD',
  'THB',
  'TRY',
  'TZS',
  'UAH',
  'UGX',
  'USD',
  'UYU',
  'VND',
  'XOF',
  'ZAR',
]

const defaultTargetCurrencies = [
  'AED',
  'ARS',
  'AUD',
  'BDT',
  'BGN',
  'BRL',
  'BWP',
  'CAD',
  'CHF',
  'CLP',
  'CNY',
  'COP',
  'CRC',
  'CZK',
  'DKK',
  'EGP',
  'EUR',
  'FJD',
  'GBP',
  'GEL',
  'GHS',
  'GTQ',
  'HKD',
  'HUF',
  'IDR',
  'ILS',
  'INR',
  'JPY',
  'KES',
  'KRW',
  'LKR',
  'MAD',
  'MXN',
  'MYR',
  'NGN',
  'NOK',
  'NPR',
  'NZD',
  'PHP',
  'PKR',
  'PLN',
  'RON',
  'SEK',
  'SGD',
  'THB',
  'TRY',
  'TZS',
  'UAH',
  'UGX',
  'USD',
  'UYU',
  'VND',
  'XOF',
  'ZAR',
  'ZMW',
]
