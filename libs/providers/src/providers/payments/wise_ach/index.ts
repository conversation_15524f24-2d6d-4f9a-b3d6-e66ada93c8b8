import { format, startOfISOWeek, subDays } from 'date-fns'
import { csv2json, json2csv } from 'json-2-csv'

import { ProjectType, Region, WiseFeeType } from '@datagatherers/datagatherers'
import { GDrive, accountConfigs, round } from '@datagatherers/worker-utils'

import { extractFeesAch, getGbpUnits, scheduleFeesAch } from '../../../lib/payments/wise'
import { moveModifiedBefore } from '../../../lib/tools/csvExportsArchiving'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractWeeklyTransferFees: {
    region: Region.eu_west_1,
    schedule: async (job, runtime) => {
      job.data = {
        gbpEquivalentArray: getGbpUnits(job.provider),
      }
      return scheduleFeesAch(job, runtime, 'GBP')
    },
    function: extractFeesAch,
    afterControllerFinished: async (config, runtime) => {
      await exportToGDrive(config, runtime)
    },
    scheduleAfterFinished: () => {
      return [
        {
          project: ProjectType.payments,
          provider: 'wise_brl_tax',
          type: 'extractWeeklyTransferFees',
        },
      ]
    },
    config: {
      constrains: { countries: ['GB', 'US', 'DE', 'FR', 'IT'] },
    },
  },
  csvFile: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (config, runtime) => {
      await exportToGDrive(config, runtime)
    },
    config: {
      concurrency: 1,
    },
  },
}

export default provider

const gDriveSettings = {
  account: 'vor',
  emptyField: '-',
  preFileName: (provider: string, gbpEquivalent?: number, prefix?: string) =>
    `${prefix ? prefix : 'Transfer Fees'}_${provider}_${gbpEquivalent ? `${gbpEquivalent} GBP_` : ''}`,
  fileName: (preFileName: string, date: Date) => `${preFileName}Week_${format(date, 'RRRR_II')}.csv`,
}

async function exportToGDrive(config, runtime, gbpEquivalent?) {
  if (!runtime.collection) return
  const gDriveAccount = gDriveSettings.account
  const providerName =
    config.provider === 'wise_ach'
      ? 'Wise ACH Old'
      : config.provider.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
  const gDrive = await GDrive(gDriveAccount)
  const date = startOfISOWeek(new Date())

  const regexMatchArray = ['Wise_ach']
  let preFileName
  let filename
  let csv
  let jsonFromCsv
  let withEmpty: object[]

  const parentFolderId = (filename: string) =>
    filename.includes('Routes')
      ? accountConfigs[gDriveSettings.account].folders.wiseOldRoutes
      : accountConfigs[gDriveSettings.account].folders.wiseOldBreakdown
  const fees = await runtime
    .collection('transferFees')
    .aggregate([
      {
        $match: {
          provider: config.provider,
          feeTimeUnit: 'week',
          feeTime: format(date, 'RRRR/II'),
          transferType: { $ne: 'CARD' },
          ...(gbpEquivalent && { gbpEquivalent }),
        },
      },
      {
        $sort: { updatedAt: -1 }, // Sort by currency and latest updatedAt
      },
      {
        $group: {
          _id: {
            currency: '$currency',
            feeTime: '$feeTime',
            feeType: '$feeType',
            gbpEquivalent: '$gbpEquivalent',
            transferType: '$transferType',
            productType: '$productType',
          },
          doc: { $first: '$$ROOT' }, // Take the latest document for each currency
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' }, // Flatten the result
      },
      {
        $project: {
          _id: 0,
          project: 0,
          provider: 0,
          updatedAt: 0,
          createdAt: 0,
          itemId: 0,
          providerIsoItemId: 0,
          iso: 0,
        },
      },
      {
        $sort: { gbpEquivalent: 1, productType: 1, transferType: 1, feeType: 1, currency: 1, amount: 1 },
      },
    ])
    .toArray()

  if (!fees?.length) {
    await gDrive.notifyDevs(
      `${providerName} Transfer Fees - Error report`,
      `ControllerId: ${config.id}

           Aggregation empty/failed`,
    )
  } else {
    const feesSorted = fees.map((item) => {
      const ob = {}
      const keysToSort = []
      for (const key of Object.keys(item)) {
        if (key.toUpperCase() !== key) ob[key] = item[key].value ?? item[key]
        else keysToSort.push(key)
      }
      const sortedCurrencies = keysToSort
        .sort((a, b) => {
          return a.localeCompare(b)
        })
        .reduce((obj, key) => {
          const val = item[key]
          if (val === 'N/A') {
            obj[key] = val
            return obj
          }

          obj[key] =
            ['wise_lfl_revolut'].includes(config.provider) && item.feeType === WiseFeeType.variable
              ? round(val.value / item.amount, 5)
              : val.value

          return obj
        }, {})

      return {
        ...ob,
        ...sortedCurrencies,
      }
    })

    preFileName = gDriveSettings.preFileName(providerName, gbpEquivalent)
    if (preFileName) {
      regexMatchArray.push(preFileName)
      filename = gDriveSettings.fileName(preFileName, date)
      if (filename) {
        csv = json2csv(feesSorted, { emptyFieldValue: gDriveSettings.emptyField })
        jsonFromCsv = csv2json(csv)

        withEmpty = []
        for (const row of jsonFromCsv) {
          let countEmpty = 0
          for (const rowElKey in row) {
            if (row[rowElKey] === gDriveSettings.emptyField) {
              countEmpty++
            }
          }
          if (countEmpty > 0) withEmpty.push(row)
        }
        if (withEmpty.length) {
          const withEmptyCsv = json2csv(withEmpty, { emptyFieldValue: gDriveSettings.emptyField })
          // const withZeroCsv = json2csv(withZero, { emptyFieldValue: gDriveSettings.emptyField })
          const message = `ControllerId: ${config.id}

           ${filename}

            --------------------------------
            Rows - Empty
            --------------------------------

            ${withEmptyCsv}

            --------------------------------

            This is an automated message`
          await gDrive.notifyDevs(`${providerName} Transfer Fees - Error report`, message)
        } else {
          await gDrive.notifyDevs(`${providerName} Transfer Fees - Error report`, 'No errors')
        }
        await gDrive.upsert(csv, filename, accountConfigs[gDriveAccount].defaultFileType, parentFolderId(filename))
      }
    }
  }

  await moveModifiedBefore(
    gDrive,
    subDays(new Date(), 1),
    accountConfigs[gDriveAccount].folders.archive,
    [
      accountConfigs[gDriveSettings.account].folders.wiseOldRoutes,
      accountConfigs[gDriveSettings.account].folders.wiseOldBreakdown,
    ],
    regexMatchArray,
  )
}
