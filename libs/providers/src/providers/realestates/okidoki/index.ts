import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'

import {
  extractListings,
  extractDealers,
  extractProviderConfig,
  scheduleDealers,
  scheduleExtract,
  snapshot,
  updateExistingDealers,
} from '../../../lib/classifieds/okidoki'

import type { Provider, JobDefinition, JobInstance } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractListingsEE: {
    cron: '0 13 * * TUE', // Every tuesday at 01:00 GMT
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.EE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDealersEE',
      }
    },
    config: extractProviderConfig,
  },
  extractDealersEE: {
    region: Region.eu_north_1,
    function: extractDealers,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleDealers(job, runtime, Iso3166Alpha2.EE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'updateRemainingDealersEE',
      }
    },
    config: extractProviderConfig,
  },
  updateRemainingDealersEE: {
    region: Region.eu_north_1,
    function: async (job: JobInstance) => updateExistingDealers(job, Iso3166Alpha2.EE),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.EE, config, runtime),
    config: extractProviderConfig,
  },
}

export default provider
