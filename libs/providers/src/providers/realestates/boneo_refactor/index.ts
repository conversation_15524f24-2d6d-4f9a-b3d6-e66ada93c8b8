import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import {
  extract,
  extractNewProduction,
  extractTowns,
  scheduleExtract,
  scheduleExtractNewProduction,
  scheduleTowns,
} from '../../../lib/realestates/boneo_refactor/index'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractTownsSE: {
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleTowns(job, runtime, Iso3166Alpha2.SE),
    function: extractTowns,
    config: {
      concurrency: 10,
    },
  },
  extractSE: {
    cron: '0 12 * * WED',
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.SE),
    function: extract,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractNewProductionSE',
      }
    },
    config: {
      concurrency: 5,
      perJobTimeout: 30 * 60 * 1000,
    },
  },
  extractNewProductionSE: {
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractNewProduction(job, runtime, Iso3166Alpha2.SE),
    function: extractNewProduction,
    snapshot: async (config, runtime) => {
      const date = new Date()
      await snapshot(Iso3166Alpha2.SE, config, runtime, undefined, date)
      await snapshot(Iso3166Alpha2.SE, config, runtime, undefined, date, 'boneo_refactor_new_production', {
        'data.adType': { $in: ['Project', 'ProjectUnit'] },
      })

      await snapshot(Iso3166Alpha2.SE, config, runtime, undefined, date, 'boneo_refactor_for_sale', {
        'data.adType': { $nin: ['Project', 'ProjectUnit'] },
      })
    },
    config: {
      concurrency: 5,
    },
  },
}

export default provider
