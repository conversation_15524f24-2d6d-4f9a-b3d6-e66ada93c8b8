import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config, snapshot } from '@datagatherers/worker-utils'

import { extract, schedule } from '../../../lib/realestates/domoplius/index'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractLT: {
    cron: '0 10 * * FRI',
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => schedule(job, Iso3166Alpha2.LT),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.LT, config, runtime),
    config,
  },
}

export default provider
