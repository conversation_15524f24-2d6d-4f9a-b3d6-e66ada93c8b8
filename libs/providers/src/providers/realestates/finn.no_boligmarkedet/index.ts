import { endOfISOWeek, format, parse, subMonths } from 'date-fns'

import { DataType, Iso3166Alpha2, Region, ResultType, TimeUnit } from '@datagatherers/datagatherers'

import type { JobInstance, Provider, Result, Snapshot } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractStatisticsNO: {
    region: Region.eu_north_1,
    cron: '0 11 * * WED',
    function: extract,
    scheduleAfterFinished: (configuration) => ({
      project: configuration.project,
      provider: configuration.provider,
      type: 'populateSnapshots',
    }),
    config: {
      concurrency: 1,
    },
  },
  populateSnapshots: {
    region: Region.eu_north_1,
    function: populateSnapshots,
    config: {
      concurrency: 1,
    },
  },
}

export default provider

async function extract(jobInstance: JobInstance): Promise<Result[]> {
  const url = 'https://www.finn.no/realestate/boligmarkedet/?filters='

  const { statusCode, body } = await jobInstance.runtime.got(url)
  if (statusCode !== 200 || !body) throw new Error(`Unable to retrieve html at ${url}`)
  const currentNowValues = extractCurrentNowStats(body)

  const remixContext = extractRemixContext(body)
  const statisticsData = remixContext?.state?.loaderData?.['routes/realestate.boligmarkedet']?.statisticsData

  if (!statisticsData) throw new Error('statisticsData not found in remixContext')

  const squareMeterPriceHistoryList: SquareMeterPriceHistoryEntry[] = statisticsData.squareMeterPriceHist || []

  const adsPublishedHistoryList: AdsPublishedHistoryEntry[] = statisticsData.adsPublishedHist || []

  const clicksFirst8DaysHistoryList: ClicksFirst8DaysHistoryEntry[] = statisticsData.clicksFirst8DaysHist || []

  const notificationsAverageHistoryList: NotificationsAvgHistoryEntry[] = statisticsData.notificationsAvgHist || []

  const buildYearMonthIndex = <T extends { yearMonth: string }>(list: T[]): Record<string, T> =>
    list.reduce<Record<string, T>>((accumulator, entry) => {
      accumulator[entry.yearMonth] = entry
      return accumulator
    }, {})

  const adsPublishedIndex = buildYearMonthIndex(adsPublishedHistoryList)

  const clicksFirst8DaysIndex = buildYearMonthIndex(clicksFirst8DaysHistoryList)

  const notificationsAverageIndex = buildYearMonthIndex(notificationsAverageHistoryList)

  const monthlySnapshotList: MonthlySnapshotData[] = squareMeterPriceHistoryList
    .map((historyEntry) => {
      const yearMonthValue = historyEntry.yearMonth
      const adsPublishedEntry = adsPublishedIndex[yearMonthValue]
      const clicksFirst8DaysEntry = clicksFirst8DaysIndex[yearMonthValue]
      const notificationsAverageEntry = notificationsAverageIndex[yearMonthValue]
      return {
        snapTime: yearMonthToSnapTime(yearMonthValue),
        yearMonth: yearMonthValue,
        squareMeterPrice: toNumber(historyEntry.avgSquareMeterPrice),
        adsPublished: toNumber(adsPublishedEntry?.publishedAds),
        clicksFirst8Days: toNumber(clicksFirst8DaysEntry?.clicks),
        notificationEmailsAvg: toNumber(notificationsAverageEntry?.emails),
        notificationPushAvg: toNumber(notificationsAverageEntry?.push),
        notificationAds: toNumber(notificationsAverageEntry?.ads),
      }
    })
    .filter((monthEntry) => !!monthEntry.snapTime)

  if (!monthlySnapshotList.length) throw new Error('No monthly stats assembled')

  const weeklySnapshot = {
    snapTime: getSnapTime(new Date(), TimeUnit.weeks),
    clicksFirst8Days: currentNowValues.clicksFirst8Days,
    squareMeterPrice: currentNowValues.squareMeterPrice,
    notificationAds: currentNowValues.notificationAds,
    adsPublished: currentNowValues.adsPublished,
  }

  return [
    {
      type: ResultType.town,
      data: {
        project: jobInstance.project,
        provider: jobInstance.provider,
        iso: Iso3166Alpha2.NO,
        itemId: Date.now().toString(),
        data: { monthlySnapshotList, weeklySnapshot },
      },
    },
  ]
}

async function populateSnapshots(jobInstance: JobInstance): Promise<[]> {
  const latestTownDocument = await jobInstance.runtime.collection<TownDocument>('town').findOne(
    {
      project: jobInstance.project,
      provider: jobInstance.provider,
      iso: Iso3166Alpha2.NO,
    },
    { sort: { itemId: -1 } },
  )
  if (!latestTownDocument) throw new Error('No latest town found')

  const monthlySnapshotDataList: unknown = latestTownDocument.data.monthlySnapshotList || []
  if (!Array.isArray(monthlySnapshotDataList) || monthlySnapshotDataList.length === 0) {
    throw new Error('Latest town has no monthly data')
  }

  const validatedMonthlySnapshotDataList = monthlySnapshotDataList as MonthlySnapshotData[]

  const statisticMappings: { key: keyof MonthlySnapshotData; stockType: string }[] = [
    { key: 'squareMeterPrice', stockType: 'square_meter_price' },
    { key: 'adsPublished', stockType: 'ads_published' },
    { key: 'clicksFirst8Days', stockType: 'clicks_first_8_days' },
    { key: 'notificationEmailsAvg', stockType: 'notifications_emails_avg' },
    { key: 'notificationPushAvg', stockType: 'notifications_push_avg' },
    { key: 'notificationAds', stockType: 'notifications_ads' },
  ]

  const now = new Date()
  const currentMonthSnap = format(now, 'yyyy/MM')
  const previousMonthSnap = format(subMonths(now, 1), 'yyyy/MM')
  const allowedMonthlySnaps = new Set([currentMonthSnap, previousMonthSnap])

  const timestampNow = new Date()
  const snapshotCollection = jobInstance.runtime.collection<Omit<Snapshot, '_id'>>('snapshot')

  for (const monthlyEntry of validatedMonthlySnapshotDataList) {
    if (!allowedMonthlySnaps.has(monthlyEntry.snapTime)) continue

    const statisticsList = statisticMappings
      .map((mapping) => ({
        stockType: mapping.stockType,
        count: toNumber(monthlyEntry[mapping.key]),
      }))
      .filter((s) => typeof s.count === 'number' && !Number.isNaN(s.count))

    const snapshotQuery = {
      project: jobInstance.project,
      controllerId: '-1',
      provider: jobInstance.provider,
      iso: Iso3166Alpha2.NO,
      snapTime: monthlyEntry.snapTime,
      snapTimeUnit: TimeUnit.months,
      dataType: DataType.yearlyProgress,
    }

    await snapshotCollection.updateOne(
      snapshotQuery,
      {
        $set: {
          updatedAt: timestampNow,
          stats: statisticsList,
        },
        $setOnInsert: {
          ...snapshotQuery,
          createdAt: timestampNow,
        },
      },
      { upsert: true },
    )
  }

  const weeklySnapshot = latestTownDocument.data.weeklySnapshot
  if (weeklySnapshot) {
    const currentWeekSnap = getSnapTime(now, TimeUnit.weeks)
    if (weeklySnapshot.snapTime === currentWeekSnap) {
      const weeklyKeys: (keyof WeeklySnapshot)[] = [
        'clicksFirst8Days',
        'squareMeterPrice',
        'notificationAds',
        'adsPublished',
      ]
      const invalidWeekly = weeklyKeys.filter(
        (k) => typeof weeklySnapshot[k] !== 'number' || weeklySnapshot[k] === 0 || Number.isNaN(weeklySnapshot[k]),
      )

      if (invalidWeekly.length === 0) {
        const weeklyStatisticMappings: { key: keyof WeeklySnapshot; stockType: string }[] = [
          { key: 'squareMeterPrice', stockType: 'square_meter_price' },
          { key: 'adsPublished', stockType: 'ads_published' },
          { key: 'clicksFirst8Days', stockType: 'clicks_first_8_days' },
          { key: 'notificationAds', stockType: 'notifications_ads' },
        ]

        const weeklyStats = weeklyStatisticMappings.map((m) => ({
          stockType: m.stockType,
          count: toNumber(weeklySnapshot[m.key]),
        }))

        const weeklySnapshotQuery = {
          project: jobInstance.project,
          controllerId: '-1',
          provider: jobInstance.provider,
          iso: Iso3166Alpha2.NO,
          snapTime: weeklySnapshot.snapTime,
          snapTimeUnit: TimeUnit.weeks,
          dataType: DataType.yearlyProgress,
        }

        await snapshotCollection.updateOne(
          weeklySnapshotQuery,
          {
            $set: {
              updatedAt: timestampNow,
              stats: weeklyStats,
            },
            $setOnInsert: {
              ...weeklySnapshotQuery,
              createdAt: timestampNow,
            },
          },
          { upsert: true },
        )
      }
    }
  }

  return []
}

function extractRemixContext(htmlString: string): RemixContext | null {
  const markerString = 'window.__remixContext'
  const markerIndex = htmlString.indexOf(markerString)
  if (markerIndex === -1) return null
  const equalsIndex = htmlString.indexOf('=', markerIndex)
  if (equalsIndex === -1) return null
  let startIndex = equalsIndex + 1
  const maxLeadingScanDistance = 10000
  let leadingScanSteps = 0
  while (startIndex < htmlString.length && htmlString[startIndex] !== '{') {
    startIndex++
    leadingScanSteps++
    if (leadingScanSteps > maxLeadingScanDistance) return null
  }
  if (htmlString[startIndex] !== '{') return null
  let position = startIndex
  let depthCount = 0
  let insideStringQuote: string | null = null
  let escapeMode = false
  const maxBodyScanLength = 2_000_000
  let processedCharacters = 0
  for (; position < htmlString.length; position++) {
    if (processedCharacters++ > maxBodyScanLength) return null
    const character = htmlString[position]
    if (insideStringQuote) {
      if (escapeMode) {
        escapeMode = false
      } else if (character === '\\') {
        escapeMode = true
      } else if (character === insideStringQuote) {
        insideStringQuote = null
      }
      continue
    } else {
      if (character === '"' || character === "'" || character === '`') {
        insideStringQuote = character
        continue
      }
      if (character === '{') depthCount++
      else if (character === '}') {
        depthCount--
        if (depthCount === 0) {
          const rawJson = htmlString
            .slice(startIndex, position + 1)
            .trim()
            .replace(/,\s*<\/script>/i, '</script>')
          try {
            return JSON.parse(rawJson) as RemixContext
          } catch {
            return null
          }
        }
      }
    }
  }
  return null
}

export function extractCurrentNowStats(html: string): {
  clicksFirst8Days: number
  squareMeterPrice: number
  notificationAds: number
  adsPublished: number
} {
  const sectionStartIdx = html.indexOf('>Akkurat nå<')
  if (sectionStartIdx === -1) {
    return { clicksFirst8Days: 0, squareMeterPrice: 0, notificationAds: 0, adsPublished: 0 }
  }

  let sectionEndIdx = html.indexOf('<h2', sectionStartIdx + 1)
  if (sectionEndIdx === -1) sectionEndIdx = html.indexOf('Historiske trender', sectionStartIdx + 1)
  if (sectionEndIdx === -1) sectionEndIdx = html.length
  const section = html
    .slice(sectionStartIdx, sectionEndIdx)
    .replace(/<!--[\s\S]*?-->/g, '')
    .replace(/&nbsp;/gi, ' ')

  const parseNumber = (raw: string): number => {
    const digits = raw.replace(/[^\d]/g, '')
    if (!digits) return 0
    return Number(digits)
  }

  const extractValue = (titlePatterns: RegExp[]): number => {
    for (const pattern of titlePatterns) {
      const h3Match = section.match(new RegExp(`<h3[^>]*>\\s*${pattern.source}\\s*</h3>`, 'i'))
      if (!h3Match) continue
      const after = section.slice(h3Match.index + h3Match[0].length)
      const pMatch = after.match(/<p[^>]*class="[^"]*font-bold[^"]*"[^>]*>(.*?)<\/p>/i)
      if (!pMatch) continue
      const text = pMatch[1].replace(/<[^>]+>/g, ' ').trim()
      const numPart = text.match(/(\d[\d\s.]*)/)
      if (numPart) return parseNumber(numPart[1])
    }
    return 0
  }

  const clicksFirst8Days = extractValue([/Klikk per annonse \(første 8 dager\)/])
  const squareMeterPrice = extractValue([/Pris per kvadratmeter\s*\(m²\)/, /Pris per kvadratmeter/])
  const notificationAds = extractValue([/Varslinger sendt per annonse/])
  const adsPublished = extractValue([/Nye annonser/])

  return {
    clicksFirst8Days,
    squareMeterPrice,
    notificationAds,
    adsPublished,
  }
}

function yearMonthToSnapTime(yearMonthString: string): string | null {
  if (!/^\d{4}-\d{2}$/.test(yearMonthString)) return null
  const parsedDate = parse(yearMonthString, 'yyyy-MM', new Date())
  if (isNaN(parsedDate.getTime())) return null
  return yearMonthString.replace('-', '/')
}

function toNumber(value: unknown): number {
  if (value === null || value === undefined) return 0
  if (typeof value === 'number') return value
  const numeric = Number(String(value).replace(/[^\d.-]/g, ''))
  return Number.isFinite(numeric) ? numeric : 0
}

function getSnapTime(startDate: Date, snapTimeUnit: TimeUnit) {
  return snapTimeUnit === TimeUnit.weeks ? format(endOfISOWeek(startDate), 'RRRR/II') : format(startDate, 'yyyy/MM')
}
interface SquareMeterPriceHistoryEntry {
  avgSquareMeterPrice: number
  ads: number
  date: string
  dateText: string
  yearMonth: string
  yearMonthText: string
}

interface AdsPublishedHistoryEntry {
  publishedAds: number
  date: string
  dateText: string
  yearMonth: string
  yearMonthText: string
}

interface ClicksFirst8DaysHistoryEntry {
  clicks: number
  ads: number
  date: string
  dateText: string
  yearMonth: string
  yearMonthText: string
}

interface NotificationsAvgHistoryEntry {
  emails: number
  push: number
  ads: number
  date: string
  dateText: string
  yearMonth: string
  yearMonthText: string
}

interface StatisticsData {
  squareMeterPriceAvgLast30Days: number
  squareMeterPriceAvg30DaysComparison: number
  squareMeterPriceHist: SquareMeterPriceHistoryEntry[]
  adsPublishedLast30Days: number
  adsPublishedLast30DaysComparison: number
  adsPublishedHist: AdsPublishedHistoryEntry[]
  clicksFirst8DaysAvgLast30Days: number
  clicksFirst8Days30DaysComparison: number
  clicksFirst8DaysHist: ClicksFirst8DaysHistoryEntry[]
  notificationAverageLast30Days: number
  notificationAverage30DaysComparison: number
  notificationsAvgHist: NotificationsAvgHistoryEntry[]
}

interface BoligRouteLoaderData {
  statisticsData?: StatisticsData
}

interface LoaderData {
  [key: string]: unknown
  'routes/realestate.boligmarkedet'?: BoligRouteLoaderData
}

interface RemixContext {
  state?: {
    loaderData?: LoaderData
  }
}

interface MonthlySnapshotData {
  snapTime: string
  yearMonth: string
  squareMeterPrice: number
  adsPublished: number
  clicksFirst8Days: number
  notificationEmailsAvg: number
  notificationPushAvg: number
  notificationAds: number
}

interface WeeklySnapshot {
  snapTime: string
  clicksFirst8Days: number
  squareMeterPrice: number
  notificationAds: number
  adsPublished: number
}

interface TownDocument {
  project: string
  provider: string
  iso: Iso3166Alpha2
  itemId: string
  data: {
    monthlySnapshotList: MonthlySnapshotData[]
    weeklySnapshot?: WeeklySnapshot
  }
}
