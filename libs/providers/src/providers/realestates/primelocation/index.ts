import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { configBrowser, snapshot } from '@datagatherers/worker-utils'

import { extract, scheduler } from '../../../lib/realestates/primelocation/index'

import type { JobInstance, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractGB: {
    region: Region.us_east_1,
    cron: '0 5 * * FRI',
    function: async (job: JobInstance) => extract(job),
    schedule: async (job, runtime) => scheduler(job, runtime, Iso3166Alpha2.GB),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.GB, config, runtime),
    config: {
      ...configBrowser,
      browser: {
        userAgent: {
          deviceCategory: 'desktop',
        },
      },
      constrains: {
        countries: ['US'],
      },
      concurrency: 5,
    },
  },
}

export default provider
