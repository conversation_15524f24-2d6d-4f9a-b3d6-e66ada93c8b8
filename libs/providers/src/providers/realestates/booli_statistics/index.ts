import { format, addMonths, isBefore } from 'date-fns'
import { JSD<PERSON> } from 'jsdom'

import { DataType, Iso3166Alpha2, ProjectType, Region, ResultType, TimeUnit } from '@datagatherers/datagatherers'

import { trackerBooliStatistikLauncherSE } from '../../non_standard/trackers'

import type { JobInstance, Provider, Snapshot } from '@datagatherers/datagatherers'

const provider: Provider = {
  trackerBooliStatistikLauncherSE,
  extractStatisticsSE: {
    region: Region.eu_north_1,
    function: extract,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'populateSnapshots',
      }
    },
    config: {
      concurrency: 1,
    },
  },
  populateSnapshots: {
    region: Region.eu_north_1,
    function: snapshots,
    scheduleAfterFinished: () => {
      return {
        project: ProjectType.non_standard,
        provider: 'csvExports',
        type: 'hemnetMonthly',
      }
    },
    config: {
      concurrency: 1,
    },
  },
}

export default provider

async function extract(job: JobInstance) {
  const { document } = new JSDOM().window
  const urls = getBooliStatisticsUrls()
  const statistics = []

  for (const url of urls) {
    const { statusCode, body } = await job.runtime.got(url)
    if (statusCode === 404) continue
    if (statusCode !== 200 || !body) throw new Error(`Unable to retrieve html at ${url}`)
    document.body.innerHTML = body

    const urlParts = url.split('-')
    const month = urlParts[urlParts.length - 2]
    const year = urlParts[urlParts.length - 1]

    const stats = {
      url,
      snapTime: getSnapTime(month, year),
      apartments: {
        forSale: extractNumberFromHeading(document, '🏷', 'till salu'),
        comingSoon: extractNumberFromTextBody(document, '⏳', 'snart till salu'),
        listingDays: extractNumberFromHeading(document, '🗓', 'dagar'),
        biddingPremium: extractNumberFromHeading(document, '📈', 'budgivning'),
        pricePerSqm: extractNumberFromHeading(document, '💰', 'kr/kvm'),
      },
      houses: {
        forSale: extractNumberFromHeading(document, '🏷', 'till salu', true),
        comingSoon: extractNumberFromTextBody(document, '⏳', 'snart till salu', true),
        listingDays: extractNumberFromHeading(document, '🗓', 'dagar', true),
        biddingPremium: extractNumberFromHeading(document, '📈', 'budgivning', true),
        averagePrice: extractNumberFromHeading(document, '💰', 'kr', true),
      },
    }
    statistics.push(stats)
  }

  return [
    {
      type: ResultType.town,
      data: {
        project: job.project,
        provider: job.provider,
        iso: Iso3166Alpha2.SE,
        itemId: new Date().getTime().toString(),
        data: statistics,
      },
    },
  ]
}

async function snapshots(job: JobInstance) {
  const itemId = null
  const statsToProcess = [
    { key: 'forSale', stockTypeSuffix: '_for_sale' },
    { key: 'comingSoon', stockTypeSuffix: '_coming_soon' },
    { key: 'listingDays', stockTypeSuffix: '_days_to_sell' },
  ]
  const latestTown = await job.runtime.collection('town').findOne(
    {
      project: job.project,
      provider: job.provider,
      iso: Iso3166Alpha2.SE,
      ...(itemId && { itemId }),
    },
    { sort: { itemId: -1 } },
  )
  if (!latestTown) throw new Error('No latest town found')

  const now = new Date()
  const latestTownData: BooliStatisticsData[] = latestTown.data
  for (const monthlyStats of latestTownData) {
    const snapStats = []
    for (const propertyType of ['houses', 'apartments']) {
      for (const statToProcess of statsToProcess) {
        const stockType = propertyType + statToProcess.stockTypeSuffix
        const count = Number(monthlyStats[propertyType][statToProcess.key])

        const stat = { stockType, count }
        snapStats.push(stat)
      }
    }
    const query = {
      project: job.project,
      controllerId: '-1',
      provider: job.provider,
      iso: Iso3166Alpha2.SE,
      snapTime: monthlyStats.snapTime,
      snapTimeUnit: TimeUnit.months,
      dataType: DataType.yearlyProgress,
    }
    await job.runtime.collection<Omit<Snapshot, '_id'>>('snapshot').deleteOne(query)
    await job.runtime.collection<Omit<Snapshot, '_id'>>('snapshot').insertOne({
      ...query,
      createdAt: now,
      updatedAt: now,
      stats: snapStats,
    })
  }

  return []
}
function getBooliStatisticsUrls(): string[] {
  // Start date - August 2022
  const startDate = new Date(2022, 7, 1)
  const currentDate = new Date()
  const urls: string[] = []

  let currentIterationDate = startDate

  while (isBefore(currentIterationDate, currentDate)) {
    const year = format(currentIterationDate, 'yyyy')
    const monthIndex = currentIterationDate.getMonth()
    const monthName = swedishMonths[monthIndex]

    urls.push(`https://www.booli.se/kunskap/statistik-bostadsmarknaden-i-${monthName}-${year}`)

    currentIterationDate = addMonths(currentIterationDate, 1)
  }

  return urls
}

function extractNumberFromHeading(document: Document, emoji: string, keyword: string, isHouse = false) {
  const docSection = document.querySelector('.article-body')
  const headings = Array.from(docSection.querySelectorAll('h2'))
  const startIndex = isHouse ? headings.length / 2 : 0
  const endIndex = isHouse ? headings.length : headings.length / 2

  for (let i = startIndex; i < endIndex; i++) {
    const heading = headings[i]
    if (heading.textContent.includes(emoji) && heading.textContent.includes(keyword)) {
      const numberMatch = heading.textContent.match(/\d[\d\s]*[\d,.]*/)
      if (numberMatch) {
        return numberMatch[0].replace(/\s/g, '').replace(',', '.')
      }
    }
  }
  return null
}

function extractNumberFromTextBody(document: Document, emoji: string, keyword: string, isHouse = false) {
  const docSection = document.querySelector('.article-body')
  const headings = Array.from(docSection.querySelectorAll('h2'))
  const startIndex = isHouse ? headings.length / 2 : 0
  const endIndex = isHouse ? headings.length : headings.length / 2

  for (let i = startIndex; i < endIndex; i++) {
    const heading = headings[i]
    const textBody = heading.nextElementSibling.textContent
    if (heading.textContent.includes(emoji) && heading.textContent.includes(keyword)) {
      const numberMatch = textBody.match(/\d[\d\s]*[\d,.]*/)
      if (numberMatch) {
        return numberMatch[0].replace(/\s/g, '').replace(',', '.')
      }
    }
  }
  return null
}

function getSnapTime(monthName: string, year: string): string {
  const monthIndex = swedishMonths.indexOf(monthName.toLowerCase())

  if (monthIndex === -1) {
    console.error(`Invalid month name: ${monthName}`)
    return `${year}/01`
  }

  const monthNumber = String(monthIndex + 1).padStart(2, '0')

  return `${year}/${monthNumber}`
}

const swedishMonths = [
  'januari',
  'februari',
  'mars',
  'april',
  'maj',
  'juni',
  'juli',
  'augusti',
  'september',
  'oktober',
  'november',
  'december',
]

interface PropertyStats {
  forSale: string | null
  comingSoon: string | null
  listingDays: string | null
  biddingPremium: string | null
}

interface ApartmentStats extends PropertyStats {
  pricePerSqm: string | null
}

interface HouseStats extends PropertyStats {
  averagePrice: string | null
}

interface BooliStatisticsData {
  url: string
  snapTime: string
  apartments: ApartmentStats
  houses: HouseStats
}
