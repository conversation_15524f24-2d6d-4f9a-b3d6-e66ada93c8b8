import { Iso3166Alpha2, ProjectType, Region } from '@datagatherers/datagatherers'
import { config, configBrowser, snapshot } from '@datagatherers/worker-utils'

import {
  extractTowns,
  extract,
  scheduleTowns,
  schedule,
  extractDealers,
  scheduleDealers,
} from '../../../lib/classifieds/crexi'

import type { ControllerRuntime, ControllerConfig, JobDefinition } from '@datagatherers/datagatherers'

const provider = {
  extractTownsUS: {
    region: Region.us_east_1,
    function: extractTowns,
    schedule: (job: JobDefinition, runtime: ControllerRuntime) => scheduleTowns(job, runtime, Iso3166Alpha2.US),
    config: {
      ...configBrowser,
      constrains: { countries: [Iso3166Alpha2.US] },
    },
  },
  extractUS: {
    region: Region.us_east_1,
    function: extract,
    cron: '0 12 * * MON',
    schedule: (job: JobDefinition, runtime: ControllerRuntime) => schedule(job, runtime, Iso3166Alpha2.US),
    scheduleAfterFinished: () => {
      return {
        project: ProjectType.auctions,
        provider: 'crexi',
        type: 'extractUS',
      }
    },
    config: {
      ...config,
      concurrency: 40,
      constrains: { countries: [Iso3166Alpha2.US] },
      overrideDeadlineSeconds: 4 * 24 * 60 * 60,
    },
  },
  extractDealersUS: {
    region: Region.us_east_1,
    function: extractDealers,
    schedule: (job: JobDefinition, runtime: ControllerRuntime) => scheduleDealers(job, runtime, Iso3166Alpha2.US),
    snapshot: (config: ControllerConfig, runtime: ControllerRuntime) => snapshot(Iso3166Alpha2.US, config, runtime),
    scheduleAfterFinished: () => {
      return {
        project: ProjectType.auctions,
        provider: 'crexi',
        type: 'snapCrexiAuctionsUS',
      }
    },
    config: {
      ...config,
      concurrency: 40,
      constrains: { countries: [Iso3166Alpha2.US] },
      overrideDeadlineSeconds: 3 * 24 * 60 * 60,
    },
  },
}
export default provider
