import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import {
  extractLocations,
  extractRE,
  getListingExtraPackageInfo,
  scheduleExtract,
  scheduleListingExtraPackageInfo,
  scheduleLocations,
} from '../../../lib/classifieds/oikotie'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  codesFI: {
    region: Region.eu_west_1,
    function: extractLocations,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleLocations(job, runtime, Iso3166Alpha2.FI),
    config: {
      concurrency: 10,
    },
  },
  extractFI: {
    cron: '0 12 * * TUE',
    region: Region.eu_north_1,
    function: extractRE,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.FI),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'listingExtraPackageInfoFI',
      }
    },
    config: {
      concurrency: 15,
    },
  },
  listingExtraPackageInfoFI: {
    region: Region.eu_north_1,
    function: getListingExtraPackageInfo,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleListingExtraPackageInfo(job, runtime, Iso3166Alpha2.FI),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.FI, config, runtime),
    config: {
      concurrency: 15,
    },
  },
}

export default provider
