import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config, snapshot, SnapshotResolution } from '@datagatherers/worker-utils'

import {
  extract,
  extractAdViews,
  extractTowns,
  scheduleExtract,
  scheduleExtractAdViews,
  scheduleTowns,
} from '../../../lib/realestates/booli'
import { trackerBooliHousingNewsWW } from '../../non_standard/trackers'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractTownsSE: {
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleTowns(job, runtime, Iso3166Alpha2.SE),
    function: extractTowns,
    config: {
      concurrency: 10,
    },
  },
  extractSE: {
    cron: '0 10 * * WED',
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.SE),
    function: extract,
    config: {
      concurrency: 20,
      perJobTimeout: 60 * 60 * 1000,
    },
  },
  extractAdViewsSE: {
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractAdViews(job, runtime, Iso3166Alpha2.SE),
    function: extractAdViews,
    scheduleAfterFinished: (config) => {
      return [
        {
          project: config.project,
          provider: 'hemnet_booli_crosspost',
          type: 'matchHemnetBooliAdsSE',
        },
      ]
    },
    config,
  },
  snapshotSE: {
    region: Region.eu_central_1,
    function: () => Promise.resolve([]),
    snapshot: async (config, runtime) => {
      const dt = new Date()
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt)
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt, 'hemnet_booli_crossposted', {
        'data.isCrossposted': true,
      })
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt, 'booli_upcoming', {
        'data.upcomingSale': true,
        'data.isNewConstruction': false,
      })
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt, 'booli_newproduction', {
        'data.isNewConstruction': true,
      })
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt, 'booli_forsale', {
        'data.__typename': 'Listing',
        'data.upcomingSale': false,
        'data.isNewConstruction': false,
      })
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt, 'booli_relisted', {
        $or: [{ 'data.isRepostedAdViews': true }, { 'data.isRepostedDaysActive': true }],
      })
      await snapshot(Iso3166Alpha2.SE, config, runtime, SnapshotResolution.Full, dt, 'booli_forsale_complete', {
        'data.__typename': 'Listing',
        'data.upcomingSale': false,
        'data.isNewConstruction': false,
        'data.hasImages': true,
      })
    },
    config: {
      concurrency: 1,
    },
  },
  trackerBooliHousingNewsWW,
}

export default provider
