import { Region } from '@datagatherers/datagatherers'

import { extractCgedd, extractReGraphsFI, extractReGraphsNO } from '../../../lib/yearlyProgress/all_country'

const provider = {
  reGraphsNO: {
    region: Region.eu_north_1,
    function: extractReGraphsNO,
    config: {
      concurrency: 1,
      keepUpdatedAt: false,
    },
  },
  propertiesSoldFR: {
    region: Region.eu_west_2,
    function: extractCgedd,
    config: {
      concurrency: 1,
      keepUpdatedAt: false,
    },
  },
  reGraphsFI: {
    region: Region.eu_north_1,
    cron: '0 0 7 * *',
    function: extractReGraphsFI,
    config: {
      concurrency: 1,
      keepUpdatedAt: false,
    },
  },
}

export default provider
