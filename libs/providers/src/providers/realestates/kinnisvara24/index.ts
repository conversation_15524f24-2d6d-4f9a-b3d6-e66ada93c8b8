import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config, snapshots } from '@datagatherers/worker-utils'

import { extractListings, scheduleListings } from '../../../lib/realestates/kinnisvara24'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractEE: {
    cron: '0 13 * * WED',
    region: Region.eu_west_1,
    function: extractListings,
    schedule: (job, runtime) => scheduleListings(job, runtime, Iso3166Alpha2.EE),
    snapshot: async (config, runtime) => snapshots.realestatesFull(Iso3166Alpha2.EE, config, runtime),
    config: {
      ...config,
      perJobTimeout: 30 * 60 * 1000,
    },
  },
}

export default provider
