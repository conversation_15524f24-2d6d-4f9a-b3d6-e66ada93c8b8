import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { config, configBrowser, snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import { setListingsPriceRanges } from '../../../lib/cars/autoscout24/index'
import {
  extractAutoscoutAdIds,
  extractAutoscoutCH,
  scheduleExtractAutoscoutAdIds,
  scheduleExtractAutoscoutCH,
  writeoutAutoscoutCrossposted,
} from '../../../lib/cars/comparis/lib'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const commonConfig = {
  concurrency: 30,
  overrideDeadlineSeconds: 60 * 60 * 24 * 4,
}

const comparisConfig = {
  ...config,
  ...commonConfig,
}

const comparisConfigBrowser = {
  ...configBrowser,
  ...commonConfig,
}

const provider: Provider = {
  extractAutoscoutCH: {
    region: Region.eu_central_1,
    function: extractAutoscoutCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractAutoscoutCH(job, runtime, Iso3166Alpha2.CH, 'autoscout24.ch'),
    scheduleAfterFinished: (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractAutoscoutAdIdsPass1',
    }),
    config: comparisConfig,
  },
  extractAutoscoutAdIdsPass1: {
    region: Region.eu_central_1,
    function: extractAutoscoutAdIds,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractAutoscoutAdIds(job, runtime),
    scheduleAfterFinished: (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractAutoscoutAdIdsPass2',
    }),
    config: comparisConfigBrowser,
  },
  extractAutoscoutAdIdsPass2: {
    region: Region.eu_central_1,
    function: extractAutoscoutAdIds,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractAutoscoutAdIds(job, runtime),
    scheduleAfterFinished: (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractAutoscoutAdIdsPass3',
    }),
    config: comparisConfigBrowser,
  },
  extractAutoscoutAdIdsPass3: {
    region: Region.eu_central_1,
    function: extractAutoscoutAdIds,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractAutoscoutAdIds(job, runtime),
    scheduleAfterFinished: (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'writeoutAutoscoutCrossposted',
    }),
    config: comparisConfigBrowser,
  },
  writeoutAutoscoutCrossposted: {
    region: Region.eu_central_1,
    function: writeoutAutoscoutCrossposted,
    scheduleAfterFinished: (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractInventoryCountCH',
    }),
    config: comparisConfig,
  },
  extractInventoryCountCH: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.CH),
    afterControllerFinished: async (config, runtime) => {
      await setListingsPriceRanges(config, runtime, Iso3166Alpha2.CH)
    },
    snapshot: async (controllerConfig, runtime) => snapshot(Iso3166Alpha2.CH, controllerConfig, runtime),
    scheduleAfterFinished: (config) => ({
      project: config.project,
      provider: 'autoscout24',
      type: 'snapComparisCrossposted',
    }),
    config,
  },
}

export default provider
