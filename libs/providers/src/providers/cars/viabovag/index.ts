import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import viabovag from '../../../lib/cars/viabovag'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractNL: {
    cron: '0 12 * * WED',
    region: Region.eu_central_1,
    function: viabovag.extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => viabovag.scheduler(job, runtime, Iso3166Alpha2.NL),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountNL',
      }
    },
    config: {
      concurrency: 1,
      returnCount: true,
    },
  },
  extractInventoryCountNL: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.NL),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.NL, config, runtime),
    config: {
      concurrency: 10,
    },
  },
}

export default provider
