import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config, snapshot } from '@datagatherers/worker-utils'

import { extractMobileOnlineSaleTotal } from '../../../lib/cars/mobile.de'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractDE: {
    cron: '0 1 * * MON',
    region: Region.eu_west_3,
    function: extractMobileOnlineSaleTotal,
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.DE, config, runtime),
    config: {
      ...config,
      concurrency: 1,
    },
  },
}

export default provider
