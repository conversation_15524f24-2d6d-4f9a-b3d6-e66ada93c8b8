import { JSD<PERSON> } from 'jsdom'

import {
  Iso3166Alpha2,
  Region,
  ResultType,
  DataType,
  ProjectType,
  VehicleType,
  DealerType,
} from '@datagatherers/datagatherers'
import { config, randomWait, snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'

import type {
  Provider,
  JobDefinition,
  JobInstance,
  ControllerRuntime,
  ResultItem,
  Town,
} from '@datagatherers/datagatherers'

const provider: Provider = {
  preprocessLT: {
    region: Region.eu_north_1,
    cron: '0 6 * * FRI',
    function: preprocessModels,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      schedulePreprocessModels(job, runtime, Iso3166Alpha2.LT),
    scheduleAfterFinished: async (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractLT',
    }),
    config,
  },
  extractLT: {
    region: Region.eu_north_1,
    function: extract,
    schedule: scheduleExtract,
    scheduleAfterFinished: async (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractInventoryCountLT',
    }),
    config,
  },
  extractInventoryCountLT: {
    region: Region.eu_north_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.LT),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.LT, config, runtime),
    config,
  },
}

export default provider

async function schedulePreprocessModels(job: JobDefinition, runtime: ControllerRuntime, iso: Iso3166Alpha2) {
  const jobs = []
  const { document } = new JSDOM().window
  const dic: Record<VehicleType, { categoryId: string; url: string }[]> = {
    [VehicleType.car]: [
      {
        categoryId: '01',
        url: 'https://autogidas.lt/skelbimai/automobiliai/',
      },
    ],
    [VehicleType.motorcycle]: [
      {
        categoryId: '02',
        url: 'https://autogidas.lt/skelbimai/motociklai/',
      },
    ],
    [VehicleType.other]: [
      {
        categoryId: '17',
        url: 'https://autogidas.lt/skelbimai/priekabos-puspriekabes/',
      },
      {
        categoryId: '22',
        url: 'https://autogidas.lt/skelbimai/zemes-ukio-padargai-savaeige/',
      },
    ],
    [VehicleType.commercial]: [
      {
        categoryId: '04',
        url: 'https://autogidas.lt/skelbimai/sunkvezimiai/',
      },
    ],
    [VehicleType.caravan]: [],
    [VehicleType.boat]: [],
  }

  for (const vehicleType of Object.keys(dic)) {
    if (!dic || !dic[vehicleType]?.length) continue
    for (const param of dic[vehicleType]) {
      if (!param?.categoryId) continue
      let makes: { value: string; title: string; search: string; count: number }[]
      const RETRIES = 5
      for (let attempt = 1; attempt <= RETRIES; attempt++) {
        try {
          makes = await getMakes(runtime, param.categoryId, document)
          break
        } catch (err) {
          if (attempt === RETRIES) throw err
          await randomWait(1000, 2000)
        }
      }
      for (const make of makes) {
        jobs.push({
          ...job,
          data: {
            iso,
            categoryId: param.categoryId,
            url: param.url,
            vehicleType,
            make: make.value,
          },
        })
      }
    }
  }

  return jobs
}

async function preprocessModels(job: JobInstance) {
  const { iso, categoryId, url, vehicleType, make } = job.data
  const townPayload = []
  const RETRIES = 5

  for (let current_retry = 0; current_retry < RETRIES; current_retry++) {
    try {
      const models = await getModels(job, categoryId, make)

      if (!models?.length) break
      townPayload.push(
        ...models.map((model) => ({
          iso,
          vehicleType,
          categoryId: categoryId,
          url,
          make,
          model: model.value ? model.value : model.title,
        })),
      )
      break
    } catch (error) {
      if (current_retry >= RETRIES) throw error
      await randomWait(1000, 2000)
      continue
    }
  }

  if (!townPayload?.length) {
    return []
  }

  return [
    {
      type: ResultType.town,
      data: {
        project: job.project,
        provider: job.provider,
        itemId: `${job.project}_${job.provider}_${categoryId}_${make}`,
        iso,
        data: townPayload,
      },
    },
  ]
}

async function scheduleExtract(job: JobDefinition, runtime: ControllerRuntime) {
  return runtime
    .collection<Town>('town')
    .find({
      project: job.project,
      provider: job.provider,
    })
    .project({ _id: 0, data: 1 })
    .toArray()
    .then((documents) =>
      documents.flatMap(({ data }) =>
        data.flatMap((payload: TownPayload) => ({
          ...job,
          data: payload,
        })),
      ),
    )
}

async function extract(job: JobInstance) {
  const payload = job.data as TownPayload
  const { document } = new JSDOM().window
  const { iso, make, model } = payload

  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType }> = new Map()

  const listings = await job.runtime.got.paginate.all<ResultItem, string>(job.data.url, {
    searchParams: {
      page: 1,
      f_245: 'Lietuva',
      'f_1[0]': make,
      ...(model && { 'f_model_14[0]': model }),
    },
    responseType: 'text',
    retry: {
      limit: 5,
    },
    pagination: {
      paginate: ({ response }) => {
        if (!document) return

        const nextButton = document.querySelector('.ag-paginator > .page-next')
        if (!nextButton || nextButton.classList.contains('disabled')) return false

        const previousSearchParams = response.request.options.searchParams as URLSearchParams
        if (!previousSearchParams) return false
        const currentPage = Number(previousSearchParams.get('page') ?? 0)
        if (!currentPage) return false

        return {
          searchParams: {
            ...previousSearchParams,
            page: currentPage + 1,
          },
        }
      },
      transform: ({ body }) => {
        if (!body) return []
        document.body.innerHTML = body
        const articles = document.querySelectorAll('article.list-item-new')
        if (!articles?.length) return []

        return Array.from(articles).flatMap((article) => {
          const inventoryId = article
            .querySelector('.comments')
            ?.getAttribute('id')
            ?.replace('bookmark-comments-block-', '')
          if (!inventoryId) return []

          const price = Number(article.querySelector('.item-price')?.textContent?.replace(/\D/g, '')?.trim() ?? '0')
          const slug = article.querySelector('.item-link')?.getAttribute('href')
          const vehicleType = getVehicleType(job)
          const stockType = article.querySelector('.icon.param-year')?.textContent?.match(/Naujas/i) ? 'new' : 'used'

          const dealerId =
            article.querySelector('.seller-logo > img')?.getAttribute('alt') ??
            article.querySelector('.business-logo > img')?.getAttribute('alt')
          const dealerType = !dealerId ? DealerType.private : DealerType.dealer
          if (dealerId) {
            dealersMap.set(dealerId, { dealerId, dealerType })
          }

          return {
            type: ResultType.item,
            data: {
              project: ProjectType.cars,
              dataType: DataType.inventory,
              iso,
              itemId: `listing_${iso}_${job.project}_${inventoryId}`,
              inventoryId,
              ...(dealerId && { dealerId }),
              dealerType,
              stockType,
              price,
              vehicleType,
              url: `https://autogidas.lt${slug}`,
            },
          }
        })
      },
    },
  })

  if (!listings?.length) return []

  const dealers = [...dealersMap.values()].map((item) => ({
    type: ResultType.item,
    data: {
      project: ProjectType.cars,
      itemId: `dealer_${iso}_${job.project}_${item.dealerId}`,
      iso,
      dataType: DataType.dealers,
      dealerId: item.dealerId,
      dealerType: item.dealerType,
      isActive: true,
      isPaying: item.dealerType === DealerType.dealer,
      data: item,
    },
  }))

  return [...listings, ...dealers]
}

function getVehicleType(job: JobInstance) {
  return job.data?.vehicleType ?? VehicleType.other
}

async function getMakes(runtime: ControllerRuntime, categoryId: string, document: Document) {
  return runtime.got('https://autogidas.lt/', { searchParams: { section: categoryId } }).then((res) => {
    document.body.innerHTML = res.body
    const scripts = Array.from(document.querySelectorAll('script'))
    const makesScript = scripts.find((script) => script.textContent?.includes('f_1'))?.textContent
    const makesScriptSplit = makesScript?.split('[')
    const makesScriptSplit2 = makesScriptSplit?.[1]?.split(']')
    try {
      const makesRaw = JSON.parse(`[${makesScriptSplit2?.[0]?.trim()}]`)
      const makes = makesRaw.filter(
        (make: { value: string; title: string; search: string; count: number }) => make.value,
      )
      if (!makes?.length) throw new Error(`No makes found for categoryId: ${categoryId}`)
      return makes
    } catch (error) {
      throw new Error(`Failed to extract makes for categoryId ${categoryId}, error: ${error}`)
    }
  })
}

async function getModels(job: JobInstance, categoryId: string, make: string) {
  return job.runtime
    .got('https://autogidas.lt/ajax/category/models', {
      searchParams: {
        category_id: categoryId,
        make,
      },
      retry: {
        limit: 6,
      },
    })
    .json<{ success: boolean; data: { title: string; value: string; count: number }[] }>()
    .then((res) => {
      if (res.success === false)
        throw new Error(`Failed to extract models for make ${make} and categoryId ${categoryId}`)
      const models = res.data?.filter((item) => item.title && item.count)
      return models
    })
}
interface TownPayload {
  iso: Iso3166Alpha2
  vehicleType: string
  categoryId: string
  url: string
  make: string
  model: string
}
