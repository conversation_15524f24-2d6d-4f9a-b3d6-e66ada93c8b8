import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import { extract, scheduler } from '../../../lib/classifieds/marktplaats'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractNL: {
    cron: '0 20 * * TUE',
    region: Region.eu_west_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      job.data = {
        ...job.data,
        categoryId: [91],
      }
      return scheduler(job, runtime, Iso3166Alpha2.NL)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.NL, config, runtime),
    config: {
      concurrency: 20,
    },
  },
}

export default provider
