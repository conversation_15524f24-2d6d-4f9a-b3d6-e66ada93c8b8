import {
  Iso3166Alpha2,
  Region,
  ResultType,
  DataType,
  AuctionsDealerType,
  ProjectType,
  ClassifiedsStockType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { snapshots, getItemId, randomWait } from '@datagatherers/worker-utils'

import { SIZE, URL } from './constants'
import cars from '../../../lib/cars'

import type { MotosNetResponse } from './types'
import type { Provider, JobDefinition, DealerType, JobInstance, ControllerRuntime } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractES: {
    region: Region.eu_west_1,
    cron: '0 8 * * MON',
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.ES),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountES',
      }
    },
    config: {
      //browser: true,
      concurrency: 10,
    },
  },
  extractInventoryCountES: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.ES),
    snapshot: async (config, runtime) => snapshots.carsFull(Iso3166Alpha2.ES, config, runtime),
    config: {
      concurrency: 10,
    },
  },
}

export default provider

async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const jobs: JobDefinition[] = []

  const startPage = 1

  const totalPages = await runtime
    .got(URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-adevinta-channel': 'web-desktop',
        'x-schibsted-tenant': 'motos',
      },
      json: {
        pagination: { page: startPage, size: SIZE },
        sort: { order: 'asc', term: 'price' },
        filters: {
          categories: { category1Ids: [3000] },
          offerTypeIds: [0, 1],
          isFinanced: false,
          price: { from: null, to: null },
        },
      },
    })
    .json<MotosNetResponse>()
    .then((json) => json.meta.totalPages)

  const pagesPerJob = 10
  for (let start = 1; start < totalPages; start += pagesPerJob) {
    const end = Math.min(totalPages, start + pagesPerJob - 1)
    jobs.push({
      ...job,
      data: {
        iso,
        start,
        end,
      },
    })
  }

  return jobs
}

async function extract(job: JobInstance) {
  const items = []

  for (let page = job.data?.start; page <= job.data?.end; page++) {
    await randomWait(2000, 4000)
    const apiItems = await job.runtime
      .got(URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-adevinta-channel': 'web-desktop',
          'x-schibsted-tenant': 'motos',
        },
        json: {
          pagination: { page, size: SIZE },
          sort: { order: 'asc', term: 'price' },
          filters: {
            categories: { category1Ids: [3000] },
            offerTypeIds: [0, 1],
            isFinanced: false,
            price: { from: null, to: null },
          },
        },
      })
      .json<MotosNetResponse>()
      .then((json) => json.items)

    if (!apiItems?.length) {
      continue
    }

    items.push(...apiItems)
  }

  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType }> = new Map()
  const resultsInventory = items.flatMap((item) => {
    if (!item.id) return []

    const dealerType = item.isProfessional ? AuctionsDealerType.dealer : AuctionsDealerType.private
    const dealerId = item.phone || ''
    if (dealerId) {
      if (!dealersMap.has(dealerId)) {
        dealersMap.set(dealerId, {
          dealerId,
          dealerType: dealerType,
        })
      }
    }

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        dataType: DataType.inventory,
        iso: job.data?.iso,
        itemId: getItemId(DataType.inventory, ProjectType.cars, item.id),
        inventoryId: item.id,
        dealerId: dealerId,
        dealerType,
        stockType: item.offerType.id === 1 ? ClassifiedsStockType.new : ClassifiedsStockType.used,
        ...(job.project === ProjectType.cars && { vehicleType: VehicleType.motorcycle }),
        ...(item.price.amount && { price: item.price.amount, currency: 'EUR' }),
      },
    }
  })

  const resultsDealers = [...dealersMap.values()].map((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: getItemId(DataType.dealers, ProjectType.cars, item.dealerId),
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === AuctionsDealerType.dealer,
      },
    }
  })

  return [...resultsInventory, ...resultsDealers]
}
