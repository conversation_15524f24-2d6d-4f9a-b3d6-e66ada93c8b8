import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import { extractListings, scheduleExtractListings } from '../../../lib/cars/bytbil'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractSE: {
    cron: '0 11 * * THU',
    region: Region.eu_west_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractListings(job, runtime, Iso3166Alpha2.SE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountSE',
      }
    },
    config: {
      concurrency: 10,
    },
  },
  extractInventoryCountSE: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime) => cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.SE),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.SE, config, runtime),
    config: {
      concurrency: 1,
    },
  },
}

export default provider
