/* eslint-disable no-console */
import {
  AuctionsDealerType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  Region,
  ResultType,
  ClassifiedsStockType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { askQuestion, wait, snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'

import type { DealerType, JobDefinition, JobInstance, Provider } from '@datagatherers/datagatherers'

interface LaCentrale {
  hits?: HitsEntity[] | null
  seed: number
  total: number
}
interface HitsEntity {
  item: Item
}
interface Item {
  delivery?: Delivery
  picturesPhotosphere: boolean
  goodDealBadge: string
  publicationOptions?: string[] | null
  vehicle: Vehicle
  reference: string
  picturesCount: number
  customerType: string
  ownerCorrelationId: string
  price: number
  customerReference: string
  lastUpdate: number
  pictures360Exterieur: boolean
  autoviza: boolean
  location: Location
  firstOnlineDate: string
  photoUrl: string
}
interface Vehicle {
  year: number
  model: string
  family: string
  version: string
  make: string
  mileage: number
}
interface Location {
  visitPlace: string
  geopoints: Geopoints
}
interface Geopoints {
  lon: number
  lat: number
}
interface Delivery {
  distanceMax?: number | null
  isActive: boolean
  prices?: number[] | null
}

let datadome = process.argv.slice(-1)[0]

const provider: Provider = {
  codesFR: {
    region: Region.us_east_1,
    function: async (job: JobInstance) => {
      return codes(job, Iso3166Alpha2.FR)
    },
    config: {
      concurrency: 1,
    },
  },
  extractFR: {
    // cron: '0 1 * * WED',
    region: Region.us_east_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleExtract(job, runtime, Iso3166Alpha2.FR)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountFR',
      }
    },
    config: {
      concurrency: 10,
    },
  },
  extractInventoryCountFR: {
    region: Region.us_east_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.FR)
    },
    snapshot: async (config, runtime) => {
      await snapshot(Iso3166Alpha2.FR, config, runtime)
      await snapshot(Iso3166Alpha2.FR, config, runtime, undefined, undefined, 'lacentrale_delivery', {
        'data.isDeliveryAvailable': true,
      })
    },
    config: {
      concurrency: 15,
    },
  },
}

export default provider

async function codes(job: JobInstance, iso: Iso3166Alpha2) {
  const collection = job.runtime.collection('postalcode')
  const filter = {
    iso,
  }
  const items = await collection.find(filter).toArray()
  const results = items
    .filter((t) => !!t.data.code)
    .map((item) => {
      const data = {
        project: job.project,
        itemId: `${item.iso}_${item.data.code}`,
        iso: item.iso,
        blacklisted: false,
        data: {
          city: item.data.city,
          code: item.data.code,
          latitude: item.data.lat,
          longitude: item.data.lng,
          adminName: item.data.state,
        },
      }
      return {
        type: ResultType.town,
        data,
      }
    })

  return results
}

async function extract(job: JobInstance) {
  console.info('----------------------------------------------------------------------')
  console.info('New Job', JSON.stringify(job.data))
  console.info('----------------------------------------------------------------------')
  const appendData = []
  const pageSize = 100

  for (let page = 0; ; page++) {
    console.info('Extracting page ---> ' + (page + 1))
    await wait(500)
    let data

    try {
      data = await job.runtime
        .got(`https://recherche.lacentrale.fr/v3/search`, {
          headers: {
            'x-api-key': '58a195bfAF434B95A93A26f71a72b0ba04e9c4a3',
            'Accept-Encoding': 'gzip',
            'User-Agent': 'okhttp/3.12.10',
            ...(datadome !== 'no-cookie' && { cookie: `datadome=${datadome}` }),
          },
          searchParams: {
            families: 'AUTO',
            order: 'asc',
            page,
            pageSize,
            sort: 'visitPlace',
            zipCode: job.data?.code,
            zipCodeDistance: '5km',
          },
        })
        .json<LaCentrale>()
      if (!data?.hits) throw new Error('No data')
      if (!data?.hits?.length) {
        break
      }
      appendData.push(...data.hits)
      if (data.total && data.total < (page + 1) * pageSize) {
        break
      }
    } catch (error) {
      console.info('Error: ' + (error as Error).message)
      if ((error as Error).message.includes('Unexpected token')) {
        console.info('Skipping job (Unexpected Token): ' + JSON.stringify(job.data))
        break
      }
      datadome = await askQuestion('Insert new datadome')
      page = page - 1
      console.info(`Cookie registered: ${datadome}`)
    }
  }

  if (!appendData?.length) {
    return []
  }
  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType; customerRef: string }> = new Map()
  const resultsInventory = appendData
    .filter((a) => a?.item?.reference)
    .map((itemParent) => {
      const item = itemParent.item
      const stockType = ClassifiedsStockType.used
      const dealerType = getDealerType(item)
      const dealerId: string = item.ownerCorrelationId

      if (dealerId && !dealersMap.has(dealerId)) {
        dealersMap.set(dealerId, {
          dealerId,
          dealerType,
          customerRef: item.customerReference,
        })
      }

      const data = {
        project: ProjectType.cars,
        itemId: `listing_${job.project}_${job.data?.iso}_${item.reference}`,
        iso: job.data?.iso,
        dataType: DataType.inventory,
        inventoryId: `${item.reference}`,
        ...(item.price && { price: item.price }),
        dealerId,
        stockType,
        dealerType,
        ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
        data: {
          isDeliveryAvailable: item.delivery?.isActive ?? false,
        },
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  const resultsDealers = [...dealersMap.values()]
    .filter((a) => a?.dealerId)
    .map((item) => {
      const data = {
        project: ProjectType.cars,
        itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === AuctionsDealerType.dealer,
        data: item,
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  return [...resultsInventory, ...resultsDealers]
}

function getDealerType(item: { customerType: string }): DealerType {
  if (!item.customerType || item.customerType === 'PART') {
    return AuctionsDealerType.private
  }
  return AuctionsDealerType.dealer
}
