import { DealerType } from '@datagatherers/datagatherers'

import type { ProductTypeTiers } from './types'

export const dealerTypes = [
  {
    type: DealerType.private,
    value: '10898',
  },
  {
    type: DealerType.dealer,
    value: '10899',
  },
]
export const MAX_RESULTS = 3250
export const LIMIT = 100
export const DISTANCEMETERS = 5000
export const CATEGORYID = 91
export const productTypePrices: ProductTypeTiers = {
  'dealer premium': [
    { minAds: 1, maxAds: 5, price: 143 },
    { minAds: 6, maxAds: 10, price: 200 },
    { minAds: 11, maxAds: 15, price: 287 },
    { minAds: 16, maxAds: 20, price: 330 },
    { minAds: 21, maxAds: 30, price: 417 },
    { minAds: 31, maxAds: 40, price: 474 },
    { minAds: 41, maxAds: 50, price: 516 },
    { minAds: 51, maxAds: 60, price: 575 },
    { minAds: 61, maxAds: 70, price: 617 },
    { minAds: 71, maxAds: 80, price: 646 },
    { minAds: 81, maxAds: 90, price: 703 },
    { minAds: 91, maxAds: 100, price: 747 },
  ],
  'dealer basic': [
    { minAds: 1, maxAds: 5, price: 111 },
    { minAds: 6, maxAds: 10, price: 155 },
    { minAds: 11, maxAds: 15, price: 221 },
    { minAds: 16, maxAds: 20, price: 254 },
    { minAds: 21, maxAds: 30, price: 320 },
    { minAds: 31, maxAds: 40, price: 364 },
    { minAds: 41, maxAds: 50, price: 398 },
    { minAds: 51, maxAds: 60, price: 442 },
    { minAds: 61, maxAds: 70, price: 475 },
    { minAds: 71, maxAds: 80, price: 497 },
    { minAds: 81, maxAds: 90, price: 541 },
    { minAds: 91, maxAds: 100, price: 575 },
  ],
}
export const providerConfig = {
  concurrency: 10,
}
