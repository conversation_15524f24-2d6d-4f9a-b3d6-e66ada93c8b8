import { startOfISOWeek } from 'date-fns'
import { ObjectId } from 'mongodb'

import {
  Iso3166Alpha2,
  Region,
  ResultType,
  DataType,
  ProjectType,
  VehicleType,
  DealerType,
} from '@datagatherers/datagatherers'
import { getItemId, getProjectProviderDataType, arraySlicer, snapshot } from '@datagatherers/worker-utils'

import { CATEGORYID, DISTANCEMETERS, LIMIT, MAX_RESULTS, dealerTypes, providerConfig } from './constants'
import { checkDealerPackage, checkStockType, getAdvertisingCost } from './utils'
import cars from '../../../lib/cars'

import type { TweedehandsApiResponse } from './types'
import type {
  Provider,
  JobDefinition,
  Town,
  JobInstance,
  ControllerRuntime,
  ResultItem,
  Job,
} from '@datagatherers/datagatherers'

const provider: Provider = {
  codesBE: {
    region: Region.eu_west_1,
    function: async (job: JobInstance) => {
      return cars.codes_postalcode(job, Iso3166Alpha2.BE)
    },
    config: {
      concurrency: 1,
    },
  },
  extractBE: {
    cron: '0 6 * * THU',
    region: Region.eu_west_1,
    function: extractBE,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractBE(job, runtime, Iso3166Alpha2.BE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractPackagePricesDealersBE',
      }
    },
    config: providerConfig,
  },
  extractPackagePricesDealersBE: {
    region: Region.eu_west_1,
    function: extractPackagePrices,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return scheduleExtractPackagePrices(job, runtime, Iso3166Alpha2.BE)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountBE',
      }
    },
    config: providerConfig,
  },
  extractInventoryCountBE: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.BE)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.BE, config, runtime),
    config: providerConfig,
  },
}

export default provider

export async function scheduleExtractBE(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const jobs: JobDefinition[] = []
  const results = await runtime
    .collection<Town>('town')
    .find({
      project: job.project,
      provider: job.provider,
      iso,
    })
    .toArray()
  for (const dealerType of dealerTypes) {
    jobs.push(
      ...results
        .filter((item) => item?.data)
        .map((item) => {
          const res = {
            ...job,
            data: {
              ...job.data,
              ...item.data,
              iso,
              dealerType: dealerType.type,
              attributesById: dealerType.value,
              _id: item._id,
              ...(job.data?.parameters && { parameters: job.data?.parameters }),
            },
          }
          return res
        }),
    )
  }

  return jobs
}

export async function extractBE(job: JobInstance) {
  const dealersMap: Map<
    string,
    { dealerId: string; dealerType: DealerType; productType: 'dealer premium' | 'dealer basic' | null }
  > = new Map()

  let l2Categories

  const listings = await job.runtime.got.paginate.all<ResultItem, TweedehandsApiResponse>(
    'https://www.2ememain.be/lrp/api/search',
    {
      searchParams: {
        'attributesByKey[]': 'Language:all-languages',
        'attributesById[]': job.data?.attributesById,
        distanceMeters: DISTANCEMETERS,
        l1CategoryId: CATEGORYID,
        ...(job.data?.l2CategoryId && { l2CategoryId: job.data?.l2CategoryId }),
        limit: LIMIT,
        offset: 0,
        postcode: job.data?.code,
      },
      responseType: 'json',
      retry: {
        limit: 5,
      },
      pagination: {
        transform: (response) => {
          if (response?.body?.totalResultCount > MAX_RESULTS) {
            if (!job.data?.l2CategoryId) {
              l2Categories = response.body.searchCategoryOptions.filter((item) => !!item.parentId)
            }
            return []
          }

          if (!response?.body?.listings?.length) return []

          return response.body.listings
            .filter((item) => item?.itemId)
            .map((item) => {
              const dealerType = job.data?.dealerType
              const dealerId = item.sellerInformation.sellerId.toString() || ''
              const inventoryId = item.itemId
              const productType = checkDealerPackage(item.traits)
              if (dealerId) {
                if (!dealersMap.has(dealerId)) {
                  dealersMap.set(dealerId, {
                    dealerId,
                    dealerType: dealerType,
                    productType,
                  })
                }
              }

              return {
                type: ResultType.item,
                data: {
                  project: ProjectType.cars,
                  dataType: DataType.inventory,
                  iso: job.data?.iso,
                  itemId: getItemId(DataType.inventory, ProjectType.cars, inventoryId),
                  inventoryId,
                  dealerId,
                  dealerType,
                  stockType: checkStockType(item.attributes),
                  ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
                  ...(item.priceInfo.priceCents && { price: item.priceInfo.priceCents / 100, currency: 'EUR' }),
                  url: item.vipUrl,
                  data: {
                    traits: item.traits,
                    attributes: item.attributes,
                    extendedAttributes: item.extendedAttributes,
                  },
                },
              }
            })
        },
        paginate: ({ response }) => {
          if (response?.body?.totalResultCount > MAX_RESULTS) return false
          if (!response?.body?.listings?.length || response.body.listings.length < LIMIT) return false

          const previousSearchParams = response.request.options.searchParams as URLSearchParams
          if (!previousSearchParams) return false

          const offset = Number(previousSearchParams.get('offset'))
          if (isNaN(offset) || offset > MAX_RESULTS) return false

          return {
            searchParams: {
              ...previousSearchParams,
              offset: offset + LIMIT,
            },
          }
        },
      },
    },
  )

  if (l2Categories?.length) {
    const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
    if (!scheduledJob) {
      throw new Error(`No scheduledJob with id ${job._id}`)
    }

    const jobsToAdd = l2Categories.map((item) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          l2CategoryId: item.id,
        },
      }
    })

    if (jobsToAdd?.length) await job.runtime.addJobs(jobsToAdd)

    return []
  }

  const resultsDealers = [...dealersMap.values()].map((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: getItemId(DataType.dealers, ProjectType.cars, item.dealerId),
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === DealerType.dealer,
        ...(item.productType && { productType: item.productType }),
      },
    }
  })

  return [...listings, ...resultsDealers]
}

export async function scheduleExtractPackagePrices(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  return runtime
    .collection('item')
    .find({
      projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
      iso,
      updatedAt: { $gte: startOfISOWeek(new Date()) },
      dealerType: { $in: [DealerType.dealer, DealerType.private] },
      dealerId: { $exists: true },
      productType: { $exists: true },
    })
    .project({
      dealerId: 1,
      productType: 1,
      dealerType: 1,
    })
    .toArray()
    .then((dealers) => {
      if (!dealers?.length) return []
      return arraySlicer(dealers, 20).map((dealers) => ({
        ...job,
        data: {
          ...job.data,
          iso,
          dealers,
        },
      }))
    })
}

export async function extractPackagePrices(job: JobInstance) {
  const appendData: { dealerId: string; listingCount: number; advertisingCost?: number }[] = []

  for (const dealer of job.data.dealers) {
    const dealerProductType = dealer.productType as 'dealer premium' | 'dealer basic'
    const listingCount = await job.runtime.collection('item').countDocuments({
      projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
      iso: job.data?.iso,
      updatedAt: { $gte: startOfISOWeek(new Date()) },
      dealerType: dealer.dealerType,
      dealerId: dealer.dealerId,
    })

    if (!listingCount) continue

    const advertisingCost = getAdvertisingCost(dealerProductType, listingCount)

    appendData.push({
      dealerId: dealer.dealerId,
      listingCount,
      ...(advertisingCost && { advertisingCost: Number(advertisingCost) }),
    })
  }

  return appendData.map((item) => {
    return {
      type: ResultType.item,
      data: {
        iso: job.data?.iso,
        itemId: getItemId(DataType.dealers, ProjectType.cars, item.dealerId),
        'data.listingCount': item.listingCount,
        ...(item.advertisingCost && { 'data.advertisingCost': item.advertisingCost }),
      },
    }
  })
}
