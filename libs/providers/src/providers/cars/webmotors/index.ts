import {
  AuctionsDealerType,
  VehicleType,
  ClassifiedsStockType,
  DataType,
  Iso3166Alpha2,
  MarketplacesDealerType,
  ProjectType,
  Region,
  ResultType,
} from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'

import type { SearchResultsEntity, WebmotorsData } from './types'
import type { ControllerRuntime, JobDefinition, JobInstance, Provider } from '@datagatherers/datagatherers'

const SIZE = 24

const provider: Provider = {
  extractBR: {
    // cron: '0 7 * * TUE',
    region: Region.us_east_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return scheduleListings(job, runtime, Iso3166Alpha2.BR)
    },
    scheduleAfterFinished: async (config) => ({
      project: config.project,
      provider: config.provider,
      type: 'extractInventoryCountBR',
    }),
    config: {
      constrains: {
        countries: ['BR'],
      },
      concurrency: 15,
    },
  },
  extractInventoryCountBR: {
    region: Region.us_east_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.BR)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.BR, config, runtime),
    config: {
      concurrency: 15,
    },
  },
}

export default provider

async function scheduleListings(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const searchResults = await runtime
    .got('https://www.webmotors.com.br/api/search/car', {
      method: 'GET',
      searchParams: {
        url: 'https://webmotors/appmobile/carro?&tipoveiculo=carros&Anunciante=',
        showCount: 'true',
        showMenu: 'true',
        showResult: 'true',
        actualPage: '1',
      },
      headers: {
        'content-type': 'application/json',
        accept: 'application/json',
        'accept-charset': 'UTF-8',
      },
      retry: {
        backoffLimit: 2000,
        limit: 20,
        statusCodes: [403],
      },
    })
    .json<WebmotorsData>()

  if (!searchResults?.Count) throw new Error('Failed to get total number of listings.')

  const nrPages = Math.ceil(Number(searchResults.Count) / SIZE)
  const pagesPerJob = 1
  const jobs = []
  for (let start = 1; start <= nrPages; start += pagesPerJob) {
    const end = Math.min(nrPages, start + pagesPerJob - 1)
    jobs.push({
      ...job,
      data: {
        iso,
        start,
        end,
      },
    })
  }

  return jobs
}

async function extractListings(job: JobInstance) {
  const appendData: SearchResultsEntity[] = []
  const data = await getResponse(job, job.data?.start)

  if (!data?.SearchResults?.length) throw new Error('Failed to extract results.')
  appendData.push(...data.SearchResults)

  const dealersMap: Map<
    string,
    {
      dealerId: string
      dealerType: MarketplacesDealerType.private | MarketplacesDealerType.dealer
      isActive: boolean
      isPaying: boolean
    }
  > = new Map()
  const resultsInventory = appendData
    .filter((item) => item.UniqueId)
    .map((item) => {
      const dealerId = item.Seller.Id.toString()
      const dealerType =
        item.Seller.SellerType === 'PF' ? MarketplacesDealerType.private : MarketplacesDealerType.dealer
      const isPaying = dealerType !== MarketplacesDealerType.private
      if (!!dealerId && !dealersMap.has(dealerId)) {
        dealersMap.set(dealerId, {
          dealerId,
          dealerType,
          isActive: true,
          isPaying,
        })
      }
      const data = {
        project: ProjectType.cars,
        itemId: `listing_${job.data?.iso}_${job.project}_${item.UniqueId}`,
        iso: job.data?.iso,
        dataType: DataType.inventory,
        currency: 'BRL',
        ...(item.Prices.Price && { price: item.Prices.Price }),
        inventoryId: item.UniqueId.toString(),
        dealerId: item.Seller.Id.toString(),
        dealerType: item.Seller.SellerType === 'PF' ? AuctionsDealerType.private : AuctionsDealerType.dealer,
        stockType: item.ListingType === 'N' ? ClassifiedsStockType.new : ClassifiedsStockType.used,
        ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  const resultsDealers = [...dealersMap.values()]
    .filter((a) => !!a && !!a.dealerId)
    .map((item) => {
      const data = {
        project: ProjectType.cars,
        itemId: `dealer_${job.data?.iso}_${job.project}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: item.isActive,
        isPaying: item.isPaying,
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  return [...resultsInventory, ...resultsDealers]
}

async function getResponse(job: JobInstance, index: number) {
  return job.runtime
    .got('https://www.webmotors.com.br/api/search/car', {
      method: 'GET',
      searchParams: {
        url: 'https://webmotors/appmobile/carro?&tipoveiculo=carros&Anunciante=',
        showCount: 'true',
        showMenu: 'true',
        showResult: 'true',
        actualPage: index.toString(),
      },
      headers: {
        'content-type': 'application/json',
        accept: 'application/json',
        'accept-charset': 'UTF-8',
      },
      retry: {
        backoffLimit: 2000,
        limit: 20,
        statusCodes: [403],
      },
    })
    .json<WebmotorsData>()
}
