import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config, snapshot } from '@datagatherers/worker-utils'

import { extract, scheduleExtract } from '../../../lib/cars/autohero'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  // Germany
  extractDE: {
    cron: '0 12 * * MON',
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.DE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractFR',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.DE, config, runtime),
    config,
  },
  extractFR: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.FR),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractES',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.FR, config, runtime),
    config,
  },
  // Spain
  extractES: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.ES),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractSE',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.ES, config, runtime),
    config,
  },
  // Sweden
  extractSE: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.SE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDK',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.SE, config, runtime),
    config,
  },
  // Denmark
  extractDK: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.DK),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractNL',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.DK, config, runtime),
    config,
  },
  // Netherlands
  extractNL: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.NL),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractBE',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.NL, config, runtime),
    config,
  },
  // Belgium
  extractBE: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.BE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractFI',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.BE, config, runtime),
    config,
  },
  // Finland
  extractFI: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.FI),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractIT',
      }
    },
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.FI, config, runtime),
    config,
  },
  // Italy
  extractIT: {
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) => scheduleExtract(job, Iso3166Alpha2.IT),
    snapshot: (config, runtime) => snapshot(Iso3166Alpha2.IT, config, runtime),
    config,
  },
}
export default provider
