import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import nederlandmobiel from '../../../lib/cars/nederlandmobiel'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractNL: {
    // cron: '0 10 * * THU', blocked cloudfare no solution yet
    region: Region.eu_central_1,
    function: nederlandmobiel.extractInventory,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return nederlandmobiel.scheduleInventory(job, runtime, Iso3166Alpha2.NL)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDetailsNL',
      }
    },
    config: {
      browser: true,
      concurrency: 15,
    },
  },
  extractDetailsNL: {
    region: Region.eu_central_1,
    function: nederlandmobiel.extractDetails,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return nederlandmobiel.scheduleDetails(job, runtime, Iso3166Alpha2.NL)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountNL',
      }
    },
    config: {
      browser: true,
      concurrency: 15,
    },
  },
  extractInventoryCountNL: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.NL)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.NL, config, runtime),
    config: {
      browser: true,
      concurrency: 10,
    },
  },
}

export default provider
