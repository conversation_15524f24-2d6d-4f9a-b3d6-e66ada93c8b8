import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import { scheduleListings, extractListings } from '../../../lib/cars/spoticar'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractFR: {
    cron: '0 6 * * WED',
    region: Region.eu_west_3,
    schedule: (job, runtime) => scheduleListings(job, runtime, Iso3166Alpha2.FR),
    function: extractListings,
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.FR, config, runtime),
    config: {
      concurrency: 15,
    },
  },
}

export default provider
