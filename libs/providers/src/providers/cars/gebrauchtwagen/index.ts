import {
  AuctionsDealerType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  Region,
  ResultType,
  ClassifiedsStockType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { getItemId, randomWait, snapshot } from '@datagatherers/worker-utils'

import { MAX_PAGES, RADIUS, SIZE, bodyTypes, custtypes, offers, transmissions } from './constants'

import type { GebrauchtwagenSearchListings } from './types'
import type {
  DealerType,
  ControllerRuntime,
  JobDefinition,
  JobInstance,
  PostalCode,
  Provider,
} from '@datagatherers/datagatherers'

const provider: Provider = {
  extractAT: {
    cron: '0 3 * * TUE',
    region: Region.eu_central_1,
    function: extract,
    schedule: async (job, runtime) => schedule(job, runtime, Iso3166Alpha2.AT),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.AT, config, runtime),
    config: {
      concurrency: 10,
      returnCount: true,
    },
  },
}

export default provider

async function schedule(job: JobDefinition, runtime: ControllerRuntime, iso: Iso3166Alpha2): Promise<JobDefinition[]> {
  const postalCodes = await runtime.collection<PostalCode>('postalcode').find({ iso }).toArray()
  const jobs: JobDefinition[] = []

  for (const postalcode of postalCodes) {
    for (const body of bodyTypes) {
      for (const offer of offers) {
        for (const custtype of custtypes) {
          for (const transmission of transmissions) {
            jobs.push({
              ...job,
              data: {
                ...job.data,
                iso,
                zip: postalcode.data.code,
                state: `${iso}-${postalcode.data.admin_code1}`,
                body: body.BODY_TYPE_ID,
                offer,
                custtype,
                gear: transmission.TRANSMISSION_ID,
                ...(job.data?.parameters && { parameters: job.data?.parameters }),
              },
            })
          }
        }
      }
    }
  }

  return jobs
}

async function extract(job: JobInstance) {
  const appendData = []
  for (let index = 1; index <= MAX_PAGES; index++) {
    await randomWait(500, 800)
    const listings = await job.runtime
      .got('https://www.gebrauchtwagen.at/api/v2/search-listings', {
        searchParams: {
          sort: 'standard',
          desc: '0',
          zip: job.data?.zip,
          zipr: RADIUS,
          custtype: job.data?.custtype,
          offer: job.data?.offer,
          body: job.data?.body,
          gear: job.data?.gear,
          size: SIZE,
          page: index,
        },
        retry: {
          limit: 3,
          errorCodes: ['ERR_GOT_REQUEST_ERROR'],
        },
      })
      .json<GebrauchtwagenSearchListings>()
      .then((json) => json.listings)

    if (!listings?.length) {
      break
    }

    appendData.push(...listings)

    if (listings.length < SIZE) {
      break
    }
  }

  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType }> = new Map()

  const resultsInventory = appendData.flatMap((item) => {
    if (!item.id) return []
    const dealerType = item.sellerType === 'D' ? AuctionsDealerType.dealer : AuctionsDealerType.private
    if (item.seller.id) {
      if (!dealersMap.has(item.seller.id)) {
        dealersMap.set(item.seller.id, {
          dealerId: item.seller.id,
          dealerType: dealerType,
        })
      }
    }

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        dataType: DataType.inventory,
        iso: job.data?.iso,
        itemId: getItemId(DataType.inventory, ProjectType.cars, item.id),
        inventoryId: item.id,
        dealerId: item.seller.id,
        dealerType,
        stockType: job.data?.offer === 'N' ? ClassifiedsStockType.new : ClassifiedsStockType.used,
        ...(item.price.raw && { price: item.price.raw, currency: 'EUR' }),
        state: job.data?.state,
        ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
      },
    }
  })

  const resultsDealers = [...dealersMap.values()].map((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: getItemId(DataType.dealers, ProjectType.cars, item.dealerId),
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === AuctionsDealerType.dealer,
      },
    }
  })

  return [...resultsInventory, ...resultsDealers]
}
