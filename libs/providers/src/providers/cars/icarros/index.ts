import {
  Iso3166Alpha2,
  Region,
  ResultType,
  DataType,
  AuctionsDealerType,
  ProjectType,
  ClassifiedsStockType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { snapshot, wait } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'

import type {
  Provider,
  JobDefinition,
  Town,
  DealerType,
  JobInstance,
  ControllerRuntime,
  JobRuntime,
  Result,
} from '@datagatherers/datagatherers'

const radius = 1
const pagesPerJob = 25
interface StatesResponse {
  sigla: string
  nome: string
}

interface CitiesResponse {
  id: number
  uf: string
  nome: string
}
interface carsResponse {
  anuncioSomenteTradein: boolean
  anuncioSomentePF: boolean
  modelId: number
  segment: string
  makeId: number
  dealerId: number
  trimId: number
  listPosition: number
  sellerPhoneNumber: string
  modelYear: string
  trimDescription: string
  sellerCity: number
  dealText: string
  dealId: number
  price: number
  offerFinancing: boolean
  conversionTag: string
  visualizationTag: string
  legalText: string
  videoUrl?: null
  sellerUf: string
  dealerAddress: string
  manufacturingYear: string
  latitude: number
  longitude: number
  makeDescription: string
  modelDescription: string
  sellerType: number
  sellerName: string
  statusAnunciantePJ: number
  regionalGroupId: number
  sellerCityDescription: string
  hidePhoneNumber: boolean
  showPhoneNumber: boolean
  regionalGroupDealersCount: number
  colorDescription: string
  equipmentsIds?: number[] | null
  plateLastNumber: string
  certificacaoId?: null
  equipments?: string[] | null
  hasDealFeiraoEnabled: boolean
  hasDealCallTrackingEnabled: boolean
  hasLeadQualifierEnabled: boolean
  sellerFR: number
  fuelDescription: string
  specificTrimDescription: string
  mid: string
  sellerCallTrackingPhoneNumber: string
  nomeCompletoRequired: boolean
  cpfRequired: boolean
  regionalGroupHasSimulationEnabled: boolean
  sellerPhoneNumbers?: string[] | null
  sellerText: string
  images?: string[] | null
  gearDescription: string
  km: number
  distKm?: null
  flagOptin: boolean
  flagOptinObrigatorio: boolean
  flagOptinTexto?: null
  flagOptinTextoLink?: null
  flagOptinLink?: null
  dealerImageVersion: string
  makeImageVersion: string
  isBlindado: boolean
  financingAvailable: boolean
  shareData: ShareData
  hasDealSimulationEnabled: boolean
  entregaFacil: boolean
  phoneRequired: boolean
  sellerLeadPhoneRequired: boolean
  sellerLeadMessageRequired: boolean
  isZeroKm: boolean
}
interface ShareData {
  url: string
  title: string
  content: string
}

const provider: Provider = {
  codesBR: {
    region: Region.eu_west_1,
    function: async (job) => makeTowns(job, Iso3166Alpha2.BR),
    schedule: async (job, runtime): Promise<JobDefinition[]> => makeTownJobs(job, runtime),
    config: {
      concurrency: 10,
    },
  },
  subdivideJobsBR: {
    //Run this after codesBR and before extractBR in local worker
    cron: '0 5 * * WED',
    region: Region.eu_west_1,
    function: populateTown,
    schedule: async (job, runtime): Promise<JobDefinition[]> => makePreJobs(job, runtime, Iso3166Alpha2.BR),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractBR',
      }
    },
    config: {
      concurrency: 15,
    },
  },
  extractBR: {
    region: Region.eu_west_1,
    function: makeResults,
    schedule: async (job, runtime): Promise<JobDefinition[]> => makeJobs(job, runtime, Iso3166Alpha2.BR),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountBR',
      }
    },
    config: {
      concurrency: 15,
    },
  },
  extractInventoryCountBR: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.BR),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.BR, config, runtime),
    config: {
      concurrency: 10,
    },
  },
}

export default provider

async function makeTownJobs(job: JobDefinition, runtime: ControllerRuntime): Promise<JobDefinition[]> {
  const stateArray = await getStates(runtime)
  if (!stateArray.length) {
    return []
  }
  const jobs: JobDefinition[] = stateArray.map((item) => {
    const res = {
      ...job,
      data: {
        ...job.data,
        ...item,
        ...(job.data?.parameters && { parameters: job.data?.parameters }),
      },
    }
    return res
  })
  return jobs
}

async function makeTowns(job: JobInstance, iso: Iso3166Alpha2) {
  const cities = await getCities(job)
  if (!cities.length) {
    return []
  }

  return cities.map((item) => {
    return {
      type: ResultType.town,
      data: {
        project: job.project,
        itemId: `${item.nome}_${item.id}`,
        iso,
        blacklisted: false,
        data: {
          id: item.id.toString(),
          state: item.uf,
          city: item.nome,
        },
      },
    }
  })
}

async function makePreJobs(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const collection = runtime.collection<Town>('town')
  const filter = {
    project: job.project,
    provider: job.provider,
    iso,
    blacklisted: false,
  }
  const townArray = await collection.find(filter).toArray()

  const jobs = townArray.map((item) => {
    const res = {
      ...job,
      data: {
        iso: item.iso,
        townId: item.itemId,
        ...item.data,
      },
    }
    return res
  })
  return jobs
}

async function populateTown(job: JobInstance): Promise<Result[]> {
  const maxPages = await getMaxPages(job, job.runtime)
  const data = [
    {
      type: ResultType.town,
      data: {
        itemId: job.data?.townId,
        iso: job.data?.iso,
        'data.maxPages': maxPages,
      },
    },
  ]
  return data
}

async function makeJobs(job: JobDefinition, runtime: ControllerRuntime, iso: Iso3166Alpha2): Promise<JobDefinition[]> {
  const collection = runtime.collection<Town>('town')
  const filter = {
    project: job.project,
    provider: job.provider,
    iso,
    blacklisted: false,
    'data.maxPages': { $exists: true },
  }
  const townArray = await collection.find(filter).toArray()
  const subDividedTownArray = []

  for (const town of townArray) {
    const nrOfJobs = Math.ceil((town.data.maxPages as number) / pagesPerJob)
    for (let i = 0; i < nrOfJobs; i++) {
      subDividedTownArray.push({ ...town.data, pageOffset: pagesPerJob * i + 1 })
    }
  }

  const jobs = subDividedTownArray.map((item) => {
    const res = {
      ...job,
      data: {
        iso,
        ...item,
      },
    }
    return res
  })
  return jobs
}

async function makeResults(job: JobInstance) {
  const listings = await getListings(job)
  if (!listings || listings.length === 0) {
    return []
  }
  const dealersMap: Map<number, { dealerId: number; dealerType: DealerType }> = new Map()
  let results = []

  const resultsInventory = listings
    .filter((item) => !!item && !!item.dealId)
    .map((item) => {
      const dealerType = item.sellerType === 2 ? AuctionsDealerType.dealer : AuctionsDealerType.private
      if (item.dealerId) {
        if (!dealersMap.has(item.dealerId)) {
          try {
            dealersMap.set(item.dealerId, {
              dealerId: item.dealerId,
              dealerType,
            })
          } catch (e) {
            console.error(e)
          }
        }
      }
      const data = {
        project: ProjectType.cars,
        dataType: DataType.inventory,
        iso: job.data?.iso,
        itemId: `listing_${job.data?.iso}_${job.project}_${item.dealId}`,
        inventoryId: item.dealId.toString(),
        dealerId: item.dealerId,
        dealerType,
        stockType: item.isZeroKm === true ? ClassifiedsStockType.new : ClassifiedsStockType.used,
        ...(item.price && { price: item.price }),
        ...(item.manufacturingYear && { year: Number(item.manufacturingYear) }),
        ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
        data: {
          ...(!!item.makeDescription && { brand: item.makeDescription }),
          ...(!!item.modelDescription && { model: item.modelDescription }),
          ...(!!item.km && { model: item.km }),
          ...(!!item.latitude && { latitude: item.latitude }),
          ...(!!item.longitude && { longitude: item.longitude }),
          ...(!!item.colorDescription && { color: item.colorDescription }),
        },
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  const resultsDealers = [...dealersMap.values()]
    .filter((item) => !!item)
    .map((item) => {
      const data = {
        project: ProjectType.cars,
        itemId: `dealer_${job.data?.iso}_${job.project}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId.toString(),
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === AuctionsDealerType.dealer,
        data: item,
      }

      return {
        type: ResultType.item,
        data,
      }
    })
  results = [...resultsInventory, ...resultsDealers]

  return results
}

async function getStates(runtime: ControllerRuntime): Promise<StatesResponse[]> {
  const response = await runtime
    .axios(`https://internal-ios-services.icarros.com.br/rest/search/v1/states`, {
      headers: { cookie: 'icarrosnginx=96d86eb1bbf817a218f7be6f4f601da87c815a21' },
    })
    .then((res) => res.data)
    .catch((error) => console.error(error))

  if (!response || response.length === 0) {
    return []
  }
  return response
}

async function getCities(job: JobInstance): Promise<CitiesResponse[]> {
  await wait(100)
  const response = await job.runtime
    .axios(`https://internal-ios-services.icarros.com.br/rest/search/v1/states/${job.data?.sigla}/cities`, {
      headers: { cookie: 'icarrosnginx=96d86eb1bbf817a218f7be6f4f601da87c815a21' },
    })
    .then((res) => res.data)
    .catch((error) => console.error(error))

  if (!response || response.length === 0) {
    return []
  }
  return response
}

async function getMaxPages(job: JobDefinition, runtime: JobRuntime): Promise<number> {
  await wait(100)
  const response = await runtime
    .axios(
      `https://internal-ios-services.icarros.com.br/rest/search/deals/est_${job.data?.state}.1-rai_${radius}.1-nta_17%7C44%7C51.1-esc_2.1-sta_1.1-cid_${job.data?.id}.1/10/50/1`,
      {
        params: { includeAMDeals: 'true' },
        headers: { cookie: 'icarrosnginx=96d86eb1bbf817a218f7be6f4f601da87c815a21' },
      },
    )
    .then((res) => res.data.numResults)
    .catch((error) => console.error(error))

  if (!response || response === 0) {
    return 0
  }

  return Math.ceil(response / 50)
}

async function getPage(job: JobInstance, pageNr: number): Promise<carsResponse[]> {
  await wait(100)
  const response = await job.runtime
    .got(
      `https://internal-ios-services.icarros.com.br/rest/search/deals/est_${job.data?.state}.1-rai_${radius}.1-nta_17%7C44%7C51.1-esc_2.1-sta_1.1-cid_${job.data?.id}.1/10/50/${pageNr}`,
      {
        searchParams: { includeAMDeals: 'true' },
        headers: { cookie: 'icarrosnginx=96d86eb1bbf817a218f7be6f4f601da87c815a21' },
      },
    )
    .json<{ deals: carsResponse[] }>()
    .then((res) => res?.deals)

  if (!response?.length) {
    return []
  }
  return response
}

async function getListings(job: JobInstance): Promise<carsResponse[]> {
  const completeListings: carsResponse[] = []

  for (let i = job.data?.pageOffset; i < job.data?.pageOffset + pagesPerJob; i++) {
    const partialListings = await getPage(job, i)
    if (partialListings.length === 0) {
      break
    }
    completeListings.push(...partialListings)
  }
  return completeListings
}
