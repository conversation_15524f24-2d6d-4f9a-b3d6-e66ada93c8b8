import { Region, Iso3166Alpha2, TimeUnit, ProjectType } from '@datagatherers/datagatherers'
import { SnapshotResolution, snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import { extractCars, pricesCsv, scheduleListings } from '../../../lib/cars/mobile.de'
import { trackerMobile_dePageStateDE, trackerMobile_dePricingDE } from '../../non_standard/trackers'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractDE: {
    cron: '0 1 * * MON',
    region: Region.eu_central_1,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleListings(job, runtime, Iso3166Alpha2.DE),
    function: extractCars,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountDE',
      }
    },
    config: {
      concurrency: 15,
      returnCount: true,
    },
  },
  extractInventoryCountDE: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.DE)
    },
    snapshot: async (config, runtime) => {
      await snapshot(Iso3166Alpha2.DE, config, runtime)
      await snapshot(Iso3166Alpha2.DE, config, runtime, SnapshotResolution.Full, undefined, 'mobile.de_leasing', {
        'data.isLeasing': true,
      })
    },
    scheduleAfterFinished: () => {
      return [
        {
          project: ProjectType.non_standard,
          provider: 'csvExports',
          type: 'mobileCarsWeeklyCsv',
        },
      ]
    },
    config: {
      concurrency: 15,
      returnCount: true,
    },
  },
  extractListingsMonthlyPrices: {
    cron: '0 1 1 * *', // Every 1st day of month at 01:00 GMT
    region: Region.eu_central_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (config, runtime) => {
      return pricesCsv(config, runtime, Iso3166Alpha2.DE, TimeUnit.months)
    },
    config: {
      concurrency: 1,
    },
  },
  trackerMobile_dePageStateDE,
  trackerMobile_dePricingDE,
}

export default provider
