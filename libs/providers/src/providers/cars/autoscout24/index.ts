import { Region, Iso3166Alpha2, ProjectType } from '@datagatherers/datagatherers'
import { snapshot, snapshots } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import {
  extract,
  extractCH,
  extractCodes,
  scheduleCodes,
  scheduleExtract,
  scheduleExtractCH,
  setListingsPriceRanges,
} from '../../../lib/cars/autoscout24'
import { autoscout24Config } from '../../../lib/cars/autoscout24/constants'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const provider: Provider = {
  codesDE: {
    region: Region.eu_central_1,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.DE),
    config: autoscout24Config,
  },
  extractDE: {
    cron: '0 8 * * MON',
    region: Region.eu_central_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.DE),
    scheduleAfterFinished: (controllerConfig) => {
      return [
        {
          project: controllerConfig.project,
          provider: controllerConfig.provider,
          type: 'extractInventoryCountDE',
        },
      ]
    },
    config: autoscout24Config,
  },
  extractInventoryCountDE: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.DE),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.DE, controllerConfig, runtime),
    config: autoscout24Config,
  },
  codesAT: {
    region: Region.eu_central_1,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.AT),
    config: autoscout24Config,
  },
  extractAT: {
    cron: '0 8 * * TUE',
    region: Region.eu_central_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.AT),
    scheduleAfterFinished: (controllerConfig) => {
      return [
        {
          project: controllerConfig.project,
          provider: controllerConfig.provider,
          type: 'extractInventoryCountAT',
        },
      ]
    },
    config: autoscout24Config,
  },
  extractInventoryCountAT: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.AT),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.AT, controllerConfig, runtime),
    config: autoscout24Config,
  },
  codesES: {
    region: Region.eu_west_1,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.ES),
    config: autoscout24Config,
  },
  extractES: {
    cron: '0 12 * * MON',
    region: Region.eu_west_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.ES),
    scheduleAfterFinished: (controllerConfig) => {
      return {
        project: controllerConfig.project,
        provider: controllerConfig.provider,
        type: 'extractInventoryCountES',
      }
    },
    config: {
      ...autoscout24Config,
      concurrency: 50,
    },
  },
  extractInventoryCountES: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.ES),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.ES, controllerConfig, runtime),
    config: autoscout24Config,
  },
  codesFR: {
    region: Region.eu_west_3,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.FR),
    config: autoscout24Config,
  },
  extractFR: {
    cron: '0 8 * * WED',
    region: Region.eu_west_3,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.FR),
    scheduleAfterFinished: (controllerConfig) => {
      return {
        project: controllerConfig.project,
        provider: controllerConfig.provider,
        type: 'extractInventoryCountFR',
      }
    },
    config: autoscout24Config,
  },
  extractInventoryCountFR: {
    region: Region.eu_west_3,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.FR),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.FR, controllerConfig, runtime),
    config: autoscout24Config,
  },
  codesIT: {
    region: Region.eu_west_3,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.IT),
    config: autoscout24Config,
  },
  extractIT: {
    cron: '0 16 * * MON',
    region: Region.eu_west_3,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.IT),
    scheduleAfterFinished: (controllerConfig) => {
      return {
        project: controllerConfig.project,
        provider: controllerConfig.provider,
        type: 'extractInventoryCountIT',
      }
    },
    config: autoscout24Config,
  },
  extractInventoryCountIT: {
    region: Region.eu_west_3,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.IT),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.IT, controllerConfig, runtime),
    config: autoscout24Config,
  },
  // ---- Belgium -----
  codesBE: {
    region: Region.eu_central_1,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.BE),
    config: autoscout24Config,
  },
  extractBE: {
    cron: '0 16 * * WED',
    region: Region.eu_central_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.BE),
    scheduleAfterFinished: (controllerConfig) => {
      return [
        {
          project: controllerConfig.project,
          provider: controllerConfig.provider,
          type: 'extractInventoryCountBE',
        },
      ]
    },
    config: autoscout24Config,
  },
  extractInventoryCountBE: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.BE),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.BE, controllerConfig, runtime),
    config: autoscout24Config,
  },
  // ---- Netherlands -----
  codesNL: {
    region: Region.eu_central_1,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.NL),
    config: autoscout24Config,
  },
  extractNL: {
    cron: '0 16 * * MON',
    region: Region.eu_central_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.NL),
    scheduleAfterFinished: (controllerConfig) => {
      return [
        {
          project: controllerConfig.project,
          provider: controllerConfig.provider,
          type: 'extractInventoryCountNL',
        },
      ]
    },
    config: autoscout24Config,
  },
  extractInventoryCountNL: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.NL),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.NL, controllerConfig, runtime),
    config: autoscout24Config,
  },
  // ---- Switzerland -----
  extractCarsCH: {
    region: Region.eu_central_1,
    cron: '0 12 * * MON',
    function: extractCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractCH(job, runtime, Iso3166Alpha2.CH, 'car'),
    config: {
      ...autoscout24Config,
      perJobTimeout: 30 * 60 * 1000,
    },
    scheduleAfterFinished: (controllerConfig) => ({
      project: controllerConfig.project,
      provider: controllerConfig.provider,
      type: 'extractCarsCamperCH',
    }),
    bottleneck: [
      {
        maxConcurrent: autoscout24Config.concurrency,
        minTime: 300,
        clearDatastore: true,
      },
    ],
  },
  extractCarsCamperCH: {
    region: Region.eu_central_1,
    function: extractCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractCH(job, runtime, Iso3166Alpha2.CH, 'camper'),
    config: autoscout24Config,
    // :  {
    // ...config,
    // constrains: { countries: ['CH'] },
    // constrains: { region: ConstrainsRegion.EU },
    // },
    scheduleAfterFinished: (controllerConfig) => ({
      project: controllerConfig.project,
      provider: controllerConfig.provider,
      type: 'extractCarsUtilityCH',
    }),
    bottleneck: [
      {
        maxConcurrent: autoscout24Config.concurrency,
        minTime: 300,
        clearDatastore: true,
      },
    ],
  },
  extractCarsUtilityCH: {
    region: Region.eu_central_1,
    function: extractCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractCH(job, runtime, Iso3166Alpha2.CH, 'utility'),
    config: autoscout24Config,
    // :  {
    // ...config,
    // constrains: { countries: ['CH'] },
    // constrains: { region: ConstrainsRegion.EU },
    // },
    scheduleAfterFinished: (controllerConfig) => ({
      project: controllerConfig.project,
      provider: controllerConfig.provider,
      type: 'extractCarsTruckCH',
    }),
    bottleneck: [
      {
        maxConcurrent: autoscout24Config.concurrency,
        minTime: 300,
        clearDatastore: true,
      },
    ],
  },
  extractCarsTruckCH: {
    region: Region.eu_central_1,
    function: extractCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractCH(job, runtime, Iso3166Alpha2.CH, 'truck'),
    config: autoscout24Config,
    // :  {
    // ...config,
    // constrains: { countries: ['CH'] },
    // constrains: { region: ConstrainsRegion.EU },
    // },
    scheduleAfterFinished: (controllerConfig) => ({
      project: controllerConfig.project,
      provider: controllerConfig.provider,
      type: 'extractCarsTrailerCH',
    }),
    bottleneck: [
      {
        maxConcurrent: autoscout24Config.concurrency,
        minTime: 300,
        clearDatastore: true,
      },
    ],
  },
  extractCarsTrailerCH: {
    region: Region.eu_central_1,
    function: extractCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractCH(job, runtime, Iso3166Alpha2.CH, 'trailer'),
    config: autoscout24Config,
    // :  {
    // ...config,
    // constrains: { countries: ['CH'] },
    // constrains: { region: ConstrainsRegion.EU },
    // },
    scheduleAfterFinished: (controllerConfig) => ({
      project: controllerConfig.project,
      provider: controllerConfig.provider,
      type: 'extractInventoryCountCH',
    }),
    bottleneck: [
      {
        maxConcurrent: autoscout24Config.concurrency,
        minTime: 300,
        clearDatastore: true,
      },
    ],
  },
  extractInventoryCountCH: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.CH),
    afterControllerFinished: async (config, runtime) => {
      await setListingsPriceRanges(config, runtime, Iso3166Alpha2.CH)
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.cars,
      provider: 'comparis',
      type: 'extractAutoscoutCH',
    }),
    snapshot: async (controllerConfig, runtime) => {
      await snapshots.carsFull(Iso3166Alpha2.CH, controllerConfig, runtime)
      await snapshot(Iso3166Alpha2.CH, controllerConfig, runtime, undefined, undefined, 'autoscout.ch_discounted', {
        'data.isDiscounted': true,
      })
      await snapshot(Iso3166Alpha2.CH, controllerConfig, runtime, undefined, undefined, 'autoscout.ch_toplist', {
        'data.hasTopList': true,
      })
    },
    config: autoscout24Config,
  },
  snapComparisCrossposted: {
    region: Region.eu_central_1,
    function: () => [],
    snapshot: async (controllerConfig, runtime) =>
      snapshot(Iso3166Alpha2.CH, controllerConfig, runtime, undefined, undefined, 'autoscout.ch_comparis_crossposted', {
        'data.isCrossposted': true,
      }),
    config: autoscout24Config,
  },
  // ---- Luxembourg -----
  codesLU: {
    region: Region.eu_central_1,
    function: extractCodes,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleCodes(job, runtime, Iso3166Alpha2.LU),
    config: autoscout24Config,
  },
  extractLU: {
    cron: '0 20 * * TUE',
    region: Region.eu_central_1,
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.LU),
    scheduleAfterFinished: (controllerConfig) => {
      return [
        {
          project: controllerConfig.project,
          provider: controllerConfig.provider,
          type: 'extractInventoryCountLU',
        },
      ]
    },
    config: autoscout24Config,
  },
  extractInventoryCountLU: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.LU),
    snapshot: async (controllerConfig, runtime) => snapshots.carsFull(Iso3166Alpha2.LU, controllerConfig, runtime),
    config: autoscout24Config,
  },
}

export default provider
