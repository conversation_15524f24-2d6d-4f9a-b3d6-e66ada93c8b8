import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { snapshots } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import { extractGB, extractUS, scheduleExtractGB, scheduleExtractUS } from '../../../lib/cars/autotrader'
import { trackerAutotraderPricingGB } from '../../../providers/non_standard/trackers'

import type { Provider, JobDefinition, JobInstance } from '@datagatherers/datagatherers'

const config = {
  concurrency: 10,
  returnCount: true,
}

const provider: Provider = {
  codesUS: {
    region: Region.us_east_1,
    function: async (job: JobInstance) => {
      return cars.codes_postalcode(job, Iso3166Alpha2.US)
    },
    config: { ...config, concurrency: 1 },
  },
  extractUS: {
    cron: '0 2 * * MON',
    region: Region.eu_west_1,
    function: extractUS,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtractUS(job, runtime),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountUS',
      }
    },
    config: {
      ...config,
      concurrency: 40,
      constrains: { countries: ['DO'] },
    },
  },
  extractInventoryCountUS: {
    region: Region.us_east_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.US)
    },
    snapshot: async (config, runtime) => {
      await snapshots.carsFull(Iso3166Alpha2.US, config, runtime)
    },
    config,
  },
  extractGB: {
    cron: '0 1 * * TUE',
    region: Region.eu_west_2,
    function: extractGB,
    schedule: scheduleExtractGB,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountGB',
      }
    },
    config: {
      ...config,
      concurrency: 40,
      //constrains: { countries: ['GB'] },
    },
  },
  extractInventoryCountGB: {
    region: Region.eu_west_2,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.GB)
    },
    snapshot: async (config, runtime) => await snapshots.carsFull(Iso3166Alpha2.GB, config, runtime),
    config,
  },
  trackerAutotraderPricingGB,
}

export default provider
