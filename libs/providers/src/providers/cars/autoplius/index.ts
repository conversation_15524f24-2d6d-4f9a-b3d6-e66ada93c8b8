import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { snapshot, config } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import {
  scheduleExtract,
  extract,
  scheduleExtractDealerDetails,
  extractDealerDetails,
} from '../../../lib/cars/autoplius/index'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractLT: {
    region: Region.eu_central_1,
    cron: '0 6 * * WED',
    function: extract,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.LT),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDealerDetailsLT',
      }
    },
    config: {
      ...config,
      perJobTimeout: 30 * 60 * 1000,
      constrains: { countries: ['DE'] },
      browser: true,
    },
  },
  extractDealerDetailsLT: {
    region: Region.eu_central_1,
    function: extractDealerDetails,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractDealerDetails(job, runtime, Iso3166Alpha2.LT),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountLT',
      }
    },
    config: {
      ...config,
      perJobTimeout: 30 * 60 * 1000,
      constrains: { countries: ['DE'] },
      browser: true,
    },
  },
  extractInventoryCountLT: {
    region: Region.eu_north_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.LT),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.LT, config, runtime),
    config,
  },
}

export default provider
