import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { config, snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import { extractCH, scheduleExtractCH } from '../../../lib/cars/autoscout24'
import { autoscout24Config } from '../../../lib/cars/autoscout24/constants'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'
const provider: Provider = {
  extractMotosCH: {
    cron: '0 12 * * WED',
    region: Region.eu_central_1,
    function: extractCH,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtractCH(job, runtime, Iso3166Alpha2.CH, 'motorcycle'),
    scheduleAfterFinished: (controllerConfig) => ({
      project: controllerConfig.project,
      provider: controllerConfig.provider,
      type: 'extractInventoryCountCH',
    }),
    config: autoscout24Config,
    // :  {
    // ...config,
    // constrains: { countries: ['CH'] },
    // constrains: { region: ConstrainsRegion.EU },
    // },
    bottleneck: [
      {
        maxConcurrent: config.concurrency,
        minTime: 300,
        clearDatastore: true,
      },
    ],
  },
  extractInventoryCountCH: {
    region: Region.eu_central_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.CH),
    snapshot: async (controllerConfig, runtime) => snapshot(Iso3166Alpha2.CH, controllerConfig, runtime),
    config: autoscout24Config,
  },
}
export default provider
