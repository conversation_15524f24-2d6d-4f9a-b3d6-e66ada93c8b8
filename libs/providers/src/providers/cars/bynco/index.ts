import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import { extract } from '../../../lib/cars/bynco'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractNL: {
    //cron: '0 12 * * TUE',
    region: Region.eu_west_1,
    function: extract,
    schedule: (job) =>
      [1, 2, 3, 4].map((item) => {
        return {
          ...job,
          data: {
            ...job.data,
            iso: Iso3166Alpha2.NL,
            transmissionTypes: item,
          },
        }
      }),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountNL',
      }
    },
    config: {
      concurrency: 10,
    },
  },
  extractInventoryCountNL: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.NL),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.NL, config, runtime),
    config: {
      concurrency: 10,
    },
  },
}

export default provider
