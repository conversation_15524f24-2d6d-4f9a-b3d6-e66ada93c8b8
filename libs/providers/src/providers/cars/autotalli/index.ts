import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import autotalli from '../../../lib/cars/autotalli'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractUsedFI: {
    cron: '0 10 * * MON',
    region: Region.eu_west_1,
    function: autotalli.extractInventory,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      autotalli.scheduleExtract(job, runtime, Iso3166Alpha2.FI, false),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractNewFI',
      }
    },
    config: {
      concurrency: 8,
      browser: {
        userAgent: {
          deviceCategory: 'desktop',
        },
        defaultTimeout: 60000,
      },
    },
  },
  extractNewFI: {
    region: Region.eu_west_1,
    function: autotalli.extractInventory,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      autotalli.scheduleExtract(job, runtime, Iso3166Alpha2.FI, true),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.FI, config, runtime),
    config: {
      concurrency: 8,
      browser: {
        userAgent: {
          deviceCategory: 'desktop',
        },
        defaultTimeout: 60000,
      },
    },
  },
}

export default provider
