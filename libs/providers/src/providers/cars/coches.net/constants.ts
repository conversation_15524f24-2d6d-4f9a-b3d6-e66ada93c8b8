export const makes = {
  '1330': 'ABARTH',
  '1408': 'AIWAYS',
  '1': 'ALFA ROMEO',
  '1377': 'ALPINE',
  '238': 'ARO',
  '1326': 'ASIA',
  '2': 'ASIA MOTORS',
  '3': 'ASTON MARTIN',
  '4': 'AUDI',
  '111': 'AUSTIN',
  '1321': 'AUVERLAND',
  '6': 'BENTLEY',
  '241': 'BERTONE',
  '7': 'BMW',
  '1345': 'BUGATTI',
  '1352': 'BYD',
  '8': 'CADILLAC',
  '1415': 'CENNTRO',
  '9': 'CHEVROLET',
  '10': 'CHRYSLER',
  '11': 'CITROEN',
  '1327': 'CORVETTE',
  '1400': 'CUPRA',
  '1011': 'DACIA',
  '12': 'DAEWOO',
  '13': 'DAIHATSU',
  '145': 'DAIMLER',
  '1351': 'DFSK',
  '173': 'DODGE',
  '1431': 'DONGFENG',
  '1401': 'DR AUTOMOBILES',
  '1358': 'DS',
  '1410': 'EVO',
  '146': 'FERRARI',
  '14': 'FIAT',
  '1383': 'FISKER',
  '15': 'FORD',
  '1430': 'FOTON',
  '16': 'GALLOPER',
  '69': 'HONDA',
  '1423': 'HONGQI',
  '234': 'HUMMER',
  '18': 'HYUNDAI',
  '1409': 'INEOS',
  '1025': 'INFINITI',
  '185': 'INNOCENTI',
  '1405': 'INVICTA',
  '1413': 'INVICTA ELECTRIC',
  '19': 'ISUZU',
  '126': 'IVECO',
  '103': 'IVECO-PEGASO',
  '1434': 'JAECOO',
  '20': 'JAGUAR',
  '21': 'JEEP',
  '1427': 'KGM',
  '22': 'KIA',
  '1349': 'KTM',
  '153': 'LADA',
  '243': 'LAMBORGHINI',
  '23': 'LANCIA',
  '24': 'LAND-ROVER',
  '128': 'LDV',
  '1432': 'LEAPMOTOR',
  '25': 'LEXUS',
  '163': 'LIGIER',
  '147': 'LOTUS',
  '1404': 'LYNK & CO',
  '246': 'MAHINDRA',
  '26': 'MASERATI',
  '1403': 'MAXUS',
  '1323': 'MAYBACH',
  '27': 'MAZDA',
  '1347': 'MCLAREN',
  '28': 'MERCEDES-BENZ',
  '29': 'MG',
  '1428': 'M-HERO',
  '1433': 'MICRO',
  '222': 'MINI',
  '30': 'MITSUBISHI',
  '149': 'MORGAN',
  '1419': 'MW MOTORS',
  '1417': 'NEXTEM',
  '31': 'NISSAN',
  '1420': 'OMODA',
  '32': 'OPEL',
  '33': 'PEUGEOT',
  '87': 'PIAGGIO',
  '1402': 'POLESTAR',
  '112': 'PONTIAC',
  '34': 'PORSCHE',
  '1348': 'QOROS',
  '1372': 'RAM',
  '35': 'RENAULT',
  '36': 'ROLLS-ROYCE',
  '37': 'ROVER',
  '38': 'SAAB',
  '1328': 'SANTANA',
  '39': 'SEAT',
  '1422': 'SERES',
  '40': 'SKODA',
  '1425': 'SKYWELL',
  '41': 'SMART',
  '42': 'SSANGYONG',
  '43': 'SUBARU',
  '44': 'SUZUKI',
  '1411': 'SWM',
  '156': 'TALBOT',
  '45': 'TATA',
  '1354': 'TESLA',
  '46': 'TOYOTA',
  '1324': 'UMM',
  '1325': 'VAZ',
  '47': 'VOLKSWAGEN',
  '48': 'VOLVO',
  '1426': 'VOYAH',
  '186': 'WARTBURG',
  '1435': 'XPENG',
  '1421': 'YUDO',
  '1418': 'ZHIDOU',
}

export const configurations = {
  url: 'https://apps.gw.coches.net/search/listing',
  itemsPerPage: 100,
  backoff: 200,
}

export const offerTypes: { offerTypeIds: number[] }[] = [{ offerTypeIds: [1] }, { offerTypeIds: [0, 2, 3, 4, 5] }]
