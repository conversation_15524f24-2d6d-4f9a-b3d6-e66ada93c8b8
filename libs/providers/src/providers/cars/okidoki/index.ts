import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'

import cars from '../../../lib/cars'
import {
  extractListings,
  extractDealers,
  extractProviderConfig,
  scheduleDealers,
  scheduleExtract,
  snapshot,
  updateExistingDealers,
} from '../../../lib/classifieds/okidoki'

import type { JobDefinition, JobInstance, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractListingsEE: {
    cron: '0 13 * * WED',
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.EE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDealersEE',
      }
    },
    config: extractProviderConfig,
  },

  extractDealersEE: {
    region: Region.eu_north_1,
    function: extractDealers,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleDealers(job, runtime, Iso3166Alpha2.EE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'updateRemainingDealersEE',
      }
    },
    config: extractProviderConfig,
  },
  updateRemainingDealersEE: {
    region: Region.eu_north_1,
    function: async (job: JobInstance) => updateExistingDealers(job, Iso3166Alpha2.EE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountEE',
      }
    },
    config: extractProviderConfig,
  },
  extractInventoryCountEE: {
    region: Region.eu_north_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.EE),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.EE, config, runtime),
    config: extractProviderConfig,
  },
}

export default provider
