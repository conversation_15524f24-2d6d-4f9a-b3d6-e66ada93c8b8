import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config, snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import {
  extractData,
  extractDataFastLane,
  extractTowns,
  scheduleExtract,
  scheduleTowns,
} from '../../../lib/cars/nettiauto'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractTownsFI: {
    region: Region.us_east_1,
    function: extractTowns,
    schedule: scheduleTowns,
    config: {
      concurrency: 10,
      constrains: {
        countries: ['US'],
      },
    },
  },
  extractInventoryFastlaneFI: {
    cron: '0 1 * * THU',
    region: Region.us_east_1,
    function: extractDataFastLane,
    schedule: scheduleExtract,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryFI',
      }
    },
    config: {
      concurrency: 10,
      constrains: {
        countries: ['US'],
      },
    },
  },
  extractInventoryFI: {
    region: Region.us_east_1,
    function: extractData,
    schedule: scheduleExtract,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountFI',
      }
    },
    config: {
      concurrency: 10,
      constrains: {
        countries: ['US'],
      },
    },
  },
  extractInventoryCountFI: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.FI)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.FI, config, runtime),
    config,
  },
}

export default provider
