import { J<PERSON><PERSON> } from 'jsdom'

import {
  AuctionsDealerType,
  VehicleType,
  DataType,
  ProjectType,
  Region,
  ResultType,
} from '@datagatherers/datagatherers'
import { config, chunks, getProjectProviderDataType, randomWait, wait, snapshot } from '@datagatherers/worker-utils'

import { iso, requestHeaders, stockTypeLib, weekStart } from './constants'
import cars from '../../../lib/cars'

import type { BiltorvetListingData } from './types'
import type {
  CarsItem,
  DealerType,
  ControllerRuntime,
  Iso3166Alpha2,
  JobDefinition,
  JobInstance,
  Provider,
} from '@datagatherers/datagatherers'

const provider: Provider = {
  extractListingsDK: {
    cron: '0 2 * * WED',
    region: Region.eu_west_1,
    function: extractListings,
    schedule: scheduleListings,
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDealersDK',
      }
    },
    config,
  },
  extractDealersDK: {
    region: Region.eu_west_1,
    function: extractDealers,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return scheduleDealers(job, runtime, iso)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'updateRemainingDealersDK',
      }
    },
    config,
  },
  updateRemainingDealersDK: {
    region: Region.eu_west_1,
    function: async (job: JobInstance) => {
      return updateExistingDealers(job, iso)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountDK',
      }
    },
    config: {
      concurrency: 1,
    },
  },
  extractInventoryCountDK: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, iso)
    },
    snapshot: async (config, runtime) => snapshot(iso, config, runtime),
    config,
  },
}

export default provider

async function scheduleListings(job: JobDefinition, runtime: ControllerRuntime): Promise<JobDefinition[]> {
  const jobs: JobDefinition[] = []
  const jobData: {
    pageNum: number
    stockType: string
    hrefTag: string
  }[] = []
  for (const stockType of Object.keys(stockTypeLib)) {
    for (const productTypeId of stockTypeLib[stockType].productTypeId) {
      const searchRequestJson = {
        searchRequest: [
          { type: 'ProductTypeId', value: productTypeId },
          { type: 'ConditionTypeId', value: stockTypeLib[stockType].conditionTypeId },
        ],
      }
      const maxPagination = Math.ceil(
        (await runtime
          .got('https://www.biltorvet.dk/Api/Search/Count', {
            method: 'POST',
            json: searchRequestJson,
            headers: requestHeaders,
          })
          .json<number>()) / 24,
      )

      if (!maxPagination) continue
      const hrefTag = await runtime
        .got('https://www.biltorvet.dk/Api/Search/Create', {
          method: 'POST',
          json: searchRequestJson,
          headers: requestHeaders,
        })
        .json<string>()

      if (!hrefTag) continue
      for (let index = 1; index <= maxPagination; index++) {
        jobData.push({ pageNum: index, stockType, hrefTag: hrefTag.replace(/.*\//, '') })
      }
    }
  }
  for (const pageData of [...chunks(jobData, 25)]) {
    jobs.push({
      ...job,
      data: {
        ...job.data,
        pageData,
      },
    })
  }

  return jobs
}

async function extractListings(job: JobInstance) {
  let appendData: BiltorvetListingData[] = []

  for (const page of job.data.pageData) {
    await randomWait(800, 1200)

    const data = await job.runtime
      .got('https://www.biltorvet.dk/Api/Search/Page', {
        method: 'POST',
        headers: requestHeaders,
        json: { pageNumber: page.pageNum, searchOrigin: 1, searchValue: page.hrefTag, sort: 'PriceDesc' },
      })
      .json<BiltorvetListingData[]>()
      .then((items) => {
        if (!items.length) return []
        return [
          ...items.map((item) => {
            return { ...item, stockType: page.stockType }
          }),
        ]
      })
      .catch((error) => {
        job.addErrorToLog(error)
        return []
      })

    if (!data?.length) {
      break
    }

    appendData = [...appendData, ...data]
  }

  const resultsInventory = appendData.flatMap((item) => {
    if (!item?.id || item.isLeasingPrice) return []

    const price = item.priceText ? Number(item.priceText.replace(/\D/g, '')) : 0
    const data = {
      project: ProjectType.cars,
      itemId: `listing_${iso}_${job.project}_${item.id}`,
      iso,
      dataType: DataType.inventory,
      url: `https://www.biltorvet.dk${item.url}`,
      inventoryId: `${item.id}`,
      dealerType: item.isPrivate ? AuctionsDealerType.private : AuctionsDealerType.dealer,
      stockType: item.stockType,
      vehicleType: VehicleType.car,
      // data: item,
      ...(price && { price }),
      ...(price && { currency: 'DKK' }),
    }

    return {
      type: ResultType.item,
      data,
    }
  })

  return [...resultsInventory]
}

async function scheduleDealers(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const jobs: JobDefinition[] = []
  const collection = runtime.collection('item')

  const items = await collection
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
        iso,
        updatedAt: { $gte: weekStart },
        dealerId: { $exists: false },
      },
      {
        projection: {
          _id: 0,
          inventoryId: 1,
          itemId: 1,
          dealerType: 1,
        },
      },
    )
    .toArray()

  for (const listings of [...chunks(items, 50)]) {
    jobs.push({
      ...job,
      data: {
        ...job.data,
        listings,
      },
    })
  }

  return jobs
}

async function extractDealers(job: JobInstance) {
  const appendData: Array<{ itemId: string; dealerId: string; dealerName: string; dealerType: string }> = []
  const { document } = new JSDOM().window

  for (const listing of job.data.listings) {
    await wait(1000)
    const data = await job.runtime
      .got(`https://www.biltorvet.dk/bil/-/-/-/${listing.inventoryId}`, {
        method: 'GET',
      })
      .text()
      .then((html) => {
        document.body.innerHTML = html
        if (listing.dealerType === AuctionsDealerType.private) {
          const dealerId = document.querySelector('a[class^="contact__phone--anchor"]')?.getAttribute('href')
          if (!dealerId) {
            return []
          }
          appendData.push({
            itemId: listing.itemId,
            dealerId,
            dealerName: AuctionsDealerType.private,
            dealerType: listing.dealerType,
          })
          return []
        }
        const dealerId = document
          .querySelector('a[class^="contact-general-info__phone--anchor"]')
          ?.getAttribute('href')
          ?.replace('tel:', '')
          .trim()
        if (!dealerId) {
          return []
        }

        const dealerName = document
          .querySelector('.contact__container')
          .querySelector('.contact__text')
          .firstChild.nextSibling.textContent.trim()

        appendData.push({ itemId: listing.itemId, dealerId, dealerName, dealerType: listing.dealerType })
      })
      .catch((error) => {
        job.addErrorToLog(error)
      })

    if (!data) {
      continue
    }
  }

  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType; dealerName: string }> = new Map()

  const listingData = appendData.flatMap((item) => {
    if (!item.dealerId) return []
    if (item.dealerId) {
      if (!dealersMap.has(item.dealerId)) {
        dealersMap.set(item.dealerId, {
          dealerId: item.dealerId,
          dealerType: item.dealerType as DealerType,
          dealerName: item.dealerName,
        })
      }
    }
    const data = {
      itemId: item.itemId,
      iso,
      dealerId: item.dealerId,
    }

    return {
      type: ResultType.item,
      data,
    }
  })

  const resultsDealers = [...dealersMap.values()].map((item) => {
    const data = {
      project: ProjectType.cars,
      itemId: `dealer_${iso}_${job.project}_${item.dealerId}`,
      iso,
      dataType: DataType.dealers,
      dealerId: item.dealerId,
      dealerType: item.dealerType,
      isActive: true,
      isPaying: true,
      data: {
        dealerId: item.dealerId,
        dealerName: item.dealerName,
      },
    }

    return {
      type: ResultType.item,
      data,
    }
  })

  return [...resultsDealers, ...listingData]
}

async function updateExistingDealers(job: JobInstance, iso: Iso3166Alpha2) {
  if (!job.runtime.skipStoreData) {
    const dealerIdsDB = await job.runtime
      .collection<Pick<CarsItem, 'dealerId'>>(ResultType.item)
      .find(
        {
          projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
          iso,
          updatedAt: { $gte: weekStart },
          dealerId: { $exists: true },
        },
        {
          projection: {
            _id: 0,
            dealerId: 1,
          },
        },
      )
      .toArray()

    if (!dealerIdsDB?.length) {
      return []
    }

    const dealerIds = [
      ...new Set(
        dealerIdsDB.map((item) => {
          return item.dealerId
        }),
      ),
    ]

    let updatedDealerIds: string[] = []
    const updatedDealerIdsDB = await job.runtime
      .collection('item')
      .find(
        {
          projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
          iso,
          updatedAt: { $gte: weekStart },
          dealerId: { $exists: true },
        },
        {
          projection: {
            _id: 0,
            dealerId: 1,
          },
        },
      )
      .toArray()

    if (!updatedDealerIdsDB?.length) {
      return []
    }

    updatedDealerIds = updatedDealerIdsDB.map((item) => {
      return item.dealerId
    })

    const dealersToUpdate = dealerIds.filter((dealerId) => dealerId && updatedDealerIds.indexOf(dealerId) === -1)

    await job.runtime.collection('item').bulkWrite(
      [
        {
          updateMany: {
            filter: {
              projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
              iso,
              updatedAt: { $lt: weekStart },
              dealerId: { $in: dealersToUpdate },
            },
            update: { $set: { updatedAt: new Date() } },
          },
        },
      ],
      { writeConcern: { w: 'majority' } },
    )
  }
  return []
}
