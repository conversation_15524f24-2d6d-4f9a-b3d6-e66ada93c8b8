import { Region, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cars from '../../../lib/cars'
import autowereld from '../../../lib/cars/autowereld'

import type { Provider, JobDefinition } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractNL: {
    cron: '0 15 * * THU',
    region: Region.eu_west_1,
    function: autowereld.extractInventory,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return autowereld.scheduleInventory(job, runtime, Iso3166Alpha2.NL)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDetailsNL',
      }
    },
    config: {
      concurrency: 15,
    },
  },
  extractDetailsNL: {
    region: Region.eu_west_1,
    function: autowereld.extractDetails,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return autowereld.scheduleDetails(job, runtime, Iso3166Alpha2.NL)
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractInventoryCountNL',
      }
    },
    config: {
      concurrency: 15,
    },
  },
  extractInventoryCountNL: {
    region: Region.eu_west_1,
    function: cars.extractInventoryCount,
    schedule: async (job, runtime): Promise<JobDefinition[]> => {
      return cars.scheduleInventoryForDealers(job, runtime, Iso3166Alpha2.NL)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.NL, config, runtime),
    config: {
      concurrency: 10,
    },
  },
}

export default provider
