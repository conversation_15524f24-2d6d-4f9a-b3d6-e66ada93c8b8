import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import cazoo from '../../../lib/cars/cazoo'

import type { JobInstance, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractGB: {
    // cron: '0 1 * * THU',
    region: Region.eu_west_1,
    function: async (job: JobInstance) => {
      return cazoo.extract(job, Iso3166Alpha2.GB)
    },
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.GB, config, runtime),
    config: {
      returnCount: true,
    },
  },
}

export default provider
