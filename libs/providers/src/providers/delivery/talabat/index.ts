import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { config } from '@datagatherers/worker-utils'

import { scheduleExtract, extract, snapshotTalabat } from '../../../lib/delivery/talabat'

import type { ControllerRuntime, JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractAE: {
    cron: '0 8 * * WED', // Every Monday at 8 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.AE),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.AE, config, runtime),
    config,
  },
  extractJO: {
    cron: '0 10 * * TUE', // Every Tuesday at 10 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.JO),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.JO, config, runtime),
    config,
  },
  extractBH: {
    cron: '0 9 * * TUE', // Every Tuesday at 9 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.BH),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.BH, config, runtime),
    config,
  },
  extractEG: {
    cron: '0 9 * * TUE', // Every Tuesday at 9 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.EG),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.EG, config, runtime),
    config,
  },
  extractKW: {
    cron: '0 10 * * MON', // Every Monday at 10 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.KW),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.KW, config, runtime),
    config,
  },
  extractOM: {
    cron: '0 9 * * MON', // Every Monday at 9 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.OM),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.OM, config, runtime),
    config,
  },
  extractQA: {
    cron: '0 9 * * MON', // Every Monday at 9 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.QA),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.QA, config, runtime),
    config,
  },
  extractSA: {
    cron: '0 9 * * TUE', // Every Tuesday at 09:00 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.SA),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.SA, config, runtime),
    config: {
      ...config,
      constrains: { countries: [Iso3166Alpha2.SA] },
    },
  },
  extractIQ: {
    cron: '0 9 * * WED', // Every Wednesday at 09:00 GMT
    region: Region.me_central_1,
    function: extract,
    schedule: async (job: JobDefinition, runtime: ControllerRuntime) => scheduleExtract(job, runtime, Iso3166Alpha2.IQ),
    snapshot: async (config: JobDefinition, runtime: ControllerRuntime) =>
      snapshotTalabat(Iso3166Alpha2.IQ, config, runtime),
    config,
  },
}

export default provider
