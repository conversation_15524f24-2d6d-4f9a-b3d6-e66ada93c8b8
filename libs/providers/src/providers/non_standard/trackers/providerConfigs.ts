import { ProjectType, Region, Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { trackerConfig as config } from '@datagatherers/worker-utils'

import {
  tracker,
  billdotcomPricingUS,
  fortnoxPricingSE,
  capcitoPricingSE,
  trademePricingNZ,
  wiseCurrenciesWW,
  finn_noPricingNO,
  blocketVehiclesPricingSE,
  blocketJobsPricingSE,
  oikotiePricingFI,
  leboncoinPricingFR,
  homesSearchRankingUS,
  bankOfAmericaPageStateUS,
  hemnetPageStateSE,
  dataAiTrackerConfigWW,
  billdotcomTOS,
  hemnetPricingSE,
  zingCurrenciesWW,
  similarWebTrackerConfigWW,
  swishSE,
  revolutCurrenciesWW,
  finn_noPricingJobsNO,
  fortnoxFaqSE,
  fortnoxNewsSE,
  leboncoinProPricingFR,
  olxPricingBR,
  marktplaatsProPricingNL,
  marktplaatsLoosePricingNL,
  marktplaatsSubscriptionPricingNL,
  autotraderPricingGB,
  government_sePageStateSE,
  fortnoxNyheterSE,
  hogiaOpenbusinessPricingSE,
  finn_noPricingRealEstatesNO,
  finn_noPricingCarsNO,
  mobile_dePageStateDE,
  ebkPageStateDE,
  marktplaatsPageStateNL,
  leboncoinPageStateFR,
  wisePageWW,
  blocketPrivatePricingSE,
  blocketProfessionalPricingSE,
  ebkRealestatesPricingDE,
  automobilePricingIT,
  bilinfoPricingDK,
  offertaPackagePricingSE,
  xeroPageStateGB,
  xeroPageStateAU,
  plandayPricingWW,
  billdotcomProductUpdatesUS,
  xeroNewsWW,
  karnovPricingDK,
  wiseCurrenciesNewWW,
  kivraPageStateSE,
  hemnetStatistikLauncherSE,
  booliStatistikLauncherSE,
  wiseNewsWW,
  booliHousingNewsWW,
  oikotieNewsFI,
  mobileDePricing,
} from '.'
import { changeWasTriggered } from './trackerUtils'
import { schedulePricingCombinations, extractPricing } from '../../../lib/classifieds/finn.no'

import type { ControllerConfig, ControllerRuntime, JobDefinition, JobInstance } from '@datagatherers/datagatherers'

const daily_1 = '0 7 * * *' // daily at 7h00
const daily_2 = '10 7 * * *' // daily at 7h00
const _3Daily_1 = '0 8,12,16 * * *' // three times per day at 7h00, 12h00, 16h00
const _3Daily_2 = '0 8,12,16 * * *' // three times per day at 7h00, 12h00, 16h00
const _2Daily_1 = '20 7,11 * * *' // twice per day at 7h00 and 11h00
const _2Daily_2 = '30 7,11 * * *' // twice per day at 7h00 and 11h00
const friday_1 = '0 11 * * FRI' // fridays at 11h00
const friday_2 = '10 11 * * FRI' // fridays at 11h00
const wednesday_8am = '0 8 * * WED' // wednesdays at 8h00 GMT
const monthly_1 = '30 8 1 * *' // first of the month at 7h00
const monthly_2 = '20 8 1 * *' // first of the month at 7h00
const currencies_1 = '30 0 * * *' // 00h30 daily
const currencies_2 = '40 0 * * *' // 00h30 daily
const externalIntegrationAvailability_1 = '5 */4 * * *' // Every 4 hours
const externalIntegrationAvailability_2 = '15 */4 * * *' // Every 4 hours
// const hourly = '0 * * * *' // Every hour
// const every10Minutes = '*/10 * 1 * *' // every 10 minutes
// const every10MinutesFirstDayOfMonth = '*/10 * 1 * *' // every 10 minutes on the first of every month
const every10MinutesFirstThreeDaysOfMonth = '*/10 * 1,2,3 * *' // every 10 minutes on the first of every month
const every15MinutesFirstThreeDaysOfMonth = '*/15 * 1,2,3 * *' // every 10

// ./dg worker accounting fortnox trackerFortnoxPricingSE
export const trackerFortnoxPricingSE = {
  region: Region.eu_north_1,
  //cron: _3Daily_1,
  cron: '0 * * * *',
  function: (job: JobInstance) => tracker(job, fortnoxPricingSE),
  constrains: { countries: ['SE'] },
  config,
}

// ./dg worker accounting bill.com trackerBilldotcomPricingUS
export const trackerBilldotcomPricingUS = {
  region: Region.us_east_1,
  cron: _3Daily_1,
  function: (job: JobInstance) => tracker(job, billdotcomPricingUS),
  config,
}
// ./dg worker accounting capcito trackerCapcitoPricingSE
export const trackerCapcitoPricingSE = {
  region: Region.eu_north_1,
  cron: _3Daily_1,
  function: (job: JobInstance) => tracker(job, capcitoPricingSE),
  config: {
    ...config,
    constrains: { countries: ['SE'] as Iso3166Alpha2[] },
    browser: {
      userAgent: {
        deviceCategory: 'desktop',
      },
      defaultTimeout: 60000,
    },
  },
}
// ./dg worker marketplaces trademe trackerTrademePricingNZ
export const trackerTrademePricingNZ = {
  region: Region.eu_west_1,
  cron: _3Daily_1,
  function: (job: JobInstance) => tracker(job, trademePricingNZ),
  config,
}
// ./dg worker realestates homes trackerHomesSearchRankingUS
export const trackerHomesSearchRankingUS = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, homesSearchRankingUS),
  cron: monthly_1,
  config,
}
// ./dg worker payments wise trackerWiseCurrenciesWW
export const trackerWiseCurrenciesWW = {
  region: Region.eu_west_1,
  cron: currencies_1,
  function: (job: JobInstance) => tracker(job, wiseCurrenciesWW),
  config: {
    ...config,
    constrains: { countries: ['GB'] as Iso3166Alpha2[] },
  },
}

// ./dg worker payments wise trackerWiseCurrenciesWW
export const trackerWiseCurrenciesNewWW = {
  region: Region.eu_west_2,
  cron: currencies_1,
  function: (job: JobInstance) => tracker(job, wiseCurrenciesNewWW),
  config: {
    ...config,
    constrains: { countries: ['GB'] as Iso3166Alpha2[] },
  },
}

// ./dg worker realestates finn.no trackerFinn_noPricingNO
export const trackerFinn_noPricingNO = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, finn_noPricingNO),
  cron: friday_1,
  config,
}

// ./dg worker marketplaces blocket trackerBlocketJobsPricingSE
export const trackerBlocketJobsPricingSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, blocketJobsPricingSE),
  // cron: friday_1, // disabled
  config,
}

// ./dg worker marketplaces blocket trackerBlocketVehiclesPricingSE
export const trackerBlocketVehiclesPricingSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, blocketVehiclesPricingSE),
  config,
  cron: friday_1,
}

// ./dg worker jobs oikotie trackerOikotiePricingFI
export const trackerOikotiePricingFI = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, oikotiePricingFI),
  cron: friday_1,
  config,
}

// ./dg worker accounting bank_of_america trackerBankOfAmericaPageStateUS
export const trackerBankOfAmericaPageStateUS = {
  region: Region.us_east_1,
  cron: _3Daily_1,
  function: (job: JobInstance) => tracker(job, bankOfAmericaPageStateUS),
  config,
}

// ./dg worker realestates hemnet trackerHemnetPageStateSE
export const trackerHemnetPageStateSE = {
  region: Region.eu_north_1,
  cron: _3Daily_1,
  function: (job: JobInstance) => tracker(job, hemnetPageStateSE),
  config,
}

// ./dg worker annie dataAi dataAiTrackerConfigWW
export const trackerDataAiDataAvailabilityWW = {
  region: Region.us_east_1,
  cron: externalIntegrationAvailability_1,
  function: (job: JobInstance) => tracker(job, dataAiTrackerConfigWW),
  config,
}

// ./dg worker similarweb similarweb trackerSimilarWebAvailabilityWW
export const trackerSimilarWebDataAvailabilityWW = {
  region: Region.us_east_1,
  cron: externalIntegrationAvailability_2,
  function: (job: JobInstance) => tracker(job, similarWebTrackerConfigWW),
  config,
}

// ./dg worker accounting bill.com trackerBilldotcomTOS
export const trackerBilldotcomTOS = {
  region: Region.us_east_1,
  cron: _3Daily_1,
  function: (job: JobInstance) => tracker(job, billdotcomTOS),
  config,
}

// ./dg worker realestates hemnet trackerHemnetPricingSE
export const trackerHemnetPricingSE = {
  region: Region.eu_west_1,
  cron: every15MinutesFirstThreeDaysOfMonth,
  function: (job: JobInstance) => tracker(job, hemnetPricingSE),
  config,
}

// ./dg worker payments zing trackerZingCurrenciesWW
export const trackerZingCurrenciesWW = {
  region: Region.eu_west_2,
  // cron: currencies_2,
  function: (job: JobInstance) => tracker(job, zingCurrenciesWW),
  config,
}

// ./dg worker payments swish trackerSwishPageStateSE
export const trackerSwishPageStateSE = {
  region: Region.eu_west_1,
  // cron: _3Daily,
  function: (job: JobInstance) => tracker(job, swishSE),
  config,
}

// ./dg worker cars autotrader trackerAutotraderPricingGB
export const trackerAutotraderPricingGB = {
  region: Region.eu_west_1,
  cron: friday_1,
  function: (job: JobInstance) => tracker(job, autotraderPricingGB),
  config: {
    ...config,
    constrains: {
      countries: ['IE', 'GB', 'FR', 'ES'] as Iso3166Alpha2[],
    },
  },
}

// This tracker depends on a pre-crawl (from its codes function) embedded in finn.no jobs, command below
// ./dg worker jobs finn.no codesTrackerFinn_noPricingJobsNO
export const codesTrackerFinn_noPricingJobsNO = {
  cron: monthly_2,
  region: Region.eu_north_1,
  function: extractPricing,
  schedule: (job: JobDefinition, runtime: ControllerRuntime) => schedulePricingCombinations(job, runtime),
  scheduleAfterFinished: () => {
    return {
      project: ProjectType.jobs,
      provider: 'finn.no',
      type: 'trackerFinn_noPricingJobsNO',
    }
  },
  config: {
    ...config,
    concurrency: 1,
  },
}
// ./dg worker jobs finn.no trackerFinn_noPricingJobsNO
export const trackerFinn_noPricingJobsNO = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, finn_noPricingJobsNO),
  config,
}

// ./dg worker realestates finn.no trackerFinn_noPricingRealEstatesNO
export const trackerFinn_noPricingRealEstatesNO = {
  region: Region.eu_west_1,
  cron: friday_1,
  function: (job: JobInstance) => tracker(job, finn_noPricingRealEstatesNO),
  config,
}

// ./dg worker realestates finn.no trackerFinn_noPricingCarsNO
export const trackerFinn_noPricingCarsNO = {
  region: Region.eu_west_1,
  cron: daily_1,
  function: (job: JobInstance) => tracker(job, finn_noPricingCarsNO),
  config,
}

// ./dg worker accounting fortnox trackerFortnoxFaqSE
export const trackerFortnoxFaqSE = {
  region: Region.eu_north_1,
  // cron: _2Daily,
  function: (job: JobInstance) => tracker(job, fortnoxFaqSE),
  config,
}

// ./dg worker accounting fortnox trackerFortnoxNyheterSE
export const trackerFortnoxNyheterSE = {
  region: Region.eu_north_1,
  cron: _2Daily_1,
  function: (job: JobInstance) => tracker(job, fortnoxNyheterSE),
  config,
}

// ./dg worker payments revolut trackerRevolutCurrenciesWW
export const trackerRevolutCurrenciesWW = {
  region: Region.eu_west_1,
  cron: currencies_2,
  function: (job: JobInstance) => tracker(job, revolutCurrenciesWW),
  config,
}

// ./dg worker accounting fortnox trackerFortnoxNewsSE
export const trackerFortnoxNewsSE = {
  region: Region.eu_north_1,
  cron: _2Daily_2,
  function: (job: JobInstance) => tracker(job, fortnoxNewsSE),
  config,
}

// ./dg worker non_standard government_se trackerGovernment_sePageStateSE
export const trackerGovernment_sePageStateSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, government_sePageStateSE),
  // cron: hourly,
  config,
}

// ./dg worker marketplaces olx trackerOlxPricingBR
export const trackerOlxPricingBR = {
  region: Region.us_east_1,
  function: (job: JobInstance) => tracker(job, olxPricingBR),
  // cron: monthly_2,
  config,
}

// ./dg worker marketplaces marktplaats trackerMarktplaatsProPricingNL
export const trackerMarktplaatsProPricingNL = {
  region: Region.eu_central_1,
  function: (job: JobInstance) => tracker(job, marktplaatsProPricingNL),
  cron: _3Daily_2,
  config,
}

// ./dg worker marketplaces marktplaats trackerMarktplaatsLoosePricingNL
export const trackerMarktplaatsLoosePricingNL = {
  region: Region.eu_central_1,
  function: (job: JobInstance) => tracker(job, marktplaatsLoosePricingNL),
  cron: _3Daily_2,
  config,
}

// ./dg worker marketplaces marktplaats trackerMarktplaatsSubscriptionPricingNL
export const trackerMarktplaatsSubscriptionPricingNL = {
  region: Region.eu_central_1,
  function: (job: JobInstance) => tracker(job, marktplaatsSubscriptionPricingNL),
  cron: _3Daily_2,
  config,
}

// ./dg worker accounting fortnox trackerFortnoxPricingSE
export const trackerHogiaOpenbusinessPricingSE = {
  region: Region.eu_north_1,
  cron: _3Daily_2,
  function: (job: JobInstance) => tracker(job, hogiaOpenbusinessPricingSE),
  config,
}

// ./dg worker cars automobile trackerAutomobilePricingIT
export const trackerAutomobilePricingIT = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, automobilePricingIT),
  cron: friday_2,
  config,
}

// ./dg worker cars mobile.de trackerMobile_dePageStateDE
export const trackerMobile_dePageStateDE = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, mobile_dePageStateDE),
  cron: daily_1,
  config,
}

// ./dg worker marketplaces marktplaats trackerMarktplaatsPageStateNL
export const trackerMarktplaatsPageStateNL = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, marktplaatsPageStateNL),
  cron: daily_1,
  config,
}

// ./dg worker marketplaces leboncoin trackerLeboncoinPageStateFR
export const trackerLeboncoinPageStateFR = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, leboncoinPageStateFR),
  cron: daily_1,
  config,
}

// ./dg worker accounting xero trackerXeroPageStateGB
export const trackerXeroPageStateGB = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, xeroPageStateGB),
  cron: daily_2,
  config,
}

// ./dg worker accounting xero trackerXeroPageStateAU
export const trackerXeroPageStateAU = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, xeroPageStateAU),
  cron: daily_2,
  config,
}

// ./dg worker accounting bill.com trackerBilldotcomProductUpdatesUS
export const trackerBilldotcomProductUpdatesUS = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, billdotcomProductUpdatesUS),
  cron: daily_2,
  config,
}

// ./dg worker accounting kivra trackerKivraPageStateSE
export const trackerKivraPageStateSE = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, kivraPageStateSE),
  cron: daily_1,
  config,
}

// LOCAL

// ./dg worker marketplaces leboncoin trackerLeboncoinPricingFR
export const trackerLeboncoinPricingFR = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, leboncoinPricingFR),
  config,
}

// ./dg worker marketplaces leboncoin trackerLeboncoinProPricingFR
export const trackerLeboncoinProPricingFR = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, leboncoinProPricingFR),
  config,
}

// ./dg worker marketplaces ebk trackerEbkPageStateDE
export const trackerEbkPageStateDE = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, ebkPageStateDE),
  config,
}

// ./dg worker payments wise trackerWisePageWW
export const trackerWisePageWW = {
  region: Region.eu_west_1,
  cron: _3Daily_2,
  function: (job: JobInstance) => tracker(job, wisePageWW),
  config: {
    ...config,
    constrains: { countries: ['GB', 'US', 'DE'] as Iso3166Alpha2[] },
  },
}

// ./dg worker marketplaces blocket blocketProfessionalPricingSE
export const trackerBlocketProfessionalPricingSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, blocketProfessionalPricingSE),
  config,
  cron: friday_2,
}

// ./dg worker marketplaces blocket blocketPrivatePricingSE
export const trackerBlocketPrivatePricingSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, blocketPrivatePricingSE),
  config,
  cron: friday_2,
}

// ./dg worker realestates ebk trackerEbkRealestatesPricingSE
export const trackerEbkRealestatesPricingSE = {
  region: Region.eu_central_1,
  function: (job: JobInstance) => tracker(job, ebkRealestatesPricingDE),
  config,
  cron: friday_2,
}

// ./dg worker realestates bilinfo trackerBilinfoCarsPricingSE
export const trackerBilinfoCarsPricingSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, bilinfoPricingDK),
  config: {
    ...config,
    //constrains: { countries: [Iso3166Alpha2.DK] },
  },
  cron: friday_2,
}

// ./dg worker marketplaces offerta trackerOffertaPackagePricingSE
export const trackerOffertaPackagePricingSE = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, offertaPackagePricingSE),
  config,
  cron: _3Daily_2,
}

// ./dg worker accounting planday trackerPlandayPricingWW
export const trackerPlandayPricingWW = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, plandayPricingWW),
  config: {
    ...config,
    constrains: { countries: [Iso3166Alpha2.GB] },
  },
  cron: _3Daily_2,
}

// ./dg worker accounting xero trackerXeroNewsWW
export const trackerXeroNewsWW = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, xeroNewsWW),
  config: {
    ...config,
    blockedResources: ['ipinfo.io'],
  },
  cron: friday_2,
}

// ./dg worker databases karnov trackerKranovPricingDK
export const trackerKarnovPricingDK = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, karnovPricingDK),
  config,
  cron: _3Daily_2,
}

// ./dg worker realestates hemnet trackerHemnetStatistikLauncherSE
export const trackerHemnetStatistikLauncherSE = {
  cron: every10MinutesFirstThreeDaysOfMonth,
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, hemnetStatistikLauncherSE),
  scheduleAfterFinished: async (cConfig: ControllerConfig, runtime: ControllerRuntime) => {
    const wasTriggered = await changeWasTriggered(
      runtime,
      cConfig.project,
      cConfig.provider,
      Iso3166Alpha2.SE,
      TrackerType.LAUNCHER,
    )
    if (wasTriggered) {
      return {
        project: cConfig.project,
        provider: cConfig.provider,
        type: 'extractStatistikSE',
      }
    }
    return {
      project: ProjectType.non_standard,
      provider: 'test',
      type: 'exec',
    }
  },
  config: {
    ...config,
    concurrency: 1,
  },
}

// ./dg worker payments wise trackerWiseNewsWW
export const trackerWiseNewsWW = {
  region: Region.us_east_1,
  function: (job: JobInstance) => tracker(job, wiseNewsWW),
  config: {
    ...config,
    //blockedResources: ['ipinfo.io'],
  },
  cron: friday_1,
}

// ./dg worker realestates booli trackerBooliHousingNewsWW
export const trackerBooliHousingNewsWW = {
  region: Region.us_east_1,
  function: (job: JobInstance) => tracker(job, booliHousingNewsWW),
  config,
  cron: wednesday_8am,
}

// ./dg worker realestates booli_statistics trackerBooliStatistikLauncherSE
export const trackerBooliStatistikLauncherSE = {
  cron: daily_1,
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, booliStatistikLauncherSE),
  scheduleAfterFinished: async (cConfig: ControllerConfig, runtime: ControllerRuntime) => {
    const wasTriggered = await changeWasTriggered(
      runtime,
      cConfig.project,
      cConfig.provider,
      Iso3166Alpha2.SE,
      TrackerType.LAUNCHER,
    )
    if (wasTriggered) {
      return {
        project: cConfig.project,
        provider: cConfig.provider,
        type: 'extractStatisticsSE',
      }
    }
    return {
      project: ProjectType.non_standard,
      provider: 'test',
      type: 'exec',
    }
  },
  config: {
    ...config,
    concurrency: 1,
  },
}

// ./dg worker jobs oikotie trackerOikotieNewsFI
export const trackerOikotieNewsFI = {
  region: Region.eu_north_1,
  function: (job: JobInstance) => tracker(job, oikotieNewsFI),
  cron: daily_1,
  config,
}

// ./dg worker cars mobile.de trackerMobile_dePricingDE
export const trackerMobile_dePricingDE = {
  region: Region.eu_west_1,
  function: (job: JobInstance) => tracker(job, mobileDePricing),
  cron: daily_1,
  config,
}
