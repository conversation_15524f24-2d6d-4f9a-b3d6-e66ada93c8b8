import { <PERSON><PERSON><PERSON> } from 'jsdom'
import UserAgent from 'user-agents'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const finn_noPricingCarsNO: TrackerConfig = {
  iso: Iso3166Alpha2.NO,
  trackerType: TrackerType.PRICING_VEHICLES,
  extractionScript: finnPricingScript,
  URLs: [new URL('https://www.finn.no/bedriftskunde/motor/prismodell-bilannonser')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function finnPricingScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}

  await job.runtime
    .got(finn_noPricingCarsNO.URLs[0].href, {
      method: 'GET',
      headers: {
        'User-Agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
      },
    })
    .text()
    .then((res) => {
      document.body.innerHTML = res
      const scriptModules = Array.from(document.querySelectorAll('script[type="module"]'))
        ?.filter((script) => script?.textContent)
        .map((script) => script.textContent?.trim())
      for (const scriptText of scriptModules) {
        const packages = scriptText.match(/"\w+-price"/g)?.map((str) => str.replace('-price', '').replace(/"/g, ''))
        const fromToStrings = scriptText
          ?.split('const')
          ?.flatMap((str) => {
            if (!str?.match(/\[{from:/i)) return []
            return str
              .split(/[a-zA-Z]=/)
              .filter((str) => str?.trim()?.length)
              .map((str) => str.substring(0, str.lastIndexOf(']') + 1).trim())
          })
          ?.filter((str) => str?.length)

        if (packages?.length === fromToStrings?.length) {
          for (let idx = 0; idx < packages.length; idx++) {
            const priceRangeArr = fromToStrings[idx]
              ?.split(';')?.[0]
              ?.replace('[', '')
              .replace(']', '')
              .trim()
              .split('},{')
              .map((str) => {
                if (str.startsWith('{')) {
                  str = str + '}'
                } else if (str.endsWith('}')) {
                  str = '{' + str
                } else {
                  str = '{' + str + '}'
                }

                return JSON.parse(
                  str.replaceAll('from', '"from"').replaceAll('to', '"to"').replaceAll('price', '"price"'),
                )
              })
            resultsObj[packages[idx]] = JSON.stringify(priceRangeArr)
          }
        }
      }
    })

  return resultsObj
}
