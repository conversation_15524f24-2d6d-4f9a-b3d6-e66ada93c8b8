import { Iso3166Alpha2, ResultType, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { PricingResponse } from '../../../../../lib/classifieds/finn.no/types'
import type { JobInstance, Town, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const finn_noPricingJobsNO: TrackerConfig = {
  iso: Iso3166Alpha2.NO,
  trackerType: TrackerType.PRICING_JOBS,
  extractionScript: finn_noJobsScript,
  URLs: [new URL('https://www.finn.no/prisoversikt')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function finn_noJobsScript(job: JobInstance) {
  const response = await job.runtime
    .collection<TownData>(ResultType.town)
    .find({ provider: 'finn.no', blacklisted: false })
    .toArray()
  if (!response?.length) throw new Error('Unable to retrieve price data from DB')
  const productArrs = response.map((doc) => doc.data.preparsed)
  let resultsObj: TrackerScriptOutput = {}
  for (const productArr of productArrs) {
    for (const product of productArr) {
      resultsObj = Object.assign(resultsObj, product)
    }
  }
  return resultsObj
}

type TownData = Omit<Town, 'data'> & {
  data: {
    raw: PricingResponse
    id: string
    preparsed: TrackerScriptOutput[]
  }
}
