import { json2csv } from 'json-2-csv'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { wait } from '@datagatherers/worker-utils'

import { jsonSnapCsvHook, writeOutFromCsvHook } from '../../csvHooks'

import type { Diff, JobInstance, TrackerConfig, TrackerMerged, TrackerScriptOutput } from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'

export const finn_noPricingRealEstatesNO: TrackerConfig = {
  iso: Iso3166Alpha2.NO,
  trackerType: TrackerType.PRICING_REALESTATES,
  extractionScript: finnPricingScript,
  URLs: [new URL('https://www.finn.no/realestate/prices')],
  pngScript: null,
  csvHooks: [customDiffsCsvHook, jsonSnapCsvHook],
}

const separator = '|'

async function finnPricingScript(job: JobInstance) {
  const resultsRaw = []
  for (const zone of zones) {
    for (const price of startingPrices) {
      await wait(2000)
      const response = await job.runtime
        .got(`${finn_noPricingRealEstatesNO.URLs[0].href}/api/2025?postNumber=${zone.postcode}&askingPrice=${price}`, {
          retry: {
            limit: 5,
            statusCodes: [403],
          },
        })
        .json<PricingResponse>()

      if (!response?.packageInfo) throw new Error('Failed to retrieve prices')

      for (const [packageSize, packageDetails] of Object.entries(response.packageInfo)) {
        resultsRaw.push({
          [`${zone.name}${separator}${packageSize.toUpperCase()}${separator}${formatPrice(price)}`]:
            packageDetails.priceRaw,
        })
      }
    }
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const packagePrice of resultsRaw) {
    resultsObj = Object.assign(resultsObj, packagePrice)
  }
  return resultsObj
}

function parseToCsv(diffs: Diff[]) {
  const parsedDiffs = diffs.map((diff) => {
    const [zone, packageSize, startingPrice] = diff.product.split('|')
    return {
      zone,
      packageSize,
      startingPrice: parseStartingPrice(startingPrice),
      price: diff.current,
    }
  })
  parsedDiffs.sort((a, b) => {
    const zoneComparison = a.zone.localeCompare(b.zone)
    if (zoneComparison !== 0) {
      return zoneComparison
    }

    const packageSizeComparison = packageSizeHierarchy[a.packageSize] - packageSizeHierarchy[b.packageSize]
    if (packageSizeComparison !== 0) {
      return packageSizeComparison
    }

    return a.startingPrice - b.startingPrice
  })
  const structuredData = parsedDiffs.reduce(
    (acc, item) => {
      acc[item.zone] ??= { zone: item.zone, packageSizes: {} }
      acc[item.zone].packageSizes[item.packageSize] ??= {}
      acc[item.zone].packageSizes[item.packageSize][item.startingPrice] = parseFloat(item.price)
      return acc
    },
    {} as Record<string, { zone: string; packageSizes: Record<string, Record<number, number>> }>,
  )
  const outputDataRaw = Object.values(structuredData).map((zoneItem) => {
    for (const [packageSize, packageValues] of Object.entries(zoneItem.packageSizes)) {
      zoneItem[packageSize] = Array.from({ length: startingPrices.length }, () => '')
      for (const [price, value] of Object.entries(packageValues)) {
        zoneItem[packageSize][startingPrices.indexOf(parseStartingPrice(price))] = value
      }
    }
    return zoneItem
  })
  const outputData = []
  for (let index = 0; index < zones.length; index++) {
    outputData.push(outputDataRaw.find((item) => item.zone === zones[index].name))
  }
  const flatData = outputData.flatMap((zoneItem) => {
    const rows: { [k: string]: string | number }[] = []
    for (const packageSize in zoneItem?.packageSizes) {
      rows.push({
        zone: zoneItem.zone,
        packageSize: packageSize,
        ...Object.fromEntries(
          startingPrices.map((price, _index) => [
            formatPriceForCsv(price),
            zoneItem.packageSizes[packageSize][price] || '',
          ]),
        ),
      })
    }
    return rows
  })
  if (!flatData?.length) {
    throw new Error('CSV parsing failed')
  }
  return flatData
}

const packageSizeHierarchy = {
  SMALL: 1,
  MEDIUM: 2,
  LARGE: 3,
}

const startingPrices = [
  1, 400000, 500000, 600000, 700000, 800000, 900000, 1000000, 1500000, 2000000, 2500000, 3000000, 3500000, 4000000,
  4500000, 5000000, 5500000, 6000000, 6500000, 7000000, 7500000, 8000000, 8500000, 9000000, 9500000, 10000000, 10500000,
  11000000, 11500000, 12000000, 12500000, 13000000, 13500000, 14000000, 14500000, 15000000, 15500000, 16000000,
  16500000, 17000000, 17500000, 18000000, 18500000, 19000000, 19500000, 20000000, 20500000, 21000000, 21500000,
  22000000, 22500000, 23000000, 23500000, 24000000, 24500000, 25000000,
]

const zones: { name: string; postcode: string }[] = [
  { name: 'Zone1', postcode: '9730' },
  { name: 'Zone2', postcode: '2985' },
  { name: 'Zone3', postcode: '1890' },
  { name: 'Zone4', postcode: '0655' },
]

function formatPrice(price: number): string {
  if (price === 1) return '1'
  if (price < 1000000) return `${price / 1000}k`
  return `${(price / 1000000).toString().replace('.', '_')}M`
}

function formatPriceForCsv(price: number): string {
  return `kr ${formatPrice(price)}`
}

function parseStartingPrice(formattedPrice: string): number {
  if (formattedPrice === 'kr 1') return 1
  if (formattedPrice.endsWith('k')) return parseFloat(formattedPrice.replace('kr ', '')) * 1000
  if (formattedPrice.endsWith('M')) return parseFloat(formattedPrice.replace('kr ', '').replace('_', '.')) * 1000000
  return parseFloat(formattedPrice.replace('kr ', ''))
}

interface PricingResponse {
  propertyInfo: {
    askingPrice: number
    location: {
      postalCode: string
      geo: string
    }
  }
  packageInfo: {
    [key: string]: {
      packageSize: string
      basePriceRaw: number
      basePriceFormatted: string
      priceRaw: number
      priceFormatted: string
    }
  }
}

async function customDiffsCsvHook(
  job: JobInstance,
  config: TrackerConfig,
  mergedToggles: TrackerMerged,
  diffs: Diff[],
  jsonSnap: TrackerScriptOutput,
  gDrive: GDriveUtil,
  currentDate: Date,
  gDriveAcc: string,
  clientEmails: string[],
) {
  const args = [job, config, mergedToggles, diffs, jsonSnap, gDrive, currentDate, gDriveAcc, clientEmails] as const

  const parsedDiffs = parseToCsv(diffs)

  const csv = json2csv(parsedDiffs, {
    emptyFieldValue: 'N/A',
  })

  await writeOutFromCsvHook(...args, csv, 'Custom Differences', 'Please review the changes below:')
}
