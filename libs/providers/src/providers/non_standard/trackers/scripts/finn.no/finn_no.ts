import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const finn_noPricingNO: TrackerConfig = {
  iso: Iso3166Alpha2.NO,
  trackerType: TrackerType.PRICING,
  extractionScript: finn_noScript,
  URLs: [new URL('https://www.finn.no/prisoversikt')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function finn_noScript(job: JobInstance) {
  return job.runtime
    .got('https://www.finn.no/prisoversikt/podium-resource/priceInfo/api/prices', {
      method: 'GET',
      searchParams: { orgId: '', market: '' },
    })
    .json<Response>()
    .then((response) => {
      if (!response?.verticals?.length) throw new Error('Failed to retrieve body from request')
      const products = []
      const verticals = response.verticals
      for (const vertical of verticals) {
        const headingLvl1 = vertical.heading
        if (!vertical?.groupings?.length) throw new Error('Groupings empty')
        for (const grouping of vertical.groupings) {
          const headingLvl2 = grouping.heading
          if (!grouping?.intervals?.length) throw new Error('Intervals empty')
          for (const interval of grouping.intervals) {
            products.push({
              [`${
                headingLvl1 + '_' + headingLvl2 + '_' + interval.lowerBoundAdCount + '-' + interval.upperBoundAdCount
              }`]: interval.price,
            })
          }
        }
      }
      let resultsObj: TrackerScriptOutput = {}
      for (const product of products) {
        resultsObj = Object.assign(resultsObj, product)
      }
      return resultsObj
    })
}

interface Response {
  isInCorporateGroup: boolean
  verticals?: VerticalsEntity[] | null
}
interface VerticalsEntity {
  heading: string
  groupings?: GroupingsEntity[] | null
}
interface GroupingsEntity {
  heading: string
  numberOfPublishedAds?: number | null
  intervals?: IntervalsEntity[] | null
}
interface IntervalsEntity {
  specificationGroupAdIntervalId: number
  lowerBoundAdCount: number
  upperBoundAdCount: number
  price: number
}
