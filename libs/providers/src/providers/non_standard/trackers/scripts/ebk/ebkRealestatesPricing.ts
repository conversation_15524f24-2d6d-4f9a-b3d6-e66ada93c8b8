import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { numberRegex } from '@datagatherers/worker-utils'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const ebkRealestatesPricingDE: TrackerConfig = {
  iso: Iso3166Alpha2.DE,
  trackerType: TrackerType.PRICING_REALESTATES,
  extractionScript: ebkRealestatesPricingScript,
  URLs: [new URL('https://themen.kleinanzeigen.de/immobilienprofis/')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function ebkRealestatesPricingScript(job: JobInstance) {
  return job.runtime
    .got(ebkRealestatesPricingDE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response

      const packages = Array.from(document.querySelectorAll('.packages-item'))
      if (!packages?.length) throw new Error('Failed to retrieve packages')
      const products = []

      for (const pkg of packages) {
        const packageName = pkg.querySelector('h3')?.textContent?.trim()

        const prices = Array.from(pkg.querySelectorAll('.prices-item'))
        if (!prices?.length) continue

        for (const price of prices) {
          let priceName = price.querySelector('.prices-limit')?.textContent?.trim()
          if (priceName) {
            const priceValue = price
              .querySelector('.prices-amount')
              ?.textContent?.replace('\\n', '')
              ?.replace('\\t', '')
              ?.trim()
              .replace(numberRegex, '')
            const priceValueSpan = price.querySelector('.prices-amount span')?.textContent

            products.push({
              [packageName + ' > ' + priceName]: `${priceValue} € ${priceValueSpan ? priceValueSpan : ''}`.trim(),
            })
          } else {
            priceName = price.textContent?.trim()
            products.push({
              [packageName + ' > ' + priceName]: 'undefined',
            })
          }
        }
      }

      let resultsObj: TrackerScriptOutput = {}
      for (const product of products) {
        resultsObj = Object.assign(resultsObj, product)
      }
      return resultsObj
    })
}
