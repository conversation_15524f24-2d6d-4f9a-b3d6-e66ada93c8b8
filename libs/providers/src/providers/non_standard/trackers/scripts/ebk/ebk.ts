import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { generateId, getJSDOMDocument } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const ebkPageStateDE: TrackerConfig = {
  iso: Iso3166Alpha2.DE,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: ebkScript,
  URLs: [new URL('https://themen.kleinanzeigen.de/medien/pressemitteilungen/?page=1')],
  pngScript: null,
  csvHooks: null,
}

async function ebkScript(job: JobInstance) {
  const datadome = process.argv.slice(-1)[0]
  let { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}
  document = await getJSDOMDocument(job.runtime, ebkPageStateDE.URLs[0].href, document, [
    { name: 'datadome', value: datadome, domain: '.kleinanzeigen.de' },
  ])
  if (!document) throw new Error('Failed to retrieve html')
  const posts = document.querySelectorAll('.post')

  posts.forEach((post) => {
    const title = post.querySelector('h2').textContent
    const url = post.querySelector('a')?.getAttribute('href')
    if (title && url) {
      const uniqueTitle = `${title.trim()}-${generateId(title + `-${url}`)}`
      resultsObj[uniqueTitle] = `https://themen.kleinanzeigen.de${url}`
    }
  })

  if (!Object.keys(resultsObj)?.length) throw new Error('No results found')
  return resultsObj
}
