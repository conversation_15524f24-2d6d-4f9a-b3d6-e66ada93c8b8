import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const bankOfAmericaPageStateUS: TrackerConfig = {
  iso: Iso3166Alpha2.US,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: bankOfAmericaScript,
  URLs: [new URL('https://www.bankofamerica.com/onlinebanking/online_business_suite_addendum.go')],
  pngScript: null,
  csvHooks: null,
}
async function bankOfAmericaScript(job: JobInstance) {
  return job.runtime
    .got(bankOfAmericaPageStateUS.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response
      const resultsObj: TrackerScriptOutput = {}
      document.querySelectorAll('.sa-main-content > .h-100').forEach((el) => {
        const siblingTextContent = []
        const topic = el.textContent?.trim()
        if (!topic) return
        let sibling = el.nextElementSibling
        while (sibling && sibling?.className !== 'h-100') {
          if (sibling.className !== 'back-to-top-link') {
            siblingTextContent.push(sibling.textContent?.trim())
          }
          sibling = sibling?.nextElementSibling
        }
        resultsObj[topic] = siblingTextContent.join(' ').trim()
      })

      return resultsObj
    })
}
