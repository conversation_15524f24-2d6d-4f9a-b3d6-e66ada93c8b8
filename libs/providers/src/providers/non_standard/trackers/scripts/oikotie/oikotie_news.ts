import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { getJSDOMDocument } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const oikotieNewsFI: TrackerConfig = {
  iso: Iso3166Alpha2.FI,
  trackerType: TrackerType.NEWS,
  extractionScript: oikotieScript,
  URLs: [new URL('https://asunnot.oikotie.fi/artikkelit/kategoria/asunto/ammattilaiselle/')],
  pngScript: null,
  csvHooks: null,
}

async function oikotieScript(job: JobInstance) {
  let { document } = new JSDOM().window
  document = await getJSDOMDocument(job.runtime, oikotieNewsFI.URLs[0].href, document)
  if (!document) throw new Error('Failed to retrieve html')
  const posts = Array.from(document.querySelectorAll('#postlist > article'))

  if (!posts?.length) throw new Error('Failed to retrieve sections')
  const products = []
  for (const post of posts) {
    const postId = post.getAttribute('id')

    if (!postId) throw new Error('Failed to retrieve post id')
    const postLink = post.querySelector('a[class^="link"]')?.getAttribute('href')

    if (!postLink) throw new Error('Failed to retrieve post id')
    products.push({ [postId]: postLink })
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const product of products) {
    resultsObj = Object.assign(resultsObj, product)
  }

  return resultsObj
}
