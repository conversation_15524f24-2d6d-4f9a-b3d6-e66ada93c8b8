import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { getJSDOMDocument, removeTabNewline } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const oikotiePricingFI: TrackerConfig = {
  iso: Iso3166Alpha2.FI,
  trackerType: TrackerType.PRICING,
  extractionScript: oikotieScript,
  URLs: [new URL('https://asunnot.oikotie.fi/hinnasto')],
  pngScript: null,
  csvHooks: null,
}

async function oikotieScript(job: JobInstance) {
  let { document } = new JSDOM().window
  document = await getJSDOMDocument(job.runtime, oikotiePricingFI.URLs[0].href, document)
  if (!document) throw new Error('Failed to retrieve html')
  const sections = Array.from(document.querySelectorAll('body > main > app-root > ng-component > pricing > .content'))
  if (!sections?.length) throw new Error('Failed to retrieve sections')
  const products = []
  for (const section of sections) {
    const section1 = Array.from(section.querySelectorAll('.feature-cards-container'))
    if (section1.length > 0) {
      const cards = Array.from(section1[0].querySelectorAll('.feature-card'))
      if (!cards?.length) throw new Error('Failed to retrieve cards')
      for (const card of cards) {
        const content = Array.from(card.querySelectorAll('.feature-card__content'))
        products.push(
          { [removeTabNewline(content[0].textContent ?? '')]: removeTabNewline(content[1].textContent ?? '') },
          { [removeTabNewline(content[0].textContent ?? '')]: removeTabNewline(content[2].textContent ?? '') },
        )
      }
    }
    const section2 = Array.from(section.querySelectorAll('.feature-table'))
    if (section2.length > 0) {
      const cards = Array.from(section2[0].children)
      if (!cards?.length) throw new Error('Failed to retrieve table 1')
      for (const card of cards) {
        const checkmarks = {
          mini: card.getAttribute('mini'),
          teho: card.getAttribute('teho'),
          super: card.getAttribute('super'),
        }
        products.push({ [removeTabNewline(card.textContent ?? '')]: JSON.stringify(checkmarks) })
      }
    }
    const section3 = Array.from(section.querySelectorAll('.table'))
    if (section3.length > 0) {
      const rows = Array.from(section3[0].children)[0].children
      if (!rows?.length) throw new Error('Failed to retrieve table 2')
      for (const row of rows) {
        const cols = Array.from(row.children).map((row) => row.textContent)
        products.push({ [removeTabNewline(cols[0] ?? '')]: JSON.stringify(cols.slice(1)) })
      }
    }
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const product of products) {
    resultsObj = Object.assign(resultsObj, product)
  }
  return resultsObj
}
