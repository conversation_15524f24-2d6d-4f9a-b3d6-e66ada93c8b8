import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { wait } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const autotraderPricingGB: TrackerConfig = {
  iso: Iso3166Alpha2.GB,
  trackerType: TrackerType.PRICING,
  extractionScript: autotraderScript,
  URLs: [new URL('https://www.autotrader.co.uk/sell-my-car/advertising-prices')],
  pngScript: null,
  csvHooks: null,
}

const variables = {
  CAR: {
    GREAT_BRITAIN: {
      TRADE: {
        ASKING_PRICES: ['0', '1001', '2001', '3001', '5001', '7001', '10001', '17001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0', '1000', '3000', '5000', '7000', '10000', '13000', '17000', '25000'],
      },
    },
    NORTHERN_IRELAND: {
      TRADE: {
        ASKING_PRICES: ['0', '1001', '2001', '3001', '5001', '7001', '10001', '17001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['1000'],
      },
    },
  },
  VAN: {
    GREAT_BRITAIN: {
      TRADE: {
        ASKING_PRICES: ['0', '3001', '5001', '7001', '10001', '13001', '17001', '25001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0', '1001'],
      },
    },
    NORTHERN_IRELAND: {
      TRADE: {
        ASKING_PRICES: ['0', '3001', '5001', '7001', '10001', '13001', '17001', '25001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0'],
      },
    },
  },
  BIKE: {
    NO_REGION: {
      TRADE: {
        ASKING_PRICES: ['0', '2001', '3001', '5001', '7001', '10001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0', '1001'],
      },
    },
  },
  MOTORHOME: {
    NO_REGION: {
      TRADE: {
        ASKING_PRICES: ['0', '7501', '20001', '35001', '50001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0', '5001'],
      },
    },
  },
  CARAVAN: {
    NO_REGION: {
      TRADE: {
        ASKING_PRICES: ['0', '5001', '10001', '20001'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0', '5001'],
      },
    },
  },
  TRUCK: {
    NO_REGION: {
      TRADE: {
        ASKING_PRICES: ['0'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0'],
      },
    },
  },
  PLANT: {
    NO_REGION: {
      TRADE: {
        ASKING_PRICES: ['0'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0'],
      },
    },
  },
  FARM: {
    NO_REGION: {
      TRADE: {
        ASKING_PRICES: ['0'],
      },
      PRIVATE: {
        ASKING_PRICES: ['0'],
      },
    },
  },
}

async function autotraderScript(job: JobInstance) {
  const resultsObj = {}

  for (const channel of Object.keys(variables)) {
    for (const region of Object.keys(variables[channel])) {
      for (const trade of Object.keys(variables[channel][region])) {
        for (const askingPrice of variables[channel][region][trade].ASKING_PRICES) {
          await wait(1000)
          const olaPackages = await fetchAutotraderData(job, channel, region, trade, askingPrice)

          for (const olaPackage of olaPackages) {
            resultsObj[
              `[${channel}][${region}][${trade}][${askingPrice}][${olaPackage.name}][${olaPackage.displayDuration}]`
            ] = olaPackage.displayPrice
          }
        }
      }
    }
  }

  return resultsObj
}

async function fetchAutotraderData(
  job: JobInstance,
  channel: string,
  region: string,
  trade: string,
  askingPrice: string,
) {
  return job.runtime
    .got('https://www.autotrader.co.uk/at-graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      json: {
        operationName: 'OlaPackagesQuery',
        variables: {
          channel,
          askingPrice,
          isTrade: trade === 'TRADE' ? 'true' : 'false',
          region: region === 'NO_REGION' ? 'GREAT_BRITAIN' : region,
        },
        query: `query OlaPackagesQuery($channel: String!, $askingPrice: String!, $isTrade: String, $region: String) { pola { olaPackages( channel: $channel askingPrice: $askingPrice isTrade: $isTrade region: $region ) { packages { id name displayDuration displayPrice originalPrice discountSaving price analyticsIdentifier headlineText products { id name description imageUrl __typename } __typename } discount { userHasDiscount __typename } __typename } __typename }}`,
      },
      retry: {
        backoffLimit: 3000,
        limit: 20,
        statusCodes: [403],
      },
    })
    .json<AutotraderPackagesQuery>()
    .then((response) => {
      const packages = response?.data?.pola?.olaPackages?.packages
      if (!packages?.length) throw new Error('Failed to extract packages from response')
      return packages
    })
}

// type definitions

interface AutotraderPackagesQuery {
  data: Data
}
interface Data {
  pola: Pola
}
interface Pola {
  olaPackages: OlaPackages
  __typename: string
}
interface OlaPackages {
  packages?: PackagesEntity[] | null
  discount: Discount
  __typename: string
}
interface PackagesEntity {
  id: number
  name: string
  displayDuration: string
  displayPrice: string
  originalPrice: string
  discountSaving?: null
  price: number
  analyticsIdentifier: string
  headlineText?: string | null
  products?: ProductsEntity[] | null
  __typename: string
}
interface ProductsEntity {
  id: number
  name: string
  description: string
  imageUrl?: string | null
  __typename: string
}
interface Discount {
  userHasDiscount: boolean
  __typename: string
}
