import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const kivraPageStateSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: kivra_seScript,
  URLs: [new URL('https://kivra.se/sv/om-kivra/press')],
  pngScript: null,
  csvHooks: null,
}

async function kivra_seScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}
  return job.runtime
    .got(kivraPageStateSE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response) throw new Error('Failed to retrieve html')
      document.body.innerHTML = response
      const listings = document.querySelectorAll('div[class^="News-module--info"]')

      const baseUrl = new URL(kivraPageStateSE.URLs[0].href).origin
      listings.forEach((listing) => {
        const linkElement = listing.querySelector('a')
        if (linkElement) {
          const title = linkElement.textContent.trim()
          const url = linkElement.getAttribute('href')
          if (title && url) {
            resultsObj[title] = `${baseUrl}${url}`
          }
        }
      })

      if (!Object.keys(resultsObj)?.length) throw new Error('No results found')
      return resultsObj
    })
}
