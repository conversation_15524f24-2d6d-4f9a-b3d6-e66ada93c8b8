import { Iso3166Alpha2, TimeUnit, TrackerType } from '@datagatherers/datagatherers'
import { wait } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const capcitoPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING,
  extractionScript: extractPricings,
  URLs: [
    'https://www.capcito.com/en/vara-tjanster/factoring/salja-fakturor',
    'https://www.capcito.com/en/vara-tjanster/factoring/fakturabelaning',
    'https://www.capcito.com/en/vara-tjanster/foretagslan',
  ].map((url) => new URL(url)),
  pngScript: null,
  csvHooks: null,
}

const categoryArray = [
  {
    url: 'https://www.capcito.com/en/vara-tjanster/factoring/salja-fakturor',
    category: 'Factoring',
    periodString: 'days',
  },
  {
    url: 'https://www.capcito.com/en/vara-tjanster/factoring/fakturabelaning',
    category: 'Invoice Discounting',
    periodString: 'days',
  },
  {
    url: 'https://www.capcito.com/en/vara-tjanster/foretagslan',
    category: 'Business Loan',
    periodString: TimeUnit.months,
  },
]

async function extractPricings(job: JobInstance) {
  const resultMap: { key: string; value: string }[] = []
  await job.runtime.page.setUserAgent('Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/112.0')
  await job.runtime.page.setViewport({
    width: 1920,
    height: 1080,
    isMobile: false,
    isLandscape: false,
    hasTouch: false,
  })
  for (const categoryData of categoryArray) {
    await job.runtime.page.goto(categoryData.url, { waitUntil: 'networkidle2' })
    try {
      await job.runtime.page.waitForSelector('#ppms_cm_reject-all')
      await job.runtime.page.click('#ppms_cm_reject-all')
    } catch {
      // EMPTY
    }

    const resultDefinition = await job.runtime.page.$eval(
      'div[class="text-sm font-semibold text-indigo-500 text-opacity-50"]',
      (el) => el.textContent,
    )
    if (!resultDefinition) throw new Error('Error 1')
    const sliderElements = await job.runtime.page.$$('.noUi-target')
    if (sliderElements?.length !== 2) {
      throw new Error('Error 2')
    }

    const valueHandle = await sliderElements[0].$('.noUi-handle')
    const timeHandle = await sliderElements[1].$('.noUi-handle')

    if (!valueHandle || !timeHandle) {
      throw new Error('Error 3')
    }
    const valueBoundingBox = await valueHandle.boundingBox()
    const timeBoundingBox = await timeHandle.boundingBox()
    if (!valueBoundingBox || !timeBoundingBox) {
      throw new Error('Error 4')
    }

    const valueTotalSlider = await sliderElements[0].$('.noUi-connects')
    const timeTotalSlider = await sliderElements[1].$('.noUi-connects')
    if (!valueTotalSlider || !timeTotalSlider) throw new Error('Error 5')

    const valueSliderWidth = await valueTotalSlider.boundingBox().then((rect) => rect?.width)
    const timeSliderWidth = await timeTotalSlider.boundingBox().then((rect) => rect?.width)
    if (!valueSliderWidth || !timeSliderWidth) throw new Error('Error 6')

    const valueNumSteps = 50
    const timeNumSteps = 20

    const valueDragDistance = valueSliderWidth / valueNumSteps
    const timeDragDistance = timeSliderWidth / timeNumSteps

    await sliderElements[0].waitForSelector('div.noUi-connect', { timeout: 10000 })
    const valueButton = await sliderElements[0].$('div.noUi-connect')
    await sliderElements[1].waitForSelector('div.noUi-connect', { timeout: 10000 })
    const timeButton = await sliderElements[1].$('div.noUi-connect')
    if (!valueButton || !timeButton) throw new Error('Error 7')

    const valueStartPosition = await valueButton.asElement().boundingBox()
    const timeStartPosition = await timeButton.asElement().boundingBox()
    if (!valueStartPosition || !timeStartPosition) throw new Error('Error 8')

    const yValuePosition = valueStartPosition.y + valueStartPosition.height / 2
    const yTimePosition = timeStartPosition.y + timeStartPosition.height / 2

    if (!yValuePosition || !yTimePosition) throw new Error('Error 9')

    if (!valueButton) throw new Error('Error 10')
    const timeValues = new Set()

    await job.runtime.page.mouse.click(timeStartPosition.x, yTimePosition)

    for (let i = 0; i <= timeNumSteps; i++) {
      if (i % 2 === 0) {
        await job.runtime.page.mouse.click(
          timeStartPosition.x + timeSliderWidth / 2 + (timeDragDistance * i) / 2,
          yTimePosition,
        )
      } else {
        await job.runtime.page.mouse.click(timeStartPosition.x + (timeDragDistance * i) / 2, yTimePosition)
      }
      await wait(300)

      const currentTimeValue = await timeHandle.evaluate((handle) => Number(handle.getAttribute('aria-valuenow')))
      if (!currentTimeValue) throw new Error('Error 11')

      if (!timeValues.has(currentTimeValue)) {
        timeValues.add(currentTimeValue)

        await job.runtime.page.mouse.click(valueStartPosition.x, yValuePosition)
        const priceValues = new Set()
        for (let k = 0; k <= valueNumSteps; k++) {
          if (k % 2 === 0) {
            await job.runtime.page.mouse.click(
              valueStartPosition.x + valueSliderWidth / 2 + (valueDragDistance * k) / 2,
              yValuePosition,
            )
          } else {
            await job.runtime.page.mouse.click(valueStartPosition.x + (valueDragDistance * k) / 2, yValuePosition)
          }
          await wait(300)

          const currentPriceValue = Number(
            await valueHandle.evaluate((handle) => Number(handle.getAttribute('aria-valuenow'))),
          )

          if (!currentPriceValue || isNaN(currentPriceValue)) throw new Error('Error 12')

          if (!priceValues.has(currentPriceValue)) {
            let resultValue = Number(
              await job.runtime.page.$eval(
                'div[class="font-serif leading-none text-3xl font-semibold lg:text-4xl lg:font-semibold text-blue-500"]',
                (el) => el.textContent?.replace(/[^0-9]/g, ''),
              ),
            )

            if (currentPriceValue === 100000 || currentPriceValue === 1000000 || currentPriceValue === 10000000) {
              if (categoryData.category === 'Business Loan') {
                const totalCost = await job.runtime.page.evaluate(() => {
                  const allElements = Array.from(document.querySelectorAll('div'))

                  const targetElement = allElements.find((element) => element?.textContent?.trim() === 'Total to pay')
                  const targetNextSibling = targetElement && targetElement.nextElementSibling

                  if (targetNextSibling) {
                    return Number(targetElement?.nextSibling?.textContent?.replace(/[^0-9]/g, ''))
                  }
                })

                if (!totalCost || isNaN(totalCost)) throw new Error('Error 13')
                resultValue = totalCost - currentPriceValue
              }
              if (categoryData.category === 'Factoring') {
                resultValue = currentPriceValue - resultValue
              }
              resultMap.push({
                key: `For ${categoryData.category} of ${currentPriceValue} for period of ${currentTimeValue} ${categoryData.periodString}`,
                value: resultValue.toString(),
              })
            }

            priceValues.add(currentPriceValue)
          }

          await wait(300)
        }
      }
      await wait(300)
    }
  }
  const results = resultMap.sort((a, b) => a.key.localeCompare(b.key))
  let resultsObj: TrackerScriptOutput = {}
  for (const product of results) {
    resultsObj = Object.assign(resultsObj, { [product.key]: product.value })
  }
  return resultsObj
}
