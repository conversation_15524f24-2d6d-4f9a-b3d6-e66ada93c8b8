import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const blocketVehiclesPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING_VEHICLES,
  extractionScript: blocketVehiclesScript,
  URLs: [new URL('https://www.blocket.se/for-foretag/fordon/pris')],
  pngScript: blocketVehiclesPngScript,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function blocketVehiclesScript(job: JobInstance) {
  return job.runtime
    .got(blocketVehiclesPricingSE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response
      const tables = Array.from(document.querySelectorAll('.StyledMarkdownRenderer-sc-1ejeafn-0 > table'))
      if (!tables?.length) throw new Error('Failed to retrieve tables')
      const products = []
      for (const table of tables) {
        const previousSelector = table.previousElementSibling
        let headerPrefix = ''
        if (previousSelector?.tagName === 'H2') {
          headerPrefix = previousSelector?.textContent?.trim() + ' > '
        }
        const headers = Array.from(table.querySelectorAll('thead > tr > th')).map(
          (header) =>
            header.textContent
              ?.split('\n')
              .reduce((acc, val) => acc?.replace('\n', ' ').trim() + ' ' + val?.replace('\n', ' ').trim()) ?? '',
        )
        if (headers[0] === 'Annonsera - Sälj dina fordon') {
          headers[1] = 'Blocket'
          headers[2] = 'Bytbil'
        }
        const rows = Array.from(table.querySelectorAll('tbody > tr'))
        if (!rows?.length) throw new Error('Unable to extract rows')
        for (const row of rows) {
          let columns = Array.from(row.querySelectorAll('td'))
          if (!columns?.[0]?.textContent) continue
          if (!columns?.length) throw new Error('Unable to retrieve columns')
          if (columns?.length > headers?.length) columns = columns.slice(0, headers.length - 1)
          for (let columnIndex = 1; columnIndex < columns.length; columnIndex++) {
            let value = columns[columnIndex].textContent?.replace('\n', ' ').trim() ?? ''
            if (!value?.length && Array.from(columns[columnIndex].querySelectorAll('i[class^="fa"]'))?.length) {
              value = 'checkmark'
            }
            products.push({
              [headerPrefix +
              headers[0] +
              ' > ' +
              (columns[0].textContent?.replace('\n', ' ').trim() ?? '') +
              ' > ' +
              headers[columnIndex]]: value,
            })
          }
        }
      }
      let resultsObj: TrackerScriptOutput = {}
      for (const product of products) {
        resultsObj = Object.assign(resultsObj, product)
      }
      return resultsObj
    })
}

async function blocketVehiclesPngScript(page: Page) {
  const div_selectors_to_remove = ['aside[class^="Modal__Background"]', '.sc-6c4174a4-0']
  for (const div_selector_to_remove of div_selectors_to_remove) {
    await page.evaluate(
      (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
      div_selector_to_remove,
    )
  }
  return page
}
