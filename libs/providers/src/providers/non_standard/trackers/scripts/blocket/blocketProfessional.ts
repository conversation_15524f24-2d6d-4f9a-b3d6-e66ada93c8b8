import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const blocketProfessionalPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING_PRO,
  extractionScript: blocketProfessionalScript,
  URLs: [new URL('https://www.blocket.se/tips-och-guider/annonsera/prislista')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function blocketProfessionalScript(job: JobInstance) {
  return job.runtime
    .got(blocketProfessionalPricingSE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response

      const nextData = document.querySelector('#__NEXT_DATA__')?.textContent
      const nextDataJson = JSON.parse(nextData ?? '')
      if (!nextDataJson) throw new Error('Failed to parse nextDataJson')

      const faqItems = nextDataJson.props?.pageProps?.dehydratedState?.queries
        ?.find((query) => query.queryKey.some((txt) => txt.match(/prislista/i)))
        ?.state?.data?.pageContentCollection?.items?.find((item) => item?.typename === 'Faq')?.itemsCollection?.items
      if (!faqItems?.length) throw new Error('Failed to retrieve faqItems')

      const resultsObj = {}
      for (const item of faqItems) {
        if (!item.question || !item.answer) continue
        resultsObj[item.question] = item.answer
      }

      return resultsObj
    })
}
