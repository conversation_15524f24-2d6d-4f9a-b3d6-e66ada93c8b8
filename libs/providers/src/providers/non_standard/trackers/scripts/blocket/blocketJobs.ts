import { JSD<PERSON> } from 'jsdom'

import { DataType, Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const blocketJobsPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING_JOBS,
  extractionScript: blocketJobsScript,
  URLs: [new URL('https://jobb.blocket.se/rekrytering')],
  pngScript: blocketJobsPngScript,
  csvHooks: null,
}
async function blocketJobsScript(job: JobInstance) {
  return job.runtime
    .got(blocketJobsPricingSE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response
      const productDivs = Array.from(document.querySelectorAll('.sc-a8107114-2 > div[class^="sc-"]'))
      if (!productDivs?.length) throw new Error('Failed to retrieve products')
      const products = []
      for (const productDiv of productDivs) {
        const header = productDiv.querySelector('h3')?.textContent?.trim() ?? ''
        const productSubDivs = productDiv.querySelectorAll('.sc-85e5a8b-2  > p[class^="sc-"')
        const price = productSubDivs[0]?.textContent?.replace(':-', '').trim() ?? ''
        const content = productDiv.textContent
        products.push({ [header + '_' + 'price']: price })
        products.push({ [header + '_' + DataType.content]: content })
      }
      let resultsObj: TrackerScriptOutput = {}
      for (const product of products) {
        resultsObj = Object.assign(resultsObj, product)
      }
      return resultsObj
    })
}

async function blocketJobsPngScript(page: Page) {
  const div_selectors_to_remove = ['aside[class^="Modal__Background"]', '.sc-6c4174a4-0']
  for (const div_selector_to_remove of div_selectors_to_remove) {
    await page.evaluate(
      (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
      div_selector_to_remove,
    )
  }
  return page
}
