import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const blocketPrivatePricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING_PRIVATE,
  extractionScript: blocketPrivateScript,
  URLs: [new URL('https://blocket.zendesk.com/hc/sv/articles/22877545778962-Prislista-f%C3%B6r-privatpersoner')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function blocketPrivateScript(job: JobInstance) {
  return job.runtime
    .got(blocketPrivatePricingSE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response

      const tables = Array.from(document.querySelectorAll('table'))
      const products = []
      for (const table of tables) {
        const previousSelector = table.previousElementSibling
        let headerPrefix = ''
        if (previousSelector?.tagName === 'P') {
          headerPrefix = previousSelector?.textContent?.trim()
        }

        const rows = Array.from(table.querySelectorAll('tbody > tr'))
        if (!rows?.length) throw new Error('Unable to extract rows')
        for (const row of rows) {
          const columns = Array.from(row.querySelectorAll('td'))
          if (!columns?.[0]?.textContent) continue

          const value = columns[columns.length - 1].textContent?.replace('\n', ' ').trim() ?? ''
          if (!value) continue

          let key = `${headerPrefix}`
          for (let columnIndex = 0; columnIndex < columns.length - 1; columnIndex++) {
            const val = columns[columnIndex].textContent?.replace('\n', ' ').trim() ?? ''
            if (!val) continue

            if (key) key += ' > '
            key += val
          }
          products.push({
            [key]: value,
          })
        }
      }

      let resultsObj: TrackerScriptOutput = {}
      for (const product of products) {
        resultsObj = Object.assign(resultsObj, product)
      }
      return resultsObj
    })
}
