import { RetryError } from 'got'
import { JSDOM } from 'jsdom'
import UserAgent from 'user-agents'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const karnovPricingDK: TrackerConfig = {
  iso: Iso3166Alpha2.DK,
  trackerType: TrackerType.PRICING,
  extractionScript: karnovPricingScript,
  URLs: [new URL('https://www.karnovgroup.dk/da-dk/kaila'), new URL('https://www.karnovgroup.dk/loesninger/jura')],
  pngScript: null,
  csvHooks: null,
}

async function karnovPricingScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj = {}

  for (const url of karnovPricingDK.URLs) {
    const preName = url.href.match(/jura/i) ? '(<PERSON><PERSON><PERSON>)' : '(<PERSON><PERSON>)'
    try {
      await job.runtime
        .got(url.href, {
          retry: {
            limit: 5,
            statusCodes: [404],
            errorCodes: ['ERR_GOT_REQUEST_ERROR', 'ERR_RETRYING'],
          },
          headers: {
            'User-Agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
          },
        })
        .then(({ body, statusCode, request }) => {
          if (statusCode !== 200) {
            throw new RetryError(request)
          }

          document.body.innerHTML = body

          const headers = Array.from(document.querySelectorAll('.pricing'))
          if (!headers?.length) throw new Error('Unable to find pricing headers')

          const plans = headers.map((header) => {
            const name = header.querySelector('h3')?.textContent?.trim()
            const priceText =
              header.querySelector('div[class^="pt-4"]')?.textContent?.trim() +
              ' ' +
              header.querySelector('div[class*="text-sm"]')?.textContent?.trim()

            let price = 0
            const priceMatch = priceText.match(/([\d.]+)/)
            if (priceMatch && priceMatch[1]) {
              price = parseFloat(priceMatch[1].replace(/\./g, ''))
            }

            return {
              name,
              price,
              priceText,
              features: {},
            }
          })

          const rows = Array.from(document.querySelectorAll('div[class*="flex-row h-"]'))
          for (const row of rows) {
            const cells = Array.from(row.querySelectorAll('div'))
            if (!cells?.length) continue

            const featureName = cells[0].textContent?.trim()
            if (!featureName) continue

            cells.slice(1).forEach((cell, index) => {
              if (index >= plans.length) return

              const hasCheckIcon = cell.querySelector('svg[class*="fill-yellow"]') !== null
              const cellText = cell.textContent?.trim() || ''

              let status = ''
              if (hasCheckIcon) {
                status = 'Included'
              } else if (cellText === 'Tilkøb') {
                status = 'Add-on'
              } else {
                status = 'Not included'
              }

              plans[index].features[featureName] = status
            })
          }

          plans.forEach((plan) => {
            const { name, price, priceText, features } = plan
            Object.assign(resultsObj, { [preName + '_' + name + '_price']: price })
            Object.assign(resultsObj, { [preName + '_' + name + '_priceText']: priceText })
            Object.entries(features).forEach(([feature, status]) => {
              Object.assign(resultsObj, { [preName + '_' + name + '_' + feature]: status })
            })
          })
        })
    } catch {
      continue
    }
  }

  if (!Object.entries(resultsObj).length) throw new Error(`Unable to retrieve pricing data`)
  return resultsObj
}
