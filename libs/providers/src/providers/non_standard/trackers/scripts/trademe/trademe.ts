import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { wait, unescapeSlashes } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const trademePricingNZ: TrackerConfig = {
  iso: Iso3166Alpha2.NZ,
  trackerType: TrackerType.PRICING,
  extractionScript: trademeScript,
  URLs: [
    'https://help.trademe.co.nz/hc/en-us/articles/360032347131-Marketplace-fees',
    'https://help.trademe.co.nz/hc/en-us/articles/360032010952-Motors-fees',
    'https://help.trademe.co.nz/hc/en-us/articles/360032007872-Property-fees',
    'https://help.trademe.co.nz/hc/en-us/articles/360032011912-Jobs-fees',
    'https://help.trademe.co.nz/hc/en-us/articles/360032350912-Services-fees',
  ].map((url) => new URL(url)),
  pngScript: null,
  csvHooks: null,
}
// snapshot urls above, api below, script crawling api
const baseURL = 'https://help.trademe.co.nz/api/v2/help_center/en-us/articles/'
const slugs = [
  '360032347131-Marketplace-fees',
  '360032010952-Motors-fees',
  '360032007872-Property-fees',
  '360032011912-Jobs-fees',
  '360032350912-Services-fees',
]

const delimiter = ' > '

function parseTable(table: Element, prefix: string) {
  const results = []
  const headColumns = Array.from(table.querySelectorAll('thead > tr > th'))
  const bodyRows = Array.from(table.querySelectorAll('tbody > tr'))
  if (!bodyRows?.length) throw new Error(`Unable to retrieve table bodyRows, table:${table.textContent}`)
  let appendSecondToPrefix = ''
  if (!headColumns?.length) appendSecondToPrefix = 'n/a'
  for (const bodyRow of bodyRows) {
    const bodyColumns = Array.from(bodyRow.querySelectorAll('td'))
    if (!bodyColumns?.length) throw new Error(`Unable to retrieve table bodyColumns, table:${table.textContent}`)

    for (let bodyColumn = 1; bodyColumn < bodyColumns.length; bodyColumn++) {
      const appendFirstToPrefix = bodyColumns[0]?.textContent ?? 'n/a'
      appendSecondToPrefix = headColumns[bodyColumn]?.textContent ?? headColumns[0]?.textContent ?? 'n/a'

      results.push({
        product: `${prefix.replace('\n', ' | ').trim()}${delimiter}${appendFirstToPrefix.replace('\n', ' | ').trim()}${delimiter}${appendSecondToPrefix.replace('\n', ' | ').trim()}`,
        value: bodyColumns[bodyColumn].textContent?.trim() ?? 'n/a',
      })
    }
  }
  return results
}

async function trademeScript(job: JobInstance) {
  const results = []
  for (const slug of slugs) {
    await wait(200)
    const response: Response = await job.runtime
      .got(baseURL + slug, {
        retry: {
          limit: 3,
        },
      })
      .text()
      .then((response) => JSON.parse(response))
    const { body, title } = response.article

    const decoded = unescapeSlashes(body)
    const { document } = new JSDOM(decoded).window
    const sections = Array.from(document.querySelectorAll('div[style]')).filter(
      (el) => Array.from(el.querySelectorAll('h2')).length > 0,
    )

    for (const section of sections) {
      const sectionTitle = section.querySelector('h2')?.textContent
      let subsections = Array.from(section.querySelectorAll('.accordion > .accordion__item'))
      if (!subsections?.length) subsections = Array.from(section.querySelectorAll('.accordion__item'))
      if (subsections.length) {
        for (const subsection of subsections) {
          const subsectionTitle = subsection.querySelector('.accordion__title')?.textContent
          const tables = subsection.querySelectorAll('.accordion__content > table[class="reformat-sm-table"]')
          if (!tables?.length) continue
          for (const table of tables) {
            const pairs = parseTable(
              table,
              `${title ?? 'n/a'}${delimiter}${sectionTitle ?? 'n/a'}${delimiter}${subsectionTitle ?? 'n/a'}`,
            )

            results.push(...pairs)
          }
        }
      } else {
        const tables = section.querySelectorAll('table[class="reformat-sm-table"]')
        if (!tables?.length) continue
        for (const table of tables) {
          const pairs = parseTable(table, `${title}${delimiter}${sectionTitle}${delimiter}`)
          results.push(...pairs)
        }
      }
    }
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const product of results) {
    resultsObj = Object.assign(resultsObj, { [product.product]: product.value })
  }
  return resultsObj
}

interface Response {
  article: Article
}

interface Article {
  id: number
  url: string
  html_url: string
  author_id: number
  comments_disabled: boolean
  draft: boolean
  promoted: boolean
  position: number
  vote_sum: number
  vote_count: number
  section_id: number
  created_at: string
  updated_at: string
  name: string
  title: string
  source_locale: string
  locale: string
  outdated: boolean
  outdated_locales?: null[] | null
  edited_at: string
  user_segment_id?: null
  permission_group_id: number
  content_tag_ids?: null[] | null
  label_names?: string[] | null
  body: string
}
