import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { TrackerConfig, TrackerScriptOutput, JobInstance } from '@datagatherers/datagatherers'

export const hogiaOpenbusinessPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING,
  URLs: [new URL('https://www.hogia.se/affarssystem/star/openbusiness-ekonomi')],
  extractionScript: hogiaOpenbusinessScript,
  pngScript: null,
  csvHooks: null,
}

async function hogiaOpenbusinessScript(job: JobInstance): Promise<TrackerScriptOutput | undefined> {
  const { document } = new JSDOM().window
  const response = await job.runtime.got(hogiaOpenbusinessPricingSE.URLs[0].href).text()
  document.body.innerHTML = response

  const productsRaw = document.querySelectorAll('.buy-product-card')
  if (!productsRaw?.length) throw new Error('Product cards not found')

  const products = Array.from(productsRaw).reduce((acc, product) => {
    const name = product.querySelector('.buy-product-card__product-text--sub-header')?.textContent?.trim()
    const price = product.querySelector('.buy-product-card__product-text--price-header')?.textContent?.trim()

    if (name && price) {
      const priceDescription = product
        .querySelector('.buy-product-card__product-text--price-sub-header')
        ?.textContent?.trim()
      let details: string | undefined
      const detailsRaw = product.querySelectorAll('.check-mark-text')
      if (detailsRaw?.length) {
        details = Array.from(detailsRaw).reduce((acc: string, detail: Element) => {
          if (detail?.textContent?.trim()) {
            if (acc?.length) {
              acc += ' | '
            }
            acc += detail.textContent.trim()
          }
          return acc
        }, '')
      }

      acc[name] = JSON.stringify({ price: `${price} ${priceDescription ?? ''}`.trim(), details })
    }

    return acc
  }, {})

  return products
}
