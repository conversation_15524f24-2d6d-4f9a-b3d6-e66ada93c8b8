import { format, fromUnixTime, isBefore, subYears, startOfYear, isAfter } from 'date-fns'

import { Iso3166Alpha2, TimeUnit, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const hemnetStatistikLauncherSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.LAUNCHER,
  extractionScript: hemnetScript,
  URLs: [new URL('https://www.hemnet.se/statistik/finansiell-rapportering')],
  pngScript: null,
  csvHooks: null,
}

async function hemnetScript(job: JobInstance) {
  const html = await job.runtime.got(hemnetStatistikLauncherSE.URLs[0], { responseType: 'text' }).text()

  const scriptMatch = html.match(/<script\s+id="__NEXT_DATA__"[^>]*>([\s\S]*?)<\/script>/)

  if (!scriptMatch?.[1]) {
    throw new Error('extractInventoryStats: unable to locate __NEXT_DATA__ JSON on financial report page')
  }
  const __NEXT_DATA__ = JSON.parse(scriptMatch[1])
  const statsMap = new Map()
  if (__NEXT_DATA__?.props?.pageProps?.data?.values?.length) {
    for (const item of __NEXT_DATA__.props.pageProps.data.values) {
      if (item.value == null) {
        continue
      }

      const currItemDate = fromUnixTime(Number(item.recordedAt))
      if (isBefore(subYears(startOfYear(new Date()), 4), currItemDate) && !isAfter(currItemDate, new Date())) {
        const timeUnit = TimeUnit.months
        const snapTime = format(currItemDate, 'yyyy/MM')
        const key = `${snapTime}_${timeUnit}`

        if (!statsMap.has(key)) {
          statsMap.set(key, {
            snapTime,
            snapTimeUnit: timeUnit,
            stats: [],
          })
        }

        statsMap.get(key).stats.push({
          count: item.value ?? 0,
          stockType: 'financial_report',
        })
      }
    }
  }
  const resultsObj: TrackerScriptOutput = {}
  for (const [key, value] of statsMap) {
    Object.assign(resultsObj, { [key]: value.snapTime })
  }
  return resultsObj
}
