import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { wait } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const hemnetPageStateSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: hemneScript,
  URLs: [new URL('https://maklare.hemnet.se/')],
  pngScript: null,
  csvHooks: null,
}

async function hemneScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}
  for (let index = 1; index <= 100; index++) {
    await wait(3000)
    const response = await job.runtime
      .got(`${hemnetPageStateSE.URLs[0].href}page/${index}`, {
        retry: {
          limit: 3,
        },
      })
      .text()

    if (!response) throw new Error('Failed to retrieve html')
    document.body.innerHTML = response

    const posts = document.querySelectorAll('.post')
    if (!posts?.length) break

    posts.forEach((post) => {
      const title = post.querySelector('.entry-title')?.textContent?.trim()
      if (!title) return

      const url = post.querySelector('.entry-title > a')?.getAttribute('href')?.trim()

      resultsObj[title] = url || ''
    })
  }

  return resultsObj
}
