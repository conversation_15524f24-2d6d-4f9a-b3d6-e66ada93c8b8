import { json2csv } from 'json-2-csv'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { wait } from '@datagatherers/worker-utils'

import { writeOutFromCsvHook } from '../../csvHooks'

import type { Diff, JobInstance, TrackerConfig, TrackerMerged, TrackerScriptOutput } from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'

export const hemnetPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING,
  extractionScript: hemnetPricingScript,
  URLs: [new URL('https://www.hemnet.se/annonsera-bostad')],
  pngScript: null,
  csvHooks: [customDiffsCsvHook, customJsonSnapCsvHook],
}

const separator = '_-_' // USED PROGRAMATICALLY TO SEPARATE LOCATION, PA<PERSON>KA<PERSON> AND STARTING PRICE, CHANGE HERE IF NEEDED (WILL AFFECT HISTORICAL DIFF COMPATIBILITY), KEEP IT COMPLICATED TO AVOID COLLISIONS !!!!

async function hemnetPricingScript(job: JobInstance) {
  const resultsRaw = []
  for (const query of locationQueries) {
    for (const price of startingPrices) {
      await wait(500)
      const response = await job.runtime
        .got('https://www.hemnet.se/graphql', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          json: {
            operationName: 'SellerMarketingProductPrices',
            variables: {
              askingPrice: price,
              locationId: query.id,
              productCodes: ['BASIC', 'PLUS', 'PREMIUM', 'MAX', 'PAID_REPUBLISH', 'TOPLISTING', 'TOPLISTING_5_DAYS'],
            },
            query:
              'query SellerMarketingProductPrices($locationId: ID!, $askingPrice: Int, $housingFormGroup: HousingFormGroup, $livingAreaInSqm: Float, $productCodes: [PackagePurchase!]!) {  sellerMarketingProductPrices(    locationId: $locationId    askingPrice: $askingPrice    productCodes: $productCodes    housingFormGroup: $housingFormGroup    livingAreaInSqm: $livingAreaInSqm  ) {    formattedValidThrough    prices {      code      price {        amount        formatted        __typename      }      immediatePrice {        amount        __typename      }      __typename    }    __typename  }}',
          },
        })
        .json<PricingResponse>()

      if (!response.data.sellerMarketingProductPrices.prices?.length) throw new Error('Failed to retrieve prices')
      const basicEntry = response.data.sellerMarketingProductPrices.prices.find((p) => p.code === 'BASIC')
      if (!basicEntry) throw new Error('Missing BASIC price')
      const basePrice = basicEntry.price.amount
      const baseImmediatePrice = basicEntry.immediatePrice.amount
      for (const priceDetails of response.data.sellerMarketingProductPrices.prices) {
        const rawStart = startingPricesRaw[startingPrices.indexOf(price)]
        const baseKey = `${query.name}${separator}${priceDetails.code}${separator}${rawStart}`

        const currentAmt = ['MAX', 'PREMIUM', 'PLUS'].includes(priceDetails.code)
          ? priceDetails.price.amount + basePrice
          : priceDetails.price.amount
        resultsRaw.push({ [baseKey]: currentAmt })

        const immediateKey = `${query.name}${separator}${priceDetails.code}_IMMEDIATE${separator}${rawStart}`
        const immediateAmt = ['MAX', 'PREMIUM', 'PLUS'].includes(priceDetails.code)
          ? priceDetails.immediatePrice.amount + baseImmediatePrice
          : priceDetails.immediatePrice.amount
        resultsRaw.push({ [immediateKey]: immediateAmt })
      }
    }
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const packagePrice of resultsRaw) {
    resultsObj = Object.assign(resultsObj, packagePrice)
  }
  return resultsObj
}

function parseToCsv(diffs: Diff[]) {
  const parsedDiffs = diffs.map((diff) => {
    const [location, pkgWithFlag, startPart] = diff.product.split('_-_')
    const isImmediate = pkgWithFlag.endsWith('_IMMEDIATE')
    const rawPkg = isImmediate ? pkgWithFlag.replace(/_IMMEDIATE$/, '') : pkgWithFlag

    const baseNormalized =
      rawPkg === 'TOPLISTING'
        ? 'ROCKET_3'
        : rawPkg === 'TOPLISTING_5_DAYS'
          ? 'ROCKET_5'
          : rawPkg === 'PAID_REPUBLISH'
            ? 'RE_PUBLISHING'
            : rawPkg

    const normalizedPkg = isImmediate ? `${baseNormalized}_IMMEDIATE` : baseNormalized

    return {
      location,
      package: normalizedPkg,
      startingPrice: parseFloat(startPart),
      price: diff.current,
    }
  })
  parsedDiffs.sort((a, b) => {
    const locationComparison = a.location.localeCompare(b.location)
    if (locationComparison !== 0) {
      return locationComparison
    }

    const packageComparison = packageHierarchy[a.package] - packageHierarchy[b.package]
    if (packageComparison !== 0) {
      return packageComparison
    }

    return a.startingPrice - b.startingPrice
  })
  const structuredData = parsedDiffs.reduce(
    (acc, item) => {
      acc[item.location] ??= { location: item.location, packages: {} }
      acc[item.location].packages[item.package] ??= {}
      acc[item.location].packages[item.package][item.startingPrice] = parseFloat(item.price)
      return acc
    },
    {} as Record<string, { location: string; packages: Record<string, Record<number, number>> }>,
  )
  const outputDataRaw = Object.values(structuredData).map((locItem) => {
    for (const [packageName, packageValues] of Object.entries(locItem.packages)) {
      locItem[packageName] = Array.from({ length: Math.max(...Object.keys(packageValues).map(Number)) + 1 }, () => '')
      for (const [price, value] of Object.entries(packageValues)) {
        locItem[packageName][Number(price)] = value
      }
    }
    return locItem
  })
  const outputData = []
  for (let index = 0; index < locationQueries.length; index++) {
    const item = outputDataRaw.find((item) => item.location === locationQueries[index].name)
    if (item) {
      outputData.push(item)
    }
  }
  const flatData = outputData.flatMap((locationItem) => {
    const rows: { [k: string]: string | number }[] = []
    for (const packageName in locationItem.packages) {
      rows.push({
        location: locationItem.location,
        package: packageName,
        ...Object.fromEntries(
          Object.entries(locationItem.packages[packageName]).map(([key, price]) => [
            `${key.replace('.', ',')}"`,
            price,
          ]),
        ),
      })
    }
    return rows
  })
  if (!flatData?.length) {
    throw new Error('CSV parsing failed')
  }
  return flatData
}

async function customDiffsCsvHook(
  job: JobInstance,
  config: TrackerConfig,
  mergedToggles: TrackerMerged,
  diffs: Diff[],
  jsonSnap: TrackerScriptOutput,
  gDrive: GDriveUtil,
  currentDate: Date,
  gDriveAcc: string,
  clientEmails: string[],
) {
  const args = [job, config, mergedToggles, diffs, jsonSnap, gDrive, currentDate, gDriveAcc, clientEmails] as const

  const parsedDiffs = parseToCsv(diffs)

  const csv = json2csv(parsedDiffs, {
    emptyFieldValue: 'N/A',
  })

  await writeOutFromCsvHook(...args, csv, 'Custom Differences', 'Please review the changes below:')
}

export async function customJsonSnapCsvHook(
  job: JobInstance,
  config: TrackerConfig,
  mergedToggles: TrackerMerged,
  diffs: Diff[],
  jsonSnap: TrackerScriptOutput,
  gDrive: GDriveUtil,
  currentDate: Date,
  gDriveAcc: string,
  clientEmails: string[],
) {
  const args = [job, config, mergedToggles, diffs, jsonSnap, gDrive, currentDate, gDriveAcc, clientEmails] as const

  const parsedData = Object.entries(jsonSnap).map(([key, price]) => {
    const [location, pkgWithFlag, startPart] = key.split('_-_')
    const isImmediate = pkgWithFlag.endsWith('_IMMEDIATE')
    const rawPkg = isImmediate ? pkgWithFlag.replace(/_IMMEDIATE$/, '') : pkgWithFlag

    const baseNormalized =
      rawPkg === 'TOPLISTING'
        ? 'ROCKET_3'
        : rawPkg === 'TOPLISTING_5_DAYS'
          ? 'ROCKET_5'
          : rawPkg === 'PAID_REPUBLISH'
            ? 'RE_PUBLISHING'
            : rawPkg

    const normalizedPkg = isImmediate ? `${baseNormalized}_IMMEDIATE` : baseNormalized

    return {
      location,
      package: normalizedPkg,
      startingPrice: parseFloat(startPart),
      price: Number(price),
    }
  })

  parsedData.sort((a, b) => {
    const locationComparison = a.location.localeCompare(b.location)
    if (locationComparison !== 0) {
      return locationComparison
    }

    const packageComparison = packageHierarchy[a.package] - packageHierarchy[b.package]
    if (packageComparison !== 0) {
      return packageComparison
    }

    return a.startingPrice - b.startingPrice
  })

  const structuredData = parsedData.reduce(
    (acc, item) => {
      acc[item.location] ??= { location: item.location, packages: {} }
      acc[item.location].packages[item.package] ??= {}
      acc[item.location].packages[item.package][item.startingPrice] = item.price
      return acc
    },
    {} as Record<string, { location: string; packages: Record<string, Record<number, number>> }>,
  )

  const outputDataRaw = Object.values(structuredData).map((locItem) => {
    for (const [packageName, packageValues] of Object.entries(locItem.packages)) {
      locItem[packageName] = Array.from({ length: Math.max(...Object.keys(packageValues).map(Number)) + 1 }, () => '')
      for (const [price, value] of Object.entries(packageValues)) {
        locItem[packageName][Number(price)] = value
      }
    }
    return locItem
  })

  const outputData = []
  for (let index = 0; index < locationQueries.length; index++) {
    const item = outputDataRaw.find((item) => item.location === locationQueries[index].name)
    if (item) {
      outputData.push(item)
    }
  }

  const flatData = outputData.flatMap((locationItem) => {
    const rows: { [k: string]: string | number }[] = []
    for (const packageName in locationItem.packages) {
      rows.push({
        location: locationItem.location,
        package: packageName,
        ...Object.fromEntries(
          Object.entries(locationItem.packages[packageName]).map(([key, price]) => [
            `${key.replace('.', ',')}"`,
            price,
          ]),
        ),
      })
    }
    return rows
  })

  if (!flatData?.length) {
    throw new Error('CSV parsing failed')
  }

  const csv = json2csv(flatData, {
    emptyFieldValue: 'N/A',
  })

  await writeOutFromCsvHook(...args, csv, 'Custom Snapshot', 'Please review the snapshot below:')
}

const packageHierarchy = {
  MAX: 1,
  MAX_IMMEDIATE: 2,
  PREMIUM: 3,
  PREMIUM_IMMEDIATE: 4,
  PLUS: 5,
  PLUS_IMMEDIATE: 6,
  BASIC: 7,
  BASIC_IMMEDIATE: 8,
  ROCKET_3: 9, // 3-day rocket
  ROCKET_3_IMMEDIATE: 10,
  ROCKET_5: 11, // 5-day rocket
  ROCKET_5_IMMEDIATE: 12,
  RE_PUBLISHING: 13,
  RE_PUBLISHING_IMMEDIATE: 14,
}

const startingPricesRaw = [
  1, 0.3, 0.6, 1.1, 1.6, 2.1, 2.6, 3.1, 4.1, 5.1, 6.1, 8.1, 10.1, 11.1, 12.1, 13.1, 14.1, 15.1, 16.1, 17.1, 18.1, 19.1,
  20.1, 21.1, 22.1, 23.1, 24.1, 25.1, 26.1, 27.1, 28.1, 29.1, 30.1,
]

const startingPrices = startingPricesRaw.map((price) => {
  if (price === 1) return 1
  return Math.ceil(1000000 * price)
})

const locationQueries: { name: string; id: number }[] = [
  { name: 'Solstormsvagen, Kurravaara', id: 897606 },
  { name: 'Sundsvalls Muncipality, Vasternorrland County', id: 18054 },
  { name: 'Lunds Kommun, Skane Ian', id: 17987 },
  { name: 'Jonkopings Kommun, Jongkopings Ian', id: 17952 },
  { name: 'Kungsgatan, Stockholm', id: 475094 },
  { name: 'Vaxjo Kommun, Kronobergs Ian', id: 17822 },
  { name: 'Kiruna Kommun, Norrbottens Ian', id: 17963 },
  // { name: 'Ostersunds Kommun, Jamtlands Ian', id: 17840 },
  // { name: 'Alingsas Kommun, Vastra Gotalands Ian', id: 17866 },
  { name: 'Are Kommun, Jamtlands Ian', id: 17827 },
  { name: 'Alvdalens Kommun, Dalarnas Ian', id: 17832 },
]

interface PricingResponse {
  data: Data
}
interface Data {
  sellerMarketingProductPrices: SellerMarketingProductPrices
}
interface SellerMarketingProductPrices {
  formattedValidThrough: string
  prices?: PricesEntity[] | null
  __typename: string
}
interface PricesEntity {
  code: string
  price: Price
  __typename: string
  immediatePrice: ImmediatePrice
}
interface Price {
  amount: number
  __typename: string
}
interface ImmediatePrice {
  amount: number
  __typename: string
}

// export const locationQueries = [
//   { name: 'Ronneby Kommun', id: 17767 },
//   { name: 'Karlskrona Kommun', id: 17958 },
//   { name: 'Karlshamn Kommun', id: 17956 },
//   { name: 'Sölvesborg Kommun', id: 17776 },
//   { name: 'Olofström Kommun', id: 18008 },
//   { name: 'Smedjebacken Kommun', id: 17861 },
//   { name: 'Malung Kommun', id: 17990 },
//   { name: 'Gagnef Kommun', id: 17910 },
//   { name: 'Vansbro Kommun', id: 17805 },
//   { name: 'Borlänge Kommun', id: 17883 },
//   { name: 'Rättvik Kommun', id: 18018 },
//   { name: 'Säter Kommun', id: 17771 },
//   { name: 'Ludvika Kommun', id: 17848 },
//   { name: 'Mora Kommun', id: 17851 },
//   { name: 'Hedemora Kommun', id: 17931 },
//   { name: 'Älvdalen Kommun', id: 17832 },
//   { name: 'Falun Kommun', id: 17904 },
//   { name: 'Leksand Kommun', id: 17978 },
//   { name: 'Avesta Kommun', id: 17874 },
//   { name: 'Orsa Kommun', id: 18009 },
//   { name: 'Gotland Kommun', id: 17914 },
//   { name: 'Gävle Kommun', id: 17919 },
//   { name: 'Sandviken Kommun', id: 17859 },
//   { name: 'Hudiksvall Kommun', id: 17937 },
//   { name: 'Bollnäs Kommun', id: 17881 },
//   { name: 'Söderhamn Kommun', id: 17773 },
//   { name: 'Ovanåker Kommun', id: 18050 },
//   { name: 'Ljusdal Kommun', id: 17984 },
//   { name: 'Ockelbo Kommun', id: 17856 },
//   { name: 'Nordanstig Kommun', id: 18001 },
//   { name: 'Hofors Kommun', id: 17935 },
//   { name: 'Halmstad Kommun', id: 17926 },
//   { name: 'Kungsbacka Kommun', id: 17971 },
//   { name: 'Varberg Kommun', id: 17807 },
//   { name: 'Falkenberg Kommun', id: 17903 },
//   { name: 'Laholm Kommun', id: 17976 },
//   { name: 'Hylte Kommun', id: 17939 },
//   { name: 'Östersund Kommun', id: 17840 },
//   { name: 'Krokom Kommun', id: 17969 },
//   { name: 'Strömsund Kommun', id: 18035 },
//   { name: 'Åre Kommun', id: 17827 },
//   { name: 'Härjedalen Kommun', id: 17942 },
//   { name: 'Berg Kommun', id: 17876 },
//   { name: 'Ragunda Kommun', id: 18016 },
//   { name: 'Bräcke Kommun', id: 17888 },
//   { name: 'Jönköping Kommun', id: 17952 },
//   { name: 'Värnamo Kommun', id: 17819 },
//   { name: 'Nässjö Kommun', id: 18007 },
//   { name: 'Gislaved Kommun', id: 17911 },
//   { name: 'Vetlanda Kommun', id: 17810 },
//   { name: 'Tranås Kommun', id: 17788 },
//   { name: 'Eksjö Kommun', id: 17897 },
//   { name: 'Vaggeryd Kommun', id: 17802 },
//   { name: 'Habo Kommun', id: 17922 },
//   { name: 'Sävsjö Kommun', id: 17772 },
//   { name: 'Mullsjö Kommun', id: 18047 },
//   { name: 'Gnosjö Kommun', id: 17913 },
//   { name: 'Aneby Kommun', id: 17868 },
//   { name: 'Kalmar Kommun', id: 17954 },
//   { name: 'Västervik Kommun', id: 17820 },
//   { name: 'Oskarshamn Kommun', id: 17857 },
//   { name: 'Nybro Kommun', id: 18005 },
//   { name: 'Vimmerby Kommun', id: 17812 },
//   { name: 'Mörbylånga Kommun', id: 17999 },
//   { name: 'Mönsterås Kommun', id: 17998 },
//   { name: 'Hultsfred Kommun', id: 17938 },
//   { name: 'Borgholm Kommun', id: 17882 },
//   { name: 'Emmaboda Kommun', id: 18040 },
//   { name: 'Torsås Kommun', id: 17786 },
//   { name: 'Högsby Kommun', id: 17947 },
//   { name: 'Växjö Kommun', id: 17822 },
//   { name: 'Ljungby Kommun', id: 17983 },
//   { name: 'Alvesta Kommun', id: 17867 },
//   { name: 'Älmhult Kommun', id: 17831 },
//   { name: 'Markaryd Kommun', id: 17992 },
//   { name: 'Tingsryd Kommun', id: 17782 },
//   { name: 'Uppvidinge Kommun', id: 17768 },
//   { name: 'Lessebo Kommun', id: 17980 },
//   { name: 'Luleå Kommun', id: 18045 },
//   { name: 'Piteå Kommun', id: 18015 },
//   { name: 'Arvidsjaur Kommun', id: 17871 },
//   { name: 'Boden Kommun', id: 17879 },
//   { name: 'Övertorneå Kommun', id: 17844 },
//   { name: 'Kalix Kommun', id: 17953 },
//   { name: 'Pajala Kommun', id: 18013 },
//   { name: 'Jokkmokk Kommun', id: 17950 },
//   { name: 'Överkalix Kommun', id: 17843 },
//   { name: 'Gällivare Kommun', id: 17918 },
//   { name: 'Kiruna Kommun', id: 17963 },
//   { name: 'Älvsbyn Kommun', id: 17834 },
//   { name: 'Haparanda Kommun', id: 17929 },
//   { name: 'Arjeplog Kommun', id: 17870 },
//   { name: 'Malmö Kommun', id: 17989 },
//   { name: 'Helsingborg Kommun', id: 17932 },
//   { name: 'Ystad Kommun', id: 17824 },
//   { name: 'Höganäs Kommun', id: 17946 },
//   { name: 'Lund Kommun', id: 17987 },
//   { name: 'Trelleborg Kommun', id: 17789 },
//   { name: 'Kävlinge Kommun', id: 17974 },
//   { name: 'Landskrona Kommun', id: 18043 },
//   { name: 'Kristianstad Kommun', id: 17967 },
//   { name: 'Hässleholm Kommun', id: 17945 },
//   { name: 'Eslöv Kommun', id: 17900 },
//   { name: 'Vellinge Kommun', id: 17809 },
//   { name: 'Ängelholm Kommun', id: 17835 },
//   { name: 'Staffanstorp Kommun', id: 18053 },
//   { name: 'Simrishamn Kommun', id: 18021 },
//   { name: 'Svedala Kommun', id: 17864 },
//   { name: 'Lomma Kommun', id: 17986 },
//   { name: 'Sjöbo Kommun', id: 18022 },
//   { name: 'Tomelilla Kommun', id: 17784 },
//   { name: 'Hörby Kommun', id: 17948 },
//   { name: 'Klippan Kommun', id: 17964 },
//   { name: 'Östra Göinge Kommun', id: 17842 },
//   { name: 'Höör Kommun', id: 17949 },
//   { name: 'Båstad Kommun', id: 17890 },
//   { name: 'Osby Kommun', id: 18011 },
//   { name: 'Burlöv Kommun', id: 17889 },
//   { name: 'Skurup Kommun', id: 18024 },
//   { name: 'Åstorp Kommun', id: 17830 },
//   { name: 'Bjuv Kommun', id: 17878 },
//   { name: 'Svalöv Kommun', id: 18038 },
//   { name: 'Bromölla Kommun', id: 17887 },
//   { name: 'Örkelljunga Kommun', id: 18041 },
//   { name: 'Perstorp Kommun', id: 18014 },
//   { name: 'Stockholm Kommun', id: 18031 },
//   { name: 'Huddinge Kommun', id: 17936 },
//   { name: 'Nacka Kommun', id: 17853 },
//   { name: 'Södertälje Kommun', id: 17775 },
//   { name: 'Botkyrka Kommun', id: 17885 },
//   { name: 'Solna Kommun', id: 18028 },
//   { name: 'Haninge Kommun', id: 17928 },
//   { name: 'Järfälla Kommun', id: 17951 },
//   { name: 'Sollentuna Kommun', id: 18027 },
//   { name: 'Täby Kommun', id: 17793 },
//   { name: 'Norrtälje Kommun', id: 18003 },
//   { name: 'Sundbyberg Kommun', id: 18042 },
//   { name: 'Lidingö Kommun', id: 17846 },
//   { name: 'Tyresö Kommun', id: 17792 },
//   { name: 'Sigtuna Kommun', id: 18020 },
//   { name: 'Upplands Väsby Kommun', id: 17798 },
//   { name: 'Österåker Kommun', id: 17769 },
//   { name: 'Värmdö Kommun', id: 17818 },
//   { name: 'Danderyd Kommun', id: 17892 },
//   { name: 'Vallentuna Kommun', id: 17804 },
//   { name: 'Nynäshamn Kommun', id: 18006 },
//   { name: 'Ekerö Kommun', id: 17896 },
//   { name: 'Upplands-Bro Kommun', id: 17799 },
//   { name: 'Salem Kommun', id: 18019 },
//   { name: 'Vaxholm Kommun', id: 17808 },
//   { name: 'Nykvarn Kommun', id: 17855 },
//   { name: 'Katrineholm Kommun', id: 17960 },
//   { name: 'Nyköping Kommun', id: 18049 },
//   { name: 'Oxelösund Kommun', id: 18012 },
//   { name: 'Gnesta Kommun', id: 17912 },
//   { name: 'Eskilstuna Kommun', id: 17899 },
//   { name: 'Flen Kommun', id: 17907 },
//   { name: 'Trosa Kommun', id: 17791 },
//   { name: 'Vingåker Kommun', id: 17814 },
//   { name: 'Strängnäs Kommun', id: 18033 },
//   { name: 'Uppsala Kommun', id: 17800 },
//   { name: 'Enköping Kommun', id: 17898 },
//   { name: 'Östhammar Kommun', id: 17841 },
//   { name: 'Håbo Kommun', id: 17940 },
//   { name: 'Tierp Kommun', id: 17780 },
//   { name: 'Heby Kommun', id: 17930 },
//   { name: 'Knivsta Kommun', id: 17965 },
//   { name: 'Älvkarleby Kommun', id: 17833 },
//   { name: 'Karlstad Kommun', id: 17959 },
//   { name: 'Hammarö Kommun', id: 17927 },
//   { name: 'Kristinehamn Kommun', id: 17968 },
//   { name: 'Säffle Kommun', id: 17770 },
//   { name: 'Sunne Kommun', id: 18036 },
//   { name: 'Arvika Kommun', id: 17872 },
//   { name: 'Kil Kommun', id: 17961 },
//   { name: 'Torsby Kommun', id: 17785 },
//   { name: 'Hagfors Kommun', id: 17923 },
//   { name: 'Forshaga Kommun', id: 17908 },
//   { name: 'Filipstad Kommun', id: 17905 },
//   { name: 'Munkfors Kommun', id: 17852 },
//   { name: 'Grums Kommun', id: 17915 },
//   { name: 'Årjäng Kommun', id: 17828 },
//   { name: 'Eda Kommun', id: 17895 },
//   { name: 'Storfors Kommun', id: 18032 },
//   { name: 'Umeå Kommun', id: 17797 },
//   { name: 'Skellefteå Kommun', id: 17860 },
//   { name: 'Lycksele Kommun', id: 17988 },
//   { name: 'Vännäs Kommun', id: 17817 },
//   { name: 'Nordmaling Kommun', id: 17854 },
//   { name: 'Vilhelmina Kommun', id: 17811 },
//   { name: 'Storuman Kommun', id: 17863 },
//   { name: 'Robertsfors Kommun', id: 18017 },
//   { name: 'Vindeln Kommun', id: 17813 },
//   { name: 'Norsjö Kommun', id: 18004 },
//   { name: 'Malå Kommun', id: 17991 },
//   { name: 'Åsele Kommun', id: 17829 },
//   { name: 'Dorotea Kommun', id: 17894 },
//   { name: 'Sorsele Kommun', id: 18029 },
//   { name: 'Bjurholm Kommun', id: 17877 },
//   { name: 'Sundsvall Kommun', id: 18054 },
//   { name: 'Örnsköldsvik Kommun', id: 17839 },
//   { name: 'Härnösand Kommun', id: 17943 },
//   { name: 'Sollefteå Kommun', id: 18026 },
//   { name: 'Kramfors Kommun', id: 17966 },
//   { name: 'Timrå Kommun', id: 17781 },
//   { name: 'Ånge Kommun', id: 17826 },
//   { name: 'Västerås Kommun', id: 17821 },
//   { name: 'Köping Kommun', id: 17975 },
//   { name: 'Sala Kommun', id: 18051 },
//   { name: 'Hallstahammar Kommun', id: 17925 },
//   { name: 'Arboga Kommun', id: 17869 },
//   { name: 'Fagersta Kommun', id: 17902 },
//   { name: 'Surahammar Kommun', id: 18037 },
//   { name: 'Kungsör Kommun', id: 17972 },
//   { name: 'Norberg Kommun', id: 18048 },
//   { name: 'Skinnskatteberg Kommun', id: 18052 },
//   { name: 'Göteborg Kommun', id: 17920 },
//   { name: 'Falköping Kommun', id: 17766 },
//   { name: 'Vänersborg Kommun', id: 17816 },
//   { name: 'Härryda Kommun', id: 17944 },
//   { name: 'Ale Kommun', id: 17865 },
//   { name: 'Mark Kommun', id: 18046 },
//   { name: 'Partille Kommun', id: 17858 },
//   { name: 'Borås Kommun', id: 17884 },
//   { name: 'Mölndal Kommun', id: 17997 },
//   { name: 'Kungälv Kommun', id: 17973 },
//   { name: 'Lidköping Kommun', id: 17981 },
//   { name: 'Ulricehamn Kommun', id: 17796 },
//   { name: 'Skövde Kommun', id: 18025 },
//   { name: 'Trollhättan Kommun', id: 17790 },
//   { name: 'Stenungsund Kommun', id: 18030 },
//   { name: 'Tjörn Kommun', id: 17783 },
//   { name: 'Alingsås Kommun', id: 17866 },
//   { name: 'Uddevalla Kommun', id: 17795 },
//   { name: 'Mariestad Kommun', id: 17850 },
//   { name: 'Vara Kommun', id: 17806 },
//   { name: 'Skara Kommun', id: 18023 },
//   { name: 'Lerum Kommun', id: 17979 },
//   { name: 'Strömstad Kommun', id: 18034 },
//   { name: 'Lysekil Kommun', id: 17849 },
//   { name: 'Öckerö Kommun', id: 17836 },
//   { name: 'Vårgårda Kommun', id: 17815 },
//   { name: 'Götene Kommun', id: 17921 },
//   { name: 'Lilla Edet Kommun', id: 18044 },
//   { name: 'Tidaholm Kommun', id: 17779 },
//   { name: 'Åmål Kommun', id: 17825 },
//   { name: 'Tranemo Kommun', id: 17787 },
//   { name: 'Orust Kommun', id: 18010 },
//   { name: 'Tanum Kommun', id: 17777 },
//   { name: 'Svenljunga Kommun', id: 18039 },
//   { name: 'Tibro Kommun', id: 17778 },
//   { name: 'Färgelanda Kommun', id: 17909 },
//   { name: 'Mellerud Kommun', id: 17993 },
//   { name: 'Bollebygd Kommun', id: 17880 },
//   { name: 'Munkedal Kommun', id: 17996 },
//   { name: 'Essunga Kommun', id: 17901 },
//   { name: 'Sotenäs Kommun', id: 17862 },
//   { name: 'Töreboda Kommun', id: 17794 },
//   { name: 'Karlsborg Kommun', id: 17955 },
//   { name: 'Hjo Kommun', id: 17934 },
//   { name: 'Grästorp Kommun', id: 17916 },
//   { name: 'Herrljunga Kommun', id: 17933 },
//   { name: 'Bengtsfors Kommun', id: 17875 },
//   { name: 'Gullspång Kommun', id: 17917 },
//   { name: 'Dals-Ed Kommun', id: 17891 },
//   { name: 'Kumla Kommun', id: 17970 },
//   { name: 'Askersund Kommun', id: 17873 },
//   { name: 'Nora Kommun', id: 18000 },
//   { name: 'Hallsberg Kommun', id: 17924 },
//   { name: 'Lekeberg Kommun', id: 17845 },
//   { name: 'Hällefors Kommun', id: 17941 },
//   { name: 'Ljusnarsberg Kommun', id: 17985 },
//   { name: 'Örebro Kommun', id: 17838 },
//   { name: 'Karlskoga Kommun', id: 17957 },
//   { name: 'Laxå Kommun', id: 17977 },
//   { name: 'Lindesberg Kommun', id: 17982 },
//   { name: 'Degerfors Kommun', id: 17893 },
//   { name: 'Valdemarsvik Kommun', id: 17803 },
//   { name: 'Ydre Kommun', id: 17823 },
//   { name: 'Söderköping Kommun', id: 17774 },
//   { name: 'Mjölby Kommun', id: 17994 },
//   { name: 'Linköping Kommun', id: 17847 },
//   { name: 'Norrköping Kommun', id: 18002 },
//   { name: 'Finspång Kommun', id: 17906 },
//   { name: 'Kinda Kommun', id: 17962 },
//   { name: 'Åtvidaberg Kommun', id: 17765 },
//   { name: 'Motala Kommun', id: 17995 },
//   { name: 'Ödeshög Kommun', id: 17837 },
//   { name: 'Vadstena Kommun', id: 17801 },
//   { name: 'Boxholm Kommun', id: 17886 },
// ]
