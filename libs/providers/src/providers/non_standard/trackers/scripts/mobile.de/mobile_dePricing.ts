import { createHash } from 'crypto'
// import fs from 'fs'

import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const mobileDePricing: TrackerConfig = {
  iso: Iso3166Alpha2.DE,
  trackerType: TrackerType.PRICING,
  extractionScript: mobile_deScript,
  URLs: [new URL('https://promo.mobile.de/b2b/preisliste/?lang=en')],
  pngScript: null,
  csvHooks: null,
}

async function mobile_deScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const resultsObj: TrackerScriptOutput = {}
  const RETRIES = 3
  const { document } = new JSDOM().window
  for (let attempt = 1; attempt <= RETRIES; attempt++) {
    try {
      const response = await job.runtime.got(mobileDePricing.URLs[0].href).text()

      if (!response) {
        throw new Error(`failed to retrieve html from ${mobileDePricing.URLs[0].href}`)
      }

      document.body.innerHTML = response
      const imageSections = document.querySelectorAll('section[class="vc_section"]')

      for (const section of imageSections) {
        const headerElement = section.querySelector('.element-heading')
        if (!headerElement?.textContent?.includes('Preisstaffeln')) continue

        const imgEl = section.querySelector('picture > img')
        if (!imgEl) continue

        const src = imgEl.getAttribute('data-src') || imgEl.getAttribute('src')

        if (!src) continue

        const res = await job.runtime.got(src, {
          headers: {
            accept: 'image/avif,image/webp,image/apng,image/*,*/*;q=0.8',
            referer: mobileDePricing.URLs[0].href,
          },
          timeout: { request: 15000 },
          retry: { limit: 2 },
        })

        const ct = res.headers['content-type'] || ''
        if (!ct.startsWith('image/')) throw new Error(`Expected image, got ${ct}`)

        const buf = res.rawBody
        const base64 = buf.toString('base64')

        const hash = createHash('sha256').update(buf).digest('hex').slice(0, 16)

        Object.assign(resultsObj, { [hash]: base64 })
      }

      if (!Object.keys(resultsObj)?.length) {
        throw new Error('No results found')
      }
      break
    } catch {
      if (attempt === RETRIES) {
        throw new Error(`Failed to extract data from ${mobileDePricing.URLs[0].href} after ${RETRIES} attempts`)
      }
      continue
    }
  }
  if (!Object.keys(resultsObj)?.length) throw new Error('No results found')

  return resultsObj
}
