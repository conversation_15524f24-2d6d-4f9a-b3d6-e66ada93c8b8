import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const mobile_dePageStateDE: TrackerConfig = {
  iso: Iso3166Alpha2.DE,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: mobile_deScript,
  URLs: [new URL('https://newsroom.mobile.de/startseite/')],
  pngScript: null,
  csvHooks: null,
}

async function mobile_deScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}

  const response = await job.runtime
    .got(mobile_dePageStateDE.URLs[0].href, {
      method: 'GET',
      searchParams: {
        sf_paged: 1,
      },
      retry: {
        limit: 10,
        statusCodes: [403],
      },
    })
    .text()

  if (!response) {
    throw new Error(`failed to retrieve html from ${mobile_dePageStateDE.URLs[0].href}`)
  }

  document.body.innerHTML = response
  const articles = document.querySelectorAll('.elementor-loop-container > div[data-elementor-type="loop-item"]')

  articles.forEach((article) => {
    const classAttr = article.getAttribute('class')
    const id = classAttr?.match(/post-(\d+)/)?.[1]

    const title = article.querySelector('h3.elementor-heading-title')?.textContent
    const href = article.querySelector('a')?.getAttribute('href')
    if (title && id && href) {
      const uniqueTitle = `[${id}]${title.trim()}`
      resultsObj[uniqueTitle] = href
    }
  })

  if (!Object.keys(resultsObj)?.length) {
    throw new Error('No results found')
  }

  return resultsObj
}
