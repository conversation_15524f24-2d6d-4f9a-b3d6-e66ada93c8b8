import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { generateId } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const government_sePageStateSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: government_seScript,
  URLs: [new URL('https://www.government.se/press-releases/')],
  pngScript: null,
  csvHooks: null,
}

export interface GovernmentSEAPIResponse {
  Message: string
  TotalCount: number
}

async function government_seScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}
  return job.runtime
    .got('https://www.government.se/Filter/GetFilteredItems', {
      method: 'GET',
      searchParams: {
        lang: 'en',
        filterType: 'Taxonomy',
        filterByType: 'FilterablePageBase',
        preFilteredCategories: '2033',
        page: 1,
      },
    })
    .json<GovernmentSEAPIResponse>()
    .then((response) => {
      if (!response) throw new Error('Failed to retrieve html')
      document.body.innerHTML = response.Message
      const listings = document.querySelectorAll('.list--block .sortcompact > a')

      listings.forEach((listing) => {
        const title = listing.textContent
        const url = listing.getAttribute('href')
        if (title && url) {
          const uniqueTitle = `${title.trim()}-${generateId(title + `-${url}`)}`
          resultsObj[uniqueTitle] = `https://www.government.se${url}`
        }
      })

      if (!Object.keys(resultsObj)?.length) throw new Error('No results found')
      return resultsObj
    })
}
