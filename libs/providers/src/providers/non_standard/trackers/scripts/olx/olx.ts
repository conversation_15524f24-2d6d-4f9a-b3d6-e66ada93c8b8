import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { randomWait } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const olxPricingBR: TrackerConfig = {
  iso: Iso3166Alpha2.BR,
  trackerType: TrackerType.PRICING,
  extractionScript: olxScript,
  URLs: [
    new URL('https://adquirir.olx.com.br/planos-carros?referral=MYPLAN'),
    new URL('https://pointsofsales.olx.com.br/v2/?type=realestate-subscription&referral=MYPLAN'),
    new URL('https://adquirir.olx.com.br/planos-marketplace?referral=MYPLAN'),
    new URL('https://pointsofsales.olx.com.br/v2/?type=trucks-subscription&referral=MYPLAN'),
    new URL('https://pointsofsales.olx.com.br/v2/?type=motorcycles-subscription&referral=MYPLAN'),
    new URL('https://pointsofsales.olx.com.br/v2/?type=autoparts-subscription&referral=MYPLAN'),
    new URL('https://pointsofsales.olx.com.br/v2/?type=boats-subscription&referral=MYPLAN'),
    new URL('https://adquirir.olx.com.br/planos-services?referral=MYPLAN'),
    new URL('https://adquirir.olx.com.br/planos-jobs?referral=MYPLAN'),
    new URL('https://adquirir.olx.com.br/planos-agro?referral=MYPLAN'),
  ],
  pngScript: null,
  csvHooks: null,
}

const projects = [
  {
    category: 'Cars',
    url: 'https://adquirir.olx.com.br/planos-carros?referral=MYPLAN',
    hasApi: false,
  },
  {
    category: 'Real Estate',
    url: 'https://octopos.olx.com.br/api/v1/render?type=realestate-subscription',
    hasApi: true,
  },
  {
    category: 'Marketplace',
    url: 'https://adquirir.olx.com.br/planos-marketplace?referral=MYPLAN',
    hasApi: false,
  },
  {
    category: 'Trucks',
    url: 'https://octopos.olx.com.br/api/v1/render?type=trucks-subscription',
    hasApi: true,
  },
  {
    category: 'Motorcycles',
    url: 'https://octopos.olx.com.br/api/v1/render?type=motorcycles-subscription',
    hasApi: true,
  },
  {
    category: 'Auto Parts',
    url: 'https://octopos.olx.com.br/api/v1/render?type=autoparts-subscription',
    hasApi: true,
  },
  {
    category: 'Boats',
    url: 'https://octopos.olx.com.br/api/v1/render?type=boats-subscription',
    hasApi: true,
  },
  {
    category: 'Services',
    url: 'https://adquirir.olx.com.br/planos-services?referral=MYPLAN',
    hasApi: false,
  },
  {
    category: 'Jobs',
    url: 'https://adquirir.olx.com.br/planos-jobs?referral=MYPLAN',
    hasApi: false,
  },
  {
    category: 'Agro',
    url: 'https://adquirir.olx.com.br/planos-agro?referral=MYPLAN',
    hasApi: false,
  },
]

async function olxScript(job: JobInstance) {
  const resultsObj = {}

  for (const project of projects) {
    await randomWait(1000, 2000)
    const { category, url, hasApi } = project
    const infos = hasApi ? await getInfoFromApi(job, url) : await getInfoFromPage(job, url)

    if (!infos?.length) throw new Error('Failed to extract info from api or page.')

    for (const info of infos) {
      resultsObj[`[${category}][${info.title}]`] = info.price
    }
  }

  return resultsObj
}
async function getInfoFromApi(job: JobInstance, url: string) {
  return job.runtime
    .got(url)
    .json<OlxTrackerPricingApi>()
    .then((response) => {
      if (!response.subscriptions) throw new Error('Subscriptions not found')
      return response.subscriptions.map((subscription) => ({
        title: subscription.title,
        price: subscription.totalPrice,
      }))
    })
}

async function getInfoFromPage(job: JobInstance, url: string) {
  const { document } = new JSDOM().window
  const response = await job.runtime.got(url).text()
  document.body.innerHTML = response

  const NextDataRaw = document.querySelector('#__NEXT_DATA__')?.textContent?.trim()
  if (!NextDataRaw) throw new Error('__NEXT_DATA__ not found')
  const NextData = JSON.parse(NextDataRaw) as OlxTrackerPricingNextData

  if (!NextData?.props?.pageProps?.pos?.data?.products) throw new Error('Products not found')

  return NextData.props.pageProps.pos.data.products.map((product) => ({
    title: product.name,
    price: product.price,
  }))
}

// type definitions

// API

interface OlxTrackerPricingApi {
  pos_name: string
  parent_pos_name: string
  limits: Limits
  content: Content
  meta: Meta
  steps?: StepsEntity[] | null
  extras: Extras
  highlights?: null[] | null
  subscriptions?: SubscriptionsEntity[] | null
  couponData: Record<string, unknown>
}
interface Limits {
  balance: BalanceOrConsumption
  consumption: BalanceOrConsumption
}
interface BalanceOrConsumption {
  show: boolean
}
interface Content {
  vertical: Vertical
}
interface Vertical {
  title: string
  subtitle: string
  checkout_button: string
  header_image: string
  header_msite_image: string
  coupon: Coupon
  telesales: Telesales
  professionalPlan: ProfessionalPlan
  headerCards?: HeaderCardsEntity[] | null
  benefits?: BenefitsEntity[] | null
  testimonials?: TestimonialsEntity[] | null
  questions?: QuestionsEntity[] | null
  awards?: AwardsEntity[] | null
  exclusives: Exclusives
  planInfo: PlanInfo
  show_telesales: boolean
}
interface Coupon {
  success: string
  error: string
  invalid: string
  expired: string
}
interface Telesales {
  title: string
  subtitle: string
  subtitle2: string
  phones?: PhonesEntity[] | null
  collector: Collector
}
interface PhonesEntity {
  label: string
  number: string
}
interface Collector {
  url: string
}
interface ProfessionalPlan {
  image: string
}
interface HeaderCardsEntity {
  title: string
  subtitle?: string | null
  icon: string
}
interface BenefitsEntity {
  title: string
  subtitle: string
  anchor: string
}
interface TestimonialsEntity {
  text: string
  author: string
  company: string
}
interface QuestionsEntity {
  title: string
  replies?: string[] | null
}
interface AwardsEntity {
  image: string
  title?: string | null
  text: string
}
interface Exclusives {
  title: string
  subtitle: string
  exclusives?: ExclusivesEntity[] | null
}
interface ExclusivesEntity {
  title: string
  text: string
  image: string
  largeImage?: string | null
}
interface PlanInfo {
  title: string
  explanations?: ExplanationsEntity[] | null
  image: string
}
interface ExplanationsEntity {
  firstLine: string
  secondLine?: string | null
}
interface Meta {
  limit_exceeded: boolean
  name: string
  funnelId: string
}
interface StepsEntity {
  default: boolean
  name: string
}
interface Extras {
  rtb: Rtb
}
interface Rtb {
  tag_id: string
  product_name: string
}
interface SubscriptionsEntity {
  type: string
  title: string
  amount: number
  description: string
  items?: ItemsEntity[] | null
  characteristics?: CharacteristicsEntity[] | null
  references?: string[] | null
  totalPrice: number
  totalReferencePrice: number
  recommended?: boolean | null
}
interface ItemsEntity {
  code: string
  name: string
  isRecurring?: boolean | null
  identifier: string
}
interface CharacteristicsEntity {
  checkmark: boolean
  text: string
  olxpro?: boolean | null
}

// PAGE

interface OlxTrackerPricingNextData {
  props: Props
}
interface Props {
  pageProps: PageProps
}
interface PageProps {
  pos: Pos
}
interface Pos {
  data: Data
}
interface Data {
  products?: ProductsEntity[] | null
}
interface ProductsEntity {
  productKey: string
  name: string
  price: string
}
