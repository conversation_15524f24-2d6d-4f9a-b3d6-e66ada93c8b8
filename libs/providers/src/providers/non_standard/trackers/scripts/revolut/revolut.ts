import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { RevolutCountry } from '../../../../../lib/payments/revolut/types'
import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const revolutCurrenciesWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.CURRENCIES,
  extractionScript: revolutCurrenciesScript,
  URLs: [new URL('https://www.revolut.com/international-transfers/')],
  pngScript: null,
  csvHooks: null,
}

function verifyIfDefaultCountry(country: RevolutCountry) {
  if (country.defaultCurrency === 'EUR' && country.code !== Iso3166Alpha2.DE) return false
  if (country.defaultCurrency === 'USD' && country.code !== Iso3166Alpha2.US) return false
  if (country.defaultCurrency === 'NZD' && country.code !== Iso3166Alpha2.NZ) return false
  if (country.defaultCurrency === 'NOK' && country.code !== Iso3166Alpha2.NO) return false
  if (country.defaultCurrency === 'DKK' && country.code !== Iso3166Alpha2.DK) return false
  if (country.defaultCurrency === 'CHF' && country.code !== Iso3166Alpha2.CH) return false
  if (country.defaultCurrency === 'AUD' && country.code !== Iso3166Alpha2.AU) return false
  if (country.defaultCurrency === 'GBP' && country.code !== Iso3166Alpha2.GB) return false
  return true
}

async function revolutCurrenciesScript(job: JobInstance) {
  const resultsObj = {}
  const sourceCountries = await job.runtime
    .got('https://www.revolut.com/api/remittance/sender-countries', { method: 'GET' })
    .json<RevolutCountry[]>()
    .then((res) => {
      return res.reduce((acc: RevolutCountry[], cur) => {
        if (!verifyIfDefaultCountry(cur)) return acc
        acc.push(cur)
        return acc.sort((a, b) => a.defaultCurrency.localeCompare(b.defaultCurrency))
      }, [])
    })

  for (const sourceCountry of sourceCountries) {
    await job.runtime
      .got('https://www.revolut.com/api/remittance/recipient-countries', {
        method: 'GET',
        searchParams: { senderCountry: sourceCountry.code },
      })
      .json<RevolutCountry[]>()
      .then((res) => {
        if (!res) return

        const reducedTargets = res.reduce((acc: RevolutCountry[], cur) => {
          if (!verifyIfDefaultCountry(cur)) return acc
          acc.push(cur)
          return acc.sort((a, b) => a.defaultCurrency.localeCompare(b.defaultCurrency))
        }, [])

        resultsObj[sourceCountry.defaultCurrency] = JSON.stringify({
          code: sourceCountry.code,
          targetCurrencies: reducedTargets,
        })
      })
  }

  return resultsObj
}
