import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { ZingTargetCurrency } from '../../../../../lib/payments/zing/types'
import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const zingCurrenciesWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.CURRENCIES,
  extractionScript: zingCurrenciesScript,
  URLs: [new URL('https://zing.me/features/multi-currency-card')],
  pngScript: null,
  csvHooks: null,
}

async function zingCurrenciesScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj = {}
  for (const url of zingCurrenciesWW.URLs) {
    await job.runtime
      .got(url.href, { method: 'GET' })
      .text()
      .then((res) => {
        if (!res) return
        document.body.innerHTML = res

        const dom = document?.getElementById('__NEXT_DATA__')?.textContent
        if (!dom) return

        const multiCurrencyCalculator = JSON.parse(dom)?.props?.pageProps?.pageData?.fields?.components?.find(
          (item: { fields: { title: string } }) => item?.fields?.title === 'Multi-currency calculator',
        )

        if (!multiCurrencyCalculator) return

        const sending: ZingTargetCurrency[] = multiCurrencyCalculator.fields.sendingCurrencies.map(
          (item: { fields: ZingTargetCurrency }) => ({
            currencyCode: item.fields.currencyCode,
            walletType: item.fields.walletType,
            deliveryType: item.fields.deliveryType,
          }),
        )
        const buying: ZingTargetCurrency[] = multiCurrencyCalculator.fields.buyingCurrencies.map(
          (item: { fields: ZingTargetCurrency }) => ({
            currencyCode: item.fields.currencyCode,
            walletType: item.fields.walletType,
            deliveryType: item.fields.deliveryType,
          }),
        )

        if (!sending?.length || !buying?.length) return

        for (const source of sending.sort((a, b) => a.currencyCode?.localeCompare(b.currencyCode))) {
          resultsObj[source.currencyCode] = JSON.stringify({
            walletType: source.walletType,
            deliveryType: source.deliveryType,
            targetCurrencies: buying
              .sort((a, b) => a.currencyCode?.localeCompare(b.currencyCode))
              .map((item: { currencyCode: string; walletType: string; deliveryType: string }) => JSON.stringify(item)),
          })
        }
      })
  }

  return resultsObj
}
