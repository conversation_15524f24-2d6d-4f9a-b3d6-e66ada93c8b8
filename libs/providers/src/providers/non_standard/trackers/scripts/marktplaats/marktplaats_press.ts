import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const marktplaatsPageStateNL: TrackerConfig = {
  iso: Iso3166Alpha2.NL,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: marktplaatsScript,
  URLs: [new URL('https://www.marktplaatsperskamer.nl')],
  pngScript: null,
  csvHooks: null,
}

async function marktplaatsScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}

  const response = await job.runtime
    .got(marktplaatsPageStateNL.URLs[0].href, {
      method: 'GET',
      retry: {
        limit: 10,
        statusCodes: [403],
      },
    })
    .text()

  if (!response) throw new Error('Failed to retrieve html')

  document.body.innerHTML = response
  const articles = document.querySelectorAll('.blog-recent > .post')

  articles.forEach((article) => {
    const id = article.classList.toString().match(/post-(\d+)/)?.[1]
    const title = article.querySelector('.title')?.textContent
    const href = article.querySelector('.entire-meta-link')?.getAttribute('href')

    if (title && id && href) {
      const uniqueTitle = `[${id}]${title.trim()}`
      resultsObj[uniqueTitle] = href
    }
  })

  if (!Object.keys(resultsObj)?.length) {
    throw new Error('No results found')
  }

  return resultsObj
}
