import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const marktplaatsProPricingNL: TrackerConfig = {
  iso: Iso3166Alpha2.NL,
  trackerType: TrackerType.PRICING_PRO,
  extractionScript: marktplaatsScript,
  URLs: [new URL('https://www.marktplaats.nl/i/help/over-marktplaats/voorwaarden-en-privacybeleid/admarkt-tarieven/')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

export const marktplaatsLoosePricingNL: TrackerConfig = {
  iso: Iso3166Alpha2.NL,
  trackerType: TrackerType.PRICING_LOOSE,
  extractionScript: marktplaatsScript,
  URLs: [
    new URL(
      'https://www.marktplaats.nl/i/help/over-marktplaats/voorwaarden-en-privacybeleid/voorwaarden-adverteren-via-een-data-leverancier/losse-plaatsingen/',
    ),
  ],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

export const marktplaatsSubscriptionPricingNL: TrackerConfig = {
  iso: Iso3166Alpha2.NL,
  trackerType: TrackerType.PRICING_SUBSCRIPTION,
  extractionScript: marktplaatsScript,
  URLs: [
    new URL(
      'https://www.marktplaats.nl/i/help/over-marktplaats/voorwaarden-en-privacybeleid/voorwaarden-adverteren-via-een-data-leverancier/abonnement/',
    ),
  ],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

function getPrefix(url: string) {
  if (url.includes('admarkt-tarieven')) {
    return '(Marketplace Pro)'
  }
  if (url.includes('losse-plaatsingen')) {
    return '(Marketplace Loose Placements)'
  }
  if (url.includes('abonnement')) {
    return '(Subscription)'
  }

  return '()'
}

async function marktplaatsScript(job: JobInstance, config: TrackerConfig) {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}

  for (const URL of config.URLs) {
    const prefix = getPrefix(URL.href)

    await job.runtime
      .got(URL.href, {
        retry: {
          limit: 3,
        },
      })
      .text()
      .then((res) => {
        if (!res) throw new Error('Failed to retrieve html')
        document.body.innerHTML = res

        const allAccordions = Array.from(document.querySelectorAll('.accordion-block'))
        if (allAccordions?.length) {
          for (const accordion of allAccordions) {
            processAccordion(prefix, accordion, resultsObj)
          }
        } else {
          const allTables = Array.from(document.querySelectorAll('.partner-price-table, table'))

          for (const table of allTables) {
            processTable(prefix, table, resultsObj)
          }
        }
      })
  }

  return resultsObj
}

function processTable(prefix: string, table: Element, resultsObj: TrackerScriptOutput) {
  const rows = Array.from(table.querySelectorAll('tr'))
  if (rows.length === 0) return

  // Check if this is a table with header cells (th elements) in the first row
  const firstRowCells = Array.from(rows[0].querySelectorAll('th, td'))
  const hasHeaderRow = firstRowCells.some((cell) => cell.tagName.toLowerCase() === 'th')

  if (hasHeaderRow) {
    const headers = firstRowCells.map((cell) => cell.textContent?.trim() || '')

    const productColumnIndex = headers.findIndex((h) => h === 'Product')
    const prijsColumnIndex = headers.findIndex((h) => h === 'Prijs')

    for (let i = 1; i < rows.length; i++) {
      const cells = Array.from(rows[i].querySelectorAll('td'))
      if (cells.length === 0) continue

      const category = cells[0]?.textContent?.trim() || ''
      if (!category) continue

      // If we have both Product and Prijs columns, handle it specially
      if (
        productColumnIndex > 0 &&
        prijsColumnIndex > 0 &&
        cells.length > Math.max(productColumnIndex, prijsColumnIndex)
      ) {
        const product = cells[productColumnIndex]?.textContent?.trim() || ''
        const prijs = cells[prijsColumnIndex]?.textContent?.trim() || ''

        if (product && prijs) {
          resultsObj[`${prefix} -> ${category} -> ${product}`] = prijs
        }
      } else {
        // Standard handling for other tables
        for (let j = 1; j < Math.min(cells.length, headers.length); j++) {
          const cellValue = cells[j]?.textContent?.trim() || ''
          if (!cellValue) continue

          const headerText = headers[j]
          if (!headerText) continue

          const key = `${prefix} -> ${category} -> ${headerText}`
          resultsObj[key] = cellValue
        }
      }
    }
  } else {
    // This is a table without header cells
    for (const row of rows) {
      const cells = Array.from(row.querySelectorAll('td'))

      if (cells.length === 2) {
        const key = cells[0]?.textContent?.trim() || ''
        const value = cells[1]?.textContent?.trim() || ''

        if (key && value) {
          resultsObj[`${key}`] = value
        }
      } else if (cells.length >= 3) {
        const category = cells[0]?.textContent?.trim() || ''
        const item = cells[1]?.textContent?.trim() || ''
        const value = cells[2]?.textContent?.trim() || ''

        if (category && item && value) {
          resultsObj[`${prefix} -> ${category} -> ${item}`] = value
        }
      }
    }
  }
}

function processAccordion(prefix: string, accordion: Element, resultsObj: TrackerScriptOutput) {
  const title = accordion.querySelector('.accordion-label')?.textContent?.trim()
  const allTables = Array.from(accordion.querySelectorAll('.partner-price-table, table'))

  for (const table of allTables) {
    processTable(`${prefix} -> ${title}`, table, resultsObj)
  }
}
