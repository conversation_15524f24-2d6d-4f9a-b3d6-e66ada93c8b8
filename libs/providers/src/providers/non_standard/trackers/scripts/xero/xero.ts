import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const xeroPageStateGB: TrackerConfig = {
  iso: Iso3166Alpha2.GB,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: xeroScript,
  URLs: [new URL('https://blog.xero.com/uk/product-updates/')],
  pngScript: null,
  csvHooks: null,
}

export const xeroPageStateAU: TrackerConfig = {
  iso: Iso3166Alpha2.AU,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: xeroScript,
  URLs: [new URL('https://blog.xero.com/au/product-updates/')],
  pngScript: null,
  csvHooks: null,
}

const typeToConfig = {
  trackerXeroPageStateGB: xeroPageStateGB,
  trackerXeroPageStateAU: xeroPageStateAU,
}

async function xeroScript(job: JobInstance) {
  const config = typeToConfig[job.type]
  const resultsObj: TrackerScriptOutput = {}

  await job.runtime.page.goto(config.URLs[0].href, { waitUntil: 'networkidle2' })
  const html = await job.runtime.page.content()
  if (!html) throw new Error('Failed to retrieve html')

  const { document } = new JSDOM(html).window

  const articles = document.querySelectorAll('.bg-brand-back, article')

  articles.forEach((article) => {
    const h2Elements = article.querySelectorAll('h2')
    const cta = article.querySelector<HTMLAnchorElement>('a[aria-label]')
    const url = cta?.href

    if (h2Elements.length && url) {
      let combinedTitle = ''

      h2Elements.forEach((h2, index) => {
        const h2Text = h2.textContent?.trim()
        if (h2Text) {
          if (index === 0) {
            combinedTitle = h2Text
          } else {
            combinedTitle += ' - ' + h2Text
          }
        }
      })

      if (combinedTitle) {
        resultsObj[combinedTitle] = url
      }
    }
  })

  if (!Object.keys(resultsObj)?.length) throw new Error('No results found')
  return resultsObj
}
