import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const xeroNewsWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.NEWS,
  extractionScript: xeroNewsScript,
  URLs: [new URL('https://innovations.xero.com/')],
  pngScript: null,
  csvHooks: null,
}

async function xeroNewsScript(job: JobInstance) {
  const { document } = new JSDOM().window

  await job.runtime.page.goto(xeroNewsWW.URLs[0].href, { waitUntil: 'networkidle2' })
  const html = await job.runtime.page.content()

  if (!document) throw new Error('Failed to retrieve html')
  document.body.innerHTML = html

  const features = Array.from(document.querySelectorAll('.site-feature'))

  const resultsObj = {}
  features.forEach((feature) => {
    const title = feature.querySelector('.site-feature__title')?.textContent?.trim()
    const id = feature.getAttribute('id')
    if (title && id) {
      resultsObj[`${xeroNewsWW.URLs[0].href}#${id}`] = title
    }
  })

  if (!Object.keys(resultsObj)?.length) {
    throw new Error(`Unable to retrieve parsed news items`)
  }

  return resultsObj
}
