import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const billdotcomTOS: TrackerConfig = {
  iso: Iso3166Alpha2.US,
  trackerType: TrackerType.TOS,
  extractionScript: billdotcomTOSScript,
  URLs: [new URL('https://www.bill.com/legal/terms-of-service')],
  pngScript: null,
  csvHooks: null,
}
async function billdotcomTOSScript(job: JobInstance) {
  return job.runtime
    .got(billdotcomTOS.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response
      const resultsObj: TrackerScriptOutput = {}

      const termsOfService = document.querySelector('.text-rich-text')
      if (!termsOfService) throw new Error('failed to retrieve TOS element')

      const childNodes = Array.from(termsOfService.childNodes)
      const textContents = childNodes
        .filter((node) => node.textContent?.trim() !== '')
        .map((node) => node.textContent?.trim())

      const formattedText = textContents.join('\n')
      resultsObj['tos'] = formattedText

      return resultsObj
    })
}
