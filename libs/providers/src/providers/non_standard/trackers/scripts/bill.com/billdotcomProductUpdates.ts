import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { generateId, getJSDOMDocument } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const billdotcomProductUpdatesUS: TrackerConfig = {
  iso: Iso3166Alpha2.US,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: billdotcomProductUpdates,
  URLs: [new URL('https://www.bill.com/product-updates')],
  pngScript: null,
  csvHooks: null,
}

async function billdotcomProductUpdates(job: JobInstance) {
  let { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}
  document = await getJSDOMDocument(job.runtime, billdotcomProductUpdatesUS.URLs[0].href, document)
  if (!document) throw new Error('Failed to retrieve html')
  const posts = document.querySelectorAll('.product-update-collection')

  posts.forEach((post) => {
    const title = post.querySelector('h2').textContent
    const text = post.querySelector('.product-update-height-fixed p')?.textContent
    if (title && text) {
      const uniqueId = generateId(title + `-${text}`)
      const uniqueTitle = `${title.trim()}[${uniqueId}]`
      resultsObj[uniqueTitle] = text
    }
  })

  if (!Object.keys(resultsObj)?.length) throw new Error('No results found')
  return resultsObj
}
