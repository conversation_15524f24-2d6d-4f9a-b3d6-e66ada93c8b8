import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const billdotcomPricingUS: TrackerConfig = {
  iso: Iso3166Alpha2.US,
  trackerType: TrackerType.PRICING,
  extractionScript: billdotcomScript,
  URLs: [new URL('https://www.bill.com/product/pricing')],
  pngScript: null,
  csvHooks: null,
}

async function billdotcomScript(job: JobInstance) {
  return job.runtime
    .got(billdotcomPricingUS.URLs[0].href, {})
    .text()
    .then((response) => {
      if (!response) throw new Error('Failed to retrieve body from request')

      const { document } = new JSDOM().window
      document.body.innerHTML = response

      const items: { [key: string]: string }[] = []

      document.querySelectorAll('div[class^="table-line"]').forEach((element) => {
        const leftItem = element.children[0]?.textContent?.trim()
        const rightItem = element.children[1]?.textContent?.trim()

        if (!leftItem || !rightItem) return
        items.push({
          [leftItem]: rightItem.replace(/([A-Z])/g, ' $1').trim(),
        })
      })

      let tabsHeader = {}
      document.querySelectorAll('.up-tab-menu-product > a').forEach((element) => {
        const leftItem = element.querySelector('div')?.textContent?.trim()
        if (leftItem) {
          tabsHeader = { ...tabsHeader, [leftItem]: element.getAttribute('data-w-tab') }
        }
      })

      for (const header in tabsHeader) {
        const targetArray = Array.from(
          document.querySelectorAll(`.w-tab-content > [data-w-tab="${tabsHeader[header]}"] .w-tab-menu [data-w-tab]`),
        )
        if (targetArray.length) {
          const pricings = targetArray.flatMap((element) => {
            const partialName = element.querySelector('div')?.textContent?.trim() || ''
            const dataTab = element.getAttribute('data-w-tab')?.trim()
            if (!dataTab) return []
            const restData = Array.from(
              document.querySelectorAll(`.w-tab-content > [data-w-tab="${dataTab}"] .card-wrapper`),
            )
            if (!restData) return []

            const businessRes = restData.flatMap((innerElement) => {
              let restName = innerElement.querySelector('.heading-style-h4')?.textContent || ''
              if (!restName) return []
              if (restName === partialName) restName = ''
              let pricing = getPricing(innerElement)
              if (!pricing)
                pricing =
                  innerElement.querySelector(':scope > div.text-size-xlarge.max-width-xsmall.text-weight-light')
                    ?.textContent || ''
              return {
                name: (header + ' ' + partialName + ' ' + restName).trim(),
                pricing,
              }
            })

            return businessRes
          })

          items.push(...pricings.map((item) => ({ [item.name]: item.pricing })))
        } else {
          const accountsRes = Array.from(
            document.querySelectorAll(`[data-w-tab="${tabsHeader[header]}"] > .w-layout-grid > .card-wrapper`),
          ).flatMap((element) => {
            const restName = element.querySelector('.heading-style-h4')?.textContent || ''

            const pricing = getPricing(element)
            return { name: (header + ' ' + restName).trim(), pricing }
          })
          items.push(...accountsRes.map((item) => ({ [item.name]: item.pricing })))
        }
      }

      let resultsObj: TrackerScriptOutput = {}
      for (const product of items) {
        resultsObj = Object.assign(resultsObj, product)
      }

      return resultsObj
    })
}

function getPricing(innerElement: Element) {
  const leftPricing =
    innerElement.querySelector('div.flex-horizontal > div.text-size-xxlarge.text-weight-light')?.textContent || ''
  const rightPricing = innerElement.querySelector('div.text-weight-light.align-center-vertical')?.textContent || ''

  return (leftPricing + ' ' + rightPricing).trim()
}
