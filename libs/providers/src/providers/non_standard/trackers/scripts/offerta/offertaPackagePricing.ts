import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const offertaPackagePricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING_PACKAGES,
  extractionScript: offertaPackagePricing,
  URLs: [new URL('https://offerta.se/anslut-ditt-foretag/skraddarsytt')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

async function offertaPackagePricing(job: JobInstance) {
  const html = await job.runtime
    .got(offertaPackagePricingSE.URLs[0], {
      retry: {
        limit: 5,
        statusCodes: [502],
      },
    })
    .text()

  if (!html) {
    throw new Error('Failed to retrieve html after retries.')
  }

  const { document } = new JSDOM().window
  document.body.innerHTML = html
  const packageCardHeaders = document.querySelectorAll('div[class^="PriceCard_headerContainer"]')
  if (!packageCardHeaders?.length) throw new Error('Package card headers not found')

  const results = {}

  for (const header of packageCardHeaders) {
    const packageName = header.querySelector('h3')?.textContent?.trim()
    if (!packageName) throw new Error('Package name not found')
    const priceString = header.querySelector('div[class^="PriceCard_price"]')?.textContent?.trim()
    if (!priceString) throw new Error('Price not found')

    Object.assign(results, { [packageName]: priceString })
  }

  if (!Object.entries(results)?.length) throw new Error('No results found')
  return results
}
