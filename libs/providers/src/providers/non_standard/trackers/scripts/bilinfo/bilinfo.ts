import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { removeTabNewline, wait } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const bilinfoPricingDK: TrackerConfig = {
  iso: Iso3166Alpha2.DK,
  trackerType: TrackerType.PRICING_VEHICLES,
  extractionScript: bilinfoScript,
  URLs: [new URL('https://bilinfo.dk/priser')],
  pngScript: null,
  csvHooks: null,
}

const retries = 10

async function bilinfoScript(job: JobInstance) {
  const { document } = new JSDOM().window

  let sections: HTMLElement[] | undefined

  for (let idx = retries; idx > 0; idx--) {
    await job.runtime.page.setViewport({
      width: 1920,
      height: 1080,
      isMobile: false,
      isLandscape: false,
      hasTouch: false,
    })

    await job.runtime.page.goto(bilinfoPricingDK.URLs[0].href, { waitUntil: 'networkidle2' })
    document.body.innerHTML = await job.runtime.page.content()

    if (document.body.innerHTML.match(/your ip has been blocked/i)) continue

    sections = Array.from(document.querySelectorAll('section'))
    if (sections?.length) break

    await job.runtime.refreshProxies()
    await wait(1000)
  }

  if (!sections?.length) throw new Error('Failed to retrieve sections')

  const products = []
  for (const section of sections) {
    // Main key title
    const rowTitles = section.querySelector('div[class*="Subscription"]')
    if (!rowTitles) continue

    const productTitle = rowTitles.querySelector('div[class^="MuiBox"]')
    let brand = productTitle.querySelector('img')?.getAttribute('src')
    if (brand.match(/bilinfo/i)) brand = 'Bilinfo'
    else if (brand.match(/bilbasen/i)) brand = 'Bilbasen'

    let key = brand

    const childNodes = productTitle.querySelector('p')?.childNodes
    if (childNodes?.length) {
      for (const node of childNodes) {
        const text = removeTabNewline(node.textContent?.trim() ?? '')
        if (text && !text.match(/.css/)) key += ` > ${removeTabNewline(node.textContent?.trim() ?? '')}`
      }
    }

    // Other row titles
    const abc = Array.from(rowTitles.querySelectorAll('div[class^="Subscription_listItem"]'))
    if (!abc?.length) continue

    const keys = []

    for (const row of abc) {
      const text = row.textContent?.replaceAll('Bliv ny forhandler', '').trim()

      if (text) keys.push(`${key} > ${text}`)
    }

    // VALUES
    const comparisons = Array.from(section.querySelectorAll('div[class*="comparisonListStyles_paper"]'))
    if (!comparisons?.length) continue

    const obj = {}
    for (const key of keys) {
      obj[key] = {}
    }

    const preObj = {}

    for (const comparison of comparisons) {
      const mainHeader = comparison.querySelector('div[class*="comparisonListStyles_header"] h4')?.textContent?.trim()
      if (!mainHeader) continue

      const subHeaders = Array.from(comparison.querySelectorAll('div[class*="comparisonListStyles_header"] p'))
      if (subHeaders?.length) {
        if (!preObj[key]) preObj[key] = {}
        for (const subHeader of subHeaders) {
          const val = Array.from(subHeader.childNodes)
            .reduce((acc, cur) => {
              if (cur.nodeType === cur.TEXT_NODE) {
                if (acc.length) acc += ' / '
                acc += `${cur.textContent?.trim()}`
              }
              return acc
            }, '')
            ?.trim()
          if (val) {
            preObj[key][mainHeader] = val
          }
        }
      }

      const subValues = Array.from(comparison.querySelectorAll('div[class*="comparisonListStyles_icons"] p')).map(
        (element) => {
          if (element.querySelector('img'))
            return element.querySelector('img').getAttribute('src').match(/check/i) ? 'Yes' : 'No'

          return element.textContent
        },
      )

      if (subValues?.length !== keys.length) continue

      for (let i = 0; i < keys.length; i++) {
        obj[keys[i]][mainHeader] = subValues[i]
      }
    }

    if (Object.keys(preObj).length) {
      products.push(preObj)
    }
    products.push(obj)
  }

  const resultsObj: TrackerScriptOutput = {}
  Object.assign(
    resultsObj,
    ...products.map((product) => {
      const objToPush = {}
      for (const [key, value] of Object.entries(product)) {
        objToPush[key] = JSON.stringify(value)
      }
      return objToPush
    }),
  )

  return resultsObj
}
