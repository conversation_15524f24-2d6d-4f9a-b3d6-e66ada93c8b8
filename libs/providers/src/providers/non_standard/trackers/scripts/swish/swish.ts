import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { wait } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const swishSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: swishScript,
  URLs: [new URL('https://www.swish.nu/about-swish')],
  pngScript: null,
  csvHooks: null,
}

async function swishScript(job: JobInstance) {
  const { document } = new JSDOM().window

  await job.runtime.page.goto(swishSE.URLs[0].href, {
    waitUntil: 'networkidle2',
  })

  await job.runtime.page.waitForSelector('.report', { timeout: 5000 })
  const html = await job.runtime.page.content()

  if (!document) throw new Error('Failed to retrieve html')
  document.body.innerHTML = html

  const reportNodes = Array.from(document.querySelectorAll('.report'))
  if (!reportNodes?.length) throw new Error('Unable to retrieve reports')

  let yearlyReportURL = ''
  let monthlyReportURL = ''
  let yearlyReportDesignation = ''
  let monthlyReportDesignation = ''

  for (const reportNode of reportNodes) {
    const reportHeader = reportNode.querySelector('p')?.textContent
    if (!reportHeader) throw new Error('Unable to extract header')
    const reportURL = reportNode.querySelector('a')?.getAttribute('href')
    if (!reportURL) throw new Error('Unable to extract URL')
    if (reportHeader?.toLowerCase().includes('annual')) {
      yearlyReportURL = 'https:' + reportURL

      yearlyReportDesignation = reportHeader.toLowerCase().replace('Swish annual report', '')?.trim()
    }
    if (reportHeader?.toLowerCase().includes('stats')) {
      monthlyReportURL = 'https:' + reportURL
      const formattedHeader = reportHeader.toLowerCase().replace('stats', '')?.trim()
      monthlyReportDesignation = formattedHeader
    }
  }
  await wait(2000)
  const yearlyRawPDF = await job.runtime.got(yearlyReportURL, { responseType: 'buffer' })
  if (!yearlyRawPDF.body?.length) throw new Error('Unable to retrieve yearly PDF')
  await wait(2000)
  const monthlyRawPDF = await job.runtime.got(monthlyReportURL, { responseType: 'buffer' })
  if (!monthlyRawPDF.body?.length) throw new Error('Unable to retrieve monthly PDF')

  const yearlyPDF = yearlyRawPDF.body.toString('base64')
  const monthlyPDF = monthlyRawPDF.body.toString('base64')
  return {
    [yearlyReportDesignation]: yearlyPDF,
    [monthlyReportDesignation]: monthlyPDF,
  }
}
