import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const leboncoinPageStateFR: TrackerConfig = {
  iso: Iso3166Alpha2.FR,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: leboncoinScript,
  URLs: [new URL('https://presse.leboncoincorporate.com/news/?page=1')],
  pngScript: null,
  csvHooks: null,
}

async function leboncoinScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const { document } = new JSDOM().window
  const resultsObj: TrackerScriptOutput = {}

  const response = await job.runtime
    .got(leboncoinPageStateFR.URLs[0].href, {
      method: 'GET',
      retry: {
        limit: 10,
        statusCodes: [403],
      },
    })
    .text()

  if (!response) throw new Error('Failed to retrieve html')

  document.body.innerHTML = response
  const articles = document.querySelectorAll('.gallery-item')

  articles.forEach((article) => {
    const card = article.querySelector('.card')
    const title = card?.querySelector('.card-title')?.textContent
    const href = card?.querySelector('.card-btn')?.getAttribute('href')
    const date = card?.querySelector('.card-subtitle-date')?.textContent

    if (title && href) {
      const uniqueTitle = `[${date?.trim()}]${title.trim()}`
      resultsObj[uniqueTitle] = `http:${href}`
    }
  })

  if (!Object.keys(resultsObj)?.length) {
    throw new Error('No results found')
  }

  return resultsObj
}
