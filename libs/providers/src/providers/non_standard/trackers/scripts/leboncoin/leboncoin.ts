import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'
import { getJSDOMDocument } from '@datagatherers/worker-utils'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const leboncoinPricingFR: TrackerConfig = {
  iso: Iso3166Alpha2.FR,
  trackerType: TrackerType.PRICING,
  extractionScript: leboncoinScript,
  URLs: [new URL('https://www.leboncoin.fr/options/prix')],
  pngScript: null,
  csvHooks: null,
}

async function leboncoinScript(job: JobInstance) {
  const datadome = process.argv.slice(-1)[0]

  let { document } = new JSDOM().window
  document = await getJSDOMDocument(job.runtime, leboncoinPricingFR.URLs[0].href, document, [
    { name: 'datadome', value: datadome, domain: '.leboncoin.fr' },
  ])
  if (!document) throw new Error('Failed to retrieve html')

  const script = document.querySelector('html body script#__NEXT_DATA__')?.innerHTML.trim()
  if (!script?.length) throw new Error('Unable to retrieve JSON')
  let categories: CgvPrices
  try {
    const parsed: Response = JSON.parse(script)
    categories = parsed?.props?.pageProps?.cgvPrices
    if (!categories || !Object.keys(categories)?.length) throw new Error()
  } catch {
    throw new Error('Unable to parse/retrieve JSON')
  }

  const categoriesSelector = Array.from(document.querySelector('#category-selector')?.children ?? [])
  if (!categoriesSelector?.length) throw new Error('Unable to retrieve raw category map')
  let categoryMap: { [index: string]: string } = {}
  for (const selector of categoriesSelector) {
    const value = selector.getAttribute('value')
    const name = selector.textContent
    if (!value?.length || !name?.length) continue
    const obj = { [value]: name }
    categoryMap = Object.assign(categoryMap, obj)
  }
  const products = []
  for (const key in categories) {
    const keyName = categoryMap[key] ?? key ?? 'N/A'
    const priceCats = categories[key]?.prices[0]?.services
    if (!priceCats) throw new Error('Unable to parse price categories')
    const parsed = [
      ...(priceCats.bumps ?? []),
      ...(priceCats.fim ?? []),
      ...(priceCats.galleries ?? []),
      ...(priceCats.other ?? []),
    ].flatMap((priceCat) => {
      try {
        const { technicalName, priceHT, priceTTC } = priceCat
        return [
          { [`{${keyName}_${technicalName}_PriceWithoutTax`]: (Number(priceHT) / 100).toFixed(2) },
          { [`{${keyName}_${technicalName}_PriceWithTax`]: (Number(priceTTC) / 100).toFixed(2) },
        ]
      } catch {
        throw new Error('Unable to parse prices')
      }
    })
    if (!parsed?.length) throw new Error('No parsed products')
    products.push(...parsed)
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const product of products) {
    resultsObj = Object.assign(resultsObj, product)
  }
  return resultsObj
}
interface Response {
  props: Props
}
interface Props {
  pageProps: PageProps
}
interface PageProps {
  cgvPrices: CgvPrices
}
interface CgvPrices {
  [index: string]: Prices
}
interface Prices {
  prices: PricesEntity[]
}
interface PricesEntity {
  services: Services
}
interface Services {
  fim?: FimEntityOrBumpsEntityOrGalleriesEntityOrOtherEntity[] | null
  bumps?: FimEntityOrBumpsEntityOrGalleriesEntityOrOtherEntity[] | null
  galleries?: FimEntityOrBumpsEntityOrGalleriesEntityOrOtherEntity[] | null
  other?: FimEntityOrBumpsEntityOrGalleriesEntityOrOtherEntity[] | null
}
interface FimEntityOrBumpsEntityOrGalleriesEntityOrOtherEntity {
  displayName: string
  priceHT: number
  priceTTC: number
  technicalName: string
}
