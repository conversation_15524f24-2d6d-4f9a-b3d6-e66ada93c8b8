import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const wiseNewsWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.NEWS,
  extractionScript: wiseNewsScript,
  URLs: [new URL('https://newsroom.wise.com/en-NAM/')],
  pngScript: null,
  csvHooks: null,
}

async function wiseNewsScript(job: JobInstance) {
  const { document } = new JSDOM().window

  const resultsObj = {}

  await job.runtime
    .got(wiseNewsWW.URLs[0].href, {
      retry: {
        limit: 3,
      },
    })
    .text()
    .then((response) => {
      if (!response) throw new Error('Failed to retrieve html')
      document.body.innerHTML = response

      const newsTitles = Array.from(
        document.querySelectorAll('section[class^="latest"] div[id="all"] div[class*="text"]'),
      )

      if (newsTitles?.length) {
        newsTitles.forEach((news) => {
          const newsDate = news.querySelector('time')?.getAttribute('datetime')
          if (newsDate) {
            const title = news.querySelector('h2 a')?.textContent?.trim()
            const hrefSplit = news.querySelector('h2 a')?.getAttribute('href')?.split('/')

            const id = Number(hrefSplit?.[hrefSplit.length - 2].split('-')?.[0])
            if (id && title) {
              resultsObj[`${newsDate} - ${id}`] = title
            }
          }
        })
      }
    })

  if (!Object.keys(resultsObj)?.length) {
    throw new Error(`Unable to retrieve parsed news items`)
  }

  return resultsObj
}
