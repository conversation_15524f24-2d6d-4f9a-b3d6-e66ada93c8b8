import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { configuration } from '../../../../../lib/payments/wise/constants'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const wiseCurrenciesNewWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.CURRENCIES,
  extractionScript: wiseCurrenciesScript_new,
  URLs: [new URL('https://wise.com/gb/pricing/send-money?sourceAmount=1000&sourceCcy=GBP&targetCcy=EUR&tab=0')],
  pngScript: null,
  csvHooks: [],
}

export async function wiseCurrenciesScript_new(job: JobInstance) {
  const guestId = await job.runtime
    .got('https://wise.com/gateway/v1/guest-send-sessions', {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      retry: configuration.retry,
      json: {
        capabilities: null,
        configuration: 'SEND_MONEY_PRICING_PAGE',
        presets: {
          sourceCurrency: 'GBP',
          sourceAmount: 1000,
          targetCurrency: 'EUR',
          profileType: 'PERSONAL',
          payInCountry: 'gb',
        },
      },
    })
    .json<{ id: string }>()
    .then((res) => res.id)

  const currencies = await job.runtime
    .got(`https://wise.com/gateway/v1/guest-send-sessions/${guestId}/calculator`, {
      method: 'POST',
      retry: configuration.retry,
      headers: {
        'content-type': 'application/json',
      },
      json: { version: 1 },
    })
    .json<{
      metadata: {
        currencies: {
          code: string
          symbol: string
          name: string
          countryKeywords: string[]
          supportsDecimals: boolean
        }[]
      }

      source: {
        currencies: {
          sections: { title: string; options: string[] }[]
        }
      }
    }>()
    .then((res) => {
      return res?.metadata?.currencies
        ?.map((currency) => ({ code: currency.code, supportsDecimals: currency.supportsDecimals }))
        .sort((a, b) => a.code.localeCompare(b.code))
    })

  const obj = currencies.reduce((a, v) => ({ ...a, [v.code]: { supportsDecimals: v.supportsDecimals } }), {})
  const allTargetCurrencies = []

  for (const key of Object.keys(obj)) {
    const additionalProperties = await job.runtime
      .got(`https://wise.com/gateway/v1/guest-send-sessions/${guestId}/calculator/action`, {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },

        retry: configuration.retry,
        json: { action: 'UPDATE_SOURCE_CURRENCY', value: { currency: key }, version: 1 },
      })
      .json<{
        paymentMethod: {
          selectPaymentMethod: {
            sections: {
              title: string
              options: { method: string }[]
            }[]
          }
        }
        source: {
          currencies: {
            sections: { title: string; options: string[] }[]
          }
        }

        target: {
          currencies: {
            sections: { title: string; options: string[] }[]
          }
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        errors?: { code: string; message: string; arguments: any[] }[]
      }>()

    if (!additionalProperties || additionalProperties.errors) {
      delete obj[key]
      continue
    }

    const targetCurrencies = additionalProperties.target.currencies.sections.find((section) =>
      section.title.match(/all/i),
    )?.options
    if (!targetCurrencies?.length) {
      delete obj[key]
      continue
    }

    allTargetCurrencies.push(...targetCurrencies)

    if (job.provider === 'wise') continue

    const targetCurrenciesOptions = []

    for (const targetCurrency of targetCurrencies) {
      const updateTargetCurrency = await job.runtime
        .got(`https://wise.com/gateway/v1/guest-send-sessions/${guestId}/calculator/action`, {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },

          retry: configuration.retry,
          json: { action: 'UPDATE_TARGET_CURRENCY', value: { currency: targetCurrency }, version: 1 },
        })
        .json<{
          paymentMethod: {
            selectPaymentMethod: {
              sections: {
                title: string
                options: { method: string; enabled?: boolean }[]
              }[]
            }
          }
          source: {
            currencies: {
              sections: { title: string; options: string[] }[]
            }
          }

          target: {
            currencies: {
              sections: { title: string; options: string[] }[]
            }
          }
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          errors?: { code: string; message: string; arguments: any[] }[]
        }>()

      if (!updateTargetCurrency || updateTargetCurrency.errors) {
        //delete obj[key]
        continue
      }

      //if()

      const hasBankTransferPayIn = updateTargetCurrency.paymentMethod.selectPaymentMethod.sections.some((section) =>
        section.options.some((option) => option.method === 'BANK_TRANSFER' && option.enabled),
      )
      const hasBankgiroPayIn = updateTargetCurrency.paymentMethod.selectPaymentMethod.sections.some((section) =>
        section.options.some((option) => option.method === 'BANKGIRO' && option.enabled),
      )
      const hasBalancePayIn = updateTargetCurrency.paymentMethod.selectPaymentMethod.sections.some((section) =>
        section.options.some((option) => option.method === 'BALANCE' && option.enabled),
      )
      const hasPISPPayIn = updateTargetCurrency.paymentMethod.selectPaymentMethod.sections.some((section) =>
        section.options.some((option) => option.method === 'PISP' && option.enabled),
      )

      targetCurrenciesOptions.push({
        currency: targetCurrency,
        hasBankTransferPayIn,
        hasBalancePayIn,
        hasBankgiroPayIn,
        hasPISPPayIn,
      })
    }

    const popularCurrencies = additionalProperties.target.currencies.sections
      .find((section) => section.title.match(/popular/i))
      ?.options.map((item) => ({ currency: item }))

    obj[key] = {
      ...obj[key],
      targetCurrencies: targetCurrenciesOptions,
      popularCurrencies,
    }
  }

  const resultsObj = {}

  for (const key of Object.keys(obj)) {
    switch (job.provider) {
      case 'wise': {
        resultsObj[key] = JSON.stringify([...new Set(allTargetCurrencies)].sort((a, b) => a.localeCompare(b)))
        break
      }
      case 'wise_unified':
      case 'wise_unified_new': {
        resultsObj[key] = JSON.stringify(obj[key])
        break
      }
      default:
        throw new Error(`Unexpected provider: ${job.provider}`)
    }
  }
  return resultsObj
}
