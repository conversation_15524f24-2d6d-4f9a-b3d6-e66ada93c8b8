import { JSDOM } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { configuration } from '../../../../../lib/payments/wise/constants'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const wisePageWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.PAGE_STATE,
  extractionScript: wisePageScript,
  URLs: [new URL('https://wise.com/gb/pricing/')],
  pngScript: null,
  csvHooks: null,
}

export async function wisePageScript(job: JobInstance) {
  const response = await job.runtime.got(wisePageWW.URLs[0], { retry: configuration.retry }).text()

  const { document } = new JSDOM().window
  document.body.innerHTML = response

  const resultsObj = {}

  const optionItems = document
    .querySelector('section[class="section HOME_SECTION_2"]')
    ?.querySelectorAll('li[class^="option_option__OjD8j"]')

  optionItems.forEach((item) => {
    const titleElement = item
      .querySelector('div[class^="option_option__text"]')
      ?.querySelector('p[class^="mw-body mw-body-2-bold"]')
    const titleText = titleElement?.textContent?.trim()
    const feeElement = item.querySelector('p[class^="mw-body mw-body-3-bold option_option__right"]')
    const feeText = feeElement?.textContent?.trim()

    if (titleText && feeText) {
      resultsObj[titleText] = feeText
    }
  })

  if (Object.entries(resultsObj).length === 0) throw new Error('No data found')
  return resultsObj
}
