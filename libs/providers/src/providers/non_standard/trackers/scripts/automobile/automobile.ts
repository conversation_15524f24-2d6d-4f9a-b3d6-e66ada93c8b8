import PDFParser from 'pdf2json'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import { diffsCsvHook, jsonSnapCsvHook } from '../../csvHooks'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'
import type { Output } from 'pdf2json'

export const automobilePricingIT: TrackerConfig = {
  iso: Iso3166Alpha2.IT,
  trackerType: TrackerType.PRICING,
  extractionScript: automobileScript,
  URLs: [new URL('https://www.automobile.it/pdf/business/listino-prezzi-automobile.pdf')],
  pngScript: null,
  csvHooks: [diffsCsvHook, jsonSnapCsvHook],
}

const Y_TOLERANCE = 0.01
const COLUMN_SPLIT = 15

async function automobileScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const resultsObj: TrackerScriptOutput = {}

  try {
    const response = await job.runtime.got(automobilePricingIT.URLs[0].href, {
      responseType: 'buffer',
    })

    const pdfParser = new PDFParser()
    const pdfData = await new Promise<Output>((resolve, reject) => {
      pdfParser.on('pdfParser_dataReady', (data: Output) => {
        resolve(data)
      })
      pdfParser.on('pdfParser_dataError', (error) => {
        reject(error)
      })
      pdfParser.parseBuffer(response.body)
    })

    const texts = pdfData.Pages[1]?.Texts
    if (!texts || texts.length === 0) {
      throw new Error('No text content found in pricing page')
    }

    const processedTexts = texts.map((text) => ({
      text: decodeURIComponent(text.R[0].T).trim(),
      y: text.y,
      x: text.x,
    }))

    const leftColumn = processedTexts.filter((t) => t.x < COLUMN_SPLIT).sort((a, b) => a.y - b.y)

    const rightColumn = processedTexts.filter((t) => t.x >= COLUMN_SPLIT).sort((a, b) => a.y - b.y)

    leftColumn.forEach((rangeText) => {
      if (rangeText.text.startsWith('da ')) {
        const matchingPrice = rightColumn.find((price) => {
          const yDiff = Math.abs(price.y - rangeText.y)
          return yDiff < Y_TOLERANCE && (price.text.endsWith('€') || price.text === 'Contattaci')
        })

        if (matchingPrice) {
          const key = `listings_${rangeText.text.replace(/\s+/g, '_')}`
          resultsObj[key] =
            matchingPrice.text === 'Contattaci' ? 'Contattaci' : matchingPrice.text.replace('€', '').trim()
        }
      }
    })

    if (Object.keys(resultsObj).length === 0) {
      throw new Error('No pricing data pairs found')
    }

    return resultsObj
  } catch (error) {
    job.addErrorToLog(error instanceof Error ? error.message : 'Unknown error')
    throw error
  }
}

export default automobilePricingIT
