import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig, TrackerScriptOutput } from '@datagatherers/datagatherers'

export const fortnoxNyheterSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PAGE_STATE,
  URLs: [new URL('https://support.fortnox.se/nyheter')],
  extractionScript: fortnoxScript,
  pngScript: null,
  csvHooks: null,
}

async function fortnoxScript(job: JobInstance) {
  return await job.runtime
    .got(fortnoxNyheterSE.URLs[0].href)
    .text()
    .then((response) => {
      if (!response?.length) throw new Error('Failed to retrieve html')
      const { document } = new JSDOM().window
      document.body.innerHTML = response
      const resultsObj: TrackerScriptOutput = {}
      document.querySelectorAll('[class*="SectionNewsItem_container"]').forEach((el) => {
        const topic = el.querySelector('[class*="SectionNewsItem_headerContainer"]')
        if (!topic) return
        const title = topic?.querySelector('h2')?.textContent?.trim()
        const date = topic?.querySelector('p')?.textContent?.trim()

        if (title && date) {
          const content = el.querySelector('[class*="ectionNewsItem_contentContainer"]')?.textContent?.trim()

          const formattedTitle = `${title} - ${date}`
          resultsObj[formattedTitle] = content?.trim() || ''
        }
      })

      if (!Object.keys(resultsObj)?.length) throw new Error('No results found')
      return resultsObj
    })
}
