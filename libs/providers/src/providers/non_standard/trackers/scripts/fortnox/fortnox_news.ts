import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { TrackerScriptOutput, TrackerConfig, JobInstance } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const fortnoxNewsSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.NEWS,
  URLs: [new URL('https://www.fortnox.se/produktnyheter')],
  extractionScript: fortnoxScript,
  pngScript: fortnoxPngScript,
  csvHooks: null,
}

async function fortnoxScript(job: JobInstance) {
  const html = await job.runtime.got(fortnoxNewsSE.URLs[0].href).text()

  const dom = new JSDOM(html)
  const doc = dom.window.document
  const results: TrackerScriptOutput = {}

  const blocks = Array.from(doc.querySelectorAll('[class*="CardLarge"],[class*="HeaderCommon"]'))
  const seen = new Set<string>()

  for (const block of blocks) {
    const heading = block.querySelector(
      'h2,h3,[class*="Heading_size--h2"],[class*="Heading_size--h3"],[class*="size--h2"],[class*="size--h3"]',
    )
    const title = heading?.textContent?.trim()
    if (!title || seen.has(title)) continue

    if (/vill du se tidigare nyheter/i.test(title)) continue
    if (/fler nyheter/i.test(title)) continue

    const link =
      block.querySelector('footer a[class*="textLink"][href]') ||
      block.querySelector('a[class*="textLink"][href]') ||
      block.querySelector('a[href]')
    const hrefRaw = link?.getAttribute('href')
    if (!hrefRaw) continue

    const linkText = link.textContent?.trim() || ''
    if (/^till nyheterna/i.test(linkText)) continue
    if (/visa fler/i.test(linkText)) continue
    if (/\/produktnyheter\/produktnyheter-/i.test(hrefRaw)) continue

    const fullUrl = new URL(hrefRaw, fortnoxNewsSE.URLs[0]).href

    if (!results[title]) {
      results[title] = fullUrl
      seen.add(title)
    }
  }

  if (!Object.keys(results).length) {
    throw new Error('No results found')
  }

  return results
}

async function fortnoxPngScript(page: Page) {
  const div_selector_to_remove = 'div[id^="ppms"]'
  await page.evaluate(
    (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
    div_selector_to_remove,
  )
  return page
}
