import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { TrackerScriptOutput, TrackerConfig, JobInstance } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const fortnoxNewsSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.NEWS,
  URLs: [new URL('https://www.fortnox.se/produktnyheter')],
  extractionScript: fortnoxScript,
  pngScript: fortnoxPngScript,
  csvHooks: null,
}

async function fortnoxScript(job: JobInstance) {
  const html = await job.runtime.got(fortnoxNewsSE.URLs[0].href).text()

  const dom = new JSDOM(html)
  const doc = dom.window.document
  const results: TrackerScriptOutput = {}

  Array.from(doc.querySelectorAll('a[class*="textLink"]')).forEach((a) => {
    const href = a.getAttribute('href')
    if (!href) return
    const fullUrl = new URL(href, fortnoxNewsSE.URLs[0]).href

    const textCol = a.closest('[class*="textColumn"]')
    if (!textCol) return

    const heading = textCol.querySelector('[class*="Heading_size--h2"],[class*="Heading_size--h3"]')
    const title = heading?.textContent?.trim()
    if (title) {
      results[title] = fullUrl
    }
  })

  if (!Object.keys(results).length) {
    throw new Error('No results found')
  }

  return results
}

async function fortnoxPngScript(page: Page) {
  const div_selector_to_remove = 'div[id^="ppms"]'
  await page.evaluate(
    (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
    div_selector_to_remove,
  )
  return page
}
