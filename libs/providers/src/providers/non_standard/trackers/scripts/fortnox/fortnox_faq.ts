import { JSD<PERSON> } from 'jsdom'

import { TrackerType, Iso3166Alpha2 } from '@datagatherers/datagatherers'
import { randomWait } from '@datagatherers/worker-utils'

import type { TrackerConfig, JobInstance, TrackerScriptOutput } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const fortnoxFaqSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.FAQ,
  URLs: [new URL('https://support.fortnox.se/sv_SE/faq-vanligaste-fragorna')],
  extractionScript: fortnoxScript,
  pngScript: fortnoxPngScript,
  csvHooks: null,
}

async function fortnoxScript(job: JobInstance) {
  const subURLs: URL[] = []
  const results: { product: string; value: string }[] = []

  await job.runtime
    .got(fortnoxFaqSE.URLs[0].href)
    .text()
    .then((response) => {
      const { document } = new JSDOM().window
      document.body.innerHTML = response
      const baseSelectors = Array.from(document.querySelectorAll('.custom-subcategories .custom-subcategory'))
      if (!baseSelectors?.length) throw new Error('Unable to parse HTML')
      for (const selector of baseSelectors) {
        const titleNav = selector.querySelector('h2 > a')
        if (!titleNav) throw new Error('Unable to extract parent title')
        const title = titleNav.textContent
        const titleSlug = titleNav.getAttribute('href')
        if (!title || !titleSlug) throw new Error('Unable to extract parent title data')
        results.push({ product: `${title}`, value: titleSlug })
        const subCats = selector.querySelectorAll('ul > li > a')
        if (!subCats?.length) throw new Error('Unable to extract child titles')
        for (const subCat of subCats) {
          const subCatTitle = subCat.textContent
          const subCatTitleSlug = subCat.getAttribute('href')
          if (!subCatTitle || !subCatTitleSlug) throw new Error(`Unable to extract child title data`)
          results.push({ product: `${subCatTitle}`, value: subCatTitleSlug })
          subURLs.push(new URL('https://' + fortnoxFaqSE.URLs[0].hostname + subCatTitleSlug))
        }
      }
    })

  for (const URL of subURLs) {
    await randomWait(500, 1000)
    await job.runtime
      .got(URL.href)
      .text()
      .then((response) => {
        const { document } = new JSDOM().window
        document.body.innerHTML = response
        const content = document.querySelector('.helpjuice-article-body-content')?.textContent
        if (!content) throw new Error(`Unable to extract FAQ subpage content, page: ${URL.href}`)
        results.push({ product: URL.href, value: content })
      })
  }
  let resultsObj: TrackerScriptOutput = {}
  for (const product of results) {
    resultsObj = Object.assign(resultsObj, { [product.product]: product.value })
  }
  fortnoxFaqSE.URLs.push(...subURLs)
  return resultsObj
}

async function fortnoxPngScript(page: Page) {
  const div_selector_to_remove = 'div[id^="ppms"]'
  await page.evaluate(
    (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
    div_selector_to_remove,
  )
  return page
}
