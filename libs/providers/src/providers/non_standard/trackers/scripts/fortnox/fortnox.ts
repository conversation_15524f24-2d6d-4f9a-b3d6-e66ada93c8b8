import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { TrackerConfig, TrackerScriptOutput, JobInstance } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const fortnoxPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING,
  URLs: [new URL('https://www.fortnox.se/produkt/prislista')],
  extractionScript: fortnoxScript,
  pngScript: fortnoxPngScript,
  csvHooks: null,
}

async function fortnoxScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const html = await job.runtime
    .got(fortnoxPricingSE.URLs[0].href, {
      retry: {
        limit: 3,
      },
    })
    .text()
  const dom = new JSDOM(html)
  const document = dom.window.document
  const resultsObj: TrackerScriptOutput = {}

  const allDivs = Array.from(document.querySelectorAll('div[class*="PriceListGroup"]'))
  const groups = allDivs.filter((group) => {
    const classList = Array.from(group.classList)
    return classList.some((className) => {
      const hasContainer = /container/i.test(className)
      const hasGridContainer = /gridContainer/i.test(className)
      return hasContainer && !hasGridContainer
    })
  })

  groups.forEach((group) => {
    const subHeading = group.querySelector('div[class^="Heading"]')
    const groupTitle = subHeading
      ? subHeading.textContent.trim()
      : (group.closest('section')?.querySelector('div[class^="Heading"]')?.textContent.trim() ?? '')

    if (!groupTitle) return

    const allRows = group.querySelectorAll('div[class*="PriceListItemCustom"]')
    const rows = Array.from(allRows).filter((row) => {
      const classList = Array.from(row.classList)
      return classList.some((className) => /priceItem/i.test(className))
    })

    for (const row of rows) {
      const nameEl = row.querySelector('p[class*="productName"]')
      if (!nameEl?.textContent) continue

      const productName = nameEl.textContent.trim()
      const cells = Array.from(row.querySelectorAll('p[class*="priceText"]'))
      if (cells.length) {
        for (const cell of cells) {
          const subEl = cell.querySelector('span[class*="subTitle"]')
          const columnTitle = subEl ? subEl.textContent.trim() : 'price'
          const priceEl = cell.querySelector('span[class*="price"]')
          if (!priceEl) continue
          const priceText = priceEl.textContent.trim()
          resultsObj[`[${groupTitle}][${productName}][${columnTitle}]`] = priceText
        }
      } else {
        const priceEl = row.querySelector('span[class*="price"]')
        if (!priceEl) continue
        const priceText = priceEl.textContent.trim()
        resultsObj[`[${groupTitle}][${productName}][price]`] = priceText
      }
    }
  })

  if (!Object.keys(resultsObj).length) {
    throw new Error('Unable to extract prices from the page')
  }
  const cleanPrice = (value: string) =>
    value
      .replace('SEK/month', 'kr/month')
      .replace('kr/mån', 'kr/month')
      .replace('kr/konsult/mån', 'kr/consultant/month')
      .replace(/\s+/g, ' ')
      .trim()

  Object.entries(resultsObj).forEach(([key, value]) => {
    resultsObj[key] = cleanPrice(value)
  })
  return resultsObj
}

async function fortnoxPngScript(page: Page) {
  const div_selector_to_remove = 'div[id^="ppms"]'
  await page.evaluate(
    (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
    div_selector_to_remove,
  )
  return page
}
