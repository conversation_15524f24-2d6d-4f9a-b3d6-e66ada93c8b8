import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { TrackerConfig, TrackerScriptOutput, JobInstance } from '@datagatherers/datagatherers'
import type { Page } from 'puppeteer-core'

export const fortnoxPricingSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.PRICING,
  URLs: [new URL('https://www.fortnox.se/produkt/prislista')],
  extractionScript: fortnoxScript,
  pngScript: fortnoxPngScript,
  csvHooks: null,
}

async function fortnoxScript(job: JobInstance): Promise<TrackerScriptOutput> {
  const html = await job.runtime
    .got(fortnoxPricingSE.URLs[0].href, {
      retry: {
        limit: 3,
      },
    })
    .text()
  const dom = new JSDOM(html)
  const document = dom.window.document
  const resultsObj: TrackerScriptOutput = {}

  const groups = Array.from(document.querySelectorAll('div[class*="PriceListGroup_container"]'))
  groups.forEach((group) => {
    const subHeading = group.querySelector('div[class*="PriceListGroup_title"]')
    const groupTitle = subHeading
      ? subHeading.textContent.trim()
      : (group.closest('section')?.querySelector('div[class*="Heading_size--h3"]')?.textContent.trim() ?? '')
    if (!groupTitle) return

    const rows = Array.from(group.querySelectorAll('div[class*="PriceListItemCustom_priceItem"]'))
    rows.forEach((row) => {
      const nameEl = row.querySelector('p[class*="PriceListItemCustom_productName"] strong')
      if (!nameEl) return
      const productName = nameEl.textContent.trim()

      const cells = Array.from(row.querySelectorAll('p[class*="PriceListItemCustom_priceText"]'))
      if (cells.length) {
        cells.forEach((cell) => {
          const subEl = cell.querySelector('span[class*="PriceListItemCustom_subTitle"]')
          const columnTitle = subEl ? subEl.textContent.trim() : 'price'
          const priceEl = cell.querySelector('span[class*="PriceListItemCustom_price"]')
          if (!priceEl) return
          const priceText = priceEl.textContent.trim()
          resultsObj[`[${groupTitle}][${productName}][${columnTitle}]`] = priceText
        })
      } else {
        const priceEl = row.querySelector('span[class*="PriceListItemCustom_price"]')
        if (!priceEl) return
        const priceText = priceEl.textContent.trim()
        resultsObj[`[${groupTitle}][${productName}][price]`] = priceText
      }
    })
  })

  if (!Object.keys(resultsObj).length) {
    throw new Error('Unable to extract prices from the page')
  }
  const cleanPrice = (value: string) =>
    value
      .replace('SEK/month', 'kr/month')
      .replace('kr/mån', 'kr/month')
      .replace('kr/konsult/mån', 'kr/consultant/month')
      .replace(/\s+/g, ' ')
      .trim()

  Object.entries(resultsObj).forEach(([key, value]) => {
    resultsObj[key] = cleanPrice(value)
  })
  return resultsObj
}

async function fortnoxPngScript(page: Page) {
  const div_selector_to_remove = 'div[id^="ppms"]'
  await page.evaluate(
    (selector) => document.querySelectorAll(selector).forEach((el) => el.remove()),
    div_selector_to_remove,
  )
  return page
}
