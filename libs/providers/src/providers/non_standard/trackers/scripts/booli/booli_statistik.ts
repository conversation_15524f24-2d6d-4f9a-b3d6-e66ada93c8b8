import { JSD<PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const booliStatistikLauncherSE: TrackerConfig = {
  iso: Iso3166Alpha2.SE,
  trackerType: TrackerType.LAUNCHER,
  extractionScript: booliScript,
  URLs: [new URL('https://www.booli.se/kunskap/category/bostadsmarknaden')],
  pngScript: null,
  csvHooks: null,
}

async function booliScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const resultsObj = {}
  await job.runtime
    .got(booliStatistikLauncherSE.URLs[0].href, {
      retry: {
        limit: 3,
      },
    })
    .text()
    .then((response) => {
      if (!response) throw new Error('Failed to retrieve html')
      document.body.innerHTML = response

      const scriptElement = document.getElementById('__NEXT_DATA__')
      if (!scriptElement) throw new Error('Could not find __NEXT_DATA__ script')

      const nextData = JSON.parse(scriptElement.textContent || '{}')

      const articles = Object.keys(nextData.props?.pageProps?.['__APOLLO_STATE__']).filter(
        (key) => key.match(/Article/i) && nextData.props?.pageProps?.['__APOLLO_STATE__'][key]?.publishedAt,
      )

      Object.assign(
        resultsObj,
        ...articles.flatMap((article) => {
          const id = nextData.props?.pageProps?.['__APOLLO_STATE__'][article]?.id
          if (!id) return []

          const title = nextData.props?.pageProps?.['__APOLLO_STATE__'][article]?.title
          if (!title?.match(/Statistik/i)) return []

          const sponsored = nextData.props?.pageProps?.['__APOLLO_STATE__'][article]?.sponsored
          const sponsor = nextData.props?.pageProps?.['__APOLLO_STATE__'][article]?.sponsorName
          const publishedAt = nextData.props?.pageProps?.['__APOLLO_STATE__'][article]?.publishedAt

          return { [`${publishedAt}${sponsored ? ' (Sponsored by ' + sponsor + ')' : ''}`]: title }
        }),
      )
    })

  return resultsObj
}
