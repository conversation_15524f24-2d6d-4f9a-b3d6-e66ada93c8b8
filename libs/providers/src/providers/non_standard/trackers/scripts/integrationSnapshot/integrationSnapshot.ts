import { got } from 'got'
import { json2csv } from 'json-2-csv'

import { Iso3166Alpha2, ProjectType, TrackerType } from '@datagatherers/datagatherers'

import { getAuthorization } from '../../../../../lib/dataAi/index'
import { similarWebAPIkey } from '../../../../../lib/similarweb/constants'
import { writeOutFromCsvHook } from '../../csvHooks'

import type { DataAiDeliveryStatusResponse } from '../../../../../lib/dataAi/types'
import type { SimilarwebCapabilitiesResponse } from '../../../../../lib/similarweb/types'
import type {
  ControllerConfig,
  Tracker,
  TrackerConfig,
  ControllerRuntime,
  Diff,
  JobInstance,
  TrackerMerged,
  TrackerScriptOutput,
} from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'

export const dataAiTrackerConfigWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.DATA_AVAILABILITY,
  extractionScript: dataAiAvailabilityScript,
  URLs: [],
  pngScript: null,
  csvHooks: [similarwebDataaiCsvHook],
}

export const similarWebTrackerConfigWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.DATA_AVAILABILITY,
  extractionScript: similarWebAvailabilityScript,
  URLs: [],
  pngScript: null,
  csvHooks: [similarwebDataaiCsvHook],
}

async function dataAiAvailabilityScript() {
  return got('https://api.data.ai/v2.0/data-delivery/status', {
    headers: {
      authorization: getAuthorization('daily'),
    },
    searchParams: {
      metric_type: 'app_usage_engagement,download_revenue',
      metric_granularity: 'monthly,weekly',
    },
  })
    .json<DataAiDeliveryStatusResponse>()
    .then((response) => {
      if (response?.code !== 200) throw new Error('Failed to retrieve body from request')
      const obj = {}
      response.delivery_status.flatMap((item) => {
        if (
          !item?.metric_date ||
          (item.delivery_type !== 'final' &&
            (item.metric_type !== 'app_usage_engagement' || item.metric_granularity !== 'monthly'))
        )
          return []

        const key =
          item.metric_type === 'app_usage_engagement' && item.metric_granularity === 'monthly'
            ? `${item.metric_type}_${item.metric_granularity}_${item.delivery_type}`
            : `${item.metric_type}_${item.metric_granularity}`
        obj[key] = item.metric_date
      })
      return obj
    })
}

async function similarWebAvailabilityScript() {
  return got({
    url: 'https://api.similarweb.com/capabilities',
    searchParams: {
      api_key: similarWebAPIkey,
    },
  })
    .json<SimilarwebCapabilitiesResponse>()
    .then((res) => {
      return { endDate: res?.web_desktop_data?.snapshot_interval?.end_date }
    })
}

export async function integrationSnapshotScheduleAfterFinished(
  config: ControllerConfig,
  runtime: ControllerRuntime,
): Promise<ControllerConfig[]> {
  const data = await runtime
    .collection<Tracker>('trackers')
    .find({ project: config.project, provider: config.provider })
    .sort({ time: -1 })
    .limit(1)
    .toArray()

  const type =
    config.provider === 'dataAi'
      ? 'extractFinalDataAi'
      : config.provider === ProjectType.similarweb
        ? 'extractFinalSimilarWeb'
        : undefined
  if (!type) return []
  const res = []
  if (data?.[0]?.time && data?.[0]?.itemId.toString().endsWith(data?.[0]?.time))
    res.push({
      project: config.project,
      provider: config.provider,
      type,
      data: {
        parameters: {
          ...(data?.[0]?.jsonDiff?.length && { jsonDiff: data[0].jsonDiff }),
          ...(config.provider === 'dataAi' && { isFinal: true }),
        },
      },
    })
  return res
}

async function similarwebDataaiCsvHook(
  job: JobInstance,
  config: TrackerConfig,
  mergedToggles: TrackerMerged,
  diffs: Diff[],
  jsonSnap: TrackerScriptOutput,
  gDrive: GDriveUtil,
  currentDate: Date,
  gDriveAcc: string,
  clientEmails: string[],
) {
  const args = [job, config, mergedToggles, diffs, jsonSnap, gDrive, currentDate, gDriveAcc, clientEmails] as const

  const csv = json2csv(diffs, {
    emptyFieldValue: 'N/A',
  })

  await writeOutFromCsvHook(...args, csv, 'Custom Differences', `${JSON.stringify(diffs ?? [])}`)
}

// async function similarwebDataaiJsonSnapCsvHook(
//   job: JobInstance,
//   config: TrackerConfig,
//   mergedToggles: TrackerMerged,
//   diffs: Diff[],
//   jsonSnap: TrackerScriptOutput,
//   gDrive: GDriveUtil,
//   currentDate: Date,
//   gDriveAcc: string,
//   clientEmails: string[],
// ) {
//   const args = [job, config, mergedToggles, diffs, jsonSnap, gDrive, currentDate, gDriveAcc, clientEmails] as const

//   const parsedJsonSnap = Object.keys(jsonSnap)?.map((key) => ({ key, value: jsonSnap[key] })) ?? []
//   const csv = json2csv(parsedJsonSnap, {
//     emptyFieldValue: 'N/A',
//   })

//   await writeOutFromCsvHook(...args, csv, 'Custom Snapshot', `${JSON.stringify(diffs ?? [])}`)
// }
