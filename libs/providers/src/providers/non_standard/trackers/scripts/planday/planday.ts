import { J<PERSON><PERSON> } from 'jsdom'

import { Iso3166Alpha2, TrackerType } from '@datagatherers/datagatherers'

import type { JobInstance, TrackerConfig } from '@datagatherers/datagatherers'

export const plandayPricingWW: TrackerConfig = {
  iso: Iso3166Alpha2.WW,
  trackerType: TrackerType.PRICING,
  extractionScript: plandayScript,
  URLs: [new URL('https://www.planday.com/pricing/')],
  pngScript: null,
  csvHooks: null,
}

async function plandayScript(job: JobInstance) {
  const { document } = new JSDOM().window
  const pricingPlansRaw = await job.runtime.got(plandayPricingWW.URLs[0].href).then(({ body, statusCode }) => {
    if (statusCode !== 200 || !body) throw new Error(`Unable to retrieve html, status code: ${statusCode}`)
    try {
      document.body.innerHTML = body
      const script = document.querySelector('script[id="__NEXT_DATA__"]')
      const json: PricingJson = JSON.parse(script.textContent)
      return json.props.pageProps.pricingPlans
    } catch {
      throw new Error(`Unable to parse request body`)
    }
  })
  if (!pricingPlansRaw?.length) throw new Error(`Unable to retrieve pricing plans`)

  const pricingPlans = pricingPlansRaw.flatMap(
    ({
      title,
      description,
      monthlyPrice,
      monthlyStartingPrice,
      customPricing,
      customDetails,
      minimumUsers,
      topFeatures,
      mostPopular,
    }) => [
      { [title + '-' + 'description']: description },
      { [title + '-' + 'minimumUsers']: minimumUsers },
      { [title + '-' + 'monthlyPrice']: monthlyPrice },
      { [title + '-' + 'monthlyStartingPrice']: monthlyStartingPrice },
      { [title + '-' + 'customPricing']: customPricing },
      { [title + '-' + 'customDetails']: customDetails },
      { [title + '-' + 'mostPopular']: mostPopular },
      ...topFeatures.flatMap(({ text }, index) => {
        return [{ [title + '-' + 'topFeature' + index]: text }]
      }),
    ],
  )
  const resultsObj = {}

  pricingPlans.forEach((item) => {
    Object.entries(item).forEach(([key, value]) => {
      resultsObj[key] = value
    })
  })
  if (!Object.keys(resultsObj)?.length) throw new Error(`Unable to retrieve pricing plans`)
  return resultsObj
}

interface PricingJson {
  props: {
    pageProps: { pricingPlans: PricingPlansEntity[] }
  }
}

interface PricingPlansEntity {
  title: string
  description: string
  minimumUsers: string
  monthlyPrice: string
  monthlyStartingPrice: string
  customPricing: string | boolean
  customDetails: string
  topFeaturesLabel: string
  topFeatures: { text: string }[]
  mostPopular: boolean
}
