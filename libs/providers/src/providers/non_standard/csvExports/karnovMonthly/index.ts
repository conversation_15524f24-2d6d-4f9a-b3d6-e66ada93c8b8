import { addMonths, format, isAfter, parse, startOfMonth, subDays, subMonths } from 'date-fns'
import isoCountries from 'i18n-iso-countries'
import { json2csv } from 'json-2-csv'

import { DataType, IntegrationType, Iso3166Alpha2, ProjectType } from '@datagatherers/datagatherers'
import { dateFnsUtils } from '@datagatherers/datagatherers/lib/snapshot/date-fns.utils'
import { accountConfigs, GDrive } from '@datagatherers/worker-utils'

import { karnovDomains, settings as karnovSettings } from './constants'
import { checkRunningStatus, getOverviewShare } from '../../../../lib/similarweb/index'
import { moveModifiedBefore } from '../../../../lib/tools/csvExportsArchiving'
import { availableIntegrationDataTypes } from '../constants'
import { getOutputMonth } from '../utils'

import type { IntegrationSnapshotCsvOptions } from '../defaultIntegrationSnapshot'
import type { ControllerRuntime, IntegrationSnapshot, Snapshot } from '@datagatherers/datagatherers'

function getDomainIndex(domainArr: string[]) {
  if (!domainArr?.length) return

  const branches = []
  for (let i = 0; i < domainArr.length; i++) {
    branches.push({ case: { $eq: ['$stats.domain', domainArr[i]] }, then: i + 1 })
  }

  return {
    $switch: {
      branches,
      default: 0,
    },
  }
}

export async function generateKarnovMonthlyCSV(runtime: ControllerRuntime, account: string) {
  const gDrive = await GDrive(account)
  const settings = karnovSettings(account)
  const csvObjectArray: object[] = []

  const options: IntegrationSnapshotCsvOptions = {
    integrations: settings.integrations,
    projects: settings.projects,
    startSnapTime: settings.startSnapTime,
    snapTimeUnit: settings.snapTimeUnit,
    includeWebpageColumn: true,
    includeDomainColumn: true,
    excludeProviderColumn: true,
    countryColumn: 'exclude',
    isoList: [Iso3166Alpha2.WW],
  }

  const isRunningSimilarWeb = await checkRunningStatus(runtime)
  if (!isRunningSimilarWeb) {
    //csvObjectArray.push({})

    const endDate = subMonths(startOfMonth(new Date()), 1)
    const startDateObj = parse(settings.startSnapTime, 'yyyy/MM', new Date())
    const months: string[] = []
    for (let d = startDateObj; !isAfter(d, endDate); d = addMonths(d, 1)) {
      months.push(format(d, 'yyyy/MM'))
    }

    let csvData

    let domains = [{ domain: 'karnovgroup.dk' }]

    for (const { domain } of domains) {
      // Overview Share %
      const overview_share = await getOverviewShare(domain, startDateObj, endDate, 'MMM-yy')

      // Direct %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Direct (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.direct?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Organic Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Organic Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.organicSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Referrals %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Referrals (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.referrals?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Paid Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Paid Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.paidSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Social %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Social (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.social?.total?.relative
      }
      csvObjectArray.push(csvData)

      // E-mail %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'E-mail (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.mail?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Display Ads %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Display Ads (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.displayAds?.total?.relative
      }
      csvObjectArray.push(csvData)
    }

    domains = [{ domain: 'karnovgroup.no' }]

    for (const { domain } of domains) {
      // Overview Share %
      const overview_share = await getOverviewShare(domain, startDateObj, endDate, 'MMM-yy')

      // Direct %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Direct (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.direct?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Organic Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Organic Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.organicSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Referrals %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Referrals (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.referrals?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Social %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Social (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.social?.total?.relative
      }
      csvObjectArray.push(csvData)

      // E-mail %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'E-mail (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.mail?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Paid Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Paid Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.paidSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Display Ads %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Display Ads (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.displayAds?.total?.relative
      }
      csvObjectArray.push(csvData)
    }

    domains = [{ domain: 'nj.se' }]

    for (const { domain } of domains) {
      // Overview Share %
      const overview_share = await getOverviewShare(domain, startDateObj, endDate, 'MMM-yy')

      // Direct %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Direct (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.direct?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Organic Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Organic Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.organicSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Referrals %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Referrals (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.referrals?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Social %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Social (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.social?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Paid Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Paid Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.paidSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Display Ads %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Display Ads (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.displayAds?.total?.relative
      }
      csvObjectArray.push(csvData)

      // E-mail %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'E-mail (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.mail?.total?.relative
      }
      csvObjectArray.push(csvData)
    }

    domains = [{ domain: 'aranzadilaley.es' }]

    for (const { domain } of domains) {
      // Overview Share %
      const overview_share = await getOverviewShare(domain, startDateObj, endDate, 'MMM-yy')

      // Direct %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Direct (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.direct?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Organic Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Organic Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.organicSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Referrals %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Referrals (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.referrals?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Paid Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Paid Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.paidSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Social %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Social (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.social?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Display Ads %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Display Ads (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.displayAds?.total?.relative
      }
      csvObjectArray.push(csvData)

      // E-mail %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'E-mail (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.mail?.total?.relative
      }
      csvObjectArray.push(csvData)
    }

    domains = [{ domain: 'lamy-liaisons.fr' }]

    for (const { domain } of domains) {
      // Overview Share %
      const overview_share = await getOverviewShare(domain, startDateObj, endDate, 'MMM-yy')

      // Organic Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Organic Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.organicSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Direct %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Direct (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.direct?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Paid Search %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Paid Search (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.paidSearch?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Referrals %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Referrals (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.referrals?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Social %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Social (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.social?.total?.relative
      }
      csvObjectArray.push(csvData)

      // Display Ads %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'Display Ads (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.displayAds?.total?.relative
      }
      csvObjectArray.push(csvData)

      // E-mail %
      csvData = {
        Integration: 'SimilarWeb',
        Type: 'E-mail (%)',
        Domain: domain,
        Webpage: '',
      }
      for (const snapTime of Object.keys(overview_share)) {
        csvData[snapTime] = overview_share[snapTime]?.mail?.total?.relative
      }
      csvObjectArray.push(csvData)
    }
  }

  for (const domainArr of karnovDomains) {
    if (domainArr === undefined) continue
    if (domainArr.length === 0) {
      csvObjectArray.push({})
      continue
    }

    const { dataTypes, ...remainingOptions } = {
      ...options,
      domains: domainArr,
      sortingIndex: getDomainIndex(domainArr),
    }

    const aggrResults = await runtime
      .collection<IntegrationSnapshot>('integrationSnapshot')
      .aggregate(getAggregation(remainingOptions))
      .toArray()
    if (!aggrResults?.length) continue

    const resultsParsed = aggrResults.reduce((acc, item) => {
      if (item.webpage === 'main' && item.domain === 'miespacio.aranzadilaley.es') return acc

      const includeWebpage =
        options.includeWebpageColumn ||
        (options.integrations.find((i) => i === IntegrationType.similarWeb) &&
          [ProjectType.accounting, ProjectType.saas].includes(item.project))
      const includeDomain = options.includeDomainColumn
      const excludeProvider = options.excludeProviderColumn
      const monthFormatted = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy')
      const Integration = item.integration.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
      const preProvider =
        item.provider === 'Amazon Prime Video' ? 'Amazon' : item.provider === 'ebk' ? 'kleinanzeigen' : item.provider

      const obj = {
        Integration,
        Type:
          availableIntegrationDataTypes[item.integration]?.find((dataTypeInfo) => dataTypeInfo.value === item.dataType)
            ?.label ?? '',
        ...(options.countryColumn !== 'exclude' && {
          Country: isoCountries.getName(item.iso, 'en'),
        }),
        ...(!excludeProvider && {
          Provider: preProvider.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())),
        }),
        ...(includeDomain && {
          Domain: item.domain,
        }),
        ...(includeWebpage && {
          Webpage: item.webpage?.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())) ?? ' ',
        }),
      }

      const itemInArray = acc.find(
        (i) =>
          i.Country === obj.Country &&
          i.Provider === obj.Provider &&
          i.Integration === obj.Integration &&
          i.Webpage === obj.Webpage &&
          i.Type === obj.Type &&
          i.Domain === obj.Domain,
      )
      if (itemInArray) {
        itemInArray[monthFormatted] = item.count
      } else {
        obj[monthFormatted] = item.count
        acc.push(obj)
      }
      return acc
    }, []) as object[]

    if (dataTypes?.length) {
      const snapshotAggrResults = await runtime
        .collection<Snapshot>('snapshot')
        .aggregate(getAggregation(options))
        .toArray()
      if (!snapshotAggrResults?.length) return

      const snapshotsResultsParsed = snapshotAggrResults.reduce((acc, item) => {
        const includeWebpage =
          options.integrations.find((i) => i === IntegrationType.similarWeb) &&
          [ProjectType.accounting, ProjectType.saas].includes(item.project)
        const monthFormatted = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy')
        const Integration = ' ' //item.integration.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
        const preProvider =
          item.provider === 'Amazon Prime Video' ? 'Amazon' : item.provider === 'ebk' ? 'kleinanzeigen' : item.provider
        const Type =
          item.dataType === DataType.app
            ? 'API Integrations'
            : item.dataType === DataType.partner
              ? 'Accounting Partners'
              : item.dataType.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))

        const obj = {
          Integration,
          Type,
          Country: isoCountries.getName(item.iso, 'en'),
          Provider: preProvider.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())),
          ...(includeWebpage && { Webpage: ' ' }),
        }

        const itemInArray = acc.find(
          (i) =>
            i.Country === obj.Country &&
            i.Provider === obj.Provider &&
            i.Integration === obj.Integration &&
            i.Webpage === obj.Webpage &&
            i.Type === obj.Type,
        )
        if (itemInArray) {
          itemInArray[monthFormatted] = item.count
        } else {
          obj[monthFormatted] = item.count
          acc.push(obj)
        }
        return acc
      }, []) as object[]
      if (snapshotsResultsParsed?.length) {
        resultsParsed.push(...snapshotsResultsParsed)
      }
    }

    csvObjectArray.push(...resultsParsed)
    // break
  }

  // CSV generation
  const fileDate = options.endSnapTime
    ? getOutputMonth(options.endSnapTime, dateFnsUtils.timeUnitFormat(options.snapTimeUnit))
    : getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))

  const filename = `${settings.filePrefix}${fileDate}.csv`
  const csv = json2csv(csvObjectArray, { emptyFieldValue: '-' })

  await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, settings.folder)

  await moveModifiedBefore(
    gDrive,
    subDays(new Date(), 1),
    accountConfigs[account].folders.archive,
    [settings.folder],
    [settings.filePrefix],
  )
}

function getAggregation(options: IntegrationSnapshotCsvOptions) {
  if (options.dataTypes?.length) {
    const providerList = options?.providerList?.length ? options.providerList : undefined
    if (providerList?.length === 1 && providerList[0] === 'xero') providerList.push('quickbooks') // Hack

    const aggr = [
      {
        $match: {
          dataType: { $in: options.dataTypes },
          ...(options?.isoList?.length && { iso: { $in: options.isoList } }),
          ...(options?.providerList?.length && { provider: { $in: providerList } }),
          ...(options?.projects?.length && { project: { $in: options.projects } }),
          snapTimeUnit: options.snapTimeUnit,
          snapTime: { $gte: options.startSnapTime, ...(options.endSnapTime && { $lte: options.endSnapTime }) },
        },
      },
    ]

    return aggr
  }

  const dataTypeExclusions = options.excludeIntegrationDataTypes ?? []

  const pipeline = []

  pipeline.push({
    $match: {
      integration: { $in: options.integrations },
      ...(options?.isoList?.length && { iso: { $in: options.isoList } }),
      ...(options?.providerList?.length && { provider: { $in: options.providerList } }),
      ...(options?.projects?.length && { project: { $in: options.projects } }),
      snapTimeUnit: options.snapTimeUnit,
      snapTime: { $gte: options.startSnapTime, ...(options.endSnapTime && { $lte: options.endSnapTime }) },
      ...(dataTypeExclusions.length && { dataType: { $nin: dataTypeExclusions } }),
    },
  })

  if (options.providerExceptions) {
    for (const [intName, badList] of Object.entries(options.providerExceptions)) {
      pipeline.push({
        $match: {
          $or: [{ integration: { $ne: intName } }, { provider: { $nin: badList } }],
        },
      })
    }
  }

  pipeline.push(
    { $sort: { _id: -1 } },
    {
      $group: {
        _id: {
          project: '$project',
          integration: '$integration',
          dataType: '$dataType',
          iso: '$iso',
          provider: '$provider',
          snapTime: '$snapTime',
          platform: '$platform',
          appId: '$appId',
        },
        stats: { $first: '$stats' },
      },
    },
    {
      $unwind: '$stats',
    },
  )

  if (options.domains?.length) {
    pipeline.push({
      $match: {
        $or: [{ '_id.integration': { $ne: IntegrationType.similarWeb } }, { 'stats.domain': { $in: options.domains } }],
      },
    })
  }

  pipeline.push(
    {
      $group: {
        _id: {
          project: '$_id.project',
          integration: '$_id.integration',
          dataType: '$_id.dataType',
          iso: '$_id.iso',
          provider: '$_id.provider',
          snapTime: '$_id.snapTime',
          webpage: '$stats.webpage',
          ...(options.includeDomainColumn && { domain: '$stats.domain' }),
          ...(options.sortingIndex && { sortingIndex: options.sortingIndex }),
        },
        count: { $sum: '$stats.count' },
      },
    },
    {
      $project: {
        _id: 0,
        integration: '$_id.integration',
        dataType: '$_id.dataType',
        iso: '$_id.iso',
        project: '$_id.project',
        provider: '$_id.provider',
        snapTime: '$_id.snapTime',
        webpage: '$_id.webpage',
        ...(options.includeDomainColumn && { domain: '$_id.domain' }),
        ...(options.countryColumn !== 'exclude' && { country: '$_id.iso' }),
        ...(options.sortingIndex && { sortingIndex: '$_id.sortingIndex' }),
        count: '$count',
      },
    },
    {
      $sort: {
        integration: 1,
        dataType: -1,
        iso: 1,
        ...(options.sortingIndex && { sortingIndex: 1 }),
        provider: 1,
        ...(options.includeDomainColumn && { domain: 1 }),
        webpage: 1,
        snapTime: 1,
      },
    },
  )

  return pipeline
}
