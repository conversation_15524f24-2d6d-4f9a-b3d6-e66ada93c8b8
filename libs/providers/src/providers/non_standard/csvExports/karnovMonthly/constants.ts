import { format, subMonths, subYears, startOfMonth } from 'date-fns'

import { IntegrationType, ProjectType, TimeUnit } from '@datagatherers/datagatherers'
import { accountConfigs } from '@datagatherers/worker-utils'

export const settings = (account: string) => {
  return {
    integrations: [IntegrationType.similarWeb],
    projects: [ProjectType.databases],
    startSnapTime: format(subMonths(subYears(startOfMonth(new Date()), 3), 1), 'yyyy/MM'),
    snapTimeUnit: TimeUnit.months,
    filePrefix: 'Karnov Monthly Csv ',
    folder: accountConfigs[account].folders.karnov,
  }
}

export const karnovDomains = [
  ['liaisons-sociales.fr', 'lamyline-notaire.fr', 'lamyline.fr', 'lamy-liaisons.fr', 'lamy.fr'],
  ['doctrine.fr', 'dalloz.fr', 'lexisnexis.com', 'lexisnexis.fr'],
  [
    'laleydigital.laleynext.es',
    'login-miespacio.aranzadilaley.es',
    'portalcliente.aranzadifusion.es',
    'miespacio.aranzadilaley.es',
    'legalteca.aranzadilaley.es',
  ],
  ['lefebvre.es', 'iberley.es', 'vlex.es', 'tirant.com', 'sepin.es'],
  [
    'karnovgroup.dk',
    'nj.se',
    'notisum.se',
    'dib.se',
    'karnovgroup.no',
    'dib.no',
    'lovdata.no',
    'lamy-liaisons.fr',
    'echoline.fr',
    'aranzadilaley.es',
    'laley.es',
    'pro.karnovgroup.dk',
    'juno.nj.se',
    'login.dib.no',
    'pro.lovdata.no',
    'espace-client.lamy-liaisons.fr',
    'app.echoline.fr',
    'miespacio.aranzadilaley.es',
  ],
]
