import {
  Iso3166Alpha2,
  ProjectType,
  IntegrationSnapshotDataType,
  DataType,
  TimeUnit,
  VehicleType,
  IntegrationType,
  RealEstatesPropertyScope,
  RealEstatesBusinessType,
  DealerType,
  ClassifiedsStockType,
} from '@datagatherers/datagatherers'

export const accountingProvidersAPExpensesUS = [
  'adp',
  'airbase',
  'avidxchange',
  'fylehq',
  'beanworks',
  'bill.com',
  'bluevine',
  'brex',
  'dailypay',
  'divvy',
  'freedommerchantservices',
  'gusto',
  'melio',
  'mineraltree',
  'paychex',
  'plateiq',
  'plooto',
  'quickbooks',
  'ramp',
  'corpay',
  'tipalti',
  'veem',
  'xero',
  'jeeves',
  'corporatespending',
  'trulyfinancial',
] // US only, has to be in both (accounts_payable+expense menagement) && (SME+mid-market)
export const datingProviders = [
  'meetme',
  'lovoo',
  'skout',
  'tagged',
  'parship',
  'eharmony',
  'growlr',
  'hi5',
  'elitepartner',
]
export const providerNameMapping = {
  DE: {
    aboalarm: 'Aboalarm',
    amorelie: 'Amorelie',
    aroundhome: 'Aroundhome',
    'billiger.mietwagen': 'Billiger-Mietwagen',
    camperdays: 'CamperDays',
    flaconi: 'Flaconi',
    'jochen.schweizer': 'Jochen Schweizer',
    moebel: 'Moebel',
    mydays: 'Mydays',
    regiondo: 'Regiondo',
    stylight: 'Stylight',
    'TV Now': 'RTL+',
    verivox: 'Verivox',
    marktguru: 'Markt Guru',
    wetter: 'Wetter',
  },
  WW: {
    elitepartner: 'ElitePartner',
    eharmony: 'eHarmony',
    growlr: 'GROWLr',
    lovoo: 'LOVOO',
    meetme: 'MeetMe',
    parship: 'Parship',
    skout: 'Skout',
    tagged: 'Tagged',
  },
  US: {
    adp: 'ADP',
    airbase: 'Airbase',
    avidxchange: 'AvidXchange',
    fylehq: 'Fylehq',
    beanworks: 'Beanworks',
    'bill.com': 'Bill.com',
    bluevine: 'Bluevine',
    brex: 'Brex',
    dailypay: 'Dailypay',
    divvy: 'Divvy',
    freedommerchantservices: 'Freedom Merchant Services',
    gusto: 'Gusto',
    melio: 'Melio',
    mineraltree: 'Mineraltree',
    paychex: 'Paychex',
    plateiq: 'Plateiq',
    plooto: 'Plooto',
    quickbooks: 'Quickbooks',
    ramp: 'Ramp',
    corpay: 'corpay',
    tipalti: 'Tipalti',
    veem: 'Veem',
    xero: 'Xero',
    jeeves: 'Jeeves',
    corporatespending: 'Corporate Spending',
    trulyfinancial: 'Truly Financial',
  },
}

export const availableIntegrationDataTypes: Record<
  IntegrationType,
  { value: IntegrationSnapshotDataType; label: string }[]
> = {
  [IntegrationType.ampere]: [],
  [IntegrationType.apptopia]: [
    { value: IntegrationSnapshotDataType.apptopiaActiveUsers, label: 'Active Users' },
    { value: IntegrationSnapshotDataType.apptopiaDownloads, label: 'Downloads' },
  ],
  [IntegrationType.dataAi]: [
    { value: IntegrationSnapshotDataType.dataAiActiveUsers, label: 'Active Users' },
    { value: IntegrationSnapshotDataType.dataAiDownloads, label: 'Downloads' },
  ],
  [IntegrationType.similarWeb]: [
    { value: IntegrationSnapshotDataType.similarWebVisits, label: 'Total Visits' },
    { value: IntegrationSnapshotDataType.similarWebUsers, label: 'Unique Visitors' },
    { value: IntegrationSnapshotDataType.similarWebPageviews, label: 'Page Views' },
  ],
  [IntegrationType.oxfordDataplan]: [
    { value: IntegrationSnapshotDataType.oxfordDataplanVolume, label: 'Volume' },
    { value: IntegrationSnapshotDataType.oxfordDataplanAdRevenue, label: 'Advertising Revenue' },
    { value: IntegrationSnapshotDataType.oxfordDataplanYoy, label: 'YoY' },
  ],
  [IntegrationType.sensortower]: [
    { value: IntegrationSnapshotDataType.sensortowerDownloads, label: 'Downloads' },
    { value: IntegrationSnapshotDataType.sensortowerActiveUsers, label: 'Active Users' },
  ],
}

// export const integrationSnapshotDataTypeNameMapping: Record<IntegrationSnapshotDataType, string> = {
//   [IntegrationSnapshotDataType.similarWebVisits]: 'Total Visitors',
//   [IntegrationSnapshotDataType.similarWebUsers]: 'Unique Visitors',
//   [IntegrationSnapshotDataType.similarWebPageviews]: 'Page Views',
//   [IntegrationSnapshotDataType.apptopiaDownloads]: 'Downloads',
//   [IntegrationSnapshotDataType.apptopiaActiveUsers]: 'Active Users',
//   [IntegrationSnapshotDataType.dataAiDownloads]: 'Downloads',
//   [IntegrationSnapshotDataType.dataAiActiveUsers]: 'Active Users',
//   [IntegrationSnapshotDataType.oxfordDataplanVolume]: 'Volume',
//   [IntegrationSnapshotDataType.oxfordDataplanAdRevenue]: 'Advertising Revenue',
//   [IntegrationSnapshotDataType.oxfordDataplanYoy]: 'YoY',
// }

export const FRBRDEProviders = [
  {
    iso: Iso3166Alpha2.FR,
    providers: ['vinted', 'leboncoin'],
    project: ProjectType.marketplaces,
  },
  {
    iso: Iso3166Alpha2.BR,
    providers: ['olx', 'enjoei'],
    project: ProjectType.marketplaces,
  },
  { iso: Iso3166Alpha2.DE, providers: ['vinted', 'ebk'], project: ProjectType.marketplaces },
  { iso: Iso3166Alpha2.DE, providers: ['mobile.de'], project: ProjectType.cars },
]

export const nordicProviders = [
  {
    iso: Iso3166Alpha2.NO,
    providers: ['finn.no', 'tise', 'temu'],
    project: ProjectType.marketplaces,
  },
  {
    iso: Iso3166Alpha2.NO,
    providers: ['hjem'],
    project: ProjectType.realestates,
  },
  {
    iso: Iso3166Alpha2.SE,
    providers: ['blocket', 'vinted', 'tise', 'plick', 'temu'],
    project: ProjectType.marketplaces,
  },
  {
    iso: Iso3166Alpha2.DK,
    providers: ['tise', 'trendsales', 'temu'],
    project: ProjectType.marketplaces,
  },
  {
    iso: Iso3166Alpha2.DK,
    providers: ['dba', 'boligsiden', 'boliga'],
    project: ProjectType.realestates,
  },
  {
    iso: Iso3166Alpha2.FI,
    providers: ['tise', 'temu'],
    project: ProjectType.marketplaces,
  },
  {
    iso: Iso3166Alpha2.FI,
    providers: ['tori'],
    project: ProjectType.realestates,
  },
]

export const accountingApiIntegrationProviders = {
  project: ProjectType.accounting,
  providers: ['fortnox', 'freee', 'ramp', 'brex', 'airbase', 'bill.com', 'melio', 'tipalti'],
  nameMapping: {
    fortnox: 'Fortnox',
    freee: 'Freee',
    ramp: 'Ramp',
    brex: 'Brex',
    airbase: 'Airbase',
    'bill.com': 'Bill',
    melio: 'Melio',
    tipalti: 'Tipalti',
  },
}

export const adevintaSnapshotFilters = {
  Unfiltered: {},
  'Total Listings For Sale Professional': {
    dealerType: 'dealer',
    stockType: ['residential_sales', 'commercial_sales'],
  },
  'Total Listings For Sale Private': {
    dealerType: 'private',
    stockType: ['residential_sales', 'commercial_sales'],
  },
  'Real Estate Sales in France': {
    stockType: 'properties_sold',
  },
  'Used Car Registrations in France': {
    stockType: 'used_registration',
  },
  'New Car Registrations in France': {
    stockType: 'new_registration',
  },
  'Used Car Registrations in Germany': {
    stockType: 'used_registration',
  },
  'New Car Registrations in Germany': {
    stockType: 'new_registration',
  },
  'Package Upsell Silver': {
    productType: 'SILVER',
  },
  'Package Upsell Gold': {
    productType: 'GOLD',
  },
  'Package Upsell Platinum': {
    productType: 'PLATINUM',
  },
  'Package Upsell Bronze': {
    productType: 'BRONZE',
  },
  'Package Upsell Partner': {
    productType: 'PARTNER',
  },
  'Total Listings Professional': {
    dealerType: 'dealer',
  },
  'Total Listings Private': {
    dealerType: 'private',
  },
  'Total Listings Professional Paying Agent': {
    dealerType: 'dealer',
    isPaying: true,
  },
  'Transactional Listings': {},
  'Total Agents Package Type Selection EBK RE': {
    productType: [
      'realestate basic',
      'realestate plus',
      'realestate pro',
      'realestate premium',
      'realestate optimum',
      'realestate start',
    ],
  },
  'Total Agents Package Type Selection EBK SMB': {
    productType: [
      'pro basic',
      'realestate pro',
      'realestate premium',
      'pro power',
      'pro optimum',
      'pro premium',
      'realestate optimum',
      'realestate basic',
      'realestate plus',
    ],
  },
  'Advertising Revenue': {
    isNew: true,
  },
  'Listings Package Type Platinum (relative %)': {
    productType: 'PLATINUM',
  },
  'Total Agents Package Type EBK RE Basic': {
    productType: ['realestate basic'],
  },
  'Total Agents Package Type EBK RE Plus': {
    productType: ['realestate plus'],
  },
  'Total Agents Package Type EBK RE Pro': {
    productType: ['realestate pro'],
  },
  'Total Agents Package Type EBK RE Premium': {
    productType: ['realestate premium'],
  },
  'Total Agents Package Type EBK RE Optimum': {
    productType: ['realestate optimum'],
  },
  'Total Agents Package Type EBK RE Start': {
    productType: ['realestate start'],
  },
}

export const adevintaQueryFilters: {
  project: ProjectType
  provider: string
  dataType: DataType
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof adevintaSnapshotFilters)[]
}[] = [
  {
    project: ProjectType.realestates,
    provider: 'leboncoin',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.FR,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'Total Listings For Sale Professional',
      'Total Listings For Sale Private',
      'Total Listings Professional',
      // 'Total Listings Professional Paying Agent',
    ],
  },
  {
    project: ProjectType.realestates,
    provider: 'all_country',
    dataType: DataType.yearlyProgress,
    iso: Iso3166Alpha2.FR,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Real Estate Sales in France'],
  },
  {
    project: ProjectType.realestates,
    provider: 'ebk',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'Total Listings Professional',
      // 'Total Listings For Sale Private',
      'Total Listings Professional Paying Agent',
    ],
  },
  {
    project: ProjectType.realestates,
    provider: 'ebk',
    dataType: DataType.dealers,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'Total Agents Package Type Selection EBK RE',
      'Advertising Revenue',
      'Total Agents Package Type EBK RE Basic',
      'Total Agents Package Type EBK RE Plus',
      'Total Agents Package Type EBK RE Pro',
      'Total Agents Package Type EBK RE Premium',
      'Total Agents Package Type EBK RE Optimum',
      'Total Agents Package Type EBK RE Start',
    ],
  },
  {
    project: ProjectType.cars,
    provider: 'all_country',
    dataType: DataType.yearlyProgress,
    iso: Iso3166Alpha2.FR,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Used Car Registrations in France', 'New Car Registrations in France'],
  },
  {
    project: ProjectType.cars,
    provider: 'all_country',
    dataType: DataType.yearlyProgress,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Used Car Registrations in Germany', 'New Car Registrations in Germany'],
  },
  {
    project: ProjectType.cars,
    provider: 'mobile.de',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'Package Upsell Partner',
      'Package Upsell Bronze',
      'Package Upsell Silver',
      'Package Upsell Gold',
      'Package Upsell Platinum',
      'Total Listings Professional',
      'Total Listings Private',
      'Listings Package Type Platinum (relative %)',
    ],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'leboncoin',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.FR,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Professional'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'ebk',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Professional', 'Transactional Listings'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'ebk_buy_now',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Transactional Listings'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'ebk_smb',
    dataType: DataType.dealers,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Agents Package Type Selection EBK SMB', 'Advertising Revenue'],
  },
]

export const schibstedSnapshotFilters = {
  'New Listings For Sale': {
    businessType: RealEstatesBusinessType.sale,
    isNew: true,
  },
  'New Listings For Sale In Oslo': {
    businessType: RealEstatesBusinessType.sale,
    isNew: true,
    state: 'NO-03',
  },
  'New Listings Professional For Sale': {
    businessType: RealEstatesBusinessType.sale,
    dealerType: DealerType.dealer,
    isNew: true,
  },
  'New Listings Professional For Sale In Oslo': {
    businessType: RealEstatesBusinessType.sale,
    dealerType: DealerType.dealer,
    isNew: true,
    state: 'NO-03',
  },
  'New Listings Residential For Sale': {
    propertyScope: RealEstatesPropertyScope.residential,
    businessType: RealEstatesBusinessType.sale,
    isNew: true,
  },
  'New Listings For Rent': {
    businessType: RealEstatesBusinessType.rent,
    isNew: true,
  },
  'New Listings Total': {
    isNew: true,
  },
  'New Listings': {
    isNew: true,
  },
  'New Listings Paid': {
    isNew: true,
  },
  'New Listings Direct': {
    isNew: true,
  },
  'New Listings Private': {
    isNew: true,
    dealerType: DealerType.private,
  },
  'New Listings Professional': {
    isNew: true,
    dealerType: DealerType.dealer,
  },
  'Total Listings For Sale': {
    businessType: RealEstatesBusinessType.sale,
  },
  'Total Listings Professional For Sale': {
    businessType: RealEstatesBusinessType.sale,
    dealerType: DealerType.dealer,
  },
  'Total Listings Professional Residential For Sale ARPL': {
    propertyScope: RealEstatesPropertyScope.residential,
    businessType: RealEstatesBusinessType.sale,
    dealerType: DealerType.dealer,
    isNew: true,
  },
  'Total Listings For Rent': {
    businessType: RealEstatesBusinessType.rent,
  },
  'Total Listings': {},
  'Total Listings Shipping': {},
  'Total Listings Shipping (Toridiili)': {},
  'Total Listings Private': {
    dealerType: DealerType.private,
  },
  'Total Listings Professional': {
    dealerType: DealerType.dealer,
  },
  'Total Listings For Sale In Oslo': {
    businessType: RealEstatesBusinessType.sale,
    state: 'NO-03',
  },
  'Total Listings Professional For Sale In Oslo': {
    businessType: RealEstatesBusinessType.sale,
    dealerType: DealerType.dealer,
    state: 'NO-03',
  },

  'New Listings Professional Cars': {
    isNew: true,
    dealerType: DealerType.dealer,
    vehicleType: VehicleType.car,
  },
  'New Listings Private Cars': {
    isNew: true,
    dealerType: DealerType.private,
    vehicleType: VehicleType.car,
  },
  'New Listings Professional Non Cars': {
    isNew: true,
    dealerType: DealerType.dealer,
    vehicleType: Object.values(VehicleType)?.filter((vehicleType) => vehicleType !== VehicleType.car),
  },
  'New Listings Private Non Cars': {
    isNew: true,
    dealerType: DealerType.private,
    vehicleType: Object.values(VehicleType)?.filter((vehicleType) => vehicleType !== VehicleType.car),
  },
  'New Listings Total Cars': {
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'Total Listings Professional Cars': {
    dealerType: DealerType.dealer,
    vehicleType: VehicleType.car,
  },
  'Total Listings Private Cars': {
    dealerType: DealerType.private,
    vehicleType: VehicleType.car,
  },
  'Total Listings Professional Non Cars': {
    dealerType: DealerType.dealer,
    vehicleType: Object.values(VehicleType)?.filter((vehicleType) => vehicleType !== VehicleType.car),
  },
  'Total Listings Private Non Cars': {
    dealerType: DealerType.private,
    vehicleType: Object.values(VehicleType)?.filter((vehicleType) => vehicleType !== VehicleType.car),
  },
  'New Sold Cars': {
    isNew: true,
  },
  'Total Sold Cars': {},
  'New Listings Professional Vehicle Parts': {
    isNew: true,
    dealerType: DealerType.dealer,
    stockType: ClassifiedsStockType.car_parts,
  },
  'New Listings Private Vehicle Parts': {
    isNew: true,
    dealerType: DealerType.private,
    stockType: ClassifiedsStockType.car_parts,
  },
}

export const schibstedQueryFilters: {
  project: ProjectType
  provider: string
  dataType: DataType
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof schibstedSnapshotFilters)[]
}[] = [
  {
    project: ProjectType.realestates,
    provider: 'finn.no_realestate_homes',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings For Sale'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no_reported',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Professional Residential For Sale ARPL'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Residential For Sale'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings For Rent'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no_reported',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings For Rent'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no_reported',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings For Sale'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no_reported',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings For Sale'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Professional For Sale In Oslo'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no_reported',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Professional For Sale'],
  },
  {
    project: ProjectType.realestates,
    provider: 'finn.no_reported',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Professional For Sale In Oslo'],
  },
  {
    project: ProjectType.realestates,
    provider: 'hjem',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'New Listings Residential For Sale',
      'New Listings Professional For Sale In Oslo',
      'Total Listings Professional For Sale',
      'Total Listings Professional For Sale In Oslo',
    ],
  },
  {
    project: ProjectType.realestates,
    provider: 'oikotie',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.FI,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Professional', 'New Listings Private'],
  },

  {
    project: ProjectType.cars,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'New Listings Private Cars',
      'New Listings Private',
      'New Listings Professional Cars',
      'New Listings Professional',
    ],
  },
  {
    project: ProjectType.cars,
    provider: 'drive.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Professional', 'New Listings Private', 'New Listings Professional Cars'],
  },
  {
    project: ProjectType.cars,
    provider: 'nettbil',
    dataType: DataType.soldCar,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Sold Cars', 'New Sold Cars'],
  },
  {
    project: ProjectType.cars,
    provider: 'blocket',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'New Listings Professional',
      'New Listings Private',
      'New Listings Professional Cars',
      'New Listings Private Cars',
    ],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'blocket',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Professional Vehicle Parts', 'New Listings Private Vehicle Parts'],
  },
  {
    project: ProjectType.cars,
    provider: 'dba',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DK,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'New Listings Professional',
      'New Listings Private',
      'New Listings Professional Cars',
      'New Listings Private Cars',
    ],
  },
  {
    project: ProjectType.cars,
    provider: 'bilbasen',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DK,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'Total Listings Professional Cars',
      'Total Listings Private Cars',
      'Total Listings Professional Non Cars',
      'Total Listings Private Non Cars',
      'New Listings Private',
    ],
  },

  {
    project: ProjectType.jobs,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Total'],
  },
  {
    project: ProjectType.jobs,
    provider: 'finn.no_paidAds',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Paid'],
  },
  {
    project: ProjectType.jobs,
    provider: 'blocket',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Total'],
  },
  {
    project: ProjectType.jobs,
    provider: 'blocket_internal',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Direct'],
  },
  {
    project: ProjectType.jobs,
    provider: 'oikotie',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.FI,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['New Listings Total'],
  },

  {
    project: ProjectType.marketplaces,
    provider: 'finn.no',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'finn.no_shipping',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.NO,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Shipping'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'blocket',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'blocket_shipping',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Shipping'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'tori',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.FI,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings'],
  },
  {
    project: ProjectType.marketplaces,
    provider: 'tori_diili',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.FI,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Total Listings Shipping'],
  },
]

export const hemnetMonthlyQueryFilters: {
  project: ProjectType
  provider: string
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof hemnetSnapshotFilters)[]
  dataType?: DataType
  integration?: IntegrationType
}[] = [
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['New Listings', 'Crossposted Listings Views'],
    dataType: DataType.inventory,
  },
  {
    project: ProjectType.realestates,
    provider: 'booli',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Crossposted Listings Views'],
    dataType: DataType.inventory,
  },
  {
    project: ProjectType.realestates,
    provider: 'booli_statistics',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: [
      'Apartments for Sale',
      'Apartments Soon to Sell',
      'Apartments Days to Sell',
      'Homes for Sale',
      'Homes Soon to Sell',
      'Homes Days to Sell',
    ],
    dataType: DataType.yearlyProgress,
  },
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Active Users Data AI'],
    integration: IntegrationType.dataAi,
  },
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['App Downloads Apptopia'],
    integration: IntegrationType.apptopia,
  },
  {
    project: ProjectType.realestates,
    provider: 'booli',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Active Users Data AI'],
    integration: IntegrationType.dataAi,
  },
  {
    project: ProjectType.realestates,
    provider: 'booli',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['App Downloads Apptopia'],
    integration: IntegrationType.apptopia,
  },
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Page Views (million)', 'Total Visitors', 'Unique Visitors'],
    integration: IntegrationType.similarWeb,
  },
  {
    project: ProjectType.realestates,
    provider: 'booli',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Page Views (million)', 'Total Visitors', 'Unique Visitors'],
    integration: IntegrationType.similarWeb,
  },
  {
    project: ProjectType.realestates,
    provider: 'boneo',
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.months,
    verticals: ['Page Views (million)', 'Total Visitors', 'Unique Visitors'],
    integration: IntegrationType.similarWeb,
  },
]

export const hemnetQueryFilters: {
  project: ProjectType
  provider: string
  dataType: DataType
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof hemnetSnapshotFilters)[]
}[] = [
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    dataType: DataType.yearlyProgress,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Reported New Listings', 'Reported Total Listings', 'Reported Average Listing Price'],
  },
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'New Listings Package Basic',
      'New Listings Package Plus',
      'New Listings Package Premium',
      'New Listings Package Max',
      'Stockholm New Listings Package Basic',
      'Stockholm New Listings Package Plus',
      'Stockholm New Listings Package Premium',
      'Stockholm New Listings Package Max',
      'Average Max Views',
      'Average Premium Views',
      'ARPL',
      'Crossposted Listings Views',
    ],
  },
  {
    project: ProjectType.realestates,
    provider: 'booli',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: ['Crossposted Listings Views'],
  },
]

export const hemnetIrQueryFilters: {
  project: ProjectType
  provider: string
  dataType: DataType
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof hemnetSnapshotFilters)[]
}[] = [
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'ARPL',
      'Stockholm New Listings Package Basic',
      'Stockholm New Listings Package Plus',
      'Stockholm New Listings Package Premium',
      'Stockholm New Listings Package Max',
      'New Listings Package Basic',
      'New Listings Package Plus',
      'New Listings Package Premium',
      'New Listings Package Max',
      'Total Listings Package Basic',
      'Total Listings Package Plus',
      'Total Listings Package Premium',
      'Total Listings Package Max',
    ],
  },
]

export const hemnetNewListingsPackagesQueryFilters: {
  project: ProjectType
  provider: string
  dataType: DataType
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof hemnetSnapshotFilters)[]
}[] = [
  {
    project: ProjectType.realestates,
    provider: 'hemnet',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.SE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'New Listings Package Basic',
      'New Listings Package Plus',
      'New Listings Package Premium',
      'New Listings Package Max',
      'Stockholm New Listings Package Basic',
      'Stockholm New Listings Package Plus',
      'Stockholm New Listings Package Premium',
      'Stockholm New Listings Package Max',
      'Blekinge New Listings Package Basic',
      'Blekinge New Listings Package Plus',
      'Blekinge New Listings Package Premium',
      'Blekinge New Listings Package Max',
      'Dalarna New Listings Package Basic',
      'Dalarna New Listings Package Plus',
      'Dalarna New Listings Package Premium',
      'Dalarna New Listings Package Max',
      'Gotland New Listings Package Basic',
      'Gotland New Listings Package Plus',
      'Gotland New Listings Package Premium',
      'Gotland New Listings Package Max',
      'Gävleborg New Listings Package Basic',
      'Gävleborg New Listings Package Plus',
      'Gävleborg New Listings Package Premium',
      'Gävleborg New Listings Package Max',
      'Halland New Listings Package Basic',
      'Halland New Listings Package Plus',
      'Halland New Listings Package Premium',
      'Halland New Listings Package Max',
      'Jämtland New Listings Package Basic',
      'Jämtland New Listings Package Plus',
      'Jämtland New Listings Package Premium',
      'Jämtland New Listings Package Max',
      'Jönköping New Listings Package Basic',
      'Jönköping New Listings Package Plus',
      'Jönköping New Listings Package Premium',
      'Jönköping New Listings Package Max',
      'Kalmar New Listings Package Basic',
      'Kalmar New Listings Package Plus',
      'Kalmar New Listings Package Premium',
      'Kalmar New Listings Package Max',
      'Kronoberg New Listings Package Basic',
      'Kronoberg New Listings Package Plus',
      'Kronoberg New Listings Package Premium',
      'Kronoberg New Listings Package Max',
      'Norrbotten New Listings Package Basic',
      'Norrbotten New Listings Package Plus',
      'Norrbotten New Listings Package Premium',
      'Norrbotten New Listings Package Max',
      'Skåne New Listings Package Basic',
      'Skåne New Listings Package Plus',
      'Skåne New Listings Package Premium',
      'Skåne New Listings Package Max',
      'Södermanland New Listings Package Basic',
      'Södermanland New Listings Package Plus',
      'Södermanland New Listings Package Premium',
      'Södermanland New Listings Package Max',
      'Uppsala New Listings Package Basic',
      'Uppsala New Listings Package Plus',
      'Uppsala New Listings Package Premium',
      'Uppsala New Listings Package Max',
      'Värmland New Listings Package Basic',
      'Värmland New Listings Package Plus',
      'Värmland New Listings Package Premium',
      'Värmland New Listings Package Max',
      'Västerbotten New Listings Package Basic',
      'Västerbotten New Listings Package Plus',
      'Västerbotten New Listings Package Premium',
      'Västerbotten New Listings Package Max',
      'Västernorrland New Listings Package Basic',
      'Västernorrland New Listings Package Plus',
      'Västernorrland New Listings Package Premium',
      'Västernorrland New Listings Package Max',
      'Västmanland New Listings Package Basic',
      'Västmanland New Listings Package Plus',
      'Västmanland New Listings Package Premium',
      'Västmanland New Listings Package Max',
      'Västra Götaland New Listings Package Basic',
      'Västra Götaland New Listings Package Plus',
      'Västra Götaland New Listings Package Premium',
      'Västra Götaland New Listings Package Max',
      'Örebro New Listings Package Basic',
      'Örebro New Listings Package Plus',
      'Örebro New Listings Package Premium',
      'Örebro New Listings Package Max',
      'Östergötland New Listings Package Basic',
      'Östergötland New Listings Package Plus',
      'Östergötland New Listings Package Premium',
      'Östergötland New Listings Package Max',
    ],
  },
]

export const hemnetSnapshotFilters = {
  'New Listings': {
    isNew: true,
  },
  'Reported New Listings': {
    stockType: 'published_listings',
    isNew: true,
  },
  'Total Listings': {},
  'Reported Total Listings': {
    stockType: 'property_supply',
  },
  ARPL: {
    isNew: true,
  },
  'Reported Average Listing Price': {
    stockType: 'asking_prices',
  },
  'Crossposted Listings Views': {
    isCrossposted: true,
  },
  'Stockholm New Listings': {
    isNew: true,
    state: 'SE-AB',
  },
  'New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
  },
  'New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
  },
  'New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
  },
  'New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
  },
  'Stockholm New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-AB',
  },
  'Stockholm New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-AB',
  },
  'Stockholm New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-AB',
  },
  'Stockholm New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-AB',
  },
  'Blekinge New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-K',
  },
  'Blekinge New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-K',
  },
  'Blekinge New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-K',
  },
  'Blekinge New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-K',
  },
  'Dalarna New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-W',
  },
  'Dalarna New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-W',
  },
  'Dalarna New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-W',
  },
  'Dalarna New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-W',
  },
  'Gotland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-I',
  },
  'Gotland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-I',
  },
  'Gotland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-I',
  },
  'Gotland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-I',
  },
  'Gävleborg New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-X',
  },
  'Gävleborg New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-X',
  },
  'Gävleborg New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-X',
  },
  'Gävleborg New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-X',
  },
  'Halland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-N',
  },
  'Halland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-N',
  },
  'Halland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-N',
  },
  'Halland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-N',
  },
  'Jämtland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-Z',
  },
  'Jämtland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-Z',
  },
  'Jämtland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-Z',
  },
  'Jämtland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-Z',
  },
  'Jönköping New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-F',
  },
  'Jönköping New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-F',
  },
  'Jönköping New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-F',
  },
  'Jönköping New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-F',
  },
  'Kalmar New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-H',
  },
  'Kalmar New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-H',
  },
  'Kalmar New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-H',
  },
  'Kalmar New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-H',
  },
  'Kronoberg New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-G',
  },
  'Kronoberg New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-G',
  },
  'Kronoberg New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-G',
  },
  'Kronoberg New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-G',
  },
  'Norrbotten New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-BD',
  },
  'Norrbotten New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-BD',
  },
  'Norrbotten New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-BD',
  },
  'Norrbotten New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-BD',
  },
  'Skåne New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-M',
  },
  'Skåne New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-M',
  },
  'Skåne New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-M',
  },
  'Skåne New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-M',
  },
  'Södermanland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-D',
  },
  'Södermanland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-D',
  },
  'Södermanland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-D',
  },
  'Södermanland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-D',
  },
  'Uppsala New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-C',
  },
  'Uppsala New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-C',
  },
  'Uppsala New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-C',
  },
  'Uppsala New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-C',
  },
  'Värmland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-S',
  },
  'Värmland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-S',
  },
  'Värmland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-S',
  },
  'Värmland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-S',
  },
  'Västerbotten New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-AC',
  },
  'Västerbotten New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-AC',
  },
  'Västerbotten New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-AC',
  },
  'Västerbotten New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-AC',
  },
  'Västernorrland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-Y',
  },
  'Västernorrland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-Y',
  },
  'Västernorrland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-Y',
  },
  'Västernorrland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-Y',
  },
  'Västmanland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-U',
  },
  'Västmanland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-U',
  },
  'Västmanland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-U',
  },
  'Västmanland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-U',
  },
  'Västra Götaland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-O',
  },
  'Västra Götaland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-O',
  },
  'Västra Götaland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-O',
  },
  'Västra Götaland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-O',
  },
  'Örebro New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-T',
  },
  'Örebro New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-T',
  },
  'Örebro New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-T',
  },
  'Örebro New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-T',
  },
  'Östergötland New Listings Package Basic': {
    productType: 'BASIC',
    isNew: true,
    state: 'SE-E',
  },
  'Östergötland New Listings Package Plus': {
    productType: 'PLUS',
    isNew: true,
    state: 'SE-E',
  },
  'Östergötland New Listings Package Premium': {
    productType: 'PREMIUM',
    isNew: true,
    state: 'SE-E',
  },
  'Östergötland New Listings Package Max': {
    productType: 'MAX',
    isNew: true,
    state: 'SE-E',
  },
  'Average Max Views': {
    productType: 'MAX',
  },
  'Average Premium Views': {
    productType: 'PREMIUM',
  },
  'Page Views (million)': {
    dataType: IntegrationSnapshotDataType.similarWebPageviews,
  },
  'Total Visitors': {
    dataType: IntegrationSnapshotDataType.similarWebVisits,
  },
  'Unique Visitors': {
    dataType: IntegrationSnapshotDataType.similarWebUsers,
  },
  'Active Users Data AI': {
    dataType: IntegrationSnapshotDataType.dataAiActiveUsers,
  },
  'App Downloads Data AI': {
    dataType: IntegrationSnapshotDataType.dataAiDownloads,
  },
  'Active Users Apptopia': {
    dataType: IntegrationSnapshotDataType.apptopiaActiveUsers,
  },
  'App Downloads Apptopia': {
    dataType: IntegrationSnapshotDataType.apptopiaDownloads,
  },
  'Active Users SensorTower': {
    dataType: IntegrationSnapshotDataType.sensortowerActiveUsers,
  },
  'App Downloads SensorTower': {
    dataType: IntegrationSnapshotDataType.sensortowerDownloads,
  },
  'Apartments for Sale': {
    stockType: 'apartments_for_sale',
  },
  'Apartments Soon to Sell': {
    stockType: 'apartments_coming_soon',
  },
  'Apartments Days to Sell': {
    stockType: 'apartments_days_to_sell',
  },
  'Homes for Sale': {
    stockType: 'houses_for_sale',
  },
  'Homes Soon to Sell': {
    stockType: 'houses_coming_soon',
  },
  'Homes Days to Sell': {
    stockType: 'houses_days_to_sell',
  },
  ////
  'Total Listings Package Basic': {
    productType: 'BASIC',
  },
  'Total Listings Package Plus': {
    productType: 'PLUS',
  },
  'Total Listings Package Premium': {
    productType: 'PREMIUM',
  },
  'Total Listings Package Max': {
    productType: 'MAX',
  },
  'Stockholm Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-AB',
  },
  'Stockholm Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-AB',
  },
  'Stockholm Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-AB',
  },
  'Stockholm Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-AB',
  },
  'Blekinge Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-K',
  },
  'Blekinge Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-K',
  },
  'Blekinge Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-K',
  },
  'Blekinge Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-K',
  },
  'Dalarna Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-W',
  },
  'Dalarna Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-W',
  },
  'Dalarna Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-W',
  },
  'Dalarna Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-W',
  },
  'Gotland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-I',
  },
  'Gotland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-I',
  },
  'Gotland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-I',
  },
  'Gotland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-I',
  },
  'Gävleborg Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-X',
  },
  'Gävleborg Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-X',
  },
  'Gävleborg Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-X',
  },
  'Gävleborg Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-X',
  },
  'Halland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-N',
  },
  'Halland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-N',
  },
  'Halland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-N',
  },
  'Halland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-N',
  },
  'Jämtland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-Z',
  },
  'Jämtland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-Z',
  },
  'Jämtland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-Z',
  },
  'Jämtland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-Z',
  },
  'Jönköping Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-F',
  },
  'Jönköping Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-F',
  },
  'Jönköping Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-F',
  },
  'Jönköping Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-F',
  },
  'Kalmar Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-H',
  },
  'Kalmar Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-H',
  },
  'Kalmar Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-H',
  },
  'Kalmar Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-H',
  },
  'Kronoberg Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-G',
  },
  'Kronoberg Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-G',
  },
  'Kronoberg Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-G',
  },
  'Kronoberg Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-G',
  },
  'Norrbotten Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-BD',
  },
  'Norrbotten Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-BD',
  },
  'Norrbotten Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-BD',
  },
  'Norrbotten Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-BD',
  },
  'Skåne Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-M',
  },
  'Skåne Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-M',
  },
  'Skåne Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-M',
  },
  'Skåne Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-M',
  },
  'Södermanland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-D',
  },
  'Södermanland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-D',
  },
  'Södermanland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-D',
  },
  'Södermanland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-D',
  },
  'Uppsala Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-C',
  },
  'Uppsala Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-C',
  },
  'Uppsala Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-C',
  },
  'Uppsala Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-C',
  },
  'Värmland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-S',
  },
  'Värmland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-S',
  },
  'Värmland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-S',
  },
  'Värmland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-S',
  },
  'Västerbotten Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-AC',
  },
  'Västerbotten Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-AC',
  },
  'Västerbotten Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-AC',
  },
  'Västerbotten Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-AC',
  },
  'Västernorrland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-Y',
  },
  'Västernorrland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-Y',
  },
  'Västernorrland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-Y',
  },
  'Västernorrland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-Y',
  },
  'Västmanland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-U',
  },
  'Västmanland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-U',
  },
  'Västmanland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-U',
  },
  'Västmanland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-U',
  },
  'Västra Götaland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-O',
  },
  'Västra Götaland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-O',
  },
  'Västra Götaland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-O',
  },
  'Västra Götaland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-O',
  },
  'Örebro Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-T',
  },
  'Örebro Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-T',
  },
  'Örebro Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-T',
  },
  'Örebro Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-T',
  },
  'Östergötland Total Listings Package Basic': {
    productType: 'BASIC',
    state: 'SE-E',
  },
  'Östergötland Total Listings Package Plus': {
    productType: 'PLUS',
    state: 'SE-E',
  },
  'Östergötland Total Listings Package Premium': {
    productType: 'PREMIUM',
    state: 'SE-E',
  },
  'Östergötland Total Listings Package Max': {
    productType: 'MAX',
    state: 'SE-E',
  },
}

export const mobileWeeklyPackagesQueryFilters: {
  project: ProjectType
  provider: string
  dataType: DataType
  iso: Iso3166Alpha2
  snapTimeUnit: TimeUnit
  verticals: (keyof typeof mobileSnapshotFilters)[]
}[] = [
  {
    project: ProjectType.cars,
    provider: 'mobile.de',
    dataType: DataType.inventory,
    iso: Iso3166Alpha2.DE,
    snapTimeUnit: TimeUnit.weeks,
    verticals: [
      'Total Listings Professional',
      'Total Listings Private',
      'New Listings Package Bronze Professional',
      'New Listings Package Silver Professional',
      'New Listings Package Gold Professional',
      'New Listings Package Platinum Professional',
      'New Listings Package Partner Professional',
      'New Listings Package FSBO Professional',
      'New Listings Package Bronze Private',
      'New Listings Package Silver Private',
      'New Listings Package Gold Private',
      'New Listings Package Platinum Private',
      'New Listings Package Partner Private',
      'New Listings Package FSBO Private',
    ],
  },
]

export const mobileSnapshotFilters = {
  'Total Listings Professional': {
    dealerType: 'dealer',
    vehicleType: VehicleType.car,
  },
  'Total Listings Private': {
    dealerType: 'private',
    vehicleType: VehicleType.car,
  },
  'Total New Listings With Package Professional': {
    dealerType: DealerType.dealer,
    productType: ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'PARTNER', 'FSBO'],
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'Total New Listings With Package Private': {
    dealerType: DealerType.private,
    productType: ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'PARTNER', 'FSBO'],
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Bronze Professional': {
    dealerType: DealerType.dealer,
    productType: 'BRONZE',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Silver Professional': {
    dealerType: DealerType.dealer,
    productType: 'SILVER',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Gold Professional': {
    dealerType: DealerType.dealer,
    productType: 'GOLD',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Platinum Professional': {
    dealerType: DealerType.dealer,
    productType: 'PLATINUM',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Partner Professional': {
    dealerType: DealerType.dealer,
    productType: 'PARTNER',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package FSBO Professional': {
    dealerType: DealerType.dealer,
    productType: 'FSBO',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Bronze Private': {
    dealerType: DealerType.private,
    productType: 'BRONZE',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Silver Private': {
    dealerType: DealerType.private,
    productType: 'SILVER',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Gold Private': {
    dealerType: DealerType.private,
    productType: 'GOLD',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Platinum Private': {
    dealerType: DealerType.private,
    productType: 'PLATINUM',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package Partner Private': {
    dealerType: DealerType.private,
    productType: 'PARTNER',
    isNew: true,
    vehicleType: VehicleType.car,
  },
  'New Listings Package FSBO Private': {
    dealerType: DealerType.private,
    productType: 'FSBO',
    isNew: true,
    vehicleType: VehicleType.car,
  },
}

export const nordicProvidersIntegrationSnapshot = [
  /Finn/i,
  /Nettbil/i,
  /Prisjakt/i,
  /Hjem/i,
  /Vinted/i,
  /Temu/i,
  /Tise/i,
  /Blocket/i,
  /Plick/i,
  /Bytbil/i,
  /Indeed/i,
  /Monster/i,
  /Lendo/i,
  /Servicefinder/i,
  /DBA/i,
  /Bilbasen/i,
  /Biltorvet/i,
  /Boligsiden/i,
  /Boliga/i,
  /Trendsales/i,
  /Vinted/i,
  /Tori/i,
  /Nettiauto/i,
  /Autotalli/i,
  /Autotie/i,
  /Oikotie/i,
  /Etuovi/i,
  /Duunitori/i,
  /Jobly/i,
  /Rakentaja/i,
  /Hintaopas/i,
  /Lendo/i,
]

export const adevintaProvidersIntegrationSnapshot = [
  /Leboncoin/i,
  /amazon/i,
  /eBay/i,
  /vinted/i,
  /lacentrale/i,
  /autoscout24/i,
  /autohero/i,
  /seloger/i,
  /logicimmo/i,
  /olx/i,
  /webmotors/i,
  /icarros/i,
  /zapimoveis/i,
  /vivareal/i,
  /imovelweb/i,
  /enjoei/i,
  /ebk/i,
  /mobile.de/i,
  /milanuncios/i,
  /wallapop/i,
  /coches/i,
  /autocasion/i,
  /autohero/i,
  /habitaclia/i,
  /fotocasa/i,
  /pisos/i,
  /idealista/i,
  /indeed/i,
  /infojobs/i,
  /monster/i,
  /kijiji/i,
  /autotrader/i,
  /cargurus/i,
  /realtor/i,
  /gumtree/i,
  /carsales/i,
  /realestate.com.au/i,
  /subito/i,
]

export const costarProvidersIntegrationSnapshot = [
  'homes',
  'realtor',
  'zillow',
  /costar/i,
  'loopnet',
  'str.com',
  'ten-x',
  'crexi',
  'bizbuysell',
  'land.com',
]

export const smgProvidersIntegrationSnapshot = [
  'autoscout24',
  'motoscout24',
  'homegate',
  'immoscout24',
  'immostreet',
  'home',
  'alleimmobilien',
  'flatfox',
  'acheter-louer',
  /iazi/i,
  /casasoft/i,
  /publimmo/i,
  'ricardo',
  'tutti',
  'anibis',
  'immobilier',
  'newhome',
  'autolina',
  'carmarket',
  'properstar',
  'realadvisor',
  'comparis',
]

export const bcgProvidersIntegrationSnapshot = [
  'autoplius',
  'auto24',
  'aruodas',
  'kv',
  'city24',
  'paslaugos',
  'cvbankas',
  'getapro',
  'skelbiu',
  'kuldnebors',
  'autogidas',
  'domoplius',
  'kinnisvara24',
  'cvmarket',
]
