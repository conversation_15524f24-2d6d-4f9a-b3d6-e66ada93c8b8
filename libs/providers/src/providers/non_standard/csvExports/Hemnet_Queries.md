# Hemnet / Booli CSV Export Aggregation Queries

Curated MongoDB aggregation pipelines used to build the various CSV exports. Use the table of contents below to jump directly to a dataset. All items are unchecked for manual bookkeeping (tick them as you go).

## Table of Contents

### For Sale – Standard Filters

- [ ] [hemnet_booli_for_sale_all.csv](#hemnet_booli_for_sale_allcsv)
- [ ] [hemnet_booli_for_sale_no_price.csv](#hemnet_booli_for_sale_no_pricecsv)
- [ ] [hemnet_booli_for_sale_no_images.csv](#hemnet_booli_for_sale_no_imagescsv)
- [ ] [hemnet_booli_for_sale_no_price_no_images.csv](#hemnet_booli_for_sale_no_price_no_imagescsv)
- [ ] [hemnet_booli_for_sale_images_no_price.csv](#hemnet_booli_for_sale_images_no_pricecsv)

### For Sale – 60 Days

- [ ] [hemnet_booli_for_sale_all_60_days.csv](#hemnet_booli_for_sale_all_60_dayscsv)
- [ ] [hemnet_booli_for_sale_no_price_60_days.csv](#hemnet_booli_for_sale_no_price_60_dayscsv)
- [ ] [hemnet_booli_for_sale_no_images_60_days.csv](#hemnet_booli_for_sale_no_images_60_dayscsv)
- [ ] [hemnet_booli_for_sale_no_price_no_images_60_days.csv](#hemnet_booli_for_sale_no_price_no_images_60_dayscsv)
- [ ] [hemnet_booli_for_sale_images_no_price_60_days.csv](#hemnet_booli_for_sale_images_no_price_60_dayscsv)

### For Sale – 5 Weeks

- [ ] [hemnet_all_items_for_sale_5_weeks.csv](#hemnet_all_items_for_sale_5_weekscsv)
- [ ] [booli_all_items_for_sale_5_weeks.csv](#booli_all_items_for_sale_5_weekscsv)

### For Sale – By Price

- [ ] [hemnet_booli_for_sale_all_by_price.csv](#hemnet_booli_for_sale_all_by_pricecsv)
- [ ] [hemnet_booli_for_sale_all_by_price_no_price.csv](#hemnet_booli_for_sale_all_by_price_no_pricecsv)

### For Sale – API Days Active

- [ ] [hemnet_all_items_for_sale_api_days.csv](#hemnet_all_items_for_sale_api_dayscsv)
- [ ] [hemnet_all_items_for_sale_api_days_no_new_productions.csv](#hemnet_all_items_for_sale_api_days_no_new_productionscsv)
- [ ] [hemnet_all_items_for_sale.csv](#hemnet_all_items_for_salecsv)

### Pre‑Market / Upcoming – Standard Filters

- [ ] [hemnet_booli_pre_market_all.csv](#hemnet_booli_pre_market_allcsv)
- [ ] [hemnet_booli_pre_market_no_price.csv](#hemnet_booli_pre_market_no_pricecsv)
- [ ] [hemnet_booli_pre_market_no_images.csv](#hemnet_booli_pre_market_no_imagescsv)
- [ ] [hemnet_booli_pre_market_no_images_no_price.csv](#hemnet_booli_pre_market_no_images_no_pricecsv)
- [ ] [hemnet_booli_pre_market_images_no_price.csv](#hemnet_booli_pre_market_images_no_pricecsv)

### Pre‑Market / Upcoming – 60 Days

- [ ] [hemnet_booli_pre_market_all_60_days.csv](#hemnet_booli_pre_market_all_60_dayscsv)
- [ ] [hemnet_booli_pre_market_no_price_60_days.csv](#hemnet_booli_pre_market_no_price_60_dayscsv)
- [ ] [hemnet_booli_pre_market_no_images_60_days.csv](#hemnet_booli_pre_market_no_images_60_dayscsv)
- [ ] [hemnet_booli_pre_market_no_price_no_images_60_days.csv](#hemnet_booli_pre_market_no_price_no_images_60_dayscsv)
- [ ] [hemnet_booli_pre_market_images_no_price_60_days.csv](#hemnet_booli_pre_market_images_no_price_60_dayscsv)

### Pre‑Market / Upcoming – 5 Weeks

- [ ] [hemnet_all_items_upcoming_5_weeks.csv](#hemnet_all_items_upcoming_5_weekscsv)
- [ ] [booli_all_items_upcoming_5_weeks.csv](#booli_all_items_upcoming_5_weekscsv)

### Additional / Derived Stats

- [ ] [hemnet_average_days_active.csv](#hemnet_average_days_activecsv)
- [ ] [hemnet_booli_for_sale_all_60_30_days](#hemnet_booli_for_sale_all_60_30_days)
- [ ] [hemnet_booli_for_sale_no_price_60_30_days](#hemnet_booli_for_sale_no_price_60_30_days)

---

## Legend

Field buckets used across many aggregations:

- daysBucket: <7, 7-30, 30-59, 60-182, 183-365, 366-730, 731-999, >999 (days active on platform)
- viewsBucket: 0, 1-5, 6-10, 11-20, 21-50, 51-100, 101-200, 201-300, 301-400, 401-1000, 1000+ (ad view counts)
- priceBucket: <1.5m, 1.5-3m, 3-5m, 5-7m, 7-9m, 9-11m, 11-15m, 15-20m, >20m SEK

Conventions:

- All queries filter project = realestates and dataType = inventory unless noted.
- Upcoming / pre‑market records: Hemnet uses data.isUpcoming, Booli uses data.upcomingSale.
- Missing price logic treats null / absent / 0 as “no price”.
- Each section header matches the CSV filename generated.
- Use the arrays directly in MongoDB Compass or the shell (they are plain aggregation pipelines).

---

## hemnet_booli_for_sale_no_price.csv

_Listings active for Hemnet & Booli without a published asking price. Filters out upcoming/pre‑market items._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      // Filter for listings WITHOUT price
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $and: [
        {
          $or: [
            {
              provider: 'booli',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
              },
              'data.upcomingSale': {
                $ne: true,
              },
            },
            {
              provider: 'hemnet',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
              },
              'data.isUpcoming': {
                $ne: true,
              },
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_no_images.csv

_Active listings that currently have no images supplied (image array empty or missing)._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': {
            $ne: true,
          },
          // Booli: Filter for listings WITHOUT images
          $or: [
            { 'data.images': { $exists: false } },
            { 'data.images': null },
            { 'data.images': [] },
            { 'data.images.0': { $exists: false } },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': {
            $ne: true,
          },
          // Hemnet: No image filtering (not tracked in database)
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByPrice',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_for_sale_no_price_no_images.csv

_Listings missing both price and images; surfaces potentially incomplete or low-quality entries._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': {
            $ne: true,
          },
          // Booli: No price filter
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: No images filter
          $and: [
            {
              $or: [
                { 'data.images': { $exists: false } },
                { 'data.images': null },
                { 'data.images': [] },
                { 'data.images.0': { $exists: false } },
              ],
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': {
            $ne: true,
          },
          // Hemnet: No price filter only
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_images_no_price.csv

_Listings with images present but no published price._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': {
            $ne: true,
          },
          // Booli: No price filter
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: HAS images (at least 1 image exists)
          'data.images.0': {
            $exists: true,
          },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': {
            $ne: true,
          },
          // Hemnet: No price filter only
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_all_60_days.csv

_All active listings (price optional) seen within the last 60 days, standard availability filter._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': {
            $ne: true,
          },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': {
            $ne: true,
          },
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_for_sale_no_price_60_days.csv

_60‑day window subset where price data is missing or zero._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      // Filter for listings WITHOUT price
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $and: [
        {
          $or: [
            {
              provider: 'booli',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
                $lt: 60,
              },
              'data.upcomingSale': {
                $ne: true,
              },
            },
            {
              provider: 'hemnet',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
                $lt: 60,
              },
              'data.isUpcoming': {
                $ne: true,
              },
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_for_sale_no_images_60_days.csv

_60‑day window subset of listings without images._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': {
            $ne: true,
          },
          // Booli: Filter for listings WITHOUT images
          $or: [
            { 'data.images': { $exists: false } },
            { 'data.images': null },
            { 'data.images': [] },
            { 'data.images.0': { $exists: false } },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': {
            $ne: true,
          },
          // Hemnet: No image filtering (not tracked in database)
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_for_sale_no_price_no_images_60_days.csv

_60‑day listings missing both price and images._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': {
            $ne: true,
          },
          // Booli: No price filter
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: No images filter
          $and: [
            {
              $or: [
                { 'data.images': { $exists: false } },
                { 'data.images': null },
                { 'data.images': [] },
                { 'data.images.0': { $exists: false } },
              ],
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': {
            $ne: true,
          },
          // Hemnet: No price filter only
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_for_sale_images_no_price_60_days.csv

_60‑day listings that have images but no price._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': {
            $ne: true,
          },
          // Booli: No price filter
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: HAS images (at least 1 image exists)
          'data.images.0': {
            $exists: true,
          },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': {
            $ne: true,
          },
          // Hemnet: No price filter only
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_all_items_for_sale_5_weeks.csv

_Hemnet listings active within the last 5 rolling weeks (excludes upcoming)._

```javascript
{
  projectProviderDataType:
    "realestates_hemnet_inventory",
 createdAt: {
        $gte: new Date("2025-09-01")
      },
  "data.isUpcoming": false
}
```

[⬆ Back to top](#table-of-contents)

## booli_all_items_for_sale_5_weeks.csv

_Booli listings active within the last 5 rolling weeks (excludes upcoming)._

```javascript
{
  projectProviderDataType:
    "realestates_booli_inventory",
 createdAt: {
        $gte: new Date("2025-09-01")
      },
  "data.upcomingSale": false
}
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_all_by_price.csv

_Aggregated distribution of all active listings by price bucket with view stats._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      $or: [
        {
          provider: 'booli',
          'data.upcomingSale': {
            $ne: true,
          },
        },
        {
          provider: 'hemnet',
          'data.isUpcoming': {
            $ne: true,
          },
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      price: {
        $ifNull: ['$price', 0],
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $group: {
      _id: {
        provider: '$provider',
        priceBucket: '$priceBucket',
      },
      listingCount: {
        $sum: 1,
      },
      totalViews: {
        $sum: '$adViews',
      },
      averageViews: {
        $avg: '$adViews',
      },
    },
  },
  {
    $project: {
      _id: 0,
      provider: '$_id.provider',
      priceBucket: '$_id.priceBucket',
      listingCount: 1,
      totalViews: 1,
      averageViews: {
        $round: ['$averageViews', 2],
      },
    },
  },
  {
    $sort: {
      provider: 1,
      priceBucket: 1,
    },
  },
]
```

## hemnet_booli_for_sale_all_by_price_no_price.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      // Filter for listings WITHOUT price
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $and: [
        {
          $or: [
            {
              provider: 'booli',
              'data.upcomingSale': {
                $ne: true,
              },
            },
            {
              provider: 'hemnet',
              'data.isUpcoming': {
                $ne: true,
              },
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
    },
  },
  {
    $group: {
      _id: {
        provider: '$provider',
      },
      listingCount: {
        $sum: 1,
      },
      totalViews: {
        $sum: '$adViews',
      },
      averageViews: {
        $avg: '$adViews',
      },
      averageDaysActive: {
        $avg: '$daysActive',
      },
    },
  },
  {
    $project: {
      _id: 0,
      provider: '$_id.provider',
      listingCount: 1,
      totalViews: 1,
      averageViews: {
        $round: ['$averageViews', 2],
      },
      averageDaysActive: {
        $round: ['$averageDaysActive', 2],
      },
    },
  },
  {
    $sort: {
      provider: 1,
    },
  },
]
```

## hemnet_all_items_for_sale_api_days.csv

```javascript
;[
  {
    $match: {
      projectProviderDataType: 'realestates_hemnet_inventory',
      'data.isUpcoming': false,
    },
  },
  {
    $project: {
      _id: 0,
      inventoryId: 1,
      daysActive: '$data.daysActive',
      packageType: '$productType',
      price: 1,
      streetAddress: '$data.streetAddress',
      createdAt: 1,
      updatedAt: 1,
      dealerId: 1,
      dealerType: 1,
      state: 1,
      iso: 1,
    },
  },
  {
    $sort: {
      createdAt: -1,
    },
  },
]
```

## hemnet_all_items_for_sale_api_days_no_new_productions.csv

```javascript
;[
  {
    $match: {
      projectProviderDataType: 'realestates_hemnet_inventory',
      'data.isUpcoming': false,
      // Exclude new production/project listings
      'data.adType': {
        $ne: 'project',
      },
    },
  },
  {
    $project: {
      _id: 0,
      inventoryId: 1,
      daysActive: '$data.daysActive',
      packageType: '$productType',
      price: 1,
      streetAddress: '$data.streetAddress',
      createdAt: 1,
      updatedAt: 1,
      dealerId: 1,
      dealerType: 1,
      state: 1,
      iso: 1,
    },
  },
  {
    $sort: {
      createdAt: -1,
    },
  },
]
```

## hemnet_all_items_for_sale.csv

```javascript
;[
  {
    $match: {
      projectProviderDataType: 'realestates_hemnet_inventory',
      'data.isUpcoming': false,
    },
  },
  {
    $addFields: {
      daysActiveCalculated: {
        $dateDiff: {
          startDate: '$createdAt',
          endDate: '$updatedAt',
          unit: 'day',
        },
      },
    },
  },
  {
    $project: {
      _id: 0,
      inventoryId: 1,
      daysActive: '$daysActiveCalculated',
      packageType: '$productType',
      price: 1,
      streetAddress: '$data.streetAddress',
      createdAt: 1,
      updatedAt: 1,
      dealerId: 1,
      dealerType: 1,
      state: 1,
      iso: 1,
    },
  },
  {
    $sort: {
      createdAt: -1,
    },
  },
]
```

## hemnet_booli_pre_market_all.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByPrice',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_no_price.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      // Filter for listings WITHOUT price
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $and: [
        {
          $or: [
            {
              provider: 'booli',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
              },
              'data.upcomingSale': true,
            },
            {
              provider: 'hemnet',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
              },
              'data.isUpcoming': true,
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_no_images.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': true,
          // Booli: Filter for listings WITHOUT images
          $or: [
            { 'data.images': { $exists: false } },
            { 'data.images': null },
            { 'data.images': [] },
            { 'data.images.0': { $exists: false } },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': true,
          // Hemnet: No image filtering (not tracked in database)
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByPrice',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_no_images_no_price.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': true,
          // Booli: No price filter
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: No images filter
          $and: [
            {
              $or: [
                { 'data.images': { $exists: false } },
                { 'data.images': null },
                { 'data.images': [] },
                { 'data.images.0': { $exists: false } },
              ],
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': true,
          // Hemnet: No price filter only
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_images_no_price.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.upcomingSale': true,
          // Booli: No price filter
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: HAS images (at least 1 image exists)
          'data.images.0': {
            $exists: true,
          },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
          },
          'data.isUpcoming': true,
          // Hemnet: No price filter only
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 182],
              },
              then: '60-182 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 365],
              },
              then: '183-365 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 730],
              },
              then: '366-730 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 999],
              },
              then: '731-999 days',
            },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_all_60_days.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_no_price_60_days.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_no_images_60_days.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          $or: [
            { 'data.images': { $exists: false } },
            { 'data.images': null },
            { 'data.images': [] },
            { 'data.images.0': { $exists: false } },
          ],
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_no_price_no_images_60_days.csv

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          $or: [
            { 'data.images': { $exists: false } },
            { 'data.images': null },
            { 'data.images': [] },
            { 'data.images.0': { $exists: false } },
          ],
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

## hemnet_booli_pre_market_images_no_price_60_days.csv

_Upcoming (pre-market) listings (<60 days active) across Hemnet & Booli that have images but no price, with distributions by days, views, and price (national & regional)._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.images.0': {
            $exists: true,
          },
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_all_items_upcoming_5_weeks.csv

_Simple filter selecting Hemnet listings flagged upcoming within last 5 weeks._

```javascript
{
  projectProviderDataType:
    "realestates_hemnet_inventory",
 createdAt: {
        $gte: new Date("2025-09-01")
      },
  "data.isUpcoming": true
}
```

[⬆ Back to top](#table-of-contents)

## booli_all_items_upcoming_5_weeks.csv

_Simple filter selecting Booli listings flagged upcoming within last 5 weeks._

```javascript
{
  projectProviderDataType:
    "realestates_booli_inventory",
 createdAt: {
        $gte: new Date("2025-09-01")
      },
  "data.upcomingSale": true
}
```

[⬆ Back to top](#table-of-contents)

## hemnet_average_days_active.csv

_Faceted averages of daysActive for Hemnet: upcoming subset, for sale (non-upcoming), and total combined._

```javascript
// Aggregation: averages of data.daysActive for Hemnet Upcoming / For Sale / Total
;[
  {
    $match: {
      project: 'realestates',
      provider: 'hemnet',
      dataType: 'inventory',
      updatedAt: { $gte: new Date('2025-09-29') },
      'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
    },
  },
  {
    $facet: {
      upcoming: [
        { $match: { 'data.isUpcoming': true } },
        {
          $group: {
            _id: null,
            listingCount: { $sum: 1 },
            averageDaysActive: { $avg: '$data.daysActive' },
          },
        },
        {
          $project: {
            _id: 0,
            segment: 'Hemnet Upcoming',
            listingCount: 1,
            averageDaysActive: { $round: ['$averageDaysActive', 2] },
          },
        },
      ],
      forSale: [
        { $match: { 'data.isUpcoming': false } },
        {
          $group: {
            _id: null,
            listingCount: { $sum: 1 },
            averageDaysActive: { $avg: '$data.daysActive' },
          },
        },
        {
          $project: {
            _id: 0,
            segment: 'Hemnet For Sale',
            listingCount: 1,
            averageDaysActive: { $round: ['$averageDaysActive', 2] },
          },
        },
      ],
      total: [
        {
          $group: {
            _id: null,
            listingCount: { $sum: 1 },
            averageDaysActive: { $avg: '$data.daysActive' },
          },
        },
        {
          $project: {
            _id: 0,
            segment: 'Total Hemnet Listings',
            listingCount: 1,
            averageDaysActive: { $round: ['$averageDaysActive', 2] },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: { $setUnion: ['$upcoming', '$forSale', '$total'] },
    },
  },
  { $unwind: '$results' },
  { $replaceRoot: { newRoot: '$results' } },
  { $sort: { segment: 1 } },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_all_60_30_days

_For-sale listings (<60 days) with full price presence; provides dual horizon (<60 & <30) distributions by days, views, and price (national & regional)._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': {
            $ne: true,
          },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': {
            $ne: true,
          },
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      isUnder30Days: {
        $lt: ['$data.daysActive', 30],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      // <60 days aggregations
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      // <30 days aggregations
      totalByDays30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          // <60 days results
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          // <30 days results
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_no_price_60_30_days

_For-sale listings (<60 days) missing price; dual horizon (<60 & <30) distributions of days and views plus regional days breakdown._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          price: {
            $exists: false,
          },
        },
        {
          price: null,
        },
        {
          price: 0,
        },
      ],
      $and: [
        {
          $or: [
            {
              provider: 'booli',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
                $lt: 60,
              },
              'data.upcomingSale': {
                $ne: true,
              },
            },
            {
              provider: 'hemnet',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
                $lt: 60,
              },
              'data.isUpcoming': {
                $ne: true,
              },
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_no_images_60_30_days

_For-sale listings (<60 days) lacking images (Booli only enforcement, Hemnet implicit); dual horizon days, views, price, and regional price breakdowns._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: { $in: ['booli', 'hemnet'] },
      dataType: 'inventory',
      updatedAt: { $gte: new Date('2025-09-29') },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': { $ne: true },
          $or: [
            { 'data.images': { $exists: false } },
            { 'data.images': null },
            { 'data.images': [] },
            {
              'data.images.0': { $exists: false },
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': { $ne: true },
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: { $ifNull: ['$price', 0] },
      region: {
        $switch: {
          branches: [
            {
              case: { $eq: ['$state', 'SE-AB'] },
              then: 'Stockholm',
            },
            {
              case: { $eq: ['$state', 'SE-M'] },
              then: 'Skane',
            },
            {
              case: { $eq: ['$state', 'SE-O'] },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: { $eq: ['$data.adViews', 0] },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: { $ifNull: ['$price', 0] },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      allSwedenByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],

      totalByDays30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      allSwedenByPrice30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByPrice30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },

          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  { $unwind: '$results' },
  { $replaceRoot: { newRoot: '$results' } },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_no_price_no_images_60_30_days

_For-sale listings (<60 days) with neither price nor images (Booli enforcement for images, both providers for price); dual horizon days, views, and regional days breakdowns._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: { $in: ['booli', 'hemnet'] },
      dataType: 'inventory',
      updatedAt: { $gte: new Date('2025-09-29') },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': { $ne: true },
          // Booli: no price (missing/zero)
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
          // Booli: no images
          $and: [
            {
              $or: [
                {
                  'data.images': {
                    $exists: false,
                  },
                },
                { 'data.images': null },
                { 'data.images': [] },
                {
                  'data.images.0': {
                    $exists: false,
                  },
                },
              ],
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': { $ne: true },
          // Hemnet: no price (missing/zero)
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: { $ifNull: ['$price', 0] },
      region: {
        $switch: {
          branches: [
            {
              case: { $eq: ['$state', 'SE-AB'] },
              then: 'Stockholm',
            },
            {
              case: { $eq: ['$state', 'SE-M'] },
              then: 'Skane',
            },
            {
              case: { $eq: ['$state', 'SE-O'] },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: { $eq: ['$data.adViews', 0] },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      // <60 scope (from initial match)
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],

      // <30 subset
      totalByDays30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByDays30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },

          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  { $unwind: '$results' },
  { $replaceRoot: { newRoot: '$results' } },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_for_sale_images_no_price_60_30_days

_For-sale listings (<60 days) that have images but lack price; dual horizon days, views, and regional days breakdowns._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: { $in: ['booli', 'hemnet'] },
      dataType: 'inventory',
      updatedAt: { $gte: new Date('2025-09-29') },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': { $ne: true }, // Booli: no price present
          $or: [
            { price: { $exists: false } },
            {
              price: null,
            },
            { price: 0 },
          ], // Booli: HAS images (at least one)
          'data.images.0': { $exists: true },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': { $ne: true }, // Hemnet: no price present
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: { $ifNull: ['$price', 0] },
      region: {
        $switch: {
          branches: [
            {
              case: { $eq: ['$state', 'SE-AB'] },
              then: 'Stockholm',
            },
            {
              case: { $eq: ['$state', 'SE-M'] },
              then: 'Skane',
            },
            {
              case: { $eq: ['$state', 'SE-O'] },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: { $eq: ['$data.adViews', 0] },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      // <60 scope (from initial match)
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ], // <30 subset
      totalByDays30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByDays30: [
        { $match: { daysActive: { $lt: 30 } } },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  { $unwind: '$results' },
  { $replaceRoot: { newRoot: '$results' } },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_pre_market_all_60_30_days

_Upcoming (pre-market) listings (<60 days) including those not yet fully live; dual horizon (<60 & <30) distributions for days, views, price (national & regional)._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': true,
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      isUnder30Days: {
        $lt: ['$data.daysActive', 30],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      // <60 days aggregations
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      // <30 days aggregations
      totalByDays30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice30: [
        {
          $match: {
            isUnder30Days: true,
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          // <60 days results
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          // <30 days results
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_pre_market_no_price_60_30_days

_Upcoming (<60 days) listings lacking price; dual horizon days, views, and regional days distributions._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $and: [
        {
          $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }],
        },
        {
          $or: [
            {
              provider: 'booli',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
                $lt: 60,
              },
              'data.upcomingSale': true,
            },
            {
              provider: 'hemnet',
              'data.daysActive': {
                $exists: true,
                $type: 'number',
                $gte: 0,
                $lt: 60,
              },
              'data.isUpcoming': true,
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_pre_market_no_images_60_30_days

_Upcoming (<60 days) listings without images (Booli explicit filter); dual horizon days, views, price (national & regional)._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': true,
          $or: [
            {
              'data.images': {
                $exists: false,
              },
            },
            {
              'data.images': null,
            },
            {
              'data.images': [],
            },
            {
              'data.images.0': {
                $exists: false,
              },
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
      priceBucket: {
        $let: {
          vars: {
            priceValue: {
              $ifNull: ['$price', 0],
            },
          },
          in: {
            $switch: {
              branches: [
                {
                  case: {
                    $lt: ['$$priceValue', 1500000],
                  },
                  then: '<1.5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 3000000],
                  },
                  then: '1.5-3m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 5000000],
                  },
                  then: '3-5m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 7000000],
                  },
                  then: '5-7m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 9000000],
                  },
                  then: '7-9m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 11000000],
                  },
                  then: '9-11m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 15000000],
                  },
                  then: '11-15m SEK',
                },
                {
                  case: {
                    $lt: ['$$priceValue', 20000000],
                  },
                  then: '15-20m SEK',
                },
              ],
              default: '>20m SEK',
            },
          },
        },
      },
    },
  },
  {
    $facet: {
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      allSwedenByPrice30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByPrice30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              priceBucket: '$priceBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$allSwedenByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'All of Sweden - Price',
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByPrice30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Price'],
                },
                category: '$$item._id.priceBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_pre_market_no_price_no_images_60_30_days

_Upcoming (<60 days) listings missing both price and images; dual horizon days, views, regional days distributions._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': true,
          // Booli: no price (missing/zero)
          $or: [
            {
              price: {
                $exists: false,
              },
            },
            {
              price: null,
            },
            {
              price: 0,
            },
          ],
          // Booli: no images
          $and: [
            {
              $or: [
                {
                  'data.images': {
                    $exists: false,
                  },
                },
                {
                  'data.images': null,
                },
                {
                  'data.images': [],
                },
                {
                  'data.images.0': {
                    $exists: false,
                  },
                },
              ],
            },
          ],
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
          // Hemnet: no price (missing/zero)
          $or: [
            {
              price: {
                $exists: false,
              },
            },
            {
              price: null,
            },
            {
              price: 0,
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      // <60 scope (from initial match)
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      // <30 subset
      totalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)

## hemnet_booli_pre_market_images_no_price_60_30_days

_Upcoming (<60 days) listings with images present but missing price; dual horizon days, views, regional days distributions._

```javascript
;[
  {
    $match: {
      project: 'realestates',
      provider: {
        $in: ['booli', 'hemnet'],
      },
      dataType: 'inventory',
      updatedAt: {
        $gte: new Date('2025-09-29'),
      },
      'data.adViews': {
        $exists: true,
        $type: 'number',
        $gte: 0,
      },
      $or: [
        {
          provider: 'booli',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.upcomingSale': true,
          // Booli: no price present
          $or: [
            {
              price: {
                $exists: false,
              },
            },
            {
              price: null,
            },
            {
              price: 0,
            },
          ],
          // Booli: HAS images (at least one)
          'data.images.0': {
            $exists: true,
          },
        },
        {
          provider: 'hemnet',
          'data.daysActive': {
            $exists: true,
            $type: 'number',
            $gte: 0,
            $lt: 60,
          },
          'data.isUpcoming': true,
          // Hemnet: no price present
          $or: [
            {
              price: {
                $exists: false,
              },
            },
            {
              price: null,
            },
            {
              price: 0,
            },
          ],
        },
      ],
    },
  },
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: {
        $ifNull: ['$price', 0],
      },
      region: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$state', 'SE-AB'],
              },
              then: 'Stockholm',
            },
            {
              case: {
                $eq: ['$state', 'SE-M'],
              },
              then: 'Skane',
            },
            {
              case: {
                $eq: ['$state', 'SE-O'],
              },
              then: 'Västra Götaland County',
            },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            {
              case: {
                $lt: ['$data.daysActive', 7],
              },
              then: '<7 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 30],
              },
              then: '7-30 days',
            },
            {
              case: {
                $lt: ['$data.daysActive', 60],
              },
              then: '30-59 days',
            },
          ],
          default: '60+ days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            {
              case: {
                $eq: ['$data.adViews', 0],
              },
              then: '0 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 5],
              },
              then: '1-5 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 10],
              },
              then: '6-10 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 20],
              },
              then: '11-20 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 50],
              },
              then: '21-50 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 100],
              },
              then: '51-100 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 200],
              },
              then: '101-200 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 300],
              },
              then: '201-300 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 400],
              },
              then: '301-400 views',
            },
            {
              case: {
                $lte: ['$data.adViews', 1000],
              },
              then: '401-1000 views',
            },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      // <60 scope (from initial match)
      totalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays60: [
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      // <30 subset
      totalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      totalByViews30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              viewsBucket: '$viewsBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
      regionalByDays30: [
        {
          $match: {
            daysActive: {
              $lt: 30,
            },
          },
        },
        {
          $group: {
            _id: {
              provider: '$provider',
              region: '$region',
              daysBucket: '$daysBucket',
            },
            listingCount: {
              $sum: 1,
            },
            totalViews: {
              $sum: '$adViews',
            },
          },
        },
      ],
    },
  },
  {
    $project: {
      results: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays60',
              as: 'item',
              in: {
                section: '<60 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays30',
              as: 'item',
              in: {
                section: '<30 days on Site',
                subsection: {
                  $concat: ['$$item._id.region', ' - Days'],
                },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
    },
  },
  {
    $unwind: '$results',
  },
  {
    $replaceRoot: {
      newRoot: '$results',
    },
  },
  {
    $sort: {
      section: 1,
      subsection: 1,
      category: 1,
      provider: 1,
    },
  },
]
```

[⬆ Back to top](#table-of-contents)
