// GOOGLE DRIVE BASE FOLDER SHARED URL = https://drive.google.com/drive/folders/1RAQRjuZlMHSZf5NGb5nnboFSjap3UGvj?usp=sharing
// import fs from 'fs'

import { S3 } from '@aws-sdk/client-s3'
import {
  addMonths,
  addWeeks,
  endOfISOWeek,
  endOfMonth,
  format,
  getMonth,
  isAfter,
  parse,
  startOfISOWeek,
  startOfMonth,
  subDays,
  subMonths,
  subWeeks,
  subYears,
} from 'date-fns'
import { got } from 'got'
import isoCountries from 'i18n-iso-countries'
import { json2csv } from 'json-2-csv'

import {
  AuctionsLiveBiddingType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  Region,
  TimeUnit,
  IntegrationType,
  defaultConfigs,
} from '@datagatherers/datagatherers'
import { GDrive, accountConfigs, camelCase, getSnapTime } from '@datagatherers/worker-utils'

import { generateMonthlyListingsCSV } from './bcgMonthly'
import { generateCSV as generateBcgWeeklyCSV } from './bcgWeekly'
import {
  accountingProvidersAPExpensesUS,
  datingProviders,
  providerNameMapping,
  FRBRDEProviders,
  nordicProviders,
  accountingApiIntegrationProviders,
  adevintaSnapshotFilters,
  adevintaQueryFilters,
  schibstedSnapshotFilters,
  schibstedQueryFilters,
  nordicProvidersIntegrationSnapshot,
  adevintaProvidersIntegrationSnapshot,
  availableIntegrationDataTypes,
  costarProvidersIntegrationSnapshot,
  smgProvidersIntegrationSnapshot,
  bcgProvidersIntegrationSnapshot,
  hemnetMonthlyQueryFilters,
  hemnetSnapshotFilters,
  hemnetQueryFilters,
  hemnetNewListingsPackagesQueryFilters,
  mobileWeeklyPackagesQueryFilters,
  mobileSnapshotFilters,
  hemnetIrQueryFilters,
} from './constants'
import { generateDatalakeWeeklyCSV } from './datalake'
import { generateCSV as generateIntegrationSnapshotCSV } from './defaultIntegrationSnapshot'
import { generateRERentPackagesCSV } from './finn'
import { generateHemnetBooliQueriesCSV, archiveOldHemnetBooliQueriesCSVs } from './hemnetBooliQueries'
import { generateKarnovMonthlyCSV } from './karnovMonthly'
import { getOutputDay, getOutputMonth, parseVerticalForHemnet, parseVerticalForMobile } from './utils'
import { csvAccountingProviders, similarWebAPIkey } from '../../../lib/similarweb/constants'
import { checkRunningStatus, getOverviewShare } from '../../../lib/similarweb/index'
import { moveModifiedBefore } from '../../../lib/tools/csvExportsArchiving'

import type { IntegrationSnapshotCsvOptions } from './defaultIntegrationSnapshot'
import type {
  ControllerConfig,
  ControllerRuntime,
  IntegrationSnapshot,
  IntegrationSnapshotDataType,
  Item,
  Provider,
  Snapshot,
} from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'
import type { Filter } from 'mongodb'

const account = 'vor'
const provider: Provider = {
  // ----------------- SimilarWeb ------------------------------
  datingAppsMonthlySimilarWeb: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'germanyNuComMonthlySimilarWeb',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      for (const dataTypeInfo of availableIntegrationDataTypes[IntegrationType.similarWeb]) {
        if (dataTypeInfo.value.match(/traffic/g)) continue
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.similarWeb,
                project: ProjectType.marketplaces,
                provider: { $in: datingProviders },
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.map((item) => {
          const country = 'Worldwide'
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.WW[item.provider] ? providerNameMapping.WW[item.provider] : item.provider
          return {
            country,
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const providers = [...new Set(resultsFormatted.map((item) => item.provider))]
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of providers) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              let order = 0
              switch (curr.provider) {
                case 'MeetMe':
                  order = 1
                  break
                case 'LOVOO':
                  order = 2
                  break
                case 'Skout':
                  order = 3
                  break
                case 'Tagged':
                  order = 4
                  break
                case 'GROWLr':
                  order = 5
                  break
                case 'hi5':
                  order = 6
                  break
                case 'eHarmony':
                  order = 7
                  break
                case 'Parship':
                  order = 8
                  break
                case 'ElitePartner':
                  order = 9
                  break
                default:
                  order = 0
              }
              const el = {
                Company: curr.provider,
                order,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.order - n2.order)
          .flatMap((item) => {
            delete item.order
            switch (item.Company) {
              case 'MeetMe':
                return [{ Company: 'Meet Group' }, item]
              case 'eHarmony':
                return [{ Company: 'eHarmony' }, item]
              case 'Parship':
                return [{ Company: 'Parship' }, item]

              default:
                return item
            }
          })
        const filename = `Dating Apps Similar Web ${dataTypeInfo.label} ${getOutputMonth(
          format(lastMonth, 'MMM/yy'),
        )}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.datingSW,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.datingSW],
          [`Dating Apps Similar Web ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  germanyNuComMonthlySimilarWeb: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'germanyVoDMonthlySimilarWeb',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.similarWeb]
      for (const dataTypeInfo of dataTypes) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.similarWeb,
                project: 'ampereTv',
                provider: {
                  $in: [
                    'verivox',
                    'billiger.mietwagen',
                    'aroundhome',
                    'flaconi',
                    //'stylight', // commented as requested in https://trello.com/c/C0EtMZH3
                    'mydays',
                    'camperdays',
                    //'regiondo', // commented as requested in https://trello.com/c/C0EtMZH3
                    'jochen.schweizer',
                    'marktguru', // added as requested in https://trello.com/c/C0EtMZH3
                    'wetter', // added as requested in https://trello.com/c/C0EtMZH3
                  ],
                },
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.flatMap((item) => {
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.DE[item.provider] ? providerNameMapping.DE[item.provider] : item.provider

          return {
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const providers = [...new Set(resultsFormatted.map((item) => item.provider))]
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of providers) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              let order = 0
              switch (curr.provider) {
                case 'Verivox':
                  order = 1
                  break
                case 'Billiger-Mietwagen':
                  order = 2
                  break
                case 'Aroundhome':
                  order = 3
                  break
                case 'Flaconi':
                  order = 4
                  break
                case 'Stylight':
                  order = 5
                  break
                case 'Mydays':
                  order = 6
                  break
                case 'CamperDays':
                  order = 7
                  break
                case 'Aboalarm':
                  order = 8
                  break
                case 'Regiondo':
                  order = 9
                  break
                case 'Jochen Schweizer':
                  order = 10
                  break
                default:
                  order = 100
              }
              const el = {
                Company: curr.provider,
                order,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.order - n2.order)
          .map((item) => {
            delete item.order
            return item
          })
        const filename = `NuCom Similar Web ${dataTypeInfo.label} ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })

        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.nuComSW,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.nuComSW],
          [`NuCom Similar Web ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  germanyVoDMonthlySimilarWeb: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'paymentsMonthlySimilarWeb',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      for (const dataTypeInfo of availableIntegrationDataTypes[IntegrationType.similarWeb]) {
        if (dataTypeInfo.value.match(/traffic/g)) continue
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.similarWeb,
                project: ProjectType.vod,
                iso: Iso3166Alpha2.DE,
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                // provider: 1,
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.map((item) => {
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.DE[item.provider] ? providerNameMapping.DE[item.provider] : item.provider
          return {
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of ['tvnow.de', 'Joyn']) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              const el = {
                Company: curr.provider,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.Company.localeCompare(n2.Company))
          .map((item) => {
            delete item.order
            return item
          })
        const filename = `Germany VoD Similar Web ${dataTypeInfo.label} ${getOutputMonth(
          format(lastMonth, 'MMM/yy'),
        )}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.vodSW,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.vodSW],
          [`Germany VoD Similar Web ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  paymentsMonthlySimilarWeb: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const providers = ['revolut', 'wise']

      for (const provider of providers) {
        for (const dataTypeInfo of availableIntegrationDataTypes[IntegrationType.similarWeb]) {
          if (dataTypeInfo.value.match(/traffic/g)) continue
          const results = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.similarWeb,
                  project: ProjectType.payments,
                  provider,
                  dataType: dataTypeInfo.value,
                  snapTimeUnit: TimeUnit.months,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: '$stats',
              },
              {
                $group: {
                  _id: {
                    iso: '$iso',
                    snapTime: '$snapTime',
                    webpage: '$stats.webpage',
                  },
                  count: { $sum: '$stats.count' },
                },
              },
              {
                $match: {
                  '_id.webpage': 'main',
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  snapTime: '$_id.snapTime',
                  count: '$count',
                },
              },
              {
                $sort: {
                  snapTime: 1,
                },
              },
            ])
            .toArray()

          if (!results?.length) continue
          const prefix = `${camelCase(provider)} Similar Web ${dataTypeInfo.label}`
          const parentFolderId =
            provider === 'revolut'
              ? accountConfigs[account].folders.revolutSimilarWeb
              : accountConfigs[account].folders.wiseSimilarWeb
          const resultsFormatted = results.flatMap((item) => {
            const country =
              item.iso === Iso3166Alpha2.WW
                ? 'Worldwide'
                : isoCountries.getName(item.iso, 'en') === 'Türkiye'
                  ? 'Turkey'
                  : isoCountries.getName(item.iso, 'en')
            const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')

            return {
              country,
              month: formattedSnapTime,
              count: item.count,
            }
          })
          const lastMonth = resultsFormatted[resultsFormatted.length - 1]?.month
          const resultsParsed = resultsFormatted
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .reduce((acc: any[], curr) => {
              if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                return acc.map((el: any) => {
                  if (el.Country === curr.country) {
                    el[curr.month] = curr.count
                  }
                  return el
                })
              } else {
                const el = {
                  Country: curr.country,
                }
                el[curr.month] = curr.count
                return [...acc, el]
              }
            }, [])
            .sort((n1, n2) => {
              const second = n2[lastMonth] ?? 0
              const first = n1[lastMonth] ?? 0
              return second - first
            })

          const filename = `${prefix} ${getOutputMonth(lastMonth)}.csv`
          const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
          const gDrive = await GDrive(account)
          await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)
          await moveModifiedBefore(
            gDrive,
            subDays(new Date(), 1),
            accountConfigs[account].folders.archive,
            [parentFolderId],
            [prefix],
          )
        }
      }
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'accountingUSExpensesSimilarWeb',
    }),
    config: {
      concurrency: 1,
    },
  },
  accountingUSExpensesSimilarWeb: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subYears(subMonths(startOfMonth(new Date()), 2), 3), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.similarWeb]
      for (const dataTypeInfo of dataTypes) {
        if (dataTypeInfo.value.match(/traffic/g)) continue
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.similarWeb,
                project: ProjectType.accounting,
                provider: { $in: accountingProvidersAPExpensesUS },
                iso: Iso3166Alpha2.US,
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.map((item) => {
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.US[item.provider] ? providerNameMapping.US[item.provider] : item.provider
          return {
            country: Iso3166Alpha2.US,
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })

        const providers = [...new Set(resultsFormatted.map((item) => item.provider))]
        const firstMonth = resultsFormatted[0]?.month
        const lastMonth = resultsFormatted[resultsFormatted.length - 1]?.month
        const deltaTime: string[] = []
        for (
          let dateToAdd = parse(firstMonth, 'MMM/yy', new Date());
          !isAfter(dateToAdd, parse(lastMonth, 'MMM/yy', new Date()));
          dateToAdd = addMonths(dateToAdd, 1)
        ) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of providers) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              const el = {
                Company: curr.provider,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => {
            const second = n2[lastMonth] ?? 0
            const first = n1[lastMonth] ?? 0
            return second - first
          })
        const filename = `US Accounting Similar Web ${dataTypeInfo.label} ${getOutputMonth(lastMonth)}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.accountingSWUS,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.accountingSWUS],
          [`US Accounting Similar Web ${dataTypeInfo.label}`],
        )
      }
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'accountingPerIsoMonthlySimilarWeb',
    }),
    config: {
      concurrency: 1,
    },
  },
  accountingPerIsoMonthlySimilarWeb: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.similarWeb]
      for (const wwProvider of csvAccountingProviders) {
        for (const dataTypeInfo of dataTypes) {
          if (dataTypeInfo.value.match(/similarWebTraffic/g)) continue
          const results = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.similarWeb,
                  project: ProjectType.accounting,
                  provider: wwProvider,
                  dataType: dataTypeInfo.value,
                  snapTimeUnit: TimeUnit.months,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: '$stats',
              },
              {
                $group: {
                  _id: {
                    iso: '$iso',
                    snapTime: '$snapTime',
                    webpage: '$stats.webpage',
                  },
                  count: { $sum: '$stats.count' },
                },
              },
              {
                $match: {
                  '_id.webpage': 'main',
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  snapTime: '$_id.snapTime',
                  count: '$count',
                },
              },
              {
                $sort: {
                  snapTime: 1,
                },
              },
            ])
            .toArray()

          if (!results?.length) continue
          const resultsFormatted = results.flatMap((item) => {
            if (item.iso === Iso3166Alpha2.WW || item.iso === Iso3166Alpha2.EU) return []
            const country = isoCountries.getName(item.iso, 'en')
            const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')

            return {
              country,
              month: formattedSnapTime,
              count: item.count,
            }
          })
          const lastMonth = resultsFormatted[resultsFormatted.length - 1]?.month
          const resultsParsed = resultsFormatted
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .reduce((acc: any[], curr) => {
              if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                return acc.map((el: any) => {
                  if (el.Country === curr.country) {
                    el[curr.month] = curr.count
                  }
                  return el
                })
              } else {
                const el = {
                  Country: curr.country,
                }
                el[curr.month] = curr.count
                return [...acc, el]
              }
            }, [])
            .sort((n1, n2) => {
              const second = n2[lastMonth] ?? 0
              const first = n1[lastMonth] ?? 0
              return second - first
            })

          const filename = `SimilarWebAccountingSelection ${wwProvider} ${dataTypeInfo.label} ${getOutputMonth(
            lastMonth,
          )}.csv`
          const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
          const gDrive = await GDrive(account)
          await gDrive.upsert(
            csv,
            filename,
            accountConfigs[account].defaultFileType,
            accountConfigs[account].folders.accountingSW,
          )
          await moveModifiedBefore(
            gDrive,
            subDays(new Date(), 1),
            accountConfigs[account].folders.archive,
            [accountConfigs[account].folders.accountingSW],
            [`SimilarWebAccountingSelection ${wwProvider} ${dataTypeInfo.label}`],
          )
        }
      }
    },
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'karnovMonthly',
      }
    },
    config: {
      concurrency: 1,
    },
  },
  // ----------------- AppAnnie / Data.ai ------------------------------
  Fortnox90DaysDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await appAnnie90Days(runtime, 'fortnox')
    },
    config: {
      concurrency: 1,
    },
  },
  Freee90DaysDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await appAnnie90Days(runtime, 'freee')
    },
    config: {
      concurrency: 1,
    },
  },
  BillCom90DaysDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await appAnnie90Days(runtime, 'bill.com')
    },
    config: {
      concurrency: 1,
    },
  },
  datingAppsMonthlyDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'germanyVoDMonthlyDataAi',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.dataAi]
      for (const dataTypeInfo of dataTypes) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.dataAi,
                project: ProjectType.marketplaces,
                provider: { $in: datingProviders },
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                snapTime: 1,
              },
            },
          ])
          .toArray()
        if (!results?.length) continue

        const resultsFormatted = results.map((item) => {
          const country = 'Worldwide'
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.WW[item.provider] ? providerNameMapping.WW[item.provider] : item.provider
          return {
            country,
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const providers = [...new Set(resultsFormatted.map((item) => item.provider))]
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of providers) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              let order = 0
              switch (curr.provider) {
                case 'MeetMe':
                  order = 1
                  break
                case 'LOVOO':
                  order = 2
                  break
                case 'Skout':
                  order = 3
                  break
                case 'Tagged':
                  order = 4
                  break
                case 'GROWLr':
                  order = 5
                  break
                case 'hi5':
                  order = 6
                  break
                case 'eHarmony':
                  order = 7
                  break
                case 'Parship':
                  order = 8
                  break
                case 'ElitePartner':
                  order = 9
                  break
                default:
                  order = 0
              }
              const el = {
                Company: curr.provider,
                order,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.order - n2.order)
          .flatMap((item) => {
            delete item.order
            switch (item.Company) {
              case 'MeetMe':
                return [{ Company: 'Meet Group' }, item]
              case 'eHarmony':
                return [{ Company: 'eHarmony' }, item]
              case 'Parship':
                return [{ Company: 'Parship' }, item]

              default:
                return item
            }
          })
        const filename = `Dating Apps AppAnnie ${dataTypeInfo.label} ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.datingA,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.datingA],
          [`Dating Apps AppAnnie ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  germanyVoDMonthlyDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'paymentsMonthlyDataAi',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.dataAi]
      for (const dataTypeInfo of dataTypes) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.dataAi,
                project: ProjectType.vod,
                iso: Iso3166Alpha2.DE,
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                provider: 1,
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.map((item) => {
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.DE[item.provider] ? providerNameMapping.DE[item.provider] : item.provider
          return {
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of ['tvnow.de', 'Joyn']) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              const el = {
                Company: curr.provider,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.Company.localeCompare(n2.Company))
          .map((item) => {
            delete item.order
            return item
          })
        const filename = `Germany VoD AppAnnie ${dataTypeInfo.label} ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })

        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.vodA,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.vodA],
          [`Germany VoD AppAnnie ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  paymentsMonthlyDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const providers = ['revolut', 'wise']
      const dataTypes = availableIntegrationDataTypes[IntegrationType.dataAi]

      for (const provider of providers) {
        for (const dataTypeInfo of dataTypes) {
          const results = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.dataAi,
                  project: ProjectType.payments,
                  provider,
                  dataType: dataTypeInfo.value,
                  snapTimeUnit: TimeUnit.months,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: '$stats',
              },
              {
                $group: {
                  _id: {
                    iso: '$iso',
                    snapTime: '$snapTime',
                  },
                  count: { $sum: '$stats.count' },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  snapTime: '$_id.snapTime',
                  count: '$count',
                },
              },
              {
                $sort: {
                  snapTime: 1,
                },
              },
            ])
            .toArray()

          if (!results?.length) continue
          const resultsFormatted = results.map((item) => {
            const country =
              item.iso === Iso3166Alpha2.WW
                ? 'Worldwide'
                : item.iso === Iso3166Alpha2.EU
                  ? 'European Union'
                  : item.iso === Iso3166Alpha2.WW
                    ? 'Worldwide'
                    : isoCountries.getName(item.iso, 'en') === 'Türkiye'
                      ? 'Turkey'
                      : isoCountries.getName(item.iso, 'en')
            const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')

            return {
              country,
              month: formattedSnapTime,
              count: item.count,
            }
          })
          const lastMonth = resultsFormatted[resultsFormatted.length - 1]?.month
          const resultsParsed = resultsFormatted
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .reduce((acc: any[], curr) => {
              if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                return acc.map((el: any) => {
                  if (el.Country === curr.country) {
                    el[curr.month] = curr.count
                  }
                  return el
                })
              } else {
                const el = {
                  Country: curr.country,
                }
                el[curr.month] = curr.count
                return [...acc, el]
              }
            }, [])
            .sort((n1, n2) => {
              return n2[lastMonth] - n1[lastMonth]
            })
          const prefix = `${camelCase(provider)} AppAnnie ${dataTypeInfo.label}`
          const parentFolderId =
            provider === 'revolut'
              ? accountConfigs[account].folders.revolutDataAi
              : accountConfigs[account].folders.wiseDataAi
          const filename = `${prefix} ${getOutputMonth(lastMonth)}.csv`
          const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
          const gDrive = await GDrive(account)
          await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)

          await moveModifiedBefore(
            gDrive,
            subDays(new Date(), 1),
            accountConfigs[account].folders.archive,
            [parentFolderId],
            [prefix],
          )
        }
      }
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'FRBRDEProvidersMonthlyDataAi',
    }),
    config: {
      concurrency: 1,
    },
  },
  FRBRDEProvidersMonthlyDataAi: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const results = []
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.dataAi]
      for (const dataTypeInfo of dataTypes) {
        for (const countryData of FRBRDEProviders) {
          const someResults = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.dataAi,
                  project: countryData.project,
                  iso: countryData.iso,
                  provider: { $in: countryData.providers },
                  snapTimeUnit: TimeUnit.months,
                  dataType: dataTypeInfo.value,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: {
                  path: '$stats',
                },
              },
              {
                $group: {
                  _id: {
                    provider: '$provider',
                    snapTime: '$snapTime',
                    iso: '$iso',
                    dataType: '$dataType',
                  },
                  count: {
                    $sum: '$stats.count',
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  provider: '$_id.provider',
                  providerIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.provider', 'leboncoin'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'olx'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'enjoei'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'ebk'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'mobile.de'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'vinted'],
                          },
                          then: 3,
                        },
                      ],
                      default: 0,
                    },
                  },
                  snapTime: '$_id.snapTime',
                  dataType: '$_id.dataType',
                  count: '$count',
                  countryIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.FR],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.BR],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.DE],
                          },
                          then: 3,
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              {
                $sort: {
                  snapTime: 1,
                  countryIndex: 1,
                  providerIndex: 1,
                },
              },
            ])
            .toArray()
          if (!someResults?.length) continue
          results.push(...someResults)
        }
      }

      const resultsFormatted = results.flatMap((item) => {
        return {
          provider: item.provider,
          month: format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy'),
          count: item.count,
          country: isoCountries.getName(item.iso, 'en'),
          dataTypeLabel: dataTypes.find((dataTypeInfo) => dataTypeInfo.value === item.dataType)?.label,
          countryIndex: item.countryIndex,
          providerIndex: item.providerIndex,
        }
      })

      const resultsParsed = []
      const countries = [
        ...new Set(
          resultsFormatted
            .sort((a, b) => {
              return a.countryIndex - b.countryIndex
            })
            .map((item) => item.country),
        ),
      ]
      const months = [...new Set(resultsFormatted.map((item) => item.month))]
      const dataAiDataTypeLabels = [...new Set(resultsFormatted.map((item) => item.dataTypeLabel))]

      for (const country of countries) {
        const countryObj = { '': country, ' ': '' }

        for (const month of months) {
          countryObj[month] = ''
        }

        resultsParsed.push(countryObj)

        for (const dataTypeLabel of dataAiDataTypeLabels) {
          resultsParsed.push({
            '': `Apps - ${dataTypeLabel}`,
            ' ': '',
            ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
          })

          const providers = Array.from(
            new Set(
              resultsFormatted
                .filter((item) => item.country === country && item.dataTypeLabel === dataTypeLabel)
                .sort((a, b) => {
                  return a.providerIndex - b.providerIndex
                })
                .map((item) => item.provider),
            ),
          )

          for (const provider of providers) {
            const providerObj = {
              '': '',
              ' ': provider,
              ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
            }

            for (const month of months) {
              const item = resultsFormatted.find(
                (result) =>
                  result.country === country &&
                  result.dataTypeLabel === dataTypeLabel &&
                  result.provider === provider &&
                  result.month === month,
              )
              providerObj[month] = item ? item.count : ''
            }

            resultsParsed.push(providerObj)
          }
        }
      }

      const filename = `AppAnnie FR_BR_DE Providers ${getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.marketplacesA,
      )
      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.marketplacesA],
        [/AppAnnie FR_BR_DE Providers/g],
      )
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'nordicProvidersMonthlyDataAi',
    }),
    config: {
      concurrency: 1,
    },
  },
  nordicProvidersMonthlyDataAi: {
    // cron: '0 8 10 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const results = []
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.dataAi]
      for (const dataTypeInfo of dataTypes) {
        for (const countryData of nordicProviders) {
          const someResults = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.dataAi,
                  project: countryData.project,
                  iso: countryData.iso,
                  provider: { $in: countryData.providers },
                  snapTimeUnit: TimeUnit.months,
                  dataType: dataTypeInfo.value,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: {
                  path: '$stats',
                },
              },
              {
                $group: {
                  _id: {
                    provider: '$provider',
                    snapTime: '$snapTime',
                    iso: '$iso',
                    dataType: '$dataType',
                  },
                  count: {
                    $sum: '$stats.count',
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  provider: '$_id.provider',
                  providerIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.provider', 'finn.no'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'tise'],
                          },
                          then: 3,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'blocket'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'vinted'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'plick'],
                          },
                          then: 4,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'dba'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'boligsiden'],
                          },
                          then: 4,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'boliga'],
                          },
                          then: 5,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'trendsales'],
                          },
                          then: 6,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'tori'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'temu'],
                          },
                          then: 10,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'hjem'],
                          },
                          then: 11,
                        },
                      ],
                      default: 0,
                    },
                  },
                  snapTime: '$_id.snapTime',
                  dataType: '$_id.dataType',
                  count: '$count',
                  countryIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.NO],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.SE],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.DK],
                          },
                          then: 3,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.FI],
                          },
                          then: 4,
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              {
                $sort: {
                  snapTime: 1,
                  countryIndex: 1,
                  providerIndex: 1,
                },
              },
            ])
            .toArray()
          if (!someResults?.length) continue
          results.push(...someResults)
        }
      }

      const resultsFormatted = results.flatMap((item) => {
        return {
          provider: item.provider,
          month: format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy'),
          count: item.count,
          country: isoCountries.getName(item.iso, 'en'),
          dataTypeLabel: dataTypes.find((dataTypeInfo) => dataTypeInfo.value === item.dataType)?.label,
          countryIndex: item.countryIndex,
          providerIndex: item.providerIndex,
        }
      })

      const resultsParsed = []
      const countries = [
        ...new Set(
          resultsFormatted
            .sort((a, b) => {
              return a.countryIndex - b.countryIndex
            })
            .map((item) => item.country),
        ),
      ]
      const months = [...new Set(resultsFormatted.map((item) => item.month))]
      const dataAiDataTypeLabels = [...new Set(resultsFormatted.map((item) => item.dataTypeLabel))]

      for (const country of countries) {
        const countryObj = { '': country, ' ': '' }

        for (const month of months) {
          countryObj[month] = ''
        }

        resultsParsed.push(countryObj)

        for (const dataTypeLabel of dataAiDataTypeLabels) {
          resultsParsed.push({
            '': `Apps - ${dataTypeLabel}`,
            ' ': '',
            ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
          })

          const providers = Array.from(
            new Set(
              resultsFormatted
                .filter((item) => item.country === country && item.dataTypeLabel === dataTypeLabel)
                .sort((a, b) => {
                  return a.providerIndex - b.providerIndex
                })
                .map((item) => item.provider),
            ),
          )

          for (const provider of providers) {
            const providerObj = {
              '': '',
              ' ': provider,
              ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
            }

            for (const month of months) {
              const item = resultsFormatted.find(
                (result) =>
                  result.country === country &&
                  result.dataTypeLabel === dataTypeLabel &&
                  result.provider === provider &&
                  result.month === month,
              )
              providerObj[month] = item ? item.count : ''
            }

            resultsParsed.push(providerObj)
          }
        }
      }

      const filename = `AppAnnie Nordic Providers ${getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.annieNordic,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.annieNordic],
        [/AppAnnie Nordic Providers/g],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  // ----------------- Apptopia ------------------------------
  apptopia90DaysFortnox: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await apptopia90Days(runtime, 'fortnox')
    },
    config: {
      concurrency: 1,
    },
  },
  apptopia90DaysDivvy: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await apptopia90Days(runtime, 'divvy')
    },
    config: {
      concurrency: 1,
    },
  },
  apptopia90DaysBillCom: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await apptopia90Days(runtime, 'bill.com')
    },
    config: {
      concurrency: 1,
    },
  },
  datingAppsMonthlyApptopia: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'germanyVoDMonthlyApptopia',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      for (const dataType of availableIntegrationDataTypes[IntegrationType.apptopia].map((item) => item.value)) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.apptopia,
                project: ProjectType.marketplaces,
                provider: { $in: datingProviders },
                dataType,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                snapTime: 1,
              },
            },
          ])
          .toArray()
        if (!results?.length) continue

        const resultsFormatted = results.map((item) => {
          const country = 'Worldwide'
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.WW[item.provider] ? providerNameMapping.WW[item.provider] : item.provider
          return {
            country,
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const providers = [...new Set(resultsFormatted.map((item) => item.provider))]
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of providers) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              let order = 0
              switch (curr.provider) {
                case 'MeetMe':
                  order = 1
                  break
                case 'LOVOO':
                  order = 2
                  break
                case 'Skout':
                  order = 3
                  break
                case 'Tagged':
                  order = 4
                  break
                case 'GROWLr':
                  order = 5
                  break
                case 'hi5':
                  order = 6
                  break
                case 'eHarmony':
                  order = 7
                  break
                case 'Parship':
                  order = 8
                  break
                case 'ElitePartner':
                  order = 9
                  break
                default:
                  order = 0
              }
              const el = {
                Company: curr.provider,
                order,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.order - n2.order)
          .flatMap((item) => {
            delete item.order
            switch (item.Company) {
              case 'MeetMe':
                return [{ Company: 'Meet Group' }, item]
              case 'eHarmony':
                return [{ Company: 'eHarmony' }, item]
              case 'Parship':
                return [{ Company: 'Parship' }, item]

              default:
                return item
            }
          })
        const filename = `Dating Apps Apptopia ${
          dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'
        } ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.datingApptopia,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.datingApptopia],
          [`Dating Apps Apptopia ${dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  germanyVoDMonthlyApptopia: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'paymentsMonthlyApptopia',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      for (const dataType of availableIntegrationDataTypes[IntegrationType.apptopia].map((item) => item.value)) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.apptopia,
                project: ProjectType.vod,
                iso: Iso3166Alpha2.DE,
                dataType,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                provider: 1,
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.map((item) => {
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.DE[item.provider] ? providerNameMapping.DE[item.provider] : item.provider
          return {
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of ['tvnow.de', 'Joyn']) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              const el = {
                Company: curr.provider,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.Company.localeCompare(n2.Company))
          .map((item) => {
            delete item.order
            return item
          })
        const filename = `Germany VoD Apptopia ${
          dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'
        } ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })

        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.vodApptopia,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.vodApptopia],
          [`Germany VoD Apptopia ${dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  paymentsMonthlyApptopia: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const providers = ['revolut', 'wise']

      for (const provider of providers) {
        for (const dataType of availableIntegrationDataTypes[IntegrationType.apptopia].map((item) => item.value)) {
          const results = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.apptopia,
                  project: ProjectType.payments,
                  provider,
                  dataType,
                  snapTimeUnit: TimeUnit.months,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: '$stats',
              },
              {
                $group: {
                  _id: {
                    iso: '$iso',
                    snapTime: '$snapTime',
                  },
                  count: { $sum: '$stats.count' },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  snapTime: '$_id.snapTime',
                  count: '$count',
                },
              },
              {
                $sort: {
                  snapTime: 1,
                },
              },
            ])
            .toArray()

          if (!results?.length) continue
          const resultsFormatted = results.map((item) => {
            const country =
              item.iso === Iso3166Alpha2.WW
                ? 'Worldwide'
                : item.iso === Iso3166Alpha2.EU
                  ? 'European Union'
                  : isoCountries.getName(item.iso, 'en') === 'Türkiye'
                    ? 'Turkey'
                    : isoCountries.getName(item.iso, 'en')
            const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')

            return {
              country,
              month: formattedSnapTime,
              count: item.count,
            }
          })
          const lastMonth = resultsFormatted[resultsFormatted.length - 1]?.month
          const resultsParsed = resultsFormatted
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .reduce((acc: any[], curr) => {
              if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                return acc.map((el: any) => {
                  if (el.Country === curr.country) {
                    el[curr.month] = curr.count
                  }
                  return el
                })
              } else {
                const el = {
                  Country: curr.country,
                }
                el[curr.month] = curr.count
                return [...acc, el]
              }
            }, [])
            .sort((n1, n2) => {
              return n2[lastMonth] - n1[lastMonth]
            })
          const prefix = `${camelCase(provider)} Apptopia ${
            dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'
          }`
          const parentFolderId =
            provider === 'revolut'
              ? accountConfigs[account].folders.revolutApptopia
              : accountConfigs[account].folders.wiseApptopia
          const filename = `${prefix} ${getOutputMonth(lastMonth)}.csv`
          const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
          const gDrive = await GDrive(account)
          await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)

          await moveModifiedBefore(
            gDrive,
            subDays(new Date(), 1),
            accountConfigs[account].folders.archive,
            [parentFolderId],
            [prefix],
          )
        }
      }
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'FRBRDEProvidersMonthlyApptopia',
    }),
    config: {
      concurrency: 1,
    },
  },
  FRBRDEProvidersMonthlyApptopia: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const results = []
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      for (const dataType of availableIntegrationDataTypes[IntegrationType.apptopia].map((item) => item.value)) {
        for (const countryData of FRBRDEProviders) {
          const someResults = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.apptopia,
                  project: countryData.project,
                  iso: countryData.iso,
                  provider: { $in: countryData.providers },
                  snapTimeUnit: TimeUnit.months,
                  dataType,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: {
                  path: '$stats',
                },
              },
              {
                $group: {
                  _id: {
                    provider: '$provider',
                    snapTime: '$snapTime',
                    iso: '$iso',
                    dataType: '$dataType',
                  },
                  count: {
                    $sum: '$stats.count',
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  provider: '$_id.provider',
                  providerIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.provider', 'leboncoin'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'olx'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'enjoei'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'ebk'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'mobile.de'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'vinted'],
                          },
                          then: 3,
                        },
                      ],
                      default: 0,
                    },
                  },
                  snapTime: '$_id.snapTime',
                  dataType: '$_id.dataType',
                  count: '$count',
                  countryIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.FR],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.BR],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.DE],
                          },
                          then: 3,
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              {
                $sort: {
                  snapTime: 1,
                  countryIndex: 1,
                  providerIndex: 1,
                },
              },
            ])
            .toArray()
          if (!someResults?.length) continue
          results.push(...someResults)
        }
      }

      const resultsFormatted = results.flatMap((item) => {
        return {
          provider: item.provider,
          month: format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy'),
          count: item.count,
          country: isoCountries.getName(item.iso, 'en'),
          dataType: item.dataType,
          countryIndex: item.countryIndex,
          providerIndex: item.providerIndex,
        }
      })

      const resultsParsed = []
      const countries = [
        ...new Set(
          resultsFormatted
            .sort((a, b) => {
              return a.countryIndex - b.countryIndex
            })
            .map((item) => item.country),
        ),
      ]
      const months = [...new Set(resultsFormatted.map((item) => item.month))]
      const apptopiaDataTypes = [...new Set(resultsFormatted.map((item) => item.dataType))]

      for (const country of countries) {
        const countryObj = { '': country, ' ': '' }

        for (const month of months) {
          countryObj[month] = ''
        }

        resultsParsed.push(countryObj)

        for (const dataType of apptopiaDataTypes) {
          resultsParsed.push({
            '': `Apps - ${dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'}`,
            ' ': '',
            ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
          })

          const providers = Array.from(
            new Set(
              resultsFormatted
                .filter((item) => item.country === country && item.dataType === dataType)
                .sort((a, b) => {
                  return a.providerIndex - b.providerIndex
                })
                .map((item) => item.provider),
            ),
          )

          for (const provider of providers) {
            const providerObj = {
              '': '',
              ' ': provider,
              ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
            }

            for (const month of months) {
              const item = resultsFormatted.find(
                (result) =>
                  result.country === country &&
                  result.dataType === dataType &&
                  result.provider === provider &&
                  result.month === month,
              )
              providerObj[month] = item ? item.count : ''
            }

            resultsParsed.push(providerObj)
          }
        }
      }

      const filename = `Apptopia FR_BR_DE Providers ${getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.frbrdeApptopia,
      )
      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.frbrdeApptopia],
        [/Apptopia FR_BR_DE Providers/g],
      )
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'nordicProvidersMonthlyApptopia',
    }),
    config: {
      concurrency: 1,
    },
  },
  nordicProvidersMonthlyApptopia: {
    // cron: '0 8 10 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const results = []
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      for (const dataType of availableIntegrationDataTypes[IntegrationType.apptopia].map((item) => item.value)) {
        for (const countryData of nordicProviders) {
          const someResults = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.apptopia,
                  project: countryData.project,
                  iso: countryData.iso,
                  provider: { $in: countryData.providers },
                  snapTimeUnit: TimeUnit.months,
                  dataType,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: {
                  path: '$stats',
                },
              },
              {
                $group: {
                  _id: {
                    provider: '$provider',
                    snapTime: '$snapTime',
                    iso: '$iso',
                    dataType: '$dataType',
                  },
                  count: {
                    $sum: '$stats.count',
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  provider: '$_id.provider',
                  providerIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.provider', 'finn.no'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'tise'],
                          },
                          then: 3,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'blocket'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'vinted'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'plick'],
                          },
                          then: 4,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'dba'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'boligsiden'],
                          },
                          then: 4,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'boliga'],
                          },
                          then: 5,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'trendsales'],
                          },
                          then: 6,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'tori'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'temu'],
                          },
                          then: 10,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'hjem'],
                          },
                          then: 11,
                        },
                      ],
                      default: 0,
                    },
                  },
                  snapTime: '$_id.snapTime',
                  dataType: '$_id.dataType',
                  count: '$count',
                  countryIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.NO],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.SE],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.DK],
                          },
                          then: 3,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.FI],
                          },
                          then: 4,
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              {
                $sort: {
                  snapTime: 1,
                  countryIndex: 1,
                  providerIndex: 1,
                },
              },
            ])
            .toArray()
          if (!someResults?.length) continue
          results.push(...someResults)
        }
      }

      const resultsFormatted = results.flatMap((item) => {
        return {
          provider: item.provider,
          month: format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy'),
          count: item.count,
          country: isoCountries.getName(item.iso, 'en'),
          dataType: item.dataType,
          countryIndex: item.countryIndex,
          providerIndex: item.providerIndex,
        }
      })

      const resultsParsed = []
      const countries = [
        ...new Set(
          resultsFormatted
            .sort((a, b) => {
              return a.countryIndex - b.countryIndex
            })
            .map((item) => item.country),
        ),
      ]
      const months = [...new Set(resultsFormatted.map((item) => item.month))]
      const apptopiaDataTypes = [...new Set(resultsFormatted.map((item) => item.dataType))]

      for (const country of countries) {
        const countryObj = { '': country, ' ': '' }

        for (const month of months) {
          countryObj[month] = ''
        }

        resultsParsed.push(countryObj)

        for (const dataType of apptopiaDataTypes) {
          resultsParsed.push({
            '': `Apps - ${dataType === 'apptopiaDownloads' ? 'Downloads' : 'Active Users'}`,
            ' ': '',
            ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
          })

          const providers = Array.from(
            new Set(
              resultsFormatted
                .filter((item) => item.country === country && item.dataType === dataType)
                .sort((a, b) => {
                  return a.providerIndex - b.providerIndex
                })
                .map((item) => item.provider),
            ),
          )

          for (const provider of providers) {
            const providerObj = {
              '': '',
              ' ': provider,
              ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
            }

            for (const month of months) {
              const item = resultsFormatted.find(
                (result) =>
                  result.country === country &&
                  result.dataType === dataType &&
                  result.provider === provider &&
                  result.month === month,
              )
              providerObj[month] = item ? item.count : ''
            }

            resultsParsed.push(providerObj)
          }
        }
      }

      const filename = `Apptopia Nordic Providers ${getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.nordicApptopia,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.nordicApptopia],
        [/Apptopia Nordic Providers/g],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  // ----------------- Others ------------------------------
  accountingApiIntegrationsCsv: {
    cron: '0 8 11 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const currentMonthNumber = getMonth(new Date()) + 1
      const previousMonthNumber = getMonth(subMonths(new Date(), 1)) + 1

      const startOfPreviousMonth = startOfMonth(subMonths(new Date(), 1))
      const endOfCurrentMonth = endOfMonth(new Date())

      const results = await runtime
        .collection('item')
        .aggregate([
          {
            $match: {
              project: accountingApiIntegrationProviders.project,
              provider: { $in: accountingApiIntegrationProviders.providers },
              dataType: DataType.app,
              updatedAt: {
                $gte: startOfPreviousMonth,
                $lte: endOfCurrentMonth,
              },
            },
          },
          {
            $project: {
              month: { $month: '$updatedAt' },
              provider: 1,
              appName: 1,
              isCurrentMonth: { $eq: [{ $month: '$updatedAt' }, currentMonthNumber] },
            },
          },
          {
            $group: {
              _id: { provider: '$provider', month: '$month' },
              appNames: { $push: '$appName' },
              totalApps: { $sum: 1 },
            },
          },
          {
            $group: {
              _id: '$_id.provider',
              months: { $push: { month: '$_id.month', appNames: '$appNames', totalApps: '$totalApps' } },
            },
          },
          {
            $project: {
              provider: '$_id',
              months: 1,
            },
          },
          {
            $addFields: {
              data: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: '$months',
                      as: 'month',
                      cond: { $eq: ['$$month.month', currentMonthNumber] },
                    },
                  },
                  0,
                ],
              },
              previousMonthTotalApps: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$months',
                        as: 'month',
                        cond: { $eq: ['$$month.month', previousMonthNumber] },
                      },
                    },
                    as: 'previous',
                    in: '$$previous.totalApps',
                  },
                },
              },
              sortIndex: { $indexOfArray: [accountingApiIntegrationProviders.providers, '$_id'] },
            },
          },
          {
            $sort: { sortIndex: 1 },
          },
          {
            $project: {
              provider: 1,
              data: {
                month: '$data.month',
                appNames: '$data.appNames',
                totalApps: '$data.totalApps',
                difference: { $subtract: ['$data.totalApps', '$previousMonthTotalApps'] },
              },
            },
          },
        ])
        .toArray()

      const providerOrder = accountingApiIntegrationProviders.providers

      const getDisplayName = (provider: string) => accountingApiIntegrationProviders.nameMapping[provider] || provider

      const resultsFormatted = results.flatMap((providerData) => {
        if (!providerData.data.appNames?.length) return []

        return providerData.data.appNames.map((appName: string, index: number) => {
          return {
            provider: getDisplayName(providerData.provider),
            appName: appName,
            index: index,
          }
        })
      })

      const resultsParsed = []

      const totalAppsRow = { Provider: 'Total Apps' }
      providerOrder.forEach((provider) => {
        const providerData = results.find((result) => result.provider === provider)
        totalAppsRow[getDisplayName(provider)] = providerData ? providerData.data.totalApps : ''
      })
      resultsParsed.push(totalAppsRow)

      const differenceRow = { Provider: 'Difference' }
      providerOrder.forEach((provider) => {
        const providerData = results.find((result) => result.provider === provider)
        differenceRow[getDisplayName(provider)] = providerData ? providerData.data.difference : ''
      })
      resultsParsed.push(differenceRow)

      const monthRow = { Provider: 'Month' }
      providerOrder.forEach((provider) => {
        monthRow[getDisplayName(provider)] = ''
      })
      resultsParsed.push(monthRow)

      const maxAppCount = Math.max(...results.map((providerData) => providerData.data?.appNames?.length ?? 0))
      for (let i = 0; i < maxAppCount; i++) {
        const row = { Provider: format(new Date(), Iso3166Alpha2.MM) }
        providerOrder.forEach((provider) => {
          const appData = resultsFormatted.find(
            (data) => data.provider === getDisplayName(provider) && data.index === i,
          )
          row[getDisplayName(provider)] = appData ? appData.appName : ''
        })
        resultsParsed.push(row)
      }

      const filename = `API Integrations New Listings ${getOutputMonth(format(Date.now(), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })

      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.apiIntegrations,
      )
      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.apiIntegrations],
        [/API Integrations New Listings/g],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  liveauctioneersMonthlyGmvPerIso: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const now = new Date()
      const results = await runtime
        .collection<Snapshot>('snapshot')
        .aggregate([
          {
            $match: {
              project: ProjectType.auctions,
              provider: 'liveauctioneers',
              dataType: DataType.inventory,
              snapTimeUnit: TimeUnit.months,
              snapTime: format(subMonths(startOfMonth(now), 1), 'yyyy/MM'),
            },
          },
          {
            $unwind: '$stats',
          },
          {
            $group: {
              _id: {
                iso: '$iso',
                liveBiddingType: '$stats.liveBiddingType',
              },
              gmv: { $sum: '$stats.gmvCurrent' },
            },
          },
          {
            $match: {
              '_id.liveBiddingType': AuctionsLiveBiddingType.NonCompeting,
            },
          },
          {
            $project: {
              _id: 0,
              ISO: '$_id.iso',
              GMV: '$gmv',
            },
          },
        ])
        .toArray()
      const filename = `liveauctioneers Listings GMV Per ISO ${format(subMonths(startOfMonth(now), 1), 'MMM yyyy')}.csv`
      const csv = json2csv(results, { emptyFieldValue: 0 })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.liveauctioneersGMV,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.liveauctioneersGMV],
        [/liveauctioneers Listings GMV Per ISO/g],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  liveauctioneersMonthlyBonhamsLots: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const now = new Date()
      const results = await runtime
        .collection('item')
        .find(
          {
            projectProviderDataType: 'auctions_liveauctioneers_inventory',
            dealerId: '1043',
            price: { $gt: 0 },
          },
          {
            projection: { _id: 0, iso: 1, price: 1, auctionDate: '$updatedAt', bidderId: '$data.winningBidderId' },
          },
        )
        .sort({ updatedAt: 1, iso: 1 })
        .toArray()
      const filename = `liveauctioneers Bonhams Lots Per ISO ${format(subMonths(startOfMonth(now), 1), 'MMM yyyy')}.csv`
      const csv = json2csv(results, { emptyFieldValue: ' ', useDateIso8601Format: true })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.liveauctioneersBonhams,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.liveauctioneersBonhams],
        [/liveauctioneers Bonhams Lots Per ISO/g],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  foxIntelligence: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config) => {
      const countries = {
        france: {
          providers: ['leboncoin'],
        },
        germany: {
          //providers: ['ebaykleinanzeigen']
        },
        spain: {},
      }
      const s3Cli = new S3({
        credentials: {
          accessKeyId: '********************',
          secretAccessKey: 'WwttyMNL5pPBxe4ViotXSQ45qqNmCdSAjoKF/5rg',
        },
      })
      const bucketParams = { Bucket: 'vorcapital-export-foxintelligence' }
      const listObj = await s3Cli.listObjects(bucketParams)
      let gDrive

      if (listObj?.Contents?.length) {
        for (const content of listObj.Contents) {
          if (!content.Key?.length || !content.LastModified || !isAfter(content.LastModified, startOfMonth(new Date())))
            continue
          if (!gDrive) gDrive = await GDrive(account)
          for (const country of Object.keys(countries)) {
            if (!countries[country].providers?.length)
              await sendQueriedData(s3Cli, bucketParams, content.Key, gDrive, country)
            else {
              for (const provider of countries[country].providers) {
                await sendQueriedData(s3Cli, bucketParams, content.Key, gDrive, country, provider)
              }
            }
          }
        }
      }
    },
    config: {
      concurrency: 1,
    },
  },
  adevintaProvidersWeekly: {
    cron: '30 10 * * SUN',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date()
      const startWeek = startOfISOWeek(subWeeks(startDate, 52))
      const preFileName = 'Adevinta Week '
      const processedMonths = new Map()

      const csvData = {}

      for (let week = startWeek; !isAfter(week, startDate); week = addWeeks(week, 1)) {
        const snapTime = getSnapTime(week, TimeUnit.weeks)
        const monthSnapTime = getSnapTime(week, TimeUnit.months)

        for (const queryFilter of adevintaQueryFilters) {
          const { verticals, snapTimeUnit, ...rest } = queryFilter
          const snapshot = await runtime.collection<Snapshot>('snapshot').findOne(
            {
              ...rest,
              snapTimeUnit,
              snapTime: snapTimeUnit === 'months' ? getSnapTime(week, TimeUnit.months) : snapTime,
            },
            { sort: { updatedAt: -1 } },
          )

          for (const vertical of verticals) {
            const key = `${queryFilter.project}_${queryFilter.provider}_${vertical}`
            if (!csvData[key]) csvData[key] = { ISOWeek: key }

            if (!processedMonths.has(key)) processedMonths.set(key, new Set())

            if (snapshot?.stats) {
              const filter = adevintaSnapshotFilters[vertical]
              let csvValue = ''

              switch (vertical) {
                // case 'Total Listings Professional Paying Agent': {
                //   if (!isBefore(startDate, week) && !isAfter(startDate, addWeeks(week, 1))) {
                //     // se for a semana corrente, contar e escrever para a snap/ variavel externa
                //     csvValue = (
                //       await runtime.collection<Item>('item').countDocuments({
                //         projectProviderDataType: 'realestates_ebk_inventory',
                //         iso: Iso3166Alpha2.DE,
                //         dealerType: DealerType.dealer,
                //         updatedAt: { $gte: week, $lte: addWeeks(week, 1) },
                //         dealerId: {
                //           $in: (
                //             await runtime
                //               .collection<Item>('item')
                //               .find({
                //                 projectProviderDataType: `${rest.project}_${rest.provider}_${DataType.dealers}`,
                //                 iso: rest.iso,
                //                 dealerType: DealerType.dealer,
                //                 updatedAt: { $gte: week, $lte: addWeeks(week, 1) },
                //                 isPaying: true,
                //               })
                //               .project({ dealerId: 1 })
                //               .toArray()
                //           ).map((item) => item.dealerId),
                //         },
                //       })
                //     ).toString()
                //     // escrever o csvValue para a snap/variavel externa a seguir a contagem
                //   } else {
                //     // senao ler da variavel externa e retornar
                //     break
                //   }
                //   break
                // }
                case 'Advertising Revenue':
                  csvValue = snapshot.stats
                    .flatMap((stat) => {
                      if (!('advertisingCost' in stat)) return []

                      return Object.keys(filter).every((key) => {
                        if (Array.isArray(filter[key])) {
                          return filter[key].includes(stat[key])
                        } else {
                          return key in stat && stat[key] === filter[key]
                        }
                      })
                        ? [stat]
                        : []
                    })
                    .reduce((acc, { advertisingCost }) => acc + (advertisingCost ?? 0), 0)
                  break
                case 'Listings Package Type Platinum (relative %)':
                  {
                    const platinumTotal = snapshot.stats
                      .flatMap((stat) => {
                        if (!('count' in stat)) return []

                        return Object.keys(filter).every((key) => {
                          if (Array.isArray(filter[key])) {
                            return filter[key].includes(stat[key])
                          } else {
                            return key in stat && stat[key] === filter[key]
                          }
                        })
                          ? [stat]
                          : []
                      })

                      .reduce((acc, { count }) => acc + (count ?? 0), 0)
                    const totalFilter = adevintaSnapshotFilters['Unfiltered']
                    const total = snapshot.stats
                      .flatMap((stat) => {
                        if (!('count' in stat)) return []

                        return Object.keys(totalFilter).every((key) => {
                          if (Array.isArray(totalFilter[key])) {
                            return totalFilter[key].includes(stat[key])
                          } else {
                            return key in stat && stat[key] === totalFilter[key]
                          }
                        })
                          ? [stat]
                          : []
                      })

                      .reduce((acc, { count }) => acc + (count ?? 0), 0)
                    csvValue = total ? `${((platinumTotal / total) * 100).toFixed(2).toString()} %` : 'N/A'
                  }
                  break
                default:
                  csvValue = snapshot.stats
                    .flatMap((stat) => {
                      if (!('count' in stat)) return []

                      return Object.keys(filter).every((key) => {
                        if (Array.isArray(filter[key])) {
                          return filter[key].includes(stat[key])
                        } else {
                          return key in stat && stat[key] === filter[key]
                        }
                      })
                        ? [stat]
                        : []
                    })
                    .reduce((acc, { count }) => acc + (count ?? 0), 0)
              }

              if (snapTimeUnit === 'months' && !processedMonths.get(key).has(monthSnapTime)) {
                csvData[key][snapTime] = csvValue
                processedMonths.get(key).add(monthSnapTime)
              } else if (snapTimeUnit === 'weeks') {
                csvData[key][snapTime] = csvValue
              }
            } else {
              csvData[key][snapTime] = ''
            }
          }
        }
      }
      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(new Date(), 'II/RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.adevintaExports,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.adevintaExports],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  schibstedWeeklyCsv: {
    cron: '30 8 * * SUN',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date() // subWeeks(new Date(), 1)
      const startWeek = startOfISOWeek(subWeeks(startDate, 52))
      const preFileName = 'Schibsted Week '

      const csvData = {}

      for (let week = startWeek; !isAfter(week, startDate); week = addWeeks(week, 1)) {
        const snapTime = getSnapTime(week, TimeUnit.weeks)

        for (const queryFilter of schibstedQueryFilters) {
          const { verticals, ...rest } = queryFilter
          const snapshot = await runtime
            .collection<Snapshot>('snapshot')
            .findOne({ ...rest, snapTime }, { sort: { updatedAt: -1 } })

          for (const vertical of verticals) {
            const filter = schibstedSnapshotFilters[vertical]
            const filtered = snapshot?.stats?.flatMap((stat) => {
              if (!('count' in stat)) return []

              return Object.keys(filter).every(
                (key) =>
                  key in stat &&
                  ((Array.isArray(filter[key]) && filter[key].includes(stat[key])) || stat[key] === filter[key]),
              )
                ? [stat]
                : []
            })
            const count = filtered?.reduce((acc, { count }) => acc + (count ?? 0), 0) ?? ''

            let outputValue = count

            if (vertical === 'Total Listings Professional Residential For Sale ARPL') {
              const totalAdCost = filtered
                ?.flatMap((stat) => ('advertisingCost' in stat ? [stat.advertisingCost ?? 0] : []))
                .reduce((sum, c) => sum + c, 0)
              outputValue = count > 0 ? Math.round(totalAdCost / count) : ''
            }

            let nettbilVertical: string
            if (queryFilter.provider === 'nettbil' && vertical === 'Total Listings') nettbilVertical = 'Volume Sold'
            if (queryFilter.provider === 'nettbil' && vertical === 'New Listings Total')
              nettbilVertical = 'Transacted This Week'

            const key = `${queryFilter.provider}_${queryFilter.project}_${nettbilVertical ?? vertical}`
            if (!csvData?.[key]) {
              const Country = isoCountries.getName(queryFilter.iso, 'en')
              const Project = queryFilter.project
              const Provider = queryFilter.provider
                .replaceAll('_reported', '')
                .replaceAll('_realestate_homes', '')
                .replaceAll('_', ' ')
                .replaceAll(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
              let Type =
                'businessType' in filter
                  ? filter.businessType.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
                  : 'vehicleType' in filter
                    ? typeof filter.vehicleType !== 'string'
                      ? 'Others'
                      : filter.vehicleType.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase())) + 's'
                    : 'All'
              let Seller = 'All'
              if ('dealerType' in filter) {
                Seller = filter.dealerType === 'private' ? 'Private' : 'Professional'
              }

              if (Seller === 'All' && queryFilter.provider.match(/_reported/)) {
                Seller = 'excl. New'
              }

              if (Seller === 'All' && queryFilter.provider.match(/_realestate_homes/)) {
                Seller = 'Homes'
              }

              if (Seller === 'All' && 'propertyScope' in filter) {
                Seller = filter.propertyScope.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
              }

              const Location = vertical.includes('Oslo') ? 'Oslo' : 'All'

              let Product = vertical
                .replace(/non cars/i, '')
                .replace(Type, '')
                .replace(Seller, '')
                .replace(Location, '')
                .replace(/ for /i, '')
                .replace(/ in /i, '')
                .trim()

              if (vertical.includes('Vehicle Parts') && queryFilter.provider === 'blocket') {
                Product = 'New Listings'
                Type = `Vehicle Parts`
              }
              csvData[key] = {
                Country,
                Project,
                Provider,
                Product,
                Type,
                Seller,
                Location,
                //Vertical: nettbilVertical ?? vertical,
              }
            }
            csvData[key][snapTime] = outputValue
          }
        }
      }

      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '-' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(startDate, 'II RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.schibstedExports,
      )

      await moveModifiedBefore(
        gDrive,
        subWeeks(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.schibstedExports],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  nordicProvidersMonthly: {
    cron: '0 8 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [IntegrationType.similarWeb, IntegrationType.apptopia, IntegrationType.dataAi]
      const isoList = ['NO', 'SE', 'FI', 'DK'] as Iso3166Alpha2[]
      const providerList = nordicProvidersIntegrationSnapshot
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 1), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'Nordic Providers '
      const folder = accountConfigs[account].folders.nordic

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        excludeIntegrationDataTypes: [DataType.similarWebPageviews],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },
  adevintaIntegrationSnapshotsMonthly: {
    cron: '0 12 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [IntegrationType.similarWeb, IntegrationType.apptopia, IntegrationType.sensortower]
      const isoList = [
        Iso3166Alpha2.FR,
        Iso3166Alpha2.BR,
        Iso3166Alpha2.DE,
        Iso3166Alpha2.ES,
        Iso3166Alpha2.CA,
        Iso3166Alpha2.AU,
        Iso3166Alpha2.IT,
      ]
      const providerList = adevintaProvidersIntegrationSnapshot
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 1), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'Adevinta Providers '
      const folder = accountConfigs[account].folders.adevinta

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        excludeIntegrationDataTypes: [DataType.similarWebPageviews],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },
  costarIntegrationSnapshotsMonthly: {
    cron: '0 12 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [IntegrationType.similarWeb, IntegrationType.apptopia, IntegrationType.sensortower]
      const isoList = [Iso3166Alpha2.US]
      const providerList = costarProvidersIntegrationSnapshot
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 1), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'CoStar Providers '
      const folder = accountConfigs[account].folders.costar

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        excludeIntegrationDataTypes: [DataType.similarWebPageviews],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },
  hemnetWeeklyCsv: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date()
      const startWeek = startOfISOWeek(subWeeks(startDate, 52))
      const preFileName = 'Hemnet Week '

      const csvData = {}

      for (let week = startWeek; !isAfter(week, startDate); week = addWeeks(week, 1)) {
        const snapTime = getSnapTime(week, TimeUnit.weeks)

        for (const queryFilter of hemnetQueryFilters) {
          const { verticals, snapTimeUnit, ...rest } = queryFilter
          const snapshot = await runtime
            .collection<Snapshot>('snapshot')
            .findOne({ ...rest, snapTimeUnit, snapTime }, { sort: { updatedAt: -1 } })

          for (const vertical of verticals) {
            const key = `${queryFilter.provider}_${vertical}`
            if (!csvData[key]) {
              const { id: parsedId, pkg: parsedPackage } = parseVerticalForHemnet(vertical)
              csvData[key] = {
                Company: queryFilter.provider,
                ID: parsedId,
                Package: parsedPackage,
              }
            }

            if (snapshot?.stats) {
              const filter = hemnetSnapshotFilters[vertical]
              let csvValue = ''

              switch (vertical) {
                case 'ARPL':
                  {
                    const advertisingCost = snapshot.stats
                      .flatMap((stat) => {
                        if (!('advertisingCost' in stat)) return []
                        return Object.keys(filter).every((key) =>
                          Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                        )
                          ? [stat]
                          : []
                      })
                      .reduce((acc, { advertisingCost }) => acc + (advertisingCost ?? 0), 0)

                    const newListingsFilter = hemnetSnapshotFilters['New Listings']
                    const total = snapshot.stats
                      .flatMap((stat) => {
                        if (!('count' in stat)) return []
                        return Object.keys(newListingsFilter).every((key) =>
                          Array.isArray(newListingsFilter[key])
                            ? newListingsFilter[key].includes(stat[key])
                            : stat[key] === newListingsFilter[key],
                        )
                          ? [stat]
                          : []
                      })
                      .reduce((acc, { count }) => acc + (Number(count) || 0), 0)

                    csvValue = total ? `${Math.round(advertisingCost / total)}` : 'N/A'
                  }
                  break
                case 'Crossposted Listings Views':
                  {
                    csvValue = snapshot.stats
                      .flatMap((stat) => {
                        if (!('adViews' in stat)) return []
                        return Object.keys(filter).every((key) =>
                          Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                        )
                          ? [stat]
                          : []
                      })
                      .reduce((acc, { adViews }) => acc + (adViews ?? 0), 0)
                      .toString()
                  }
                  break
                case 'Average Max Views': {
                  const maxStats = snapshot.stats.flatMap((stat) => {
                    if (!('adViews' in stat) || !('count' in stat)) return []
                    return Object.keys(filter).every((k) =>
                      Array.isArray(filter[k]) ? filter[k].includes(stat[k]) : stat[k] === filter[k],
                    )
                      ? [stat]
                      : []
                  })
                  const totalAdViews = maxStats.reduce((sum, s) => sum + (s.adViews ?? 0), 0)
                  const totalCount = maxStats.reduce((sum, s) => sum + (s.count ?? 0), 0)
                  csvValue = totalCount > 0 ? (totalAdViews / totalCount).toFixed(2) : 'N/A'
                  break
                }

                case 'Average Premium Views': {
                  const premStats = snapshot.stats.flatMap((stat) => {
                    if (!('adViews' in stat) || !('count' in stat)) return []
                    return Object.keys(filter).every((k) =>
                      Array.isArray(filter[k]) ? filter[k].includes(stat[k]) : stat[k] === filter[k],
                    )
                      ? [stat]
                      : []
                  })
                  const totalAdViewsP = premStats.reduce((sum, s) => sum + (s.adViews ?? 0), 0)
                  const totalCountP = premStats.reduce((sum, s) => sum + (s.count ?? 0), 0)
                  csvValue = totalCountP > 0 ? (totalAdViewsP / totalCountP).toFixed(2) : 'N/A'
                  break
                }
                case 'New Listings Package Basic':
                case 'New Listings Package Plus':
                case 'New Listings Package Premium':
                case 'New Listings Package Max':
                case 'Stockholm New Listings Package Basic':
                case 'Stockholm New Listings Package Plus':
                case 'Stockholm New Listings Package Premium':
                case 'Stockholm New Listings Package Max': {
                  const packageFilter = filter

                  const numerator = snapshot.stats
                    .filter((stat) => {
                      if (!('count' in stat)) return false
                      return Object.keys(packageFilter).every((key) =>
                        Array.isArray(packageFilter[key])
                          ? packageFilter[key].includes(stat[key])
                          : stat[key] === packageFilter[key],
                      )
                    })
                    .reduce((acc, { count }) => acc + (Number(count) || 0), 0)

                  const totalNewListingsFilterKey = vertical.startsWith('Stockholm')
                    ? 'Stockholm New Listings'
                    : 'New Listings'
                  const totalNewListingsFilter = hemnetSnapshotFilters[totalNewListingsFilterKey]

                  const denominator = snapshot.stats
                    .filter((stat) => {
                      if (!('count' in stat)) return false
                      return Object.keys(totalNewListingsFilter).every((key) =>
                        Array.isArray(totalNewListingsFilter[key])
                          ? totalNewListingsFilter[key].includes(stat[key])
                          : stat[key] === totalNewListingsFilter[key],
                      )
                    })
                    .reduce((acc, { count }) => acc + (Number(count) || 0), 0)

                  csvValue = denominator > 0 ? `${((numerator / denominator) * 100).toFixed(2)} %` : 'N/A'
                  break
                }
                default:
                  csvValue = snapshot.stats
                    .flatMap((stat) => {
                      if (!('count' in stat)) return []
                      return Object.keys(filter).every((key) =>
                        Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                      )
                        ? [stat]
                        : []
                    })
                    .reduce((acc, { count }) => acc + (Number(count) || 0), 0)
                    .toString()
              }

              csvData[key][snapTime] = csvValue
            } else {
              csvData[key][snapTime] = ''
            }
          }
        }
      }
      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(new Date(), 'II/RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.realEstateSE,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.realEstateSE],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  hemnetIrWeeklyCsv: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date()
      const startWeek = startOfISOWeek(subWeeks(startDate, 52))
      const preFileName = 'Hemnet IR Week '

      const csvData = {}

      for (let week = startWeek; !isAfter(week, startDate); week = addWeeks(week, 1)) {
        const snapTime = getSnapTime(week, TimeUnit.weeks)

        for (const queryFilter of hemnetIrQueryFilters) {
          const { verticals, snapTimeUnit, ...rest } = queryFilter
          const snapshot = await runtime
            .collection<Snapshot>('snapshot')
            .findOne({ ...rest, snapTimeUnit, snapTime }, { sort: { updatedAt: -1 } })

          for (const vertical of verticals) {
            const key = `${queryFilter.provider}_${vertical}`
            if (!csvData[key]) {
              const { id: parsedId, pkg: parsedPackage } = parseVerticalForHemnet(vertical)
              csvData[key] = {
                Company: queryFilter.provider,
                ID: parsedId,
                Package: parsedPackage,
              }
            }

            if (snapshot?.stats) {
              const filter = hemnetSnapshotFilters[vertical]
              let csvValue = ''

              switch (vertical) {
                case 'ARPL':
                  {
                    const advertisingCost = snapshot.stats
                      .flatMap((stat) => {
                        if (!('advertisingCost' in stat)) return []
                        return Object.keys(filter).every((key) =>
                          Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                        )
                          ? [stat]
                          : []
                      })
                      .reduce((acc, { advertisingCost }) => acc + (advertisingCost ?? 0), 0)

                    const newListingsFilter = hemnetSnapshotFilters['New Listings']
                    const total = snapshot.stats
                      .flatMap((stat) => {
                        if (!('count' in stat)) return []
                        return Object.keys(newListingsFilter).every((key) =>
                          Array.isArray(newListingsFilter[key])
                            ? newListingsFilter[key].includes(stat[key])
                            : stat[key] === newListingsFilter[key],
                        )
                          ? [stat]
                          : []
                      })
                      .reduce((acc, { count }) => acc + (Number(count) || 0), 0)

                    csvValue = total ? `${Math.round(advertisingCost / total)}` : 'N/A'
                  }
                  break
                case 'Crossposted Listings Views':
                  {
                    csvValue = snapshot.stats
                      .flatMap((stat) => {
                        if (!('adViews' in stat)) return []
                        return Object.keys(filter).every((key) =>
                          Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                        )
                          ? [stat]
                          : []
                      })
                      .reduce((acc, { adViews }) => acc + (adViews ?? 0), 0)
                      .toString()
                  }
                  break
                case 'Average Max Views': {
                  const maxStats = snapshot.stats.flatMap((stat) => {
                    if (!('adViews' in stat) || !('count' in stat)) return []
                    return Object.keys(filter).every((k) =>
                      Array.isArray(filter[k]) ? filter[k].includes(stat[k]) : stat[k] === filter[k],
                    )
                      ? [stat]
                      : []
                  })
                  const totalAdViews = maxStats.reduce((sum, s) => sum + (s.adViews ?? 0), 0)
                  const totalCount = maxStats.reduce((sum, s) => sum + (s.count ?? 0), 0)
                  csvValue = totalCount > 0 ? (totalAdViews / totalCount).toFixed(2) : 'N/A'
                  break
                }

                case 'Average Premium Views': {
                  const premStats = snapshot.stats.flatMap((stat) => {
                    if (!('adViews' in stat) || !('count' in stat)) return []
                    return Object.keys(filter).every((k) =>
                      Array.isArray(filter[k]) ? filter[k].includes(stat[k]) : stat[k] === filter[k],
                    )
                      ? [stat]
                      : []
                  })
                  const totalAdViewsP = premStats.reduce((sum, s) => sum + (s.adViews ?? 0), 0)
                  const totalCountP = premStats.reduce((sum, s) => sum + (s.count ?? 0), 0)
                  csvValue = totalCountP > 0 ? (totalAdViewsP / totalCountP).toFixed(2) : 'N/A'
                  break
                }
                case 'New Listings Package Basic':
                case 'New Listings Package Plus':
                case 'New Listings Package Premium':
                case 'New Listings Package Max':
                case 'Stockholm New Listings Package Basic':
                case 'Stockholm New Listings Package Plus':
                case 'Stockholm New Listings Package Premium':
                case 'Stockholm New Listings Package Max':
                case 'Total Listings Package Basic':
                case 'Total Listings Package Plus':
                case 'Total Listings Package Premium':
                case 'Total Listings Package Max': {
                  const packageFilter = filter

                  const numerator = snapshot.stats
                    .filter((stat) => {
                      if (!('count' in stat)) return false
                      return Object.keys(packageFilter).every((key) =>
                        Array.isArray(packageFilter[key])
                          ? packageFilter[key].includes(stat[key])
                          : stat[key] === packageFilter[key],
                      )
                    })
                    .reduce((acc, { count }) => acc + (Number(count) || 0), 0)

                  // const totalNewListingsFilterKey = vertical.startsWith('Stockholm')
                  //   ? 'Stockholm New Listings'
                  //   : 'New Listings'
                  // const totalNewListingsFilter = hemnetSnapshotFilters[totalNewListingsFilterKey]

                  // const denominator = snapshot.stats
                  //   .filter((stat) => {
                  //     if (!('count' in stat)) return false
                  //     return Object.keys(totalNewListingsFilter).every((key) =>
                  //       Array.isArray(totalNewListingsFilter[key])
                  //         ? totalNewListingsFilter[key].includes(stat[key])
                  //         : stat[key] === totalNewListingsFilter[key],
                  //     )
                  //   })
                  //   .reduce((acc, { count }) => acc + (Number(count) || 0), 0)

                  csvValue = numerator > 0 ? `${numerator}` : 'N/A'
                  break
                }
                default:
                  csvValue = snapshot.stats
                    .flatMap((stat) => {
                      if (!('count' in stat)) return []
                      return Object.keys(filter).every((key) =>
                        Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                      )
                        ? [stat]
                        : []
                    })
                    .reduce((acc, { count }) => acc + (Number(count) || 0), 0)
                    .toString()
              }

              csvData[key][snapTime] = csvValue
            } else {
              csvData[key][snapTime] = ''
            }
          }
        }
      }
      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(startDate, 'II/RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.realEstateSE,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.realEstateSE],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  hemnetNewListingsWeeklyCsv: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date()
      const startWeek = startOfISOWeek(subWeeks(startDate, 52))
      const preFileName = 'Hemnet New Listings Week '

      const csvData = {}

      for (let week = startWeek; !isAfter(week, startDate); week = addWeeks(week, 1)) {
        const snapTime = getSnapTime(week, TimeUnit.weeks)

        for (const queryFilter of hemnetNewListingsPackagesQueryFilters) {
          const { verticals, snapTimeUnit, ...rest } = queryFilter
          const snapshot = await runtime
            .collection<Snapshot>('snapshot')
            .findOne({ ...rest, snapTimeUnit, snapTime }, { sort: { updatedAt: -1 } })

          for (const vertical of verticals) {
            const key = `${queryFilter.provider}_${vertical}`
            if (!csvData[key]) {
              const { id: parsedId, pkg: parsedPackage } = parseVerticalForHemnet(vertical)
              csvData[key] = {
                Company: queryFilter.provider,
                ID: parsedId,
                Package: parsedPackage,
              }
            }

            if (snapshot?.stats) {
              const filter = hemnetSnapshotFilters[vertical]

              const csvValue = snapshot.stats
                .flatMap((stat) => {
                  if (!('count' in stat)) return []
                  return Object.keys(filter).every((key) =>
                    Array.isArray(filter[key]) ? filter[key].includes(stat[key]) : stat[key] === filter[key],
                  )
                    ? [stat]
                    : []
                })
                .reduce((acc, { count }) => acc + (count ?? 0), 0)

              csvData[key][snapTime] = csvValue
            } else {
              csvData[key][snapTime] = ''
            }
          }
        }
      }
      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(new Date(), 'II/RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.realEstateSE,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.realEstateSE],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  hemnetPriceRangeWeeklyCsv: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date()
      const preFileName = 'Hemnet Pricing Range Week '

      const totals = (
        await runtime
          .collection<Item>('item')
          .aggregate([
            {
              $match: {
                projectProviderDataType: 'realestates_hemnet_inventory',
                updatedAt: { $gte: startOfISOWeek(startDate), $lte: endOfISOWeek(startDate) },
                iso: 'SE',
              },
            },
            { $group: { _id: { priceRange: '$priceRange' }, count: { $sum: 1 } } },
            {
              $project: {
                _id: 0,
                priceRange: '$_id.priceRange',
                priceRangeIndex: {
                  $switch: {
                    branches: [
                      { case: { $eq: ['$_id.priceRange', '0 - 299,999'] }, then: 1 },
                      { case: { $eq: ['$_id.priceRange', '300,000 - 599,999'] }, then: 2 },
                      { case: { $eq: ['$_id.priceRange', '600,000 - 1,099,999'] }, then: 3 },
                      { case: { $eq: ['$_id.priceRange', '1,100,000 - 1,599,999'] }, then: 4 },
                      { case: { $eq: ['$_id.priceRange', '1,600,000 - 2,099,999'] }, then: 5 },
                      { case: { $eq: ['$_id.priceRange', '2,100,000 - 2,599,999'] }, then: 6 },
                      { case: { $eq: ['$_id.priceRange', '2,600,000 - 3,099,999'] }, then: 7 },
                      { case: { $eq: ['$_id.priceRange', '3,100,000 - 4,099,999'] }, then: 8 },
                      { case: { $eq: ['$_id.priceRange', '4,100,000 - 5,099,999'] }, then: 9 },
                      { case: { $eq: ['$_id.priceRange', '5,100,000 - 6,099,999'] }, then: 10 },
                      { case: { $eq: ['$_id.priceRange', '6,100,000 - 8,099,999'] }, then: 11 },
                      { case: { $eq: ['$_id.priceRange', '8,100,000 - 10,099,999'] }, then: 12 },
                      { case: { $eq: ['$_id.priceRange', '10,100,000 - 11,099,999'] }, then: 13 },
                      { case: { $eq: ['$_id.priceRange', '11,100,000 - 12,099,999'] }, then: 14 },
                      { case: { $eq: ['$_id.priceRange', '12,100,000 - 13,099,999'] }, then: 15 },
                      { case: { $eq: ['$_id.priceRange', '13,100,000 - 14,099,999'] }, then: 16 },
                      { case: { $eq: ['$_id.priceRange', '14,100,000 - 15,099,999'] }, then: 17 },
                      { case: { $eq: ['$_id.priceRange', '15,100,000 - 16,099,999'] }, then: 18 },
                      { case: { $eq: ['$_id.priceRange', '16,100,000 - 17,099,999'] }, then: 19 },
                      { case: { $eq: ['$_id.priceRange', '17,100,000 - 18,099,999'] }, then: 20 },
                      { case: { $eq: ['$_id.priceRange', '18,100,000 - 19,099,999'] }, then: 21 },
                      { case: { $eq: ['$_id.priceRange', '19,100,000 - 20,099,999'] }, then: 22 },
                      { case: { $eq: ['$_id.priceRange', '20,100,000 - 21,099,999'] }, then: 23 },
                      { case: { $eq: ['$_id.priceRange', '21,100,000 - 22,099,999'] }, then: 24 },
                      { case: { $eq: ['$_id.priceRange', '22,100,000 - 23,099,999'] }, then: 25 },
                      { case: { $eq: ['$_id.priceRange', '23,100,000 - 24,099,999'] }, then: 26 },
                      { case: { $eq: ['$_id.priceRange', '24,100,000 - 25,099,999'] }, then: 27 },
                      { case: { $eq: ['$_id.priceRange', '25,100,000 - 26,099,999'] }, then: 28 },
                      { case: { $eq: ['$_id.priceRange', '26,100,000 - 27,099,999'] }, then: 29 },
                      { case: { $eq: ['$_id.priceRange', '27,100,000 - 28,099,999'] }, then: 30 },
                      { case: { $eq: ['$_id.priceRange', '28,100,000 - 29,099,999'] }, then: 31 },
                      { case: { $eq: ['$_id.priceRange', '29,100,000 - 30,099,999'] }, then: 32 },
                      { case: { $eq: ['$_id.priceRange', '30,100,000 +'] }, then: 101 },
                      { case: { $eq: ['$_id.priceRange', '10,100,000 +'] }, then: 100 },
                    ],
                    default: 0,
                  },
                },
                count: '$count',
              },
            },
            {
              $sort: { priceRangeIndex: 1 },
            },
            {
              $project: {
                priceRangeIndex: 0,
              },
            },
          ])
          .toArray()
      )?.map((item) => ({ priceRange: item.priceRange, isNew: '', count: item.count }))

      const newListings = (
        await runtime
          .collection<Item>('item')
          .aggregate([
            {
              $match: {
                projectProviderDataType: 'realestates_hemnet_inventory',
                createdAt: { $gte: startOfISOWeek(startDate), $lte: endOfISOWeek(startDate) },
                updatedAt: { $gte: startOfISOWeek(startDate), $lte: endOfISOWeek(startDate) },
                iso: 'SE',
              },
            },
            { $group: { _id: { priceRange: '$priceRange' }, count: { $sum: 1 } } },
            {
              $project: {
                _id: 0,
                priceRange: '$_id.priceRange',
                priceRangeIndex: {
                  $switch: {
                    branches: [
                      { case: { $eq: ['$_id.priceRange', '0 - 299,999'] }, then: 1 },
                      { case: { $eq: ['$_id.priceRange', '300,000 - 599,999'] }, then: 2 },
                      { case: { $eq: ['$_id.priceRange', '600,000 - 1,099,999'] }, then: 3 },
                      { case: { $eq: ['$_id.priceRange', '1,100,000 - 1,599,999'] }, then: 4 },
                      { case: { $eq: ['$_id.priceRange', '1,600,000 - 2,099,999'] }, then: 5 },
                      { case: { $eq: ['$_id.priceRange', '2,100,000 - 2,599,999'] }, then: 6 },
                      { case: { $eq: ['$_id.priceRange', '2,600,000 - 3,099,999'] }, then: 7 },
                      { case: { $eq: ['$_id.priceRange', '3,100,000 - 4,099,999'] }, then: 8 },
                      { case: { $eq: ['$_id.priceRange', '4,100,000 - 5,099,999'] }, then: 9 },
                      { case: { $eq: ['$_id.priceRange', '5,100,000 - 6,099,999'] }, then: 10 },
                      { case: { $eq: ['$_id.priceRange', '6,100,000 - 8,099,999'] }, then: 11 },
                      { case: { $eq: ['$_id.priceRange', '8,100,000 - 10,099,999'] }, then: 12 },
                      { case: { $eq: ['$_id.priceRange', '10,100,000 - 11,099,999'] }, then: 13 },
                      { case: { $eq: ['$_id.priceRange', '11,100,000 - 12,099,999'] }, then: 14 },
                      { case: { $eq: ['$_id.priceRange', '12,100,000 - 13,099,999'] }, then: 15 },
                      { case: { $eq: ['$_id.priceRange', '13,100,000 - 14,099,999'] }, then: 16 },
                      { case: { $eq: ['$_id.priceRange', '14,100,000 - 15,099,999'] }, then: 17 },
                      { case: { $eq: ['$_id.priceRange', '15,100,000 - 16,099,999'] }, then: 18 },
                      { case: { $eq: ['$_id.priceRange', '16,100,000 - 17,099,999'] }, then: 19 },
                      { case: { $eq: ['$_id.priceRange', '17,100,000 - 18,099,999'] }, then: 20 },
                      { case: { $eq: ['$_id.priceRange', '18,100,000 - 19,099,999'] }, then: 21 },
                      { case: { $eq: ['$_id.priceRange', '19,100,000 - 20,099,999'] }, then: 22 },
                      { case: { $eq: ['$_id.priceRange', '20,100,000 - 21,099,999'] }, then: 23 },
                      { case: { $eq: ['$_id.priceRange', '21,100,000 - 22,099,999'] }, then: 24 },
                      { case: { $eq: ['$_id.priceRange', '22,100,000 - 23,099,999'] }, then: 25 },
                      { case: { $eq: ['$_id.priceRange', '23,100,000 - 24,099,999'] }, then: 26 },
                      { case: { $eq: ['$_id.priceRange', '24,100,000 - 25,099,999'] }, then: 27 },
                      { case: { $eq: ['$_id.priceRange', '25,100,000 - 26,099,999'] }, then: 28 },
                      { case: { $eq: ['$_id.priceRange', '26,100,000 - 27,099,999'] }, then: 29 },
                      { case: { $eq: ['$_id.priceRange', '27,100,000 - 28,099,999'] }, then: 30 },
                      { case: { $eq: ['$_id.priceRange', '28,100,000 - 29,099,999'] }, then: 31 },
                      { case: { $eq: ['$_id.priceRange', '29,100,000 - 30,099,999'] }, then: 32 },
                      { case: { $eq: ['$_id.priceRange', '30,100,000 +'] }, then: 101 },
                      { case: { $eq: ['$_id.priceRange', '10,100,000 +'] }, then: 100 },
                    ],
                    default: 0,
                  },
                },
                count: '$count',
              },
            },
            {
              $sort: { priceRangeIndex: 1 },
            },
            {
              $project: {
                priceRangeIndex: 0,
              },
            },
          ])
          .toArray()
      )?.map((item) => ({ priceRange: item.priceRange, isNew: true, count: item.count }))

      const csv = json2csv([...totals, ...newListings], { emptyFieldValue: '' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(startDate, 'II/RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.realEstateSE,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.realEstateSE],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  hemnetMonthly: {
    cron: '0 8 10,11 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const endDate = subMonths(startOfMonth(new Date()), 1)
      const startDateObj = subYears(endDate, 3)
      const startSnapTime = format(startDateObj, 'yyyy/MM')
      const months: string[] = []
      for (let d = parse(startSnapTime, 'yyyy/MM', new Date()); !isAfter(d, endDate); d = addMonths(d, 1)) {
        months.push(format(d, 'yyyy/MM'))
      }

      const csvData = {}

      function sumField(
        stats: { count: number }[],
        filterEntries: [string, string | boolean][],
        field: string,
      ): number {
        return stats
          .filter((stat) =>
            filterEntries.every(([key, val]) => (Array.isArray(val) ? val.includes(stat[key]) : stat[key] === val)),
          )
          .reduce((sum, s) => sum + (s[field] ?? 0), 0)
      }

      for (const snapTime of months) {
        const label = format(parse(snapTime, 'yyyy/MM', new Date()), 'MMM/yy')

        for (const q of hemnetMonthlyQueryFilters) {
          const { project, provider, dataType, iso, snapTimeUnit, verticals, integration } = q

          for (const vertical of verticals) {
            const filter = hemnetSnapshotFilters[vertical]
            const filterEntries = Object.entries(filter).filter(([k]) => k !== 'dataType')
            const rowKey = `${provider}_${vertical}`

            const section = q.integration
              ? q.integration === IntegrationType.similarWeb
                ? 'Similar Web'
                : q.integration === IntegrationType.apptopia
                  ? 'Apptopia'
                  : q.integration === IntegrationType.dataAi
                    ? 'Data AI'
                    : q.integration === IntegrationType.sensortower
                      ? 'SensorTower'
                      : 'Unknown'
              : q.provider === 'booli_statistics'
                ? 'Website'
                : 'Database'

            const displayCompany = provider === 'booli_statistics' ? 'booli' : provider
            const Metric = vertical.replace(/data ai|apptopia|sensortower/i, '').trim() ?? vertical

            if (!csvData[rowKey]) {
              csvData[rowKey] = {
                Company: displayCompany,
                Section: section,
                Metric,
              }
            }
            let statsArray = []

            if ([IntegrationType.apptopia, IntegrationType.dataAi, IntegrationType.sensortower].includes(integration)) {
              const matchIntegration: Filter<IntegrationSnapshot> = {
                integration,
                project,
                provider,
                iso,
                dataType: filter['dataType'] as IntegrationSnapshotDataType,
                snapTimeUnit,
                snapTime,
              }

              const docs = await runtime
                .collection<IntegrationSnapshot>('integrationSnapshot')
                .find(matchIntegration)
                .toArray()
              const latestByPlatform = docs.reduce<Record<string, IntegrationSnapshot>>((acc, doc) => {
                const p = doc.platform
                if (!acc[p] || doc.updatedAt > acc[p].updatedAt) acc[p] = doc
                return acc
              }, {})

              statsArray = Object.values(latestByPlatform).flatMap((d) => d.stats)
            } else if (integration) {
              const matchIntegration: Filter<IntegrationSnapshot> = {
                integration,
                project,
                provider,
                iso,
                dataType: filter['dataType'] as IntegrationSnapshotDataType,
                snapTimeUnit,
                snapTime,
              }
              const doc = await runtime
                .collection<IntegrationSnapshot>('integrationSnapshot')
                .findOne(matchIntegration, { sort: { updatedAt: -1 } })

              statsArray = doc?.stats ?? []
            } else {
              const matchSnapshot: Filter<Snapshot> = {
                project,
                provider,
                iso,
                dataType,
                snapTimeUnit,
                snapTime,
              }
              const doc = await runtime
                .collection<Snapshot>('snapshot')
                .findOne(matchSnapshot, { sort: { updatedAt: -1 } })

              statsArray = doc?.stats ?? []
            }

            let csvValue = ''
            if (vertical === 'Crossposted Listings Views') {
              csvValue = sumField(
                statsArray.filter((s) => 'adViews' in s),
                filterEntries,
                'adViews',
              ).toString()
            } else {
              csvValue = sumField(
                statsArray.filter((s) => 'count' in s),
                filterEntries,
                'count',
              ).toString()
            }

            if (vertical === 'Page Views (million)') {
              csvValue = (Number(csvValue) / 1e6).toString()
            }

            csvData[rowKey][label] = csvValue
          }
        }
      }

      const isRunningSimilarWeb = await checkRunningStatus(runtime)
      if (!isRunningSimilarWeb) {
        const domains = [
          { domain: 'hemnet.se', name: 'hemnet' },
          { domain: 'booli.se', name: 'booli' },
        ]

        for (const { domain, name } of domains) {
          // Visits Duration (s)
          const durUrl = `https://api.similarweb.com/v1/website/${domain}/total-traffic-and-engagement/average-visit-duration`
          const durJson = await got(durUrl, {
            searchParams: {
              api_key: similarWebAPIkey,
              granularity: 'monthly',
              main_domain_only: 'false',
              format: 'json',
            },
          }).json<{ average_visit_duration: { date: string; average_visit_duration: number }[] }>()

          const durMap = (durJson.average_visit_duration || []).reduce<Record<string, number>>((m, r) => {
            const snap = format(parse(r.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM')
            m[snap] = Math.round(r.average_visit_duration)
            return m
          }, {})

          const durKey = `${name}_visit_duration`
          csvData[durKey] = {
            Company: name,
            Section: 'Similar Web',
            Metric: `${name.charAt(0).toUpperCase() + name.slice(1)} Visit Duration (sec)`,
          }
          for (const m of months) {
            const col = format(parse(m, 'yyyy/MM', new Date()), 'MMM/yy')
            csvData[durKey][col] = durMap[m]?.toString() || ''
          }

          // Paid Search (%)
          const overview_share = await getOverviewShare(domain, startDateObj, endDate, 'MMM/yy')

          const paidKey = `${name}_paid_search_pct`
          csvData[paidKey] = {
            Company: name,
            Section: 'Similar Web',
            Metric: 'Paid Search (%)',
          }
          for (const snapTime of Object.keys(overview_share)) {
            csvData[paidKey][snapTime] = overview_share[snapTime]?.paidSearch?.total?.relative
          }
        }
      }

      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '' })
      const filename = `Hemnet Monthly ${format(endDate, 'MMM yyyy')}.csv`
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.realEstateSE,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.realEstateSE],
        ['Hemnet Monthly'],
      )
    },
    config: { concurrency: 1 },
  },
  mobileCarsWeeklyCsv: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startDate = new Date()
      const startWeek = startOfISOWeek(subWeeks(startDate, 52))
      const preFileName = 'Mobile Cars Week '

      const csvData = {}

      for (let week = startWeek; !isAfter(week, startDate); week = addWeeks(week, 1)) {
        const snapTime = getSnapTime(week, TimeUnit.weeks)

        for (const queryFilter of mobileWeeklyPackagesQueryFilters) {
          const { verticals, snapTimeUnit, ...rest } = queryFilter
          const snapshot = await runtime
            .collection<Snapshot>('snapshot')
            .findOne({ ...rest, snapTimeUnit, snapTime }, { sort: { updatedAt: -1 } })

          for (const vertical of verticals) {
            const key = `${queryFilter.provider}_${vertical}`
            if (!csvData[key]) {
              const { id: parsedId, pkg: parsedPackage } = parseVerticalForMobile(vertical)
              csvData[key] = {
                Company: queryFilter.provider,
                ID: parsedId,
                Package: parsedPackage,
              }
            }

            if (snapshot?.stats) {
              const filter = mobileSnapshotFilters[vertical]
              const numerator = snapshot.stats
                .filter(
                  (stat) =>
                    'count' in stat &&
                    Object.keys(filter).every((k) =>
                      Array.isArray(filter[k]) ? filter[k].includes(stat[k]) : stat[k] === filter[k],
                    ),
                )
                .reduce((sum, { count }) => sum + (count ?? 0), 0)

              const packageRows = [
                'New Listings Package Bronze Professional',
                'New Listings Package Silver Professional',
                'New Listings Package Gold Professional',
                'New Listings Package Platinum Professional',
                'New Listings Package Partner Professional',
                'New Listings Package FSBO Professional',
                'New Listings Package Bronze Private',
                'New Listings Package Silver Private',
                'New Listings Package Gold Private',
                'New Listings Package Platinum Private',
                'New Listings Package Partner Private',
                'New Listings Package FSBO Private',
              ]
              let csvValue: number | string
              if (packageRows.includes(vertical)) {
                const denomKey = vertical.endsWith('Professional')
                  ? 'Total New Listings With Package Professional'
                  : 'Total New Listings With Package Private'
                const denomFilter = mobileSnapshotFilters[denomKey]
                const denominator = snapshot.stats
                  .filter(
                    (stat) =>
                      'count' in stat &&
                      Object.keys(denomFilter).every((k) =>
                        Array.isArray(denomFilter[k]) ? denomFilter[k].includes(stat[k]) : stat[k] === denomFilter[k],
                      ),
                  )
                  .reduce((sum, { count }) => sum + (count ?? 0), 0)
                csvValue = denominator > 0 ? `${((numerator / denominator) * 100).toFixed(2)} %` : 'N/A'
              } else {
                csvValue = numerator
              }
              csvData[key][snapTime] = csvValue
            } else {
              csvData[key][snapTime] = ''
            }
          }
        }
      }
      const csv = json2csv(Object.values(csvData), { emptyFieldValue: '' })
      const gDrive = await GDrive(account)

      await gDrive.upsert(
        csv,
        `${preFileName}${format(new Date(), 'II/RRRR')}.csv`,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.adevintaExports,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.adevintaExports],
        [preFileName],
      )
    },
    config: {
      concurrency: 1,
    },
  },
  bcgWeeklyCsv: {
    cron: '30 8 * * SUN',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const gDrive = await GDrive(account)
      await generateBcgWeeklyCSV(runtime, gDrive, account)
    },
    config: {
      concurrency: 1,
    },
  },
  bcgMonthlyCsv: {
    cron: '32 8 * * SUN',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const gDrive = await GDrive(account)
      await generateMonthlyListingsCSV(runtime, gDrive, account)
    },
    config: {
      concurrency: 1,
    },
  },
  datalakeWeeklyCsv: {
    cron: '30 9 * * SUN',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      await generateDatalakeWeeklyCSV(runtime, account)
    },
    config: {
      concurrency: 1,
    },
  },
  karnovMonthly: {
    //cron: '0 12 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) =>
      generateKarnovMonthlyCSV(runtime, account),
    config: {
      concurrency: 1,
    },
  },

  // ./dg worker non_standard csvExports finnRealestatesRentPackagesCsv -p (run when requested, add new cookie if necessary by logging in)
  finnRealestatesRentPackagesCsv: {
    region: Region.eu_north_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const gDrive = await GDrive(account)
      await generateRERentPackagesCSV(runtime, gDrive, account)
    },
    config: {
      concurrency: 1,
    },
  },
  smgProvidersMonthly: {
    cron: '0 8 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [IntegrationType.similarWeb, IntegrationType.apptopia, IntegrationType.sensortower]
      const isoList = ['CH'] as Iso3166Alpha2[]
      const providerList = smgProvidersIntegrationSnapshot
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 1), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'SMG Providers '
      const folder = accountConfigs[account].folders.smg

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        excludeIntegrationDataTypes: [DataType.similarWebPageviews],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },
  bcgProvidersMonthly: {
    cron: '0 8 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [IntegrationType.similarWeb, IntegrationType.apptopia, IntegrationType.sensortower]
      const isoList = ['LV', 'EE', 'LT'] as Iso3166Alpha2[]
      const providerList = bcgProvidersIntegrationSnapshot
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 1), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'BCG Providers '
      const folder = accountConfigs[account].folders.bcgExports

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        excludeIntegrationDataTypes: [DataType.similarWebPageviews],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },

  accountingBookkeepingMonthly: {
    cron: '0 8 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [IntegrationType.similarWeb, IntegrationType.apptopia, IntegrationType.sensortower]
      const isoList = [Iso3166Alpha2.AU, Iso3166Alpha2.NZ, Iso3166Alpha2.GB, Iso3166Alpha2.US]
      const providerList = defaultConfigs.accounting.additionalFilters
        .find((item) => item.key === 'verticalType')
        .entries.find((item) => item.value === 'bookkeeping').providers
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'Accounting Bookkeeping '
      const folder = accountConfigs[account].folders.accountingBookkeeping

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        dataTypes: [DataType.app, DataType.partner],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },
  xeroMonthly: {
    cron: '0 9 9 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config: ControllerConfig, runtime: ControllerRuntime) => {
      const gDrive = await GDrive(account)
      const integrations = [
        IntegrationType.similarWeb,
        IntegrationType.apptopia,
        IntegrationType.dataAi,
        IntegrationType.sensortower,
      ]
      const isoList = [Iso3166Alpha2.AU, Iso3166Alpha2.NZ, Iso3166Alpha2.GB, Iso3166Alpha2.US]
      const providerList = ['xero']
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const snapTimeUnit = TimeUnit.months
      const filePrefix = 'Xero Monthly '
      const folder = accountConfigs[account].folders.xero

      const options: IntegrationSnapshotCsvOptions = {
        integrations,
        isoList,
        providerList,
        startSnapTime,
        snapTimeUnit,
        dataTypes: [DataType.app, DataType.partner],
      }

      await generateIntegrationSnapshotCSV(runtime, gDrive, account, options, filePrefix, folder)
    },
    config: {
      concurrency: 1,
    },
  },

  // ----------------- Sensortower ------------------------------
  Fortnox90DaysSensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => sensortower90Days(runtime, 'fortnox'),
    config: {
      concurrency: 1,
    },
  },
  Freee90DaysSensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => sensortower90Days(runtime, 'freee'),
    config: {
      concurrency: 1,
    },
  },
  BillCom90DaysSensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => sensortower90Days(runtime, 'bill.com'),
    config: {
      concurrency: 1,
    },
  },
  datingAppsMonthlySensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'germanyVoDMonthlySensortower',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.sensortower]
      for (const dataTypeInfo of dataTypes) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.sensortower,
                project: ProjectType.marketplaces,
                provider: { $in: datingProviders },
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                snapTime: 1,
              },
            },
          ])
          .toArray()
        if (!results?.length) continue

        const resultsFormatted = results.map((item) => {
          const country = 'Worldwide'
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.WW[item.provider] ? providerNameMapping.WW[item.provider] : item.provider
          return {
            country,
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const providers = [...new Set(resultsFormatted.map((item) => item.provider))]
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of providers) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              let order = 0
              switch (curr.provider) {
                case 'MeetMe':
                  order = 1
                  break
                case 'LOVOO':
                  order = 2
                  break
                case 'Skout':
                  order = 3
                  break
                case 'Tagged':
                  order = 4
                  break
                case 'GROWLr':
                  order = 5
                  break
                case 'hi5':
                  order = 6
                  break
                case 'eHarmony':
                  order = 7
                  break
                case 'Parship':
                  order = 8
                  break
                case 'ElitePartner':
                  order = 9
                  break
                default:
                  order = 0
              }
              const el = {
                Company: curr.provider,
                order,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.order - n2.order)
          .flatMap((item) => {
            delete item.order
            switch (item.Company) {
              case 'MeetMe':
                return [{ Company: 'Meet Group' }, item]
              case 'eHarmony':
                return [{ Company: 'eHarmony' }, item]
              case 'Parship':
                return [{ Company: 'Parship' }, item]

              default:
                return item
            }
          })
        const filename = `Dating Apps Sensortower ${dataTypeInfo.label} ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.datingSensortower,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.datingSensortower],
          [`Dating Apps Sensortower ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  germanyVoDMonthlySensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'paymentsMonthlySensortower',
      }
    },
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.sensortower]
      for (const dataTypeInfo of dataTypes) {
        const results = await runtime
          .collection<IntegrationSnapshot>('integrationSnapshot')
          .aggregate([
            {
              $match: {
                integration: IntegrationType.sensortower,
                project: ProjectType.vod,
                iso: Iso3166Alpha2.DE,
                dataType: dataTypeInfo.value,
                snapTimeUnit: TimeUnit.months,
                snapTime: { $gte: startSnapTime },
              },
            },
            {
              $unwind: '$stats',
            },
            {
              $group: {
                _id: {
                  iso: '$iso',
                  provider: '$provider',
                  snapTime: '$snapTime',
                },
                count: { $sum: '$stats.count' },
              },
            },
            {
              $project: {
                _id: 0,
                iso: '$_id.iso',
                provider: '$_id.provider',
                snapTime: '$_id.snapTime',
                count: '$count',
              },
            },
            {
              $sort: {
                provider: 1,
                snapTime: 1,
              },
            },
          ])
          .toArray()

        if (!results?.length) continue
        const resultsFormatted = results.map((item) => {
          const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')
          const provider = providerNameMapping.DE[item.provider] ? providerNameMapping.DE[item.provider] : item.provider
          return {
            provider,
            month: formattedSnapTime,
            count: item.count,
          }
        })
        const firstMonth = parse(resultsFormatted[0]?.month, 'MMM/yy', new Date()) //
        const lastMonth = parse(resultsFormatted[resultsFormatted.length - 1]?.month, 'MMM/yy', new Date())
        const deltaTime: string[] = []
        for (let dateToAdd = firstMonth; !isAfter(dateToAdd, lastMonth); dateToAdd = addMonths(dateToAdd, 1)) {
          deltaTime.push(format(dateToAdd, 'MMM/yy'))
        }
        const output: Array<{ provider: string; month: string; count: number }> = []
        for (const month of deltaTime) {
          for (const provider of ['tvnow.de', 'Joyn']) {
            const filtered = resultsFormatted.filter((item) => item.provider === provider && item.month === month)
            if (!filtered?.length) {
              output.push({ provider, month, count: 0 })
            } else {
              output.push({ provider, month, count: filtered[0].count })
            }
          }
        }
        const resultsParsed = output
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc: any[], curr) => {
            if (acc.find((el: { Company: string }) => el.Company === curr.provider)) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return acc.map((el: any) => {
                if (el.Company === curr.provider) {
                  el[curr.month] = curr.count
                }
                return el
              })
            } else {
              const el = {
                Company: curr.provider,
              }
              el[curr.month] = curr.count
              return [...acc, el]
            }
          }, [])
          .sort((n1, n2) => n1.Company.localeCompare(n2.Company))
          .map((item) => {
            delete item.order
            return item
          })
        const filename = `Germany VoD Sensortower ${dataTypeInfo.label} ${getOutputMonth(format(lastMonth, 'MMM/yy'))}.csv`
        const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })

        const gDrive = await GDrive(account)
        await gDrive.upsert(
          csv,
          filename,
          accountConfigs[account].defaultFileType,
          accountConfigs[account].folders.vodSensortower,
        )

        await moveModifiedBefore(
          gDrive,
          subDays(new Date(), 1),
          accountConfigs[account].folders.archive,
          [accountConfigs[account].folders.vodSensortower],
          [`Germany VoD Sensortower ${dataTypeInfo.label}`],
        )
      }
    },
    config: {
      concurrency: 1,
    },
  },
  paymentsMonthlySensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const providers = ['revolut', 'wise']
      const dataTypes = availableIntegrationDataTypes[IntegrationType.sensortower]

      for (const provider of providers) {
        for (const dataTypeInfo of dataTypes) {
          const results = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.sensortower,
                  project: ProjectType.payments,
                  provider,
                  dataType: dataTypeInfo.value,
                  snapTimeUnit: TimeUnit.months,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: '$stats',
              },
              {
                $group: {
                  _id: {
                    iso: '$iso',
                    snapTime: '$snapTime',
                  },
                  count: { $sum: '$stats.count' },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  snapTime: '$_id.snapTime',
                  count: '$count',
                },
              },
              {
                $sort: {
                  snapTime: 1,
                },
              },
            ])
            .toArray()

          if (!results?.length) continue
          const resultsFormatted = results.map((item) => {
            const country =
              item.iso === Iso3166Alpha2.WW
                ? 'Worldwide'
                : item.iso === Iso3166Alpha2.EU
                  ? 'European Union'
                  : item.iso === Iso3166Alpha2.WW
                    ? 'Worldwide'
                    : isoCountries.getName(item.iso, 'en') === 'Türkiye'
                      ? 'Turkey'
                      : isoCountries.getName(item.iso, 'en')
            const formattedSnapTime = format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM/yy')

            return {
              country,
              month: formattedSnapTime,
              count: item.count,
            }
          })
          const lastMonth = resultsFormatted[resultsFormatted.length - 1]?.month
          const resultsParsed = resultsFormatted
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .reduce((acc: any[], curr) => {
              if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                return acc.map((el: any) => {
                  if (el.Country === curr.country) {
                    el[curr.month] = curr.count
                  }
                  return el
                })
              } else {
                const el = {
                  Country: curr.country,
                }
                el[curr.month] = curr.count
                return [...acc, el]
              }
            }, [])
            .sort((n1, n2) => {
              return n2[lastMonth] - n1[lastMonth]
            })
          const prefix = `${camelCase(provider)} Sensortower ${dataTypeInfo.label}`
          const parentFolderId =
            provider === 'revolut'
              ? accountConfigs[account].folders.revolutSensortower
              : accountConfigs[account].folders.wiseSensortower
          const filename = `${prefix} ${getOutputMonth(lastMonth)}.csv`
          const csv = json2csv(resultsParsed, { emptyFieldValue: 0 })
          const gDrive = await GDrive(account)
          await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)

          await moveModifiedBefore(
            gDrive,
            subDays(new Date(), 1),
            accountConfigs[account].folders.archive,
            [parentFolderId],
            [prefix],
          )
        }
      }
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'FRBRDEProvidersMonthlySensortower',
    }),
    config: {
      concurrency: 1,
    },
  },
  FRBRDEProvidersMonthlySensortower: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const results = []
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.sensortower]
      for (const dataTypeInfo of dataTypes) {
        for (const countryData of FRBRDEProviders) {
          const someResults = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.sensortower,
                  project: countryData.project,
                  iso: countryData.iso,
                  provider: { $in: countryData.providers },
                  snapTimeUnit: TimeUnit.months,
                  dataType: dataTypeInfo.value,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: {
                  path: '$stats',
                },
              },
              {
                $group: {
                  _id: {
                    provider: '$provider',
                    snapTime: '$snapTime',
                    iso: '$iso',
                    dataType: '$dataType',
                  },
                  count: {
                    $sum: '$stats.count',
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  provider: '$_id.provider',
                  providerIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.provider', 'leboncoin'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'olx'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'enjoei'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'ebk'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'mobile.de'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'vinted'],
                          },
                          then: 3,
                        },
                      ],
                      default: 0,
                    },
                  },
                  snapTime: '$_id.snapTime',
                  dataType: '$_id.dataType',
                  count: '$count',
                  countryIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.FR],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.BR],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.DE],
                          },
                          then: 3,
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              {
                $sort: {
                  snapTime: 1,
                  countryIndex: 1,
                  providerIndex: 1,
                },
              },
            ])
            .toArray()
          if (!someResults?.length) continue
          results.push(...someResults)
        }
      }

      const resultsFormatted = results.flatMap((item) => {
        return {
          provider: item.provider,
          month: format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy'),
          count: item.count,
          country: isoCountries.getName(item.iso, 'en'),
          dataTypeLabel: dataTypes.find((dataTypeInfo) => dataTypeInfo.value === item.dataType)?.label,
          countryIndex: item.countryIndex,
          providerIndex: item.providerIndex,
        }
      })

      const resultsParsed = []
      const countries = [
        ...new Set(
          resultsFormatted
            .sort((a, b) => {
              return a.countryIndex - b.countryIndex
            })
            .map((item) => item.country),
        ),
      ]
      const months = [...new Set(resultsFormatted.map((item) => item.month))]
      const sensortowerDataTypeLabels = [...new Set(resultsFormatted.map((item) => item.dataTypeLabel))]

      for (const country of countries) {
        const countryObj = { '': country, ' ': '' }

        for (const month of months) {
          countryObj[month] = ''
        }

        resultsParsed.push(countryObj)

        for (const dataTypeLabel of sensortowerDataTypeLabels) {
          resultsParsed.push({
            '': `Apps - ${dataTypeLabel}`,
            ' ': '',
            ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
          })

          const providers = Array.from(
            new Set(
              resultsFormatted
                .filter((item) => item.country === country && item.dataTypeLabel === dataTypeLabel)
                .sort((a, b) => {
                  return a.providerIndex - b.providerIndex
                })
                .map((item) => item.provider),
            ),
          )

          for (const provider of providers) {
            const providerObj = {
              '': '',
              ' ': provider,
              ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
            }

            for (const month of months) {
              const item = resultsFormatted.find(
                (result) =>
                  result.country === country &&
                  result.dataTypeLabel === dataTypeLabel &&
                  result.provider === provider &&
                  result.month === month,
              )
              providerObj[month] = item ? item.count : ''
            }

            resultsParsed.push(providerObj)
          }
        }
      }

      const filename = `Sensortower FR_BR_DE Providers ${getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.frbrdeSensortower,
      )
      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.frbrdeSensortower],
        [/Sensortower FR_BR_DE Providers/g],
      )
    },
    scheduleAfterFinished: () => ({
      project: ProjectType.non_standard,
      provider: 'csvExports',
      type: 'nordicProvidersMonthlySensortower',
    }),
    config: {
      concurrency: 1,
    },
  },
  nordicProvidersMonthlySensortower: {
    // cron: '0 8 10 * *',
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const results = []
      const startSnapTime = format(subMonths(subYears(startOfMonth(new Date()), 3), 2), 'yyyy/MM')
      const dataTypes = availableIntegrationDataTypes[IntegrationType.sensortower]
      for (const dataTypeInfo of dataTypes) {
        for (const countryData of nordicProviders) {
          const someResults = await runtime
            .collection<IntegrationSnapshot>('integrationSnapshot')
            .aggregate([
              {
                $match: {
                  integration: IntegrationType.sensortower,
                  project: countryData.project,
                  iso: countryData.iso,
                  provider: { $in: countryData.providers },
                  snapTimeUnit: TimeUnit.months,
                  dataType: dataTypeInfo.value,
                  snapTime: { $gte: startSnapTime },
                },
              },
              {
                $unwind: {
                  path: '$stats',
                },
              },
              {
                $group: {
                  _id: {
                    provider: '$provider',
                    snapTime: '$snapTime',
                    iso: '$iso',
                    dataType: '$dataType',
                  },
                  count: {
                    $sum: '$stats.count',
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  iso: '$_id.iso',
                  provider: '$_id.provider',
                  providerIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.provider', 'finn.no'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'tise'],
                          },
                          then: 3,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'blocket'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'vinted'],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'plick'],
                          },
                          then: 4,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'dba'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'boligsiden'],
                          },
                          then: 4,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'boliga'],
                          },
                          then: 5,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'trendsales'],
                          },
                          then: 6,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'tori'],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'temu'],
                          },
                          then: 10,
                        },
                        {
                          case: {
                            $eq: ['$_id.provider', 'hjem'],
                          },
                          then: 11,
                        },
                      ],
                      default: 0,
                    },
                  },
                  snapTime: '$_id.snapTime',
                  dataType: '$_id.dataType',
                  count: '$count',
                  countryIndex: {
                    $switch: {
                      branches: [
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.NO],
                          },
                          then: 1,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.SE],
                          },
                          then: 2,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.DK],
                          },
                          then: 3,
                        },
                        {
                          case: {
                            $eq: ['$_id.iso', Iso3166Alpha2.FI],
                          },
                          then: 4,
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              {
                $sort: {
                  snapTime: 1,
                  countryIndex: 1,
                  providerIndex: 1,
                },
              },
            ])
            .toArray()
          if (!someResults?.length) continue
          results.push(...someResults)
        }
      }

      const resultsFormatted = results.flatMap((item) => {
        return {
          provider: item.provider,
          month: format(parse(item.snapTime, 'yyyy/MM', new Date()), 'MMM-yy'),
          count: item.count,
          country: isoCountries.getName(item.iso, 'en'),
          dataTypeLabel: dataTypes.find((dataTypeInfo) => dataTypeInfo.value === item.dataType)?.label,
          countryIndex: item.countryIndex,
          providerIndex: item.providerIndex,
        }
      })

      const resultsParsed = []
      const countries = [
        ...new Set(
          resultsFormatted
            .sort((a, b) => {
              return a.countryIndex - b.countryIndex
            })
            .map((item) => item.country),
        ),
      ]
      const months = [...new Set(resultsFormatted.map((item) => item.month))]
      const sensortowerDataTypeLabels = [...new Set(resultsFormatted.map((item) => item.dataTypeLabel))]

      for (const country of countries) {
        const countryObj = { '': country, ' ': '' }

        for (const month of months) {
          countryObj[month] = ''
        }

        resultsParsed.push(countryObj)

        for (const dataTypeLabel of sensortowerDataTypeLabels) {
          resultsParsed.push({
            '': `Apps - ${dataTypeLabel}`,
            ' ': '',
            ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
          })

          const providers = Array.from(
            new Set(
              resultsFormatted
                .filter((item) => item.country === country && item.dataTypeLabel === dataTypeLabel)
                .sort((a, b) => {
                  return a.providerIndex - b.providerIndex
                })
                .map((item) => item.provider),
            ),
          )

          for (const provider of providers) {
            const providerObj = {
              '': '',
              ' ': provider,
              ...months.reduce((obj, month) => ({ ...obj, [month]: '' }), {}),
            }

            for (const month of months) {
              const item = resultsFormatted.find(
                (result) =>
                  result.country === country &&
                  result.dataTypeLabel === dataTypeLabel &&
                  result.provider === provider &&
                  result.month === month,
              )
              providerObj[month] = item ? item.count : ''
            }

            resultsParsed.push(providerObj)
          }
        }
      }

      const filename = `Sensortower Nordic Providers ${getOutputMonth(format(subMonths(Date.now(), 1), 'MMM/yy'))}.csv`
      const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
      const gDrive = await GDrive(account)
      await gDrive.upsert(
        csv,
        filename,
        accountConfigs[account].defaultFileType,
        accountConfigs[account].folders.nordicSensortower,
      )

      await moveModifiedBefore(
        gDrive,
        subDays(new Date(), 1),
        accountConfigs[account].folders.archive,
        [accountConfigs[account].folders.nordicSensortower],
        [/Sensortower Nordic Providers/g],
      )
    },
    config: {
      concurrency: 1,
    },
  },

  // ----------------- Hemnet Booli Queries ------------------------------
  hemnetBooliAggregations: {
    region: Region.eu_west_1,
    function: () => Promise.resolve([]),
    afterControllerFinished: async (_config, runtime) => {
      const gDrive = await GDrive(account)
      await generateHemnetBooliQueriesCSV(runtime, gDrive, account, {
        updatedAtDate: format(startOfISOWeek(new Date()), 'yyyy-MM-dd'),
      })
      await archiveOldHemnetBooliQueriesCSVs(gDrive, account)
    },
    config: {
      concurrency: 1,
    },
  },

  // hemnetBooliQueriesWeekly: {
  //   region: Region.eu_west_1,
  //   function: () => Promise.resolve([]),
  //   afterControllerFinished: async (_config, runtime) => {
  //     const gDrive = await GDrive(account)
  //     await generateHemnetBooliQueriesCSV(runtime, gDrive, account, {
  //       updatedAtDate: format(subWeeks(new Date(), 1), 'yyyy-MM-dd'),
  //     })
  //   },
  //   config: {
  //     concurrency: 1,
  //   },
  // },

  // hemnetBooliQueriesMonthly: {
  //   region: Region.eu_west_1,
  //   function: () => Promise.resolve([]),
  //   afterControllerFinished: async (_config, runtime) => {
  //     const gDrive = await GDrive(account)
  //     await generateHemnetBooliQueriesCSV(runtime, gDrive, account, {
  //       updatedAtDate: format(subMonths(new Date(), 1), 'yyyy-MM-dd'),
  //     })
  //   },
  //   config: {
  //     concurrency: 1,
  //   },
  // },
}

export default provider

async function sendQueriedData(
  s3Cli: S3,
  bucketParams: { Bucket: string },
  Key: string,
  gDrive: GDriveUtil,
  country: string,
  provider?: string,
) {
  const csvHeader =
    'provider,customer_gender,customer_country,customer_region,customer_zipcode,customer_age_range,merchant_name,id_order,order_date,order_time,order_total_paid,order_currency,payment_method_name,order_delivery_fee,order_processing_fee,order_other_fee,order_total_fees,order_total_promo,total_order_item_quantity,order_distinct_item_quantity,id_order_item,product_name,product_description,brand_name,seller_name,category_0_authorized,category_1_authorized,category_2_authorized,category_3_authorized,order_item_total_price_paid,order_item_quantity,order_item_is_marketplace,delivery_address_country,delivery_address_region,delivery_address_sub_region,delivery_address_zipcode,delivery_address_city,order_item_total_price_paid_with_contributions,is_loyalty_amazon,is_loyalty_cdiscount,order_item_total_price_paid_with_contributions_ponderated_v2,order_item_weight_ponderated_v2,order_item_quantity_ponderated,order_item_total_price_paid_with_contributions_coef_v2,order_is_deleted,order_item_delivery_type,order_item_delivery_date,product_size,product_is_bio,is_second_hand,customer_created_at,order_created_at\n'

  let result = ''
  let stop = false
  let Expression = `SELECT * FROM S3Object WHERE delivery_address_country = '${country}'`
  let filePrefix = country
  if (provider?.length) {
    Expression += ` AND merchant_name = '${provider}'`
    filePrefix += `_${provider}`
  }
  while (!stop) {
    const data = await s3Cli.selectObjectContent({
      ...bucketParams,
      Key,
      Expression,
      ExpressionType: 'SQL',
      InputSerialization: {
        CompressionType: 'NONE',
        CSV: {
          FileHeaderInfo: 'USE',
          RecordDelimiter: '\n',
          FieldDelimiter: ',',
          QuoteCharacter: '"',
          QuoteEscapeCharacter: '"',
        },
      },
      OutputSerialization: {
        CSV: {
          QuoteFields: 'ASNEEDED',
          QuoteCharacter: '"',
          QuoteEscapeCharacter: '"',
          RecordDelimiter: '\n',
        },
      },
      RequestProgress: {
        Enabled: true,
      },
      // ScanRange: {
      //   Start: start_range,
      //   End: fileSize,
      // },
    })
    if (!data?.Payload) break
    for await (const event of data.Payload) {
      if (event['Records']) {
        try {
          const merchant = event?.['Records']?.['Payload']?.toString()
          if (merchant && !result.includes(merchant)) result += merchant
        } catch (err) {
          console.error(err)
          throw err
        }
      } else if (event['End']) {
        stop = true
      }
    }
  }
  await gDrive.upsert(
    `${csvHeader}${result}`,
    `${filePrefix}_${Key}`,
    accountConfigs[account].defaultFileType,
    accountConfigs[account].folders.foxIntel,
  )

  await moveModifiedBefore(gDrive, subDays(new Date(), 1), accountConfigs[account].folders.archive, [
    accountConfigs[account].folders.foxIntel,
  ])
}

async function appAnnie90Days(runtime: ControllerRuntime, provider: string) {
  const parentFolderId = accountConfigs[account].folders[provider]
  if (!parentFolderId) return
  const startSnapTime = format(subDays(new Date(), 91), 'yyyy/DDD')
  for (const dataTypeInfo of availableIntegrationDataTypes[IntegrationType.dataAi]) {
    const results = await runtime
      .collection<IntegrationSnapshot>('integrationSnapshot')
      .aggregate([
        {
          $match: {
            integration: IntegrationType.dataAi,
            project: ProjectType.accounting,
            provider,
            dataType: dataTypeInfo.value,
            snapTimeUnit: 'days',
            snapTime: { $gte: startSnapTime },
          },
        },
        {
          $unwind: '$stats',
        },
        {
          $group: {
            _id: {
              iso: '$iso',
              snapTime: '$snapTime',
            },
            count: { $sum: '$stats.count' },
          },
        },
        {
          $project: {
            _id: 0,
            iso: '$_id.iso',
            snapTime: '$_id.snapTime',
            count: '$count',
          },
        },
        {
          $sort: {
            snapTime: 1,
          },
        },
      ])
      .toArray()
    if (!results?.length) continue

    const resultsFormatted = results.map((item) => {
      const country =
        item.iso === Iso3166Alpha2.WW
          ? 'Worldwide'
          : item.iso === Iso3166Alpha2.EU
            ? 'European Union'
            : isoCountries.getName(item.iso, 'en')
      const formattedSnapTime = getOutputDay(item.snapTime)
      return {
        country,
        day: formattedSnapTime,
        count: item.count,
      }
    })
    const lastDay = resultsFormatted[resultsFormatted.length - 1]?.day
    const resultsParsed = resultsFormatted
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .reduce((acc: any[], curr) => {
        if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          return acc.map((el: any) => {
            if (el.Country === curr.country) {
              el[curr.day] = curr.count
            }
            return el
          })
        } else {
          const el = {
            Country: curr.country,
          }
          el[curr.day] = curr.count
          return [...acc, el]
        }
      }, [])
      .sort((n1, n2) => {
        return n2[lastDay] - n1[lastDay]
      })

    const filenameBase = `${provider.replace(/\w\S*/g, (w) =>
      w.replace(/^\w/, (c) => c.toUpperCase()),
    )} AppAnnie 90 days`

    const filename = `${filenameBase} ${dataTypeInfo.label} ${lastDay}.csv`
    const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
    const gDrive = await GDrive(account)
    await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)
    await moveModifiedBefore(
      gDrive,
      subDays(new Date(), 1),
      accountConfigs[account].folders.archive,
      [parentFolderId],
      [`${filenameBase} ${dataTypeInfo.label}`],
    )
  }
}

async function apptopia90Days(runtime: ControllerRuntime, provider: string) {
  const parentFolderId = accountConfigs[account].folders[provider]
  if (!parentFolderId) return
  const startSnapTime = format(subDays(new Date(), 91), 'yyyy/DDD')
  for (const dataType of availableIntegrationDataTypes[IntegrationType.apptopia].map((item) => item.value)) {
    const results = await runtime
      .collection<IntegrationSnapshot>('integrationSnapshot')
      .aggregate<{ iso: Iso3166Alpha2; snapTime: string; count: number }>([
        {
          $match: {
            integration: IntegrationType.apptopia,
            project: ProjectType.accounting,
            provider,
            dataType,
            snapTimeUnit: 'days',
            snapTime: { $gte: startSnapTime },
          },
        },
        {
          $unwind: '$stats',
        },
        {
          $group: {
            _id: {
              iso: '$iso',
              snapTime: '$snapTime',
            },
            count: { $sum: '$stats.count' },
          },
        },
        {
          $project: {
            _id: 0,
            iso: '$_id.iso',
            snapTime: '$_id.snapTime',
            count: '$count',
          },
        },
        {
          $sort: {
            snapTime: 1,
          },
        },
      ])
      .toArray()
    if (!results?.length) continue

    const resultsFormatted = results.flatMap((item) => {
      const country =
        item.iso === Iso3166Alpha2.WW
          ? 'Worldwide'
          : item.iso === Iso3166Alpha2.EU
            ? 'European Union'
            : isoCountries.getName(item.iso, 'en')
      const formattedSnapTime = getOutputDay(item.snapTime)
      return {
        country,
        day: formattedSnapTime,
        count: item.count,
      }
    })

    const lastDay = resultsFormatted[resultsFormatted.length - 1]?.day
    const resultsParsed = resultsFormatted
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .reduce((acc: any[], curr) => {
        if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          return acc.map((el: any) => {
            if (el.Country === curr.country) {
              el[curr.day] = curr.count
            }
            return el
          })
        } else {
          const el = {
            Country: curr.country,
          }
          el[curr.day] = curr.count
          return [...acc, el]
        }
      }, [])
      .sort((n1, n2) => {
        return n2[lastDay] - n1[lastDay]
      })

    const filenameBase = `${provider.replace(/\w\S*/g, (w) =>
      w.replace(/^\w/, (c) => c.toUpperCase()),
    )} Apptopia 90 days ${
      dataType === 'apptopiaDownloads' ? 'Downloads' : dataType === 'apptopiaActiveUsers' ? 'Active Users' : dataType
    }`

    // let a
    // switch (dataType) {

    // }

    const filename = `${filenameBase} ${lastDay}.csv`
    const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
    const gDrive = await GDrive(account)
    await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)
    await moveModifiedBefore(
      gDrive,
      subDays(new Date(), 1),
      accountConfigs[account].folders.archive,
      [parentFolderId],
      [filenameBase],
    )
  }
}

async function sensortower90Days(runtime: ControllerRuntime, provider: string) {
  const parentFolderId = accountConfigs[account].folders[provider]
  if (!parentFolderId) return
  const startSnapTime = format(subDays(new Date(), 91), 'yyyy/DDD')
  for (const dataTypeInfo of availableIntegrationDataTypes[IntegrationType.sensortower]) {
    const results = await runtime
      .collection<IntegrationSnapshot>('integrationSnapshot')
      .aggregate([
        {
          $match: {
            integration: IntegrationType.sensortower,
            project: ProjectType.accounting,
            provider,
            dataType: dataTypeInfo.value,
            snapTimeUnit: 'days',
            snapTime: { $gte: startSnapTime },
          },
        },
        {
          $unwind: '$stats',
        },
        {
          $group: {
            _id: {
              iso: '$iso',
              snapTime: '$snapTime',
            },
            count: { $sum: '$stats.count' },
          },
        },
        {
          $project: {
            _id: 0,
            iso: '$_id.iso',
            snapTime: '$_id.snapTime',
            count: '$count',
          },
        },
        {
          $sort: {
            snapTime: 1,
          },
        },
      ])
      .toArray()
    if (!results?.length) continue

    const resultsFormatted = results.map((item) => {
      const country =
        item.iso === Iso3166Alpha2.WW
          ? 'Worldwide'
          : item.iso === Iso3166Alpha2.EU
            ? 'European Union'
            : isoCountries.getName(item.iso, 'en')
      const formattedSnapTime = getOutputDay(item.snapTime)
      return {
        country,
        day: formattedSnapTime,
        count: item.count,
      }
    })
    const lastDay = resultsFormatted[resultsFormatted.length - 1]?.day
    const resultsParsed = resultsFormatted
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .reduce((acc: any[], curr) => {
        if (acc.find((el: { Country: string }) => el.Country === curr.country)) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          return acc.map((el: any) => {
            if (el.Country === curr.country) {
              el[curr.day] = curr.count
            }
            return el
          })
        } else {
          const el = {
            Country: curr.country,
          }
          el[curr.day] = curr.count
          return [...acc, el]
        }
      }, [])
      .sort((n1, n2) => {
        return n2[lastDay] - n1[lastDay]
      })

    const filenameBase = `${provider.replace(/\w\S*/g, (w) =>
      w.replace(/^\w/, (c) => c.toUpperCase()),
    )} Sensortower 90 days`

    const filename = `${filenameBase} ${dataTypeInfo.label} ${lastDay}.csv`
    const csv = json2csv(resultsParsed, { emptyFieldValue: '' })
    const gDrive = await GDrive(account)
    await gDrive.upsert(csv, filename, accountConfigs[account].defaultFileType, parentFolderId)
    await moveModifiedBefore(
      gDrive,
      subDays(new Date(), 1),
      accountConfigs[account].folders.archive,
      [parentFolderId],
      [`${filenameBase} ${dataTypeInfo.label}`],
    )
  }
}
