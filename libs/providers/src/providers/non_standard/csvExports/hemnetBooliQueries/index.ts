import { subDays } from 'date-fns'
import { json2csv } from 'json-2-csv'

import { settings, totalQueries, thirtyToSixtyDaysQueries, commonPipelineStages } from './constants'
import { moveModifiedBefore } from '../../../../lib/tools/csvExportsArchiving'

import type { ControllerRuntime } from '@datagatherers/datagatherers'
import type { GDriveUtil } from '@datagatherers/worker-utils'

interface QueryOptions {
  updatedAtDate?: string
}

interface QueryResult {
  section: string
  subsection: string
  category: string
  provider: string
  listingCount: number
  totalViews: number
}

/**
 * Generate CSV files for all Hemnet Booli query types
 */
export async function generateHemnetBooliQueriesCSV(
  runtime: ControllerRuntime,
  gDrive: GDriveUtil,
  account: string,
  options: QueryOptions = {},
) {
  const config = settings(account)
  const updatedAtDate = options.updatedAtDate || config.updatedAtDate

  // Process Total section queries
  for (const [queryKey, queryConfig] of Object.entries(totalQueries)) {
    await generateSingleQueryCSV(runtime, gDrive, account, queryKey, queryConfig, updatedAtDate)
  }

  // Process 30-60 days section queries
  for (const [queryKey, queryConfig] of Object.entries(thirtyToSixtyDaysQueries)) {
    await generateSingleQueryCSV(runtime, gDrive, account, queryKey, queryConfig, updatedAtDate)
  }
}

/**
 * Generate CSV for a single query type
 */
async function generateSingleQueryCSV(
  runtime: ControllerRuntime,
  gDrive: GDriveUtil,
  account: string,
  queryKey: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  queryConfig: any,
  updatedAtDate: string,
) {
  const config = settings(account)

  try {
    // Build the complete aggregation pipeline
    const pipeline = buildQueryPipeline(queryConfig, updatedAtDate)

    // Execute the aggregation
    const results = await runtime.collection('item').aggregate(pipeline).toArray()

    // Format results for CSV
    const csvData = formatResultsForCSV(results, queryConfig)

    // Generate CSV content
    const csv = json2csv(csvData, { emptyFieldValue: '0' })

    // Create filename
    const filename = `${queryConfig.section}_${queryConfig.name}.csv`

    // Upload to Google Drive
    await gDrive.upsert(csv, filename, config.filePrefix, config.folder)
  } catch (error) {
    console.error(`Error generating CSV for ${queryConfig.name}:`, error)
    throw error
  }
}

/**
 * Build the complete MongoDB aggregation pipeline for a query
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function buildQueryPipeline(queryConfig: any, updatedAtDate: string): any[] {
  // Start with the match stage from the query config
  const pipeline = [...queryConfig.pipeline]

  // Update the updatedAt date in the match stage
  const matchStage = pipeline[0]
  if (matchStage && matchStage.$match) {
    // For Total queries, add updatedAt filter
    // if (queryConfig.section === 'Total') {
    matchStage.$match.updatedAt = { $gte: new Date(updatedAtDate) }
    // }
    // // For 30-60 days queries, the date range is already in the pipeline
    // // but we might want to make it dynamic based on updatedAtDate
    // if (queryConfig.section === '30-60 days') {
    //   // Find the updatedAt condition in the $and array and update it
    //   if (matchStage.$match.$and) {
    //     const dateCondition = matchStage.$match.$and.find((condition: any) => condition.updatedAt)
    //     if (dateCondition) {
    //       const targetDate = new Date(updatedAtDate)
    //       const thirtyDaysAgo = new Date(targetDate)
    //       thirtyDaysAgo.setDate(targetDate.getDate() - 30)

    //       dateCondition.updatedAt = {
    //         $gte: thirtyDaysAgo,
    //         $lt: targetDate,
    //       }
    //     }
    //   }
    // }
  }

  // Add the common pipeline stages
  pipeline.push(...commonPipelineStages)

  return pipeline
}

/**
 * Format aggregation results for CSV output
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function formatResultsForCSV(results: any[], _queryConfig: any): QueryResult[] {
  return results.map((result) => ({
    section: result.section || '',
    subsection: result.subsection || '',
    category: result.category || '',
    provider: result.provider || '',
    listingCount: result.listingCount || 0,
    totalViews: result.totalViews || 0,
    // averageViews: result.listingCount > 0 ? Math.round((result.totalViews / result.listingCount) * 100) / 100 : 0,
  }))
}

/**
 * Archive old Hemnet Booli Queries CSV files
 */
export async function archiveOldHemnetBooliQueriesCSVs(gDrive: GDriveUtil, account: string) {
  const config = settings(account)

  await moveModifiedBefore(gDrive, subDays(new Date(), 1), config.folder, [config.folder], [config.filePrefix])
}
