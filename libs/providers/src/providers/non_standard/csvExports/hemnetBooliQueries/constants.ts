import { format, startOfISOWeek } from 'date-fns'

import { ProjectType } from '@datagatherers/datagatherers'
import { accountConfigs } from '@datagatherers/worker-utils'

export const settings = (account: string) => {
  return {
    projects: [ProjectType.realestates],
    providers: ['booli', 'hemnet'],
    dataType: 'inventory',
    updatedAtDate: format(startOfISOWeek(new Date()), 'yyyy-MM-dd'),
    filePrefix: '',
    folder: accountConfigs[account].folders.hemnet_booli_aggregations,
  }
}

// Total section queries
export const totalQueries = {
  forSale: {
    name: 'ForSale',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $or: [
            { provider: 'booli', 'data.upcomingSale': { $ne: true } },
            { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
          ],
        },
      },
    ],
  },
  forSaleNoPrice: {
    name: 'ForSale_NoPrice',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
  forSaleNoImages: {
    name: 'ForSale_NoImages',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
          ],
        },
      },
    ],
  },
  forSaleNoImagesNoPrice: {
    name: 'ForSale_NoPrice_NoImages',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
  preMarket: {
    name: 'PreMarket',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $or: [
            { provider: 'booli', 'data.upcomingSale': true },
            { provider: 'hemnet', 'data.isUpcoming': true },
          ],
        },
      },
    ],
  },
  preMarketNoPrice: {
    name: 'PreMarket_NoPrice',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
  preMarketNoImages: {
    name: 'PreMarket_NoImages',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
          ],
        },
      },
    ],
  },
  preMarketNoImagesNoPrice: {
    name: 'PreMarket_NoPrice_NoImages',
    section: 'Total',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
}

// 30-60 days section queries
export const thirtyToSixtyDaysQueries = {
  forSale: {
    name: 'ForSale',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
          ],
        },
      },
    ],
  },
  forSaleNoPrice: {
    name: 'ForSale_NoPrice',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
  forSaleNoImages: {
    name: 'ForSale_NoImages',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
          ],
        },
      },
    ],
  },
  forSaleNoImagesNoPrice: {
    name: 'ForSale_NoPrice_NoImages',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': { $ne: true } },
                { provider: 'hemnet', 'data.isUpcoming': { $ne: true } },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
  preMarket: {
    name: 'PreMarket',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
          ],
        },
      },
    ],
  },
  preMarketNoPrice: {
    name: 'PreMarket_NoPrice',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
  preMarketNoImages: {
    name: 'PreMarket_NoImages',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
          ],
        },
      },
    ],
  },
  preMarketNoImagesNoPrice: {
    name: 'PreMarket_NoPrice_NoImages',
    section: '30-60-days',
    pipeline: [
      {
        $match: {
          project: 'realestates',
          provider: { $in: ['booli', 'hemnet'] },
          dataType: 'inventory',
          'data.adViews': { $exists: true, $type: 'number', $gte: 0 },
          'data.daysActive': { $exists: true, $type: 'number', $gte: 0, $lt: 60 },
          $and: [
            {
              $or: [
                { provider: 'booli', 'data.upcomingSale': true },
                { provider: 'hemnet', 'data.isUpcoming': true },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { provider: 'booli' },
                    {
                      $or: [
                        { 'data.images': { $exists: false } },
                        { 'data.images': null },
                        { 'data.images': [] },
                        { 'data.images.0': { $exists: false } },
                      ],
                    },
                  ],
                },
                { provider: 'hemnet' },
              ],
            },
            { $or: [{ price: { $exists: false } }, { price: null }, { price: 0 }] },
          ],
        },
      },
    ],
  },
}

// Common pipeline stages used by all queries
export const commonPipelineStages = [
  {
    $project: {
      provider: 1,
      adViews: '$data.adViews',
      daysActive: '$data.daysActive',
      price: { $ifNull: ['$price', 0] },
      region: {
        $switch: {
          branches: [
            { case: { $eq: ['$state', 'SE-AB'] }, then: 'Stockholm' },
            { case: { $eq: ['$state', 'SE-M'] }, then: 'Skane' },
            { case: { $eq: ['$state', 'SE-O'] }, then: 'Västra Götaland County' },
          ],
          default: 'Rural',
        },
      },
      daysBucket: {
        $switch: {
          branches: [
            { case: { $lt: ['$data.daysActive', 7] }, then: '<7 days' },
            { case: { $lt: ['$data.daysActive', 30] }, then: '7-30 days' },
            { case: { $lt: ['$data.daysActive', 60] }, then: '30-59 days' },
            { case: { $lt: ['$data.daysActive', 182] }, then: '60-182 days' },
            { case: { $lt: ['$data.daysActive', 365] }, then: '183-365 days' },
            { case: { $lt: ['$data.daysActive', 730] }, then: '366-730 days' },
            { case: { $lt: ['$data.daysActive', 999] }, then: '731-999 days' },
          ],
          default: '>999 days',
        },
      },
      viewsBucket: {
        $switch: {
          branches: [
            { case: { $eq: ['$data.adViews', 0] }, then: '0 views' },
            { case: { $lte: ['$data.adViews', 5] }, then: '1-5 views' },
            { case: { $lte: ['$data.adViews', 10] }, then: '6-10 views' },
            { case: { $lte: ['$data.adViews', 20] }, then: '11-20 views' },
            { case: { $lte: ['$data.adViews', 50] }, then: '21-50 views' },
            { case: { $lte: ['$data.adViews', 100] }, then: '51-100 views' },
            { case: { $lte: ['$data.adViews', 200] }, then: '101-200 views' },
            { case: { $lte: ['$data.adViews', 300] }, then: '201-300 views' },
            { case: { $lte: ['$data.adViews', 400] }, then: '301-400 views' },
            { case: { $lte: ['$data.adViews', 1000] }, then: '401-1000 views' },
          ],
          default: '1000+ views',
        },
      },
    },
  },
  {
    $facet: {
      totalByDays: [
        {
          $group: {
            _id: { provider: '$provider', daysBucket: '$daysBucket' },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      totalByViews: [
        {
          $group: {
            _id: { provider: '$provider', viewsBucket: '$viewsBucket' },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      regionalByDays: [
        {
          $group: {
            _id: { provider: '$provider', region: '$region', daysBucket: '$daysBucket' },
            listingCount: { $sum: 1 },
            totalViews: { $sum: '$adViews' },
          },
        },
      ],
      allCombinations: [
        { $limit: 1 },
        {
          $project: {
            providers: ['booli', 'hemnet'],
            daysAll: [
              '<7 days',
              '7-30 days',
              '30-59 days',
              '60-182 days',
              '183-365 days',
              '366-730 days',
              '731-999 days',
              '>999 days',
            ],
            viewsAll: [
              '0 views',
              '1-5 views',
              '6-10 views',
              '11-20 views',
              '21-50 views',
              '51-100 views',
              '101-200 views',
              '201-300 views',
              '301-400 views',
              '401-1000 views',
              '1000+ views',
            ],
            regionsAll: ['Stockholm', 'Skane', 'Västra Götaland County', 'Rural'],
          },
        },
        { $unwind: '$providers' },
        {
          $project: {
            combosDays: {
              $map: {
                input: '$daysAll',
                as: 'd',
                in: {
                  section: 'Total (no time-line)',
                  subsection: 'Days',
                  category: '$$d',
                  provider: '$providers',
                  listingCount: 0,
                  totalViews: 0,
                },
              },
            },
            combosViews: {
              $map: {
                input: '$viewsAll',
                as: 'v',
                in: {
                  section: 'Total (no time-line)',
                  subsection: 'Views',
                  category: '$$v',
                  provider: '$providers',
                  listingCount: 0,
                  totalViews: 0,
                },
              },
            },
            combosRegionalDays: {
              $reduce: {
                input: {
                  $map: {
                    input: '$regionsAll',
                    as: 'r',
                    in: {
                      $map: {
                        input: '$daysAll',
                        as: 'd2',
                        in: {
                          section: 'Regional',
                          subsection: { $concat: ['$$r', ' - Days'] },
                          category: '$$d2',
                          provider: '$providers',
                          listingCount: 0,
                          totalViews: 0,
                        },
                      },
                    },
                  },
                },
                initialValue: [],
                in: { $concatArrays: ['$$value', '$$this'] },
              },
            },
          },
        },
        {
          $project: {
            combinations: { $concatArrays: ['$combosDays', '$combosViews', '$combosRegionalDays'] },
          },
        },
        { $unwind: '$combinations' },
        { $replaceRoot: { newRoot: '$combinations' } },
      ],
    },
  },
  {
    $project: {
      actualResults: {
        $concatArrays: [
          {
            $map: {
              input: '$totalByDays',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Days',
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$totalByViews',
              as: 'item',
              in: {
                section: 'Total (no time-line)',
                subsection: 'Views',
                category: '$$item._id.viewsBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
          {
            $map: {
              input: '$regionalByDays',
              as: 'item',
              in: {
                section: 'Regional',
                subsection: { $concat: ['$$item._id.region', ' - Days'] },
                category: '$$item._id.daysBucket',
                provider: '$$item._id.provider',
                listingCount: '$$item.listingCount',
                totalViews: '$$item.totalViews',
              },
            },
          },
        ],
      },
      allCombinations: '$allCombinations',
    },
  },
  { $unwind: '$allCombinations' },
  {
    $project: {
      combo: '$allCombinations',
      match: {
        $filter: {
          input: '$actualResults',
          as: 'actual',
          cond: {
            $and: [
              { $eq: ['$$actual.section', '$allCombinations.section'] },
              { $eq: ['$$actual.subsection', '$allCombinations.subsection'] },
              { $eq: ['$$actual.category', '$allCombinations.category'] },
              { $eq: ['$$actual.provider', '$allCombinations.provider'] },
            ],
          },
        },
      },
    },
  },
  {
    $project: {
      section: '$combo.section',
      subsection: '$combo.subsection',
      category: '$combo.category',
      provider: '$combo.provider',
      listingCount: { $ifNull: [{ $arrayElemAt: ['$match.listingCount', 0] }, 0] },
      totalViews: { $ifNull: [{ $arrayElemAt: ['$match.totalViews', 0] }, 0] },
    },
  },
  { $sort: { section: 1, subsection: 1, category: 1, provider: 1 } },
]
