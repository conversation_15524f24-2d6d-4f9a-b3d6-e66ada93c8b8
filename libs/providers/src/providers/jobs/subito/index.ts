import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import { extract, scheduleExtract } from '../../../lib/classifieds/subito'

import type { Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractIT: {
    region: Region.eu_central_1,
    cron: '0 12 * * WED',
    schedule: async (job, runtime) => scheduleExtract(job, runtime, Iso3166Alpha2.IT),
    function: extract,
    snapshot: async (controller, controllerInstance) => snapshot(Iso3166Alpha2.IT, controller, controllerInstance),
    config: {
      concurrency: 20,
      perJobTimeout: 40 * 60 * 1000,
    },
  },
}

export default provider
