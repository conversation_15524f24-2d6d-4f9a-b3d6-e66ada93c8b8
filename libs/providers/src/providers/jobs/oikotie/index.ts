import { Iso3166Alpha2, Region } from '@datagatherers/datagatherers'
import { snapshot } from '@datagatherers/worker-utils'

import { extractJobs, extractLocations, scheduleExtract, scheduleLocations } from '../../../lib/classifieds/oikotie'
import { trackerOikotieNewsFI, trackerOikotiePricingFI } from '../../non_standard/trackers'

import type { JobDefinition, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  codesFI: {
    region: Region.eu_west_1,
    function: extractLocations,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleLocations(job, runtime, Iso3166Alpha2.FI),
    config: {
      concurrency: 10,
    },
  },
  extractFI: {
    // cron: '0 12 * * THU',
    region: Region.eu_west_1,
    function: extractJobs,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleExtract(job, runtime, Iso3166Alpha2.FI),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.FI, config, runtime),
    config: {
      concurrency: 15,
    },
  },
  trackerOikotiePricingFI,
  trackerOikotieNewsFI,
}

export default provider
