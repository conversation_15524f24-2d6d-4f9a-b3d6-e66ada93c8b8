import { AuctionsStockType, ClassifiedsStockType, Iso3166Alpha2, Region } from '@datagatherers/datagatherers'

import {
  extractListings,
  extractDealers,
  extractProviderConfig,
  scheduleDealers,
  scheduleExtract,
  snapshot,
  updateExistingDealers,
} from '../../../lib/classifieds/okidoki'

import type { JobDefinition, JobInstance, Provider } from '@datagatherers/datagatherers'

const provider: Provider = {
  extractCarpartsListingsEE: {
    cron: '0 5 * * TUE',
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.car_parts),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractLeisureListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractLeisureListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.leisure),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractOtherListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractOtherListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, AuctionsStockType.other),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractElectronicsListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractElectronicsListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.electronics),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractFashionListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractFashionListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.fashion),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractKidsListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractKidsListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.kids),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractWomenListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractWomenListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.women),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractMenListingsEE',
      }
    },
    config: extractProviderConfig,
  },
  extractMenListingsEE: {
    region: Region.eu_north_1,
    function: extractListings,
    schedule: async (job, runtime): Promise<JobDefinition[]> =>
      scheduleExtract(job, runtime, Iso3166Alpha2.EE, ClassifiedsStockType.men),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'extractDealersEE',
      }
    },
    config: extractProviderConfig,
  },
  extractDealersEE: {
    region: Region.eu_north_1,
    function: extractDealers,
    schedule: async (job, runtime): Promise<JobDefinition[]> => scheduleDealers(job, runtime, Iso3166Alpha2.EE),
    scheduleAfterFinished: (config) => {
      return {
        project: config.project,
        provider: config.provider,
        type: 'updateRemainingDealersEE',
      }
    },
    config: extractProviderConfig,
  },
  updateRemainingDealersEE: {
    region: Region.eu_north_1,
    function: async (job: JobInstance) => updateExistingDealers(job, Iso3166Alpha2.EE),
    snapshot: async (config, runtime) => snapshot(Iso3166Alpha2.EE, config, runtime),
    config: extractProviderConfig,
  },
}

export default provider
