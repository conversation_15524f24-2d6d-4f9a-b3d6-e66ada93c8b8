import {
  RealEstatesStockType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesPropertyType,
  ResultType,
  DataType,
  MarketplacesDealerType,
  ProjectType,
  GeoJSONTypes,
  DealerType,
} from '@datagatherers/datagatherers'
import { getItemId, getREArea } from '@datagatherers/worker-utils'

import { configuration } from './constants'

import type { BoneoProject, BoneoPropertiesResponse, BoneoProperty } from './types'
import type {
  RealEstatesDealerItem,
  JobDefinition,
  Iso3166Alpha2,
  GeoJSONPoint,
  JobInstance,
  ControllerRuntime,
  Town,
  ResultItem,
} from '@datagatherers/datagatherers'

export async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const towns = await runtime
    .collection<Town>('town')
    .aggregate([{ $match: { project: job.project, provider: job.provider, iso } }])
    .toArray()

  if (!towns?.length) return []

  return ['kommersiella', 'bostad'].flatMap((type) =>
    towns?.map((town) => ({ ...job, data: { ...job.data, ...town.data, type } })),
  )
}

function getPropertiesForm(job: JobInstance, start: number) {
  return {
    type: job.data?.type,
    start,
    limit: configuration.limitPerPage,
    sort: 'field_property_publish_date',
    order: 'desc',
    ...(job.data?._source.places && {
      locs_field_name: `[{"locs_field_name":"${job.data?._source.places}","locs_field_combination":"field_property_lan","id":"${job.data?._id}"}]`,
    }),
  }
}

export async function extract(job: JobInstance) {
  const dealersMap: Map<
    string,
    Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'>
  > = new Map()

  let start = 0
  const listings = await job.runtime.got.paginate.all<ResultItem, BoneoPropertiesResponse>(
    'https://objectsapi.boneo.se/v1/search/es/properties',
    {
      method: 'POST',
      responseType: 'json',
      // headers: {
      //   'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      // },
      form: getPropertiesForm(job, start),
      retry: configuration.retry,
      pagination: {
        paginate: ({ response }) => {
          if (!response?.body?.data?.docs?.length || response.body.data.docs.length < configuration.limitPerPage)
            return false

          start += response.body.data.docs.length
          return {
            form: getPropertiesForm(job, start),
          }
        },
        transform: ({ body }) => {
          if (!body?.data?.docs?.length) return []

          return body.data.docs.flatMap((item) => {
            if (!('property_id' in item._source)) return []

            const property = item._source as BoneoProperty

            const dealer = getDealer(property)
            const dealerId = dealer?.dealerId ? dealer.dealerId : 'unknown'
            const dealerType = dealer?.dealerType ? dealer.dealerType : DealerType.private

            if (dealer && !dealersMap.has(dealer.dealerId)) {
              try {
                dealersMap.set(dealer.dealerId, dealer)
              } catch (error) {
                console.error(error)
              }
            }
            const url = `https://www.boneo.se${property.slug}`
            const state = getState(job)
            const price = getPrice(property)
            const area = getArea(property.field_property_floor_area, property.field_property_lot_size)
            const stockType = getStockType(job)
            const businessType = RealEstatesBusinessType.sale
            const propertyScope = getPropertyScope(property)
            const propertyType = getPropertyType(property)
            const productType = getProductType(property)
            const geoJson = getGeoJson(property.field_coordinates?.lat, property.field_coordinates?.lon)
            const inventoryId = property.property_id.toString()
            const data = {
              project: job.project,
              itemId: getItemId(DataType.inventory, job.project, inventoryId),
              iso: job.data?.iso,
              dataType: DataType.inventory,
              inventoryId,
              url,
              stockType,
              dealerId,
              dealerType,
              ...(state && { state }),
              ...(price && { price }),
              ...(price && { currency: 'SEK' }),
              ...(area && { area }),
              ...(businessType && { businessType }),
              ...(propertyScope && { propertyScope }),
              ...(propertyType && { propertyType }),
              ...(productType && { productType }),
              ...(geoJson && { geoJson }),
              data: {
                ...item,
              },
            }
            return {
              type: ResultType.item,
              data,
            }
          })
        },
      },
    },
  )

  const dealers = [...dealersMap.values()]
    .filter((a) => a?.dealerId)
    .map((item) => {
      const dealerId = item.dealerId
      const data = {
        project: job.project,
        itemId: getItemId(DataType.dealers, job.project, dealerId),
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId,
        dealerType: item.dealerType,
        name: item.name,
        isActive: item.isActive,
        isPaying: item.isPaying,
        data: {
          ...item.data,
        },
      }

      return {
        type: ResultType.item,
        data,
      }
    })
  return [...listings, ...dealers]
}

export async function scheduleExtractNewProduction(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const towns = await runtime
    .collection<Town>('town')
    .aggregate([{ $match: { project: job.project, provider: job.provider, iso } }])
    .toArray()
  return towns.map((town) => ({ ...job, data: { ...job.data, ...town.data } }))
}

function getNewProductionForm(job: JobInstance, start: number) {
  return {
    start,
    limit: configuration.limitPerPage,
    sort: 'created_at',
    order: 'desc',
    ...(job.data?._source.places && {
      locs_field_name: `[{"locs_field_name":"${job.data?._source.places}","locs_field_combination":"field_property_lan","id":"${job.data?._id}"}]`,
    }),
  }
}

export async function extractNewProduction(job: JobInstance) {
  const dealersMap: Map<
    string,
    Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'>
  > = new Map()
  const projectUnits = []

  let start = 0
  const projects = await job.runtime.got.paginate.all<ResultItem, BoneoPropertiesResponse>(
    `https://objectsapi.boneo.se/v1/search/projects`,
    {
      method: 'POST',
      responseType: 'json',
      retry: configuration.retry,
      // headers: {
      //   // 'User-Agent':
      //   //   'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36',
      //   'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      // },
      form: getNewProductionForm(job, start),
      pagination: {
        paginate: ({ response }) => {
          if (!response?.body?.data?.docs?.length || response.body.data.docs.length < configuration.limitPerPage)
            return false

          start += response.body.data.docs.length

          return {
            form: getNewProductionForm(job, start),
          }
        },
        transform: ({ body }) => {
          if (!body?.data?.docs?.length) return []

          return body.data.docs.flatMap((item) => {
            if (!('project_id' in item._source)) return []

            const project = item._source as BoneoProject

            const dealer = getNewProductionDealer(project)
            const dealerId = dealer?.dealerId ? dealer.dealerId : 'unknown'
            const dealerType = dealer?.dealerType ? dealer.dealerType : DealerType.private
            if (dealer && !dealersMap.has(dealer.dealerId)) {
              try {
                dealersMap.set(dealer.dealerId, dealer)
              } catch (error) {
                console.error(error)
              }
            }
            const url = `https://www.boneo.se${project.slug}`
            const state = getState(job)
            // const price = getNewProductionPrice(item)
            // const area = getNewProductionArea(item)
            const stockType = getNewProductionStockType()
            const businessType = RealEstatesBusinessType.sale
            const propertyScope = getNewProductionPropertyScope(project)
            const propertyType = RealEstatesPropertyType.other
            const productType = getProductType(project)
            const geoJson = getGeoJson(project.location.lat, project.location.lon)

            const data = {
              project: ProjectType.realestates,
              itemId: getItemId(DataType.inventory, ProjectType.realestates, `p${project.project_id}`),
              iso: job.data?.iso,
              dataType: DataType.inventory,
              inventoryId: `p${project.project_id}`,
              url,
              stockType,
              dealerId,
              dealerType,
              ...(state && { state }),
              // ...(price && { price }),
              // ...(area && { area }),
              ...(businessType && { businessType }),
              ...(propertyScope && { propertyScope }),
              ...(propertyType && { propertyType }),
              ...(productType && { productType }),
              ...(geoJson && { geoJson }),
              data: {
                ...item,
                adType: 'Project',
              },
            }

            if (project.property_details?.length) {
              projectUnits.push(
                ...project.property_details.flatMap((propertyInProject) => {
                  const url = `https://www.boneo.se${propertyInProject.slug}`
                  const state = getState(job)
                  const price = getPrice(propertyInProject)
                  const area = getArea(
                    propertyInProject.field_property_floor_area,
                    propertyInProject.field_property_lot_size,
                  )
                  const stockType = getStockType(job)
                  const propertyScope = getPropertyScope(propertyInProject)
                  const propertyType = getPropertyType(propertyInProject)
                  const inventoryId = `${propertyInProject.property_id}`
                  const data = {
                    project: job.project,
                    itemId: getItemId(DataType.inventory, ProjectType.realestates, inventoryId),
                    iso: job.data?.iso,
                    dataType: DataType.inventory,
                    inventoryId,
                    url,
                    stockType,
                    dealerId,
                    dealerType,
                    ...(state && { state }),
                    ...(price && { price }),
                    ...(price && { currency: 'SEK' }),
                    ...(area && { area }),
                    ...(businessType && { businessType }),
                    ...(propertyScope && { propertyScope }),
                    ...(propertyType && { propertyType }),
                    // ...(productType && { productType }),
                    ...(geoJson && { geoJson }),
                    data: {
                      project_id: project.project_id,
                      ...propertyInProject,
                      agent_details: project.agent_details,
                      agency_details: project.agency_details,
                      brand_details: project.brand_details,
                      adType: 'ProjectUnit',
                    },
                  }
                  return {
                    type: ResultType.item,
                    data,
                  }
                }),
              )
            }

            return {
              type: ResultType.item,
              data,
            }
          })
        },
      },
    },
  )

  const dealers = [...dealersMap.values()]
    .filter((a) => a?.dealerId)
    .map((item) => {
      const data = {
        project: job.project,
        itemId: getItemId(DataType.dealers, job.project, item.dealerId),
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        name: item.name,
        isActive: item.isActive,
        isPaying: item.isPaying,
        data: {
          ...item.data,
        },
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  return [...projects, ...projectUnits, ...dealers]
}

export async function scheduleTowns(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const statesArr = await runtime
    .collection('worldcities')
    .aggregate([
      { $match: { iso2: iso, admin_code: { $exists: true, $ne: '' } } },
      { $group: { _id: { name: '$admin_name', code: '$admin_code' } } },
    ])
    .toArray()
  return statesArr.map((item) => {
    return {
      ...job,
      data: {
        ...job.data,
        stateName: item._id.name,
        stateCode: item._id.code,
        iso,
      },
    }
  })
}

export async function extractTowns(job: JobInstance) {
  const docs = await job.runtime
    .axios(`https://apigateway.boneo.se/v1/autocomplete/es/places`, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36',
        Accept: '*/*',
      },
      params: {
        type: ' ',
        q: job.data?.stateName,
      },
    })
    .then((res) => res.data?.data?.docs)
    .catch((err) => job.addErrorToLog(err))
  if (!docs?.length) return []
  return docs
    .filter((item: { _source: { type: string } }) => item._source?.type === 'Län')
    .map((item: { _id: string }) => {
      return {
        type: ResultType.town,
        data: {
          project: ProjectType.realestates,
          itemId: `${job.data?.iso}_${item._id}`,
          iso: job.data?.iso,
          data: {
            ...job.data,
            ...item,
          },
        },
      }
    })
}

function getState(job: JobInstance) {
  return job.data?.stateCode
}

function getStockType(job: JobInstance): RealEstatesStockType {
  return job.data?.type === 'bostad' ? RealEstatesStockType.residential_sales : RealEstatesStockType.commercial_sales
}

function getNewProductionStockType(): RealEstatesStockType {
  return RealEstatesStockType.residential_sales
}

function getArea(propertyFloorArea?: number, propertyLotSize?: number) {
  return propertyFloorArea
    ? getREArea(Number(propertyFloorArea))
    : propertyLotSize
      ? getREArea(Number(propertyLotSize))
      : 0
}
function getPrice(item: BoneoProperty) {
  return item.field_property_price
}

function getPropertyScope(item: BoneoProperty): RealEstatesPropertyScope {
  if (item.field_property_type?.length) {
    if (item.field_property_type.includes('Fritidshus') || item.field_property_type.includes('Kommersiella objekt'))
      return RealEstatesPropertyScope.commercial
    if (item.field_property_type.includes('Tomt') || item.field_property_type.includes('Gård/Skog'))
      return RealEstatesPropertyScope.land
    if (
      item.field_property_type.includes('Bostadsrätt') ||
      item.field_property_type.includes('Lägenhet') ||
      item.field_property_type.includes('Villa') ||
      item.field_property_type.includes('Radhus')
    )
      return RealEstatesPropertyScope.residential
  }
  return RealEstatesPropertyScope.other
}

function getNewProductionPropertyScope(item: BoneoProject): RealEstatesPropertyScope {
  if (item.project_type?.length) {
    if (item.project_type.includes('Fritidshus') || item.project_type.includes('Kommersiella objekt'))
      return RealEstatesPropertyScope.commercial
    if (item.project_type.includes('Tomt') || item.project_type.includes('Gård/Skog'))
      return RealEstatesPropertyScope.land
    if (
      item.project_type.includes('Bostadsrätt') ||
      item.project_type.includes('Lägenhet') ||
      item.project_type.includes('Villa') ||
      item.project_type.includes('Radhus')
    )
      return RealEstatesPropertyScope.residential
  }
  return RealEstatesPropertyScope.other
}

function getPropertyType(item: BoneoProperty): RealEstatesPropertyType {
  if (item.field_property_type?.length) {
    if (item.field_property_type.includes('Bostadsrätt') || item.field_property_type.includes('Lägenhet'))
      return RealEstatesPropertyType.apartment
    if (
      item.field_property_type.includes('Villa') || // Villa
      item.field_property_type.includes('Radhus') || // Townhouse
      item.field_property_type.includes('Fritidshus') // Holiday Home
    )
      return RealEstatesPropertyType.house
    if (item.field_property_type.includes('Tomt')) return RealEstatesPropertyType.urban
    if (item.field_property_type.includes('Gård/Skog')) return RealEstatesPropertyType.agriculture
  }
  return RealEstatesPropertyType.other
}

function getProductType(item: BoneoProperty | BoneoProject) {
  switch (item.package_details?.package_name) {
    case 'Bas': {
      return 'Basic'
    }
    case 'Extra': {
      return 'Extra'
    }
    case 'Komplett': {
      return 'Complete'
    }
    case 'Small': {
      return 'Small'
    }
    case 'Återpublicerad': {
      return 'Republished'
    }
    case 'Kostnadsfri':
    default: {
      return 'Free'
    }
  }
}

function getDealer(
  item: BoneoProperty,
): Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'> | undefined {
  if (item.agent_details?.length) {
    return {
      dealerId: item.agent_details[0].agent_id,
      dealerType: MarketplacesDealerType.dealer,
      isActive: item.agent_details[0].is_active,
      isPaying: item.agent_details[0].is_active,
      name: item.agent_details[0].name,
      data: {
        ...item.agent_details[0],
        source: 'agent',
      },
    }
  } else if (item.agency_details?.length) {
    return {
      dealerId: item.agency_details[0]._id,
      dealerType: MarketplacesDealerType.dealer,
      isActive: item.agency_details[0].is_active,
      isPaying: item.agency_details[0].is_active,
      name: item.agency_details[0].title_field,
      data: {
        ...item.agency_details[0],
        source: 'agency',
      },
    }
  }
  return undefined
}

function getNewProductionDealer(
  item: BoneoProject,
): Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'> | undefined {
  if (item.agent_details?.agent_id) {
    return {
      dealerId: item.agent_details.agent_id,
      dealerType: MarketplacesDealerType.dealer,
      isActive: item.agent_details.is_active,
      isPaying: item.agent_details.is_active,
      name: item.agent_details.name,
      data: {
        ...item.agent_details,
        source: 'agent',
      },
    }
  } else if (item.agency_details?._id) {
    return {
      dealerId: item.agency_details._id,
      dealerType: MarketplacesDealerType.dealer,
      isActive: item.agency_details.is_active,
      isPaying: item.agency_details.is_active,
      name: item.agency_details.title_field,
      data: {
        ...item.agency_details,
        source: 'agency',
      },
    }
  } else if (item.construction_company?._id) {
    return {
      dealerId: item.construction_company._id,
      dealerType: MarketplacesDealerType.dealer,
      isActive: true,
      isPaying: true,
      name: item.construction_company.subtitle
        ? item.construction_company.subtitle
        : item.construction_company.company_name,
      data: {
        ...item.construction_company,
        source: 'construction_company',
      },
    }
  }
  return undefined
}

function getGeoJson(latitude: number | string, longitude: number | string): GeoJSONPoint | undefined {
  if (!latitude || !longitude) return
  return {
    type: GeoJSONTypes.Point,
    coordinates: [Number(longitude), Number(latitude)],
  }
}
