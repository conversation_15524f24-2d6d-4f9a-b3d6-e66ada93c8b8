export interface BoneoPropertiesResponse {
  data: {
    count: number
    docs: BoneoDocument[]
  }
}

interface BoneoDocument {
  _index: string
  _type: string
  _id: string
  _score?: null
  _source: BoneoProperty | BoneoProject
  sort?: number[] | null
}
export interface BoneoProperty {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [x: string]: any
  field_property_municipality: string
  field_property_shared_pool?: null
  field_county_parish_code: string
  body: string
  field_property_bedroom?: null
  field_property_accepting_bids: boolean
  field_property_publish_date: string
  field_property_city: string
  field_address_country_code: string
  brand_details?: BrandDetailsEntity[] | null
  field_property_seaview?: null
  field_commercial_marketing?: null[] | null
  field_property_dist_citycenter?: null
  field_property_constructed_area?: null
  slug: string
  field_floor: string
  field_monthly_fee: number
  field_property_terrace?: null
  field_property_rooms: string
  field_property_private_pool?: null
  field_property_arrende?: null
  field_property_country: string
  field_property_other_currency: string
  field_property_ac?: null
  property_id: number
  project_details?: null[] | null
  field_property_rum_filter: number
  field_property_tomtr: boolean
  field_status_id: string
  field_total_number_floors: string
  field_building_year: string
  field_property_squremeter_price: number
  field_foretag_till_salu?: null[] | null
  field_url_image_list: string
  field_property_lot_size?: null
  field_price_other_currencie?: null
  field_property_converted_price: number
  field_property_type_code?: null
  field_property_bathroom?: null
  agency_details?: AgencyDetailsEntity[] | null
  field_property_area: string
  group_viewings?: GroupViewingsEntity[] | null
  field_property_construction: boolean
  field_property_balcony?: null
  title_field: string
  field_property_sjonara: boolean
  field_elevator: string
  field_search_objective?: null[] | null
  field_property_type?: string[] | null
  field_property_province: string
  agent_details?: AgentDetailsEntity[] | null
  field_property_zip_code: string
  field_property_min_bedroom: number
  field_property_selling_type: string
  field_url_description: string
  field_expense_operating_cost: string
  field_coordinates: Coordinates
  field_property_biarea?: null
  field_property_vendor: string
  field_property_distance_sea?: null
  field_premises_marketing?: null
  field_property_typ?: null
  field_property_reference_number: string
  field_property_floor_area: number
  field_property_lan: string
  p_id: string
  field_total_assessed_value?: null
  field_property_unpublish_date?: null
  field_property_price: number
}
interface BrandDetailsEntity {
  ads_package_id: string
  object_detail_logo: string
  utland_banner: string
  city: string
  subscription_package: string
  number_of_brokeroffices?: null
  ads_enable: boolean
  desktop_banner: string
  object_list_logo: string
  maklare_list_logo: string
  office_id?: null
  organizationId: string
  vendor: string
  post_address: string
  serviceId?: null
  agency_map_field?: string[] | null
  email: string
  identifier: string
  contact_name: string
  website: string
  is_active: boolean
  adfenix_logo: string
  ads_listing_types?: string[] | null
  telephone: string
  accessToken: string
  ads_property_status?: string[] | null
  is_delete: boolean
  zipcode: string
  ads_property_types?: string[] | null
  is_allow_single_ad_creation: boolean
  name: string
  kommun: string
  email_logo: string
  external_bevaka: boolean
  _id: string
  updated_date: string
  created_date: string
  customer_id?: null
  abm_logo?: null
  organization_number?: null
}
interface AgencyDetailsEntity {
  ads_package_id?: string
  drupal_agency_id?: string[] | null
  primary_agentid?: string
  maklare_list_logo?: null
  office_id?: null
  province?: string
  corporate_number: string
  postalgiro?: string
  slug: string
  postal_address?: null
  ads_listing_types?: string[] | string
  vat?: null
  agency_id: number
  brand_id: string
  zipcode: string
  is_allow_single_ad_creation: boolean
  external_bevaka: boolean
  phone_number: string
  _id: string
  region?: null
  is_override_logo_banner: boolean
  office_number?: null
  utland_banner?: null
  object_detail_logo?: null
  city: string
  description?: null
  ads_enable: boolean
  desktop_banner?: null
  object_list_logo?: null
  title_field: string
  street: string
  banner_url?: null
  email: string
  website?: null
  bankgiro?: null
  is_active: boolean
  legal_customer_name?: null
  adfenix_logo?: null
  ads_is_override: boolean
  coordinates: Coordinates
  company_place?: null
  ads_property_status?: null[] | null
  is_delete: boolean
  ads_property_types?: null[] | null
  office_name?: null
  tax_certifica?: null
  email_logo?: null
  updated_date: string
  created_date: string
  customer_id: string
}
interface GroupViewingsEntity {
  field_viewing_type?: null
  field_viewing_start_time: string
  field_viewing_id?: null
  _id: string
  field_viewing_commentary: string
  field_viewing_end_time: string
}
interface AgentDetailsEntity {
  is_active: boolean
  agent_id: string
  agency: string
  agency_id: string
  telephone: string
  title: string
  brand_id: string
  is_delete: boolean
  socialads_image: string
  image_changed_date?: string
  bevaka_image: string
  name: string
  cellphone?: string
  logo: string
  _id: string
  created_date: string
  updated_date: string
  email: string
  agent_image: string
}

export interface BoneoProject {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [x: string]: any
  contact_person: ContactPerson
  project_id: number
  external_id: string
  publish: boolean
  slug: string
  name: string
  status: string
  sell_start: string
  preliminary_date: string
  ownership_type?: string[]
  project_type?: string[]
  no_of_properties: number
  title: string
  description: string
  website_url: string
  created_at: string
  constructor_id: string
  address: string
  kommun_id: string
  kommun_name: string
  omrad_id: string
  omrad_name: string
  registration_interest: boolean
  contact_url: string
  office_id?: null
  customer_id?: null
  primary_agent_id?: null
  secondary_agent_id?: null
  street?: null
  zip_code?: null
  city?: null
  area_name?: null
  country_code?: null
  country?: null
  parish_code?: null
  lan?: string
  short_sale_description?: null
  sale_description?: null
  sale_phrase?: null
  sale_heading?: null
  vendor_changed_at?: null
  possible_admission_date?: null
  url_description?: null
  url_image_list?: null
  marketing?: null
  group_viewings?: null[] | null
  is_delete: boolean
  vendor?: null
  version: number
  project_version_type: string
  brand_details: BrandDetailsEntity
  agency_details: AgencyDetailsEntity
  agent_details: AgentDetailsEntity
  additional_area?: null[] | null
  embed_video?: null[] | null
  article_id: string
  property_details?: BoneoProperty[]
  price_range: Ranges
  fee_range: Ranges
  squaremeter_price_range: Ranges
  living_area_range: Ranges
  no_of_rooms_range: Ranges
  location: Coordinates
  construction_company: ConstructionCompany
}
interface ContactPerson {
  name: string
  email: string
  phone: string
}
interface Ranges {
  gte: number | string
  lte: number | string
}
interface Coordinates {
  lat: number | string
  lon: number | string
}
interface ConstructionCompany {
  _id: string
  company_name: string
  subtitle: string
  address: string
  zip_code: string
  phone_no: string
  website: string
  logo: string
  status: boolean
  created_at: string
  updated_at: string
  __v: number
}
