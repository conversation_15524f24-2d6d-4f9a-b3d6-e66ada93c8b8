import {
  DataType,
  DealerType,
  ProjectType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesStockType,
  ResultType,
} from '@datagatherers/datagatherers'
import { getItemId, getREArea } from '@datagatherers/worker-utils'

import { crawlData } from './constants'
import { parseArea, slugify } from './utils'

import type { DomopliusResponse, Property } from './types'
import type { Iso3166Alpha2, JobDefinition, JobInstance, ResultItem } from '@datagatherers/datagatherers'

export function schedule(job: JobDefinition, iso: Iso3166Alpha2) {
  const jobData = []
  for (const key in crawlData.propertyTypeSlugs) {
    const slug = crawlData.propertyTypeSlugs[key]
    let propertyScope: RealEstatesPropertyScope = RealEstatesPropertyScope.other
    let propertyType: string | RealEstatesPropertyScope = RealEstatesPropertyScope.other

    if (key === 'land') propertyScope = RealEstatesPropertyScope.land
    else if (key === 'commercial') propertyScope = RealEstatesPropertyScope.commercial
    else propertyScope = RealEstatesPropertyScope.residential

    if (key === 'apartments') propertyType = 'apartment'
    else if (key === 'house') propertyType = 'house'
    else if (key === 'garage') propertyType = 'parking'
    else if (key === 'land') propertyType = 'other'

    for (const businessType of crawlData.businessTypes) {
      for (const sellerType of crawlData.sellerTypes) {
        const dealerType = sellerType === 'broker' ? DealerType.dealer : DealerType.private
        if (key !== 'commercial') {
          jobData.push({
            businessType,
            dealerType,
            propertyScope,
            propertyType,
            url: crawlData.baseUrl + slug,
            searchParams: JSON.stringify({
              sale_type: businessType === RealEstatesBusinessType.sale ? 'sale' : 'rent',
              sort_order: 'newest',
              page: 1,
              map_hidden: 'true',
              'seller_type[]': sellerType,
            }),
          })
        } else {
          for (const purpose in crawlData.commercialPurposes) {
            const mappedType = crawlData.commercialPurposes[purpose]
              ? crawlData.commercialPurposes[purpose]
              : RealEstatesPropertyScope.other
            jobData.push({
              businessType,
              dealerType,
              propertyScope,
              propertyType: mappedType,
              url: crawlData.baseUrl + slug,
              searchParams: JSON.stringify({
                sale_type: businessType === RealEstatesBusinessType.sale ? 'sale' : 'rent',
                sort_order: 'newest',
                page: 1,
                map_hidden: 'true',
                'purpose[]': purpose,
                'seller_type[]': sellerType,
              }),
            })
          }
        }
      }
    }
  }
  const jobs: JobDefinition[] = []
  for (const jobD of jobData) {
    jobs.push({
      ...job,
      data: {
        iso,
        ...jobD,
      },
    })
  }
  return jobs
}

export async function extract(job: JobInstance) {
  if (!job.data.searchParams) return []

  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType; isActive: boolean; isPaying: boolean }> =
    new Map()

  const listings = await job.runtime.got.paginate.all<ResultItem, DomopliusResponse>(job.data.url, {
    method: 'GET',
    searchParams: {
      ...JSON.parse(job.data.searchParams),
    },
    responseType: 'json',
    retry: { limit: 5 },
    pagination: {
      paginate: ({ response }) => {
        const body = response.body as DomopliusResponse
        const pagination = body?.pagination
        if (!pagination) return false
        if (pagination.current_page >= pagination.last_page) return false

        const prev = response.request.options.searchParams
        return {
          searchParams: {
            ...prev,
            page: pagination.current_page + 1,
          },
        }
      },
      transform: ({ body }) => {
        const properties: Property[] = body?.properties || []
        if (!properties.length) return []

        return properties.map((p) => {
          const price = Number(p.price) || 0
          const areaRaw = parseArea(p.metaInfo)
          const area = getREArea(areaRaw)
          const id = p.id
          const seller = p.seller
          let dealerId = ''
          const dealerType: DealerType = job.data?.dealerType || DealerType.private
          if (seller) {
            dealerId = slugify(seller) || ''
            if (dealerId && !dealersMap.has(dealerId)) {
              dealersMap.set(dealerId, {
                dealerId,
                dealerType,
                isActive: true,
                isPaying: dealerType === DealerType.dealer,
              })
            }
          }

          let stockType = RealEstatesStockType.other
          if (
            job.data?.businessType == RealEstatesBusinessType.sale &&
            job.data?.propertyScope == RealEstatesPropertyScope.residential
          )
            stockType = RealEstatesStockType.residential_sales
          if (
            job.data?.businessType == RealEstatesBusinessType.rent &&
            job.data?.propertyScope == RealEstatesPropertyScope.residential
          )
            stockType = RealEstatesStockType.residential_lettings
          if (
            job.data?.businessType == RealEstatesBusinessType.sale &&
            job.data?.propertyScope == RealEstatesPropertyScope.commercial
          )
            stockType = RealEstatesStockType.commercial_sales
          if (
            job.data?.businessType == RealEstatesBusinessType.rent &&
            job.data?.propertyScope == RealEstatesPropertyScope.commercial
          )
            stockType = RealEstatesStockType.commercial_lettings

          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId: getItemId(DataType.inventory, ProjectType.realestates, id.toString()),
              dataType: DataType.inventory,
              iso: job.data?.iso,
              inventoryId: id.toString(),
              stockType,
              dealerId,
              dealerType,
              price,
              currency: 'EUR',
              area,
              businessType: job.data?.businessType,
              propertyScope: job.data?.propertyScope,
              propertyType: job.data?.propertyType,
            },
          }
        })
      },
    },
  })

  if (!listings?.length) return []

  const dealers = [...dealersMap.values()].map((d) => ({
    type: ResultType.item,
    data: {
      project: ProjectType.realestates,
      itemId: getItemId(DataType.dealers, ProjectType.realestates, d.dealerId),
      iso: job.data?.iso,
      dataType: DataType.dealers,
      dealerId: d.dealerId,
      dealerType: d.dealerType,
      isActive: d.isActive,
      isPaying: d.isPaying,
    },
  }))

  return [...listings, ...dealers]
}
