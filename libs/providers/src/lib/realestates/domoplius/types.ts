export interface DomopliusResponse {
  params: Params
  filters: Filters
  active?: unknown[] | null
  properties?: Property[] | null
  allProperties?: AllProperty[] | null
  availableFilters: AvailableFilters
  likedIds?: unknown[] | null
  pagination: Pagination
  area_polygons?: unknown
  declinedTitle: string
}

interface Params {
  sale_type: string
  sort_order: string
  page: string
  map_hidden: string
  propertyType?: string
}

interface Filters {
  topBar?: FilterEntry[] | null
  moreFilters?: FilterEntry[] | null
  selectedData: SelectedData
  filterOrder: FilterOrder
}

interface FilterEntry {
  name: string
  availableData: AvailableData | string
}

interface AvailableData {
  minMax?: number[] | null
  minMaxWhole?: number[] | null
  minMaxSqm?: number[] | null
  options?: AvailableOption[] | null
}

interface AvailableOption {
  label: string
  value: string
  icon?: string | null
  path?: string | null
  id?: number | null
}

interface SelectedData {
  propertyType: string
  saleType: string
  sortOrder: string
  price: RangeWithUnit
  landPrice: Range
  areaSquareMeter: Range
  landSize: RangeWithUnit
  rooms: Range
  floor: Range
  yearBuilt: Range
  condition?: unknown[] | null
  heating?: unknown[] | null
  houseType?: unknown[] | null
  buildingType?: unknown[] | null
  energyEfficiencyClass?: unknown[] | null
  purpose?: unknown[] | null
  garageType?: unknown[] | null
  gasSupply?: unknown[] | null
  waterSupply?: unknown[] | null
  drainage?: unknown[] | null
  siteMeasuringType?: unknown[] | null
  country?: unknown[] | null
  city?: unknown[] | null
  features?: unknown[] | null
  sellerType?: unknown[] | null
  listingDate: string
  textSearch: string
  location?: unknown[] | null
}

interface RangeWithUnit {
  unit: string
  min: number | null
  max: number | null
}

interface Range {
  min: number | null
  max: number | null
}

interface FilterOrder {
  topBar?: string[] | null
  moreFilters?: string[] | null
}

export interface Property {
  id: number
  type: string
  slug: string
  price: string
  pricePerSqm: number
  seller?: string | null
  city: string
  microdistrict?: string | null
  highlight: boolean
  isNew: boolean
  raiseUnits: number
  savedTimes: number
  metaInfo: string
  url: string
  isLiked: boolean
  image: string
}

interface AllProperty {
  type: string
  count?: string | null
  numeric_count?: number | null
  coordinates: Coordinates
  id?: string | null
  price?: string | null
  image?: string | null
  highlight?: boolean | null
  property_type?: string | null
}

interface Coordinates {
  lat: number
  lon: number
}

interface AvailableFilters {
  topBar?: FilterEntry[] | null
  moreFilters?: FilterEntry[] | null
  selectedData: SelectedData
  filterOrder: FilterOrder
}

interface Pagination {
  current_page: number
  per_page: number
  total: number
  last_page: number
}
