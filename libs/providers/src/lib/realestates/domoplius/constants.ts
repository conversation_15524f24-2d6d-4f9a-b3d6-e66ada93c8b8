import { RealEstatesBusinessType } from '@datagatherers/datagatherers'

export const crawlData = {
  baseUrl: 'https://domoplius.lt/api/listings/',
  propertyTypeSlugs: {
    apartments: 'butai',
    house: 'namai-kotedzai-sodai',
    commercial: 'komercines-patalpos',
    land: 'sklypai',
    garage: 'garazai',
  },
  commercialPurposes: {
    office: 'office',
    production_storage: 'factories',
    storage: 'storage',
    trade_services: 'retail',
    food_service: 'retail',
    residential: 'other',
    tourism: 'other',
    other: 'other',
  },
  businessTypes: [RealEstatesBusinessType.sale, RealEstatesBusinessType.rent],
  sellerTypes: ['private', 'broker'] as const,
}
