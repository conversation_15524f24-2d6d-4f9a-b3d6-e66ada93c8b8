import { startOfISOWeek } from 'date-fns'
import { JSD<PERSON> } from 'jsdom'

import {
  DataType,
  DealerType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesPropertyType,
  ResultType,
} from '@datagatherers/datagatherers'
import { chunks, getItemId, getProjectProviderDataType, getREArea } from '@datagatherers/worker-utils'

import type {
  ControllerRuntime,
  Iso3166Alpha2,
  JobDefinition,
  JobInstance,
  ResultItem,
} from '@datagatherers/datagatherers'

const schedulerInformation = [
  {
    op: 'sale',
    businessType: RealEstatesBusinessType.sale,
  },
  {
    op: 'rent',
    businessType: RealEstatesBusinessType.rent,
  },
  {
    op: 'rent_short_term',
    businessType: RealEstatesBusinessType.rent,
  },
]

async function getEstateTypes(runtime: ControllerRuntime, businessTypeOp: string) {
  return await runtime
    .got('https://www.realu.lt/index.php', {
      method: 'POST',
      searchParams: {
        route: 'common/filter/getOpEstateTypes',
      },
      retry: {
        limit: 3,
      },
      form: {
        op: businessTypeOp,
        route: 'product/category',
      },
    })
    .json<{ name: string; value: string; slug: string }[]>()
    .then((res) => {
      return res
    })
}

export async function scheduleListingsExtract(job: JobDefinition, runtime: ControllerRuntime, iso: Iso3166Alpha2.LT) {
  const jobs: JobDefinition[] = []
  for (const item of schedulerInformation) {
    const estateTypes = await getEstateTypes(runtime, item.op)
    jobs.push(
      ...estateTypes
        .filter((estateType) => !estateType?.value.match(/all/i))
        .map((estateType) => {
          return {
            ...job,
            data: {
              //...job.data,
              iso,
              op: item.op,
              businessType: item.businessType,
              estate_type: estateType.value,
            },
          }
        }),
    )
  }
  return jobs
}

function getPropertyType(estateType: string) {
  switch (estateType) {
    case 'flat':
      return RealEstatesPropertyType.apartment
    case 'house':
      return RealEstatesPropertyType.house
    case 'site':
      return RealEstatesPropertyType.urban
    case 'parking':
      return RealEstatesPropertyType.parking
    case 'commercial':
    default:
      return RealEstatesPropertyType.other
  }
}

function getPropertyScope(estateType: string) {
  switch (estateType) {
    case 'flat':
    case 'house':
      return RealEstatesPropertyScope.residential
    case 'site':
      return RealEstatesPropertyScope.land
    case 'parking':
    case 'commercial':
      return RealEstatesPropertyScope.commercial

    default:
      return RealEstatesPropertyScope.other
  }
}

export async function extractListings(job: JobInstance) {
  const { document } = new JSDOM().window
  const propertyType = getPropertyType(job.data.estate_type)
  const propertyScope = getPropertyScope(job.data.estate_type)
  const businessType = job.data.businessType

  let page = 1

  const results = await job.runtime.got.paginate.all<
    ResultItem,
    { total: number; per_page: number; current_page: number; last_page: number; view: string }
  >('https://www.realu.lt/index.php', {
    method: 'POST',
    responseType: 'json',
    searchParams: {
      route: 'product/category/getEstates',
    },
    retry: {
      limit: 3,
      errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
    },
    form: {
      op: job.data.op,
      estate_type: job.data.estate_type,
      page,
      sort: 'default',
      limit: 72,
      current_layout: 'layout-list',
    },
    pagination: {
      backoff: 200,
      paginate: ({ response }) => {
        if (!response?.body?.last_page || response.body.current_page === response.body.last_page) return false

        page++

        return {
          form: {
            op: job.data.op,
            estate_type: job.data.estate_type,
            page,
            sort: 'default',
            limit: 72,
            current_layout: 'layout-list',
          },
        }
      },
      transform: ({ body }) => {
        document.body.innerHTML = body.view

        const items = Array.from(document.querySelectorAll('div[class^="card-product card-swiper"]'))?.flatMap(
          (element) => {
            const inventoryId = element?.getAttribute('data-estate-id')
            if (!inventoryId) return []

            const isHighlighted = element.className?.includes('highlighted')
            const isExclusive = element.querySelector('div[class="only-realu"]') ? true : false

            const url = `https://www.realu.lt${element.querySelector('a')?.getAttribute('href')}`

            const numberOfStars =
              Number(element.querySelector('div[class^="ico ico-auto btn-star"]')?.textContent ?? 0) || 0

            let price =
              Number(
                element
                  .querySelector('div[class^="price"]')
                  ?.querySelector('.main')
                  ?.textContent?.replace(/[^0-9,]/g, '') ?? 0,
              ) || 0
            if (!price) {
              price =
                Number(
                  element
                    .querySelector('div[class^="price"]')
                    ?.textContent?.trim()
                    ?.replace(/[^0-9,]/g, '') ?? 0,
                ) || 0
            }

            // const area = getREArea(
            //   Number(
            //     element
            //       .querySelector('h3[class="meta"]')
            //       ?.querySelector('span[class="s2"]')
            //       ?.textContent?.replace(/[^0-9.]/g, '') ?? 0,
            //   ) || 0,
            //   element.querySelector('h3[class="meta"]')?.querySelector('span[class="s2"]')?.textContent?.includes('a')
            //     ? 'acres'
            //     : undefined,
            // )

            return {
              type: ResultType.item,
              data: {
                project: job.project,
                dataType: DataType.inventory,
                iso: job.data.iso,
                itemId: getItemId(DataType.inventory, job.project, inventoryId),
                url,
                inventoryId,
                businessType,
                propertyScope,
                propertyType,
                // area,
                price,
                currency: 'EUR',
                data: {
                  isHighlighted,
                  isExclusive,
                  numberOfStars,
                },
              },
            }
          },
        )

        return items ?? []
      },
    },
  })

  return results
}

export async function scheduleExtractDetails(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const items = await runtime
    .collection('item')
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        $or: [{ area: { $exists: false } }, { dealerId: { $exists: false } }],
      },
      {
        projection: {
          _id: 0,
          inventoryId: 1,
          itemId: 1,
          url: 1,
          dealerId: 1,
          area: 1,
        },
      },
    )
    .toArray()

  return (
    [...chunks(items, 1)]?.map((listings) => ({
      ...job,
      data: {
        iso,
        listings,
      },
    })) ?? []
  )
}

export async function extractDetails(job: JobInstance) {
  const listingsToUpdate = []
  const dealers = []

  const { document } = new JSDOM().window

  for (const listing of job.data.listings) {
    if (!listing.url) continue

    await job.runtime
      .got(listing.url, {
        retry: {
          limit: 5,
          errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
        },
        timeout: {
          request: 20_000,
        },
      })
      .text()
      .then((res) => {
        document.body.innerHTML = res

        const productBroker = document.querySelector('div[class="card-product-broker"]')

        const areaTxt = Array.from(document.querySelector('.meta').children)?.find((item) =>
          item.textContent.match(/plotas/i),
        )?.textContent
        const area = getREArea(Number(areaTxt?.replace(/[^0-9.]/g, '') ?? 0), areaTxt?.includes('a') ? 'acres' : 'sqm')

        if ((productBroker && !listing.dealerId) || (area && typeof listing.area !== 'number')) {
          const name = productBroker.querySelector('div[class="broker-name"]')?.textContent?.trim()
          const url = productBroker.querySelector('a[class="more"]')?.getAttribute('href')
          if (url && !listing.dealerId) {
            dealers.push({
              type: ResultType.item,
              data: {
                project: job.project,
                dataType: DataType.dealers,
                iso: job.data.iso,
                dealerId: url,
                dealerType: DealerType.dealer,
                itemId: getItemId(DataType.dealers, job.project, url),
                name,
                isActive: true,
                isPaying: true,
                // data: {
                //     agency:
                // }
              },
            })
          }

          listingsToUpdate.push({
            type: ResultType.item,
            data: {
              project: job.project,
              dataType: DataType.inventory,
              iso: job.data.iso,
              inventoryId: listing.inventoryId,
              itemId: listing.itemId,
              ...(url && !listing.dealerId && { dealerId: url }),
              ...(url && !listing.dealerId && { dealerType: DealerType.dealer }),
              ...(area && typeof listing.area !== 'number' && { area }),
            },
          })
        }
      })
  }

  return [...listingsToUpdate, ...dealers]
}
