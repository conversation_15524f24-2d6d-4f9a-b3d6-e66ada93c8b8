import { JSD<PERSON> } from 'jsdom'

import {
  MarketplacesDealerType,
  RealEstatesStockType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesPropertyType,
  ResultType,
} from '@datagatherers/datagatherers'
import { GotProxy, wait } from '@datagatherers/worker-utils'

import type { ControllerRuntime, JobDefinition, JobInstance, Town } from '@datagatherers/datagatherers'
import type { Filter, WithId } from 'mongodb'

interface LogicImmoCodes extends Town {
  lct_id_2: string
  lct_level: string
  lct_name_suggest: string
  lct_post_code: string
}
interface LogicImmoItems {
  info: {
    id: string
    client_id: string
    client_type: string
    distribution_type: number
    price: number
  }
  url: string | null | undefined
}

interface DealerMap {
  dealerId: string
  dealerType: MarketplacesDealerType
}

interface LogicImmoApi {
  items?: ItemsEntity[] | null
  pagination: Pagination
}
interface ItemsEntity {
  agencyAddress: string
  agencyCity: string
  agencyFeesPdf: string
  agencyFeesUrl: string
  agencyLogo: string
  agencyMail: string
  agencyName: string
  agencyPhone: string
  agencyRentFees: number
  agencySiret?: string | null
  agencyZipCode: string
  agentFlag?: string | null
  additionnalPrice: number
  annualCosts?: number | null
  area: number
  bedrooms: number
  boxes: number
  city: string
  condominiumFlag: boolean
  costsCountFrequency: string
  description: string
  energy: string
  energyVal: number
  feesFreePrice: number
  floors?: number | null
  gardenArea: number
  greenhouseEffect: string
  greenhouseEffectVal: number
  propertyId: number
  guaranteeDeposit: number
  hasBalcony: boolean
  hasBasement: boolean
  hasCaretaker: boolean
  hasDisabledAccess: boolean
  hasPool: boolean
  hasTerrace: boolean
  id: string
  inventoryFees: number
  furnishingState: number
  isPhoneMandatory: boolean
  isRenovated: boolean
  livingArea: number
  lotNumber?: string | null
  lotNumbers?: number | null
  monthlyEstimations?: number | null
  nbGarage: number
  offerName: string
  parkings: number
  payingFees?: string | null
  percentFees?: number | null
  percentFeesNetSeller?: number | null
  photos?: (string | null)[] | null
  price: number
  prestoId: string
  propertyCharges: number
  procedureFlag: number
  procedureText?: string | null
  promotionType: number
  propertyTypeId: number
  propertyType: string
  rooms: number
  squareMeterPrice: number
  title: string
  transactionTypeId: number
  updateDate: number
  valuesFees: number
  url: string
  zipCode: string
  reference: string
  firstOnlineDate: number
  availabilityRangeDate: number
  videoUrl: string
  boostExtendLevel: number
  isEnlargementResult: boolean
  agencyIdRcu?: string
  listingType: number
  bathrooms?: number | null
  chargesRegularisationFrequency?: string | null
  dueDateRent?: string | null
  floor?: number | null
  hasSupplementPrice?: string | null
  rentFrequency?: string | null
  frequencyCharges?: string | null
  dueDateCharges?: string | null
  showers?: number | null
}
interface Pagination {
  pageCount: number
  pageIndex: number
  pageSize: number
  totalCount: number
}

const LIMIT = 100

export async function codes(job: JobInstance) {
  let appendData: LogicImmoCodes[] = []
  let maxResults = 50000
  for (let offset = 0; offset < maxResults; offset += LIMIT) {
    const params = {
      query: '(lct_level:2)',
      limit: LIMIT,
      offset,
    }
    const items = await job.runtime
      .axios('https://api.logic-immo.com/localities', {
        params,
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36',
        },
      })
      .then((res) => res.data)
      .then((json) => json.result)
      .catch((error: Error) => job.addErrorToLog(error))

    if (!items || !items.data || items.data.length === 0) {
      break
    }

    if (items.total) {
      maxResults = items.total
    }

    appendData = [...appendData, ...items.data]

    await wait(500)
  }

  const results = appendData
    .filter((t) => !!t.lct_id_2)
    .map((item) => {
      const data = {
        project: job.project,
        itemId: `${item.lct_id_2}_${item.lct_level}_${item.lct_name_suggest}`,
        iso: Iso3166Alpha2.FR,
        blacklisted: false,
        data: item,
      }
      return {
        type: ResultType.town,
        data,
      }
    })

  return results
}

export async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const collection = runtime.collection<Town>('town')
  const filter: Filter<WithId<Town>> = {
    project: job.project,
    provider: job.provider,
    iso,
  }
  const data = await collection.find(filter).toArray()
  const jobs: JobDefinition[] = data
    .filter((item) => !!item.itemId)
    .map((town) => {
      return {
        ...job,
        data: {
          ...town.data,
          iso,
          ...(job.data?.parameters && { parameters: job.data?.parameters }),
        },
      }
    })

  return jobs
}

export async function extractListingsResidential(job: JobInstance) {
  const appendData: LogicImmoItems[] = []
  const gotProxy = new GotProxy(false, 2, 2, 10000)
  let maxPages = 500

  const { document } = new JSDOM().window

  const saleType = ['vente', 'location']

  for (const type of saleType) {
    for (let index = 1; index <= maxPages; index++) {
      await wait(500)

      const config = {
        method: 'GET' as const,
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36',
        },
        responseType: 'text' as const,
      }

      const data = await gotProxy
        .retryWithCaptcha<string>(
          job,
          `https://www.logic-immo.com/${type}-immobilier-${job.data?.lct_post_code},${job.data?.lct_id_2}_${job.data?.lct_level}/options/groupprptypesids=1,2,3,5,6,7,8,9,10,11,12,14,15/page=${index}`,
          config,
          'geetest-puzzle',
        )
        .catch((error) => job.addErrorToLog(error))

      if (typeof data === 'undefined' || !data?.length) {
        break
      }

      document.body.innerHTML = data

      const listings = document.querySelectorAll('#announcesList .standard-offer')

      const summary = document.querySelector('#titleSummary')?.innerHTML.replace(/\s+/g, '')
      const nrAds = Number(summary?.substring(0, summary.indexOf('annonces')))

      if (nrAds) {
        maxPages = Math.ceil(nrAds / 20)
      }

      if (!listings?.length) {
        break
      }

      const items: LogicImmoItems[] = []
      listings.forEach((element) => {
        if (element.innerHTML.length) {
          const string = element
            .querySelector('.announceDetailDecouvrir')
            ?.getAttribute('onclick')
            ?.replace(/\s+/g, '')
            ?.trim()
          const data = string?.substring(string.indexOf(`pushEvent('interaction',`) + 24)
          const info = JSON.parse(
            data?.substring(data.lastIndexOf('[{') + 1, data.lastIndexOf('}]') + 1).replace(/'/g, '"') as string,
          )
          const url = element.querySelector('.announceDetailDecouvrir')?.getAttribute('href')
          items.push({
            info,
            url,
          })
        }
      })
      appendData.push(...items)

      if (listings.length < 20) {
        break
      }
    }
  }
  const dealersMap: Map<string, DealerMap> = new Map()

  const resultsInventory = appendData.flatMap((item) => {
    if (!item?.info?.id) return []
    const stockType =
      item.info.distribution_type === 2
        ? RealEstatesStockType.residential_sales
        : RealEstatesStockType.residential_lettings
    const dealerType = item.info.client_type === 'pro' ? MarketplacesDealerType.dealer : MarketplacesDealerType.private
    const dealerId = item.info.client_id

    if (dealerId && !dealersMap.has(dealerId)) {
      try {
        dealersMap.set(dealerId, {
          dealerId,
          dealerType,
        })
      } catch (e) {
        console.error(e)
      }
    }

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: `listing_${job.data?.iso}_${item.info.id}`,
        iso: job.data?.iso,
        dataType: DataType.inventory,
        inventoryId: item.info.id,
        dealerId,
        stockType,
        ...(item.info.price && { price: item.info.price }),
        dealerType,
        ...(item.url && { url: item.url }),
        data: item.info,
      },
    }
  })

  const resultsDealers = [...dealersMap.values()].map((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === MarketplacesDealerType.dealer,
      },
    }
  })

  return [...resultsInventory, ...resultsDealers]
}

export async function extractListingsCommercial(job: JobInstance) {
  const appendData: LogicImmoItems[] = []
  const gotProxy = new GotProxy(false, 2, 2, 10000)
  let maxPages = 500

  const saleType = ['vente', 'location']

  const { document } = new JSDOM().window

  for (const type of saleType) {
    for (let index = 1; index <= maxPages; index++) {
      await wait(500)

      const config = {
        method: 'GET' as const,
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36',
        },
        responseType: 'text' as const,
      }

      const data = await gotProxy
        .retryWithCaptcha<string>(
          job,
          `https://www.logic-immo.com/${type}-immobilier-${job.data?.lct_post_code},${job.data?.lct_id_2}_${job.data?.lct_level}/options/groupprptypesids=4,13/page=${index}`,
          config,
          'geetest-puzzle',
        )
        .catch((error) => job.addErrorToLog(error))

      if (typeof data === 'undefined' || !data?.length) {
        break
      }

      document.body.innerHTML = data

      const listings = document.querySelectorAll('#announcesList .standard-offer')

      const summary = document.querySelector('#titleSummary')?.innerHTML.replace(/\s+/g, '')
      const nrAds = Number(summary?.substring(0, summary.indexOf('annonces')))

      if (nrAds) {
        maxPages = Math.ceil(nrAds / 20)
      }

      if (!listings?.length) {
        break
      }

      const items: LogicImmoItems[] = []
      listings.forEach((element) => {
        if (element.innerHTML.length) {
          const string = element
            .querySelector('.announceDetailDecouvrir')
            ?.getAttribute('onclick')
            ?.replace(/\s+/g, '')
            ?.trim()
          const data = string?.substring(string.indexOf(`pushEvent('interaction',`) + 24)
          const info = JSON.parse(
            data?.substring(data.lastIndexOf('[{') + 1, data.lastIndexOf('}]') + 1).replace(/'/g, '"') as string,
          )
          const url = element.querySelector('.announceDetailDecouvrir')?.getAttribute('href')

          items.push({
            info,
            url,
          })
        }
      })
      appendData.push(...items)

      if (listings.length < 20) {
        break
      }
    }
  }
  const dealersMap: Map<string, DealerMap> = new Map()

  const resultsInventory = appendData.flatMap((item) => {
    if (!item?.info?.id) return []
    const stockType =
      item.info.distribution_type === 2
        ? RealEstatesStockType.commercial_sales
        : RealEstatesStockType.commercial_lettings
    const dealerType = item.info.client_type === 'pro' ? MarketplacesDealerType.dealer : MarketplacesDealerType.private
    const dealerId = item.info.client_id

    if (dealerId && !dealersMap.has(dealerId)) {
      dealersMap.set(dealerId, {
        dealerId,
        dealerType,
      })
    }

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: `listing_${job.data?.iso}_${item.info.id}`,
        iso: job.data?.iso,
        dataType: DataType.inventory,
        inventoryId: item.info.id,
        dealerId,
        stockType,
        ...(item.info.price && { price: item.info.price }),
        dealerType,
        url: item.url ? item.url : undefined,
        data: item.info,
      },
    }
  })

  const resultsDealers = [...dealersMap.values()].flatMap((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === MarketplacesDealerType.dealer,
      },
    }
  })

  return [...resultsInventory, ...resultsDealers]
}

export async function extractListings(job: JobInstance) {
  let appendData: ItemsEntity[] = []
  let maxItems = 10000
  const gotProxy = new GotProxy(false, 2, 2, 10000, false)

  for (let offset = 0; offset <= maxItems; offset += LIMIT) {
    await wait(500)

    const config = {
      method: 'POST' as const,
      headers: {
        'user-agent': 'LogicImmo/10.5.1 Dalvik/2.1.0 (Linux; U; Android 11; sdk_gphone_x86_arm Build/RSR1.201013.001)',
        'content-type': 'application/json',
      },
      json: {
        searchParameters: { LIMIT, offset, sortBy: 1 },
        listingSearchCriterias: {
          locations: [{ id: job.data?.lct_id_2, type: job.data?.lct_level, postalcode: job.data?.lct_post_code }],
          propertyTypesIds: [1, 2, 6, 7, 3, 12, 9, 14, 8, 15, 5, 13, 4, 11, 16],
          transactionTypesIds: [1, 2],
          pictureOnly: false,
        },
      },
      responseType: 'json' as const,
      retry: {
        limit: 5,
      },
    }

    const data = await gotProxy.retryWithCaptcha<LogicImmoApi>(
      job,
      'https://api-logicimmo.svc.groupe-seloger.com/api/v2/listings/search',
      config,
      'geetest-puzzle',
    )

    if (!data?.items?.length) {
      break
    }

    if (data.pagination.totalCount) {
      maxItems = data.pagination.totalCount
    }

    appendData = [...appendData, ...data.items]

    if (data.items.length < LIMIT) {
      break
    }
  }

  const dealersMap: Map<string, DealerMap> = new Map()

  const resultsInventory = appendData
    .filter((t) => !!t.id)
    .map((item) => {
      const stockType = getStockType(item)
      const propertyScope = getPropertyScope(item)
      const propertyType = getPropertyType(item)
      const businessType =
        item.transactionTypeId === 1 ? 'sale' : item.transactionTypeId === 2 ? RealEstatesBusinessType.rent : undefined
      const dealerType = item.agencyName ? MarketplacesDealerType.dealer : MarketplacesDealerType.private
      const dealerId = item.agencyName
      const area = item.area

      if (!!dealerId && !dealersMap.has(dealerId)) {
        dealersMap.set(dealerId, {
          dealerId,
          dealerType,
        })
      }

      const data = {
        project: ProjectType.realestates,
        itemId: `listing_${job.data?.iso}_${item.id}`,
        iso: job.data?.iso,
        dataType: DataType.inventory,
        inventoryId: item.id,
        dealerId,
        stockType,
        propertyScope,
        propertyType,
        businessType,
        area,
        ...(item.price && { price: item.price }),
        dealerType,
        url: item.url ? item.url : undefined,
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  const resultsDealers = [...dealersMap.values()]
    .filter((a) => !!a.dealerId)
    .map((item) => {
      const data = {
        project: ProjectType.realestates,
        itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: item.dealerType === MarketplacesDealerType.dealer,
      }

      return {
        type: ResultType.item,
        data,
      }
    })

  return [...resultsInventory, ...resultsDealers]
}

function getStockType(item: ItemsEntity): RealEstatesStockType {
  if (item.transactionTypeId === 1) {
    if (propertyMap[RealEstatesPropertyScope.residential].includes(item.propertyTypeId)) {
      return RealEstatesStockType.residential_sales
    } else if (propertyMap[RealEstatesPropertyScope.commercial].includes(item.propertyTypeId)) {
      return RealEstatesStockType.commercial_sales
    } else if (propertyMap[RealEstatesStockType.other].includes(item.propertyTypeId)) {
      return RealEstatesStockType.other
    }
  } else if (item.transactionTypeId === 2) {
    if (propertyMap[RealEstatesPropertyScope.residential].includes(item.propertyTypeId)) {
      return RealEstatesStockType.residential_lettings
    } else if (propertyMap[RealEstatesPropertyScope.commercial].includes(item.propertyTypeId)) {
      return RealEstatesStockType.commercial_lettings
    } else if (propertyMap[RealEstatesStockType.other].includes(item.propertyTypeId)) {
      return RealEstatesStockType.other
    }
  }

  return RealEstatesStockType.other
}

function getPropertyScope(item: ItemsEntity): RealEstatesPropertyScope {
  if (!item.propertyTypeId) {
    return RealEstatesPropertyScope.other
  }

  if (item.propertyTypeId === 4 || item.propertyTypeId === 9 || item.propertyTypeId === 16) {
    return RealEstatesPropertyScope.commercial
  }
  if (item.propertyTypeId === 3 || item.propertyTypeId == 13 || item.propertyTypeId == 15) {
    return RealEstatesPropertyScope.land
  }
  if (item.propertyTypeId === 12) {
    return RealEstatesPropertyScope.other
  }

  return RealEstatesPropertyScope.residential
}

function getPropertyType(item: ItemsEntity): RealEstatesPropertyType {
  if (!item.propertyTypeId) {
    return RealEstatesPropertyType.other
  }

  switch (item.propertyTypeId) {
    case 1:
      return RealEstatesPropertyType.apartment
    case 2:
      return RealEstatesPropertyType.house
    case 5:
      return RealEstatesPropertyType.apartment
    case 6:
      return RealEstatesPropertyType.apartment
    case 7:
      return RealEstatesPropertyType.house
    case 8:
      return RealEstatesPropertyType.house
    case 11:
      return RealEstatesPropertyType.house
    case 14:
      return RealEstatesPropertyType.house
    case 3:
      return RealEstatesPropertyType.other
    case 4:
      return RealEstatesPropertyType.office
    case 9:
      return RealEstatesPropertyType.retail
    case 16:
      return RealEstatesPropertyType.retail
    case 12:
      return RealEstatesPropertyType.parking
    case 13:
      return RealEstatesPropertyType.agriculture
    case 15:
      return RealEstatesPropertyType.agriculture

    default:
      return RealEstatesPropertyType.other
  }
}

export const propertyMap = {
  residential: [
    1, // Appartement
    2, // House
    5, // Building
    6, // Loft
    7, // Villa
    8, // Chalet
    11, // Chateau
    14, // Property
  ],
  commercial: [
    3, // Terrain
    4, // Bureau
    9, // Commerce
    16, // Hotel
  ],
  other: [
    12, //Parking
    13, // Farm
    15, // Wine Property
  ],
}
