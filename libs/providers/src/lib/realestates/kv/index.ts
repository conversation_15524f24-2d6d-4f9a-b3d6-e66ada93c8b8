import { startOfISOWeek } from 'date-fns'
import { J<PERSON><PERSON> } from 'jsdom'
import UserAgent from 'user-agents'

import {
  MarketplacesDealerType,
  RealEstatesPropertyType,
  RealEstatesStockType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  RealEstatesPropertyScope,
  ResultType,
  ClassifiedsStockType,
  RealEstatesBusinessType,
  DealerType,
  TimeUnit,
} from '@datagatherers/datagatherers'
import {
  fetchFingerprintingParams,
  getItemId,
  getProjectProviderDataType,
  getREArea,
  getSnapTime,
  numberRegex,
  randomWait,
  splitRangeSize,
} from '@datagatherers/worker-utils'
import { ResponseDetailsError } from '@datagatherers/worker-utils/lib/errors'

import {
  dealTypeMap,
  DEVELOPMENTS_BLACKLIST,
  DEVELOPMENTS_URI_COMPONENT,
  MAX_CUSTOM_RETRIES,
  requestVars,
  LISTINGS_LIMIT,
  NEW_DEVELOPMENT_TYPES,
  <PERSON>OKER_SEARCH_SIZE,
  MAX_CUSTOM_RETRIES_LOW,
  MAX_CUSTOM_RETRIES_HIGH,
  NEW_DEVELOPMENTS_LISTINGS_PER_PAGE,
  PARISH_MAPPINGS,
} from './constants'
import {
  BASE_URL,
  brokerSearchRequestOptions,
  brokerSearchRequestOptionsHTML,
  LISTINGS_URL,
  newDevelopmentsDetailsRequestOptions,
  newDevelopmentsRequestOptions,
} from './requests'

import type { BrokerSearchResponse, KVDevelopments } from './types'
import type {
  ControllerRuntime,
  Item,
  JobDefinition,
  JobInstance,
  Result,
  Snapshot,
} from '@datagatherers/datagatherers'

export async function extractListingsTotal(job: JobInstance) {
  const CLOUD_FLARE_MAX = 3

  const urls = [
    'https://www.kv.ee/en/search?deal_type=20', // apartments
    'https://www.kv.ee/en/search?deal_type=21', // houses
    'https://www.kv.ee/en/search?deal_type=25', // sharehouses
    'https://www.kv.ee/en/search?deal_type=22', // plots
    'https://www.kv.ee/en/search?deal_type=23', // premises
    'https://www.kv.ee/en/search?deal_type=24', // garages
  ]

  const iso = Iso3166Alpha2.EE
  const now = new Date()
  const snapTime = getSnapTime(now, TimeUnit.weeks)

  let totalAds = 0

  const getCountFromHTML = (html: string): number => {
    const { document } = new JSDOM(html).window
    const raw = document.querySelector('.left.hide-on-fixit .large.stronger')?.textContent?.replace(/[^0-9]/g, '')
    return Number(raw || 0)
  }

  const refreshHeaders = async (target: string) => {
    const { headersGot } = await fetchFingerprintingParams(job.runtime, target, 'desktop', undefined, 15000)
    return {
      ...headersGot,
      'accept-language': 'en-US,en;q=0.9',
      referer: 'https://www.kv.ee/en/',
    }
  }

  await job.runtime.page.setUserAgent(new UserAgent({ deviceCategory: 'desktop' }).toString())
  await job.runtime.page.goto('https://www.kv.ee/en/', { waitUntil: 'domcontentloaded' })
  await randomWait(800, 1500)

  for (const targetUrl of urls) {
    let headers = await refreshHeaders(targetUrl)
    let cloudflareHits = 0
    let success = false

    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES_HIGH; attempt++) {
      try {
        const res = await job.runtime.got(new URL(targetUrl), {
          headers,
          timeout: { request: 20000 },
          http2: false,
        })

        if (res.statusCode === 403) throw new Error('403')
        const body = res.body
        if (body.includes('cf-challenge') || body.includes('Cloudflare')) {
          cloudflareHits++
          if (cloudflareHits >= CLOUD_FLARE_MAX) throw new Error('cloudflare_block')
          throw new Error('cloudflare')
        }

        const count = getCountFromHTML(body)
        if (!count) {
          await job.runtime.page.setUserAgent(new UserAgent({ deviceCategory: 'desktop' }).toString())
          await job.runtime.page.goto(targetUrl, { waitUntil: 'networkidle2' })
          const html = await job.runtime.page.content()
          const altCount = getCountFromHTML(html)
          totalAds += altCount
        } else {
          totalAds += count
        }
        success = true
        break
      } catch (error) {
        const terminal = attempt + 1 >= MAX_CUSTOM_RETRIES_HIGH
        if (terminal) {
          job.addErrorToLog(
            new Error(`Failed totals URL ${targetUrl} after ${MAX_CUSTOM_RETRIES_HIGH} attempts: ${error.message}`),
          )
          break
        }

        await randomWait(1000 + attempt * 250, 1800 + attempt * 300)

        if (['cloudflare', '403', 'cloudflare_block'].includes(error.message)) {
          await job.runtime.page.setUserAgent(new UserAgent({ deviceCategory: 'desktop' }).toString())
          await job.runtime.page.goto('https://www.kv.ee/en/', { waitUntil: 'domcontentloaded' })
          await randomWait(600, 1200)
        }

        headers = await refreshHeaders(targetUrl)
        continue
      }
    }

    if (!success) continue
    await randomWait(400, 900)
  }

  if (!job.runtime.skipStoreData) {
    const stats = [{ count: totalAds }]
    await job.runtime.collection<Snapshot>('snapshot').updateOne(
      {
        project: job.project,
        controllerId: '-1',
        provider: 'kv_totals',
        iso,
        snapTime,
        snapTimeUnit: TimeUnit.weeks,
        dataType: DataType.inventory,
      },
      {
        $setOnInsert: {
          project: job.project,
          controllerId: '-1',
          provider: 'kv_totals',
          iso,
          snapTime,
          snapTimeUnit: TimeUnit.weeks,
          dataType: DataType.inventory,
          createdAt: now,
        },
        $set: {
          updatedAt: now,
          stats,
        },
      },
      { upsert: true, writeConcern: { w: 'majority' } },
    )
  }

  return []
}

export async function scheduleListings(
  job: JobDefinition,
  runtime: ControllerRuntime,
  developmentsCrawl = false,
  iso = Iso3166Alpha2.EE,
) {
  const jobs = []

  const uniqueParishes = Array.from(
    new Map(
      PARISH_MAPPINGS.map((p) => [
        p.parishId,
        {
          countyId: p.newCountyId || p.countyId,
          parishId: p.parishId,
        },
      ]),
    ).values(),
  )

  for (const parish of uniqueParishes) {
    jobs.push(
      ...Object.keys(dealTypeMap).flatMap((businessType) => {
        if (
          !dealTypeMap[businessType] ||
          (developmentsCrawl && DEVELOPMENTS_BLACKLIST.includes(dealTypeMap[businessType]))
        )
          return []

        return dealTypeMap[businessType].flatMap((deal) => ({
          ...job,
          data: {
            countyId: parish.countyId,
            parishId: parish.parishId,
            searchType: ClassifiedsStockType.new,
            iso,
            dealType: deal,
            businessType,
            developmentsCrawl,
          },
        }))
      }),
    )
  }

  return jobs
}

export const extractListings = async (job: JobInstance, results: Result[] = []): Promise<Result[]> => {
  const searchParams = {
    deal_type: job.data.dealType,
    search_type: job.data?.searchType,
    county: job.data.countyId,
    parish: job.data.parishId,
    page_size: requestVars.maxItems,
    orderby: requestVars.sortBy,
    start: 0,
    limit: requestVars.maxItems,
    ...(job.data.developmentsCrawl && DEVELOPMENTS_URI_COMPONENT),
  }

  await job.runtime.page.setUserAgent(new UserAgent({ deviceCategory: 'desktop' }).toString())
  await job.runtime.page.setViewport({ width: 1366, height: 900, deviceScaleFactor: 1 })
  await job.runtime.page.goto('https://www.kv.ee/en/', { waitUntil: 'domcontentloaded' })
  await randomWait(500, 1200)

  const url = new URL(LISTINGS_URL)
  Object.keys(searchParams).forEach((k) => url.searchParams.set(k, String(searchParams[k])))

  let headersTemplate: Record<string, string>
  let total = 0

  const refreshHeaders = async (target: string) => {
    const { headersGot } = await fetchFingerprintingParams(job.runtime, target, 'desktop', undefined, 15000)
    headersTemplate = {
      ...headersGot,
      'accept-language': 'en-US,en;q=0.9',
      referer: 'https://www.kv.ee/en/',
    }
  }

  await refreshHeaders(url.href)

  const fetchPage = async (start: number): Promise<string> => {
    const pageUrl = new URL(url.href)
    pageUrl.searchParams.set('start', start.toString())
    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES_HIGH; attempt++) {
      try {
        const response = await job.runtime.got(pageUrl, {
          headers: headersTemplate,
          timeout: { request: 20000 },
          http2: false,
        })
        if (response.statusCode === 403) throw new Error('403')
        const body = response.body
        if (body.includes('cf-challenge') || body.includes('Cloudflare')) throw new Error('cloudflare')
        return body
      } catch {
        if (attempt + 1 >= MAX_CUSTOM_RETRIES_HIGH)
          throw new Error(`Failed to fetch page start=${start} after ${MAX_CUSTOM_RETRIES_HIGH} attempts`)
        await randomWait(800, 1800)
        await refreshHeaders(pageUrl.href)
      }
    }
    throw new Error('Unreachable fetchPage state')
  }

  const parseHTML = (html: string, start: number, acc: Result[]) => {
    const { document } = new JSDOM(html).window
    if (start === 0) {
      const totalRaw = document.querySelector('.sorter span')?.textContent?.replace(/\D/g, '')
      total = Number(totalRaw || 0)
    }
    const items = Array.from(document.querySelectorAll('.results-default article'))
    if (!items.length) return
    for (const element of items) {
      const id = element.getAttribute('data-object-id')
      if (!id) continue
      const href = element.getAttribute('data-object-url')
      const price = Number(element.querySelector('.price > div')?.getAttribute('data-price') ?? 0)
      const areaRaw = element.querySelector('.area')?.textContent
      const area = getREArea(Number(areaRaw?.replace(numberRegex, '') ?? 0))
      const advertisingLevel = Number(element.querySelector('.object-promoted')?.textContent) || undefined
      const dealerType = element
        .querySelector('a[data-add-filter="company_id_check"')
        ?.textContent?.trim()
        .includes('Directly from owner')
        ? 'private'
        : 'dealer'
      const { propertyType, propertyScope, stockType, businessType } = determinePropertyAttributes(
        element.getAttribute('class') || '',
        job.data.businessType,
      )
      const item = {
        type: ResultType.item,
        data: {
          project: job.project,
          inventoryId: id,
          itemId: getItemId(DataType.inventory, job.project, id),
          iso: job.data.iso,
          dataType: DataType.inventory,
          url: `https://www.kv.ee/en${href}`,
          price,
          currency: 'EUR',
          businessType,
          propertyType,
          propertyScope,
          stockType,
          area,
          dealerId: 'unknown',
          dealerType,
          data: {
            isDevelopment: job.data.developmentsCrawl,
            isNewDevelopment: false,
            advertisment_level: advertisingLevel ?? 0,
            raw_advertisment_level: advertisingLevel,
            isProcessed: false,
          },
        },
      }
      acc.push(item)
    }
  }

  try {
    const firstHTML = await fetchPage(0)
    parseHTML(firstHTML, 0, results)
  } catch {
    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES; attempt++) {
      try {
        await job.runtime.page.setUserAgent(new UserAgent({ deviceCategory: 'desktop' }).toString())
        await job.runtime.page.goto(url.href, { waitUntil: 'networkidle2' })
        const html = await job.runtime.page.content()
        parseHTML(html, 0, results)
        break
      } catch (e) {
        if (attempt + 1 >= MAX_CUSTOM_RETRIES) throw e
        await randomWait(1500, 2500)
      }
    }
  }

  if (total <= requestVars.maxItems) return results

  for (let start = requestVars.maxItems; start < total; start += requestVars.maxItems) {
    await randomWait(500, 1400)
    try {
      const html = await fetchPage(start)
      parseHTML(html, start, results)
    } catch {
      try {
        const pageUrl = new URL(url.href)
        pageUrl.searchParams.set('start', start.toString())
        await job.runtime.page.setUserAgent(new UserAgent({ deviceCategory: 'desktop' }).toString())
        await job.runtime.page.goto(pageUrl.href, { waitUntil: 'domcontentloaded' })
        const html = await job.runtime.page.content()
        parseHTML(html, start, results)
      } catch (inner) {
        job.addErrorToLog(new Error(`Failed page segment start=${start}: ${inner.message}`))
        continue
      }
    }
  }

  return results
}

export async function scheduleExtractNewDevelopments(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso = Iso3166Alpha2.EE,
): Promise<JobDefinition[]> {
  const jobs: JobDefinition[] = []

  await runtime.page.setViewport({
    width: 1366,
    height: 900,
    isMobile: false,
    isLandscape: false,
    hasTouch: false,
  })

  const parseTotalFromHTML = (html: string): number | undefined => {
    const { document } = new JSDOM(html).window
    return Number(document.querySelector('.btn-box span')?.textContent?.replace(numberRegex, '')) || undefined
  }

  const tryParseJSON = (body: string) => {
    try {
      return JSON.parse(body) as KVDevelopments
    } catch {
      return null
    }
  }

  const refreshHeaders = async (target: string) => {
    const { headersGot } = await fetchFingerprintingParams(runtime, target, 'desktop', undefined, 15000)
    return {
      ...headersGot,
      'accept-language': 'en,en-US;q=0.5',
      referer: 'https://www.kv.ee/en/kinnisvara/uusarendused',
      'x-requested-with': 'XMLHttpRequest',
    }
  }

  const fetchPage = async (url: URL, baseHeaders: Record<string, string>): Promise<string> => {
    let headersTemplate = baseHeaders
    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES; attempt++) {
      try {
        const res = await runtime.got(url, {
          headers: headersTemplate,
          timeout: { request: 20000 },
          http2: false,
        })
        if (res.statusCode === 403) throw new Error('403')
        const body = res.body
        if (body.includes('cf-challenge') || body.includes('Cloudflare')) throw new Error('cloudflare')
        return body
      } catch (e) {
        if (attempt + 1 >= MAX_CUSTOM_RETRIES) throw e
        await randomWait(800, 1800)
        headersTemplate = await refreshHeaders(url.href)
      }
    }
    throw new Error('Unreachable fetchPage state')
  }

  developmentsLoop: for (const devTypeId in NEW_DEVELOPMENT_TYPES) {
    const req = newDevelopmentsRequestOptions(devTypeId, 0)

    let totalForType: number | undefined

    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES; attempt++) {
      try {
        const headers = await refreshHeaders(req.url.href)
        const body = await fetchPage(req.url, headers)

        const parsedJSON = tryParseJSON(body)
        if (parsedJSON?.premiumDevelopments || parsedJSON?.developments) {
          totalForType = (parsedJSON.premiumDevelopments?.length || 0) + (parsedJSON.developments?.length || 0)
        } else {
          totalForType = parseTotalFromHTML(body)
        }

        if (!totalForType) {
          await runtime.page.goto(req.url.href, { waitUntil: 'networkidle2' })
          const html = await runtime.page.content()
          totalForType = parseTotalFromHTML(html)
        }

        if (totalForType === 0) {
          continue developmentsLoop
        }
        if (!totalForType) throw new Error('Unable to extract developments total')

        jobs.push(
          ...splitRangeSize(0, totalForType, NEW_DEVELOPMENTS_LISTINGS_PER_PAGE).map((range) => ({
            ...job,
            data: {
              iso,
              ...range,
              devTypeId,
            },
          })),
        )
        continue developmentsLoop
      } catch (error) {
        if (attempt + 1 >= MAX_CUSTOM_RETRIES) throw error
        await randomWait(1200, 2500)
        continue
      }
    }
  }

  return jobs
}

export async function extractNewDevelopments(job: JobInstance) {
  const MAX_CUSTOM_RETRIES_HIGH = 6
  const MAX_CUSTOM_RETRIES = 4
  const devTypeId = job.data.devTypeId
  const offset = job.data.rangeMin ?? 0

  const newDevelopmentOptions = newDevelopmentsRequestOptions(devTypeId, offset)

  let headersTemplate: Record<string, string> | undefined
  const dealersMap = new Map<string, Result>()
  let developmentsRaw = []

  const refreshHeaders = async (target: string) => {
    const { headersGot } = await fetchFingerprintingParams(job.runtime, target, 'desktop', undefined, 15000)
    headersTemplate = {
      ...headersGot,
      'accept-language': 'en-US,en;q=0.9',
      referer: 'https://www.kv.ee/en/',
    }
  }

  const fetchPage = async (): Promise<string> => {
    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES_HIGH; attempt++) {
      try {
        if (!headersTemplate) await refreshHeaders(newDevelopmentOptions.url.href)
        newDevelopmentOptions.options.headers = Object.assign(
          {},
          newDevelopmentOptions.options.headers,
          headersTemplate,
        )
        const response = await job.runtime.got(newDevelopmentOptions.url, newDevelopmentOptions.options)
        if (response.statusCode === 403) throw new Error('403')
        if (!response.body) throw new Error('Empty body')
        if (response.body.includes('cf-challenge') || response.body.includes('Cloudflare'))
          throw new Error('cloudflare')
        return response.body
      } catch (e) {
        if (attempt + 1 >= MAX_CUSTOM_RETRIES_HIGH) throw e
        await randomWait(800, 1800)
        await refreshHeaders(newDevelopmentOptions.url.href)
      }
    }
    throw new Error('Unreachable fetchPage state')
  }

  const parseJSON = (body: string) => {
    let parsed: KVDevelopments
    try {
      parsed = JSON.parse(body)
    } catch (error) {
      throw new Error('Unable to parse JSON' + error.message)
    }
    developmentsRaw = [...parsed.premiumDevelopments, ...parsed.developments]
  }

  for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES; attempt++) {
    try {
      const body = await fetchPage()
      parseJSON(body)
      break
    } catch (e) {
      if (attempt + 1 >= MAX_CUSTOM_RETRIES) throw e
      await randomWait(1200, 2500)
      headersTemplate = undefined
    }
  }

  if (!developmentsRaw?.length) throw new Error('Unable to extract developments')

  const results: Result[] = developmentsRaw.map((development) => {
    const id = development.development_id.toString()
    const itemId = getItemId(DataType.inventory, job.project, id)
    const dealerId = development.broker_id.toString()
    const { propertyScope, propertyType, businessType, stockType } = NEW_DEVELOPMENT_TYPES[devTypeId]

    const productType = development.premium ? 'ELITE' : 'STANDARD'
    const advertisingCost = productType === 'ELITE' ? 499 : 199

    if (!dealersMap.has(dealerId)) {
      const res = {
        type: ResultType.item,
        data: {
          project: ProjectType.realestates,
          itemId: getItemId(DataType.dealers, ProjectType.realestates, dealerId),
          iso: job.data.iso,
          dataType: DataType.dealers,
          dealerId,
          dealerType: 'dealer' as const,
          isActive: true,
          isPaying: true,
          productType,
          data: {
            packageName: productType,
            advertisingCost,
            dealerName: development.company_name,
          },
        },
      } as Result
      dealersMap.set(dealerId, res)
    }

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId,
        iso: job.data.iso,
        dataType: DataType.inventory,
        inventoryId: id,
        dealerId,
        stockType,
        businessType,
        propertyScope,
        propertyType,
        productType,
        data: {
          advertisingCost,
          isDevelopment: true,
          isNewDevelopment: true,
          packet: development.packet,
          premium: development.premium,
          is_rally_dev: development.is_rally_dev,
          isProcessed: false,
        },
      },
    }
  })
  return [...results, ...Array.from(dealersMap.values())]
}

export async function scheduleExtractNewDevelopmentListingDetails(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const listings = await runtime
    .collection<Item>('item')
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        'data.isNewDevelopment': true,
        'data.isProcessed': false,
      },
      {
        projection: {
          _id: 0,
          inventoryId: 1,
          itemId: 1,
          data: 1,
        },
      },
    )
    .toArray()
  if (!listings?.length) throw new Error('No unprocessed listings')

  return listings.map((listing) => ({
    ...job,
    data: {
      iso,
      listing,
    },
  }))
}

export async function extractNewDevelopmentListingDetails(job: JobInstance) {
  const inventoryId = job.data.listing.inventoryId

  const locale = job.data.iso === Iso3166Alpha2.EE ? 'et' : 'en'
  const requestOptions = newDevelopmentsDetailsRequestOptions(Number(inventoryId), locale)

  let headersTemplate: Record<string, string>

  const refreshHeaders = async (target: string) => {
    const { headersGot } = await fetchFingerprintingParams(job.runtime, target, 'desktop', undefined, 30000)
    headersTemplate = {
      ...headersGot,
      'accept-language': 'en,en-US;q=0.5',
      referer: `https://www.kv.ee/${locale}/kinnisvara/uusarendused`,
      'x-requested-with': 'XMLHttpRequest',
    }
  }

  const safeParse = (body: string) => {
    try {
      return JSON.parse(body)
    } catch {
      return null
    }
  }

  const fetchJSON = async () => {
    await refreshHeaders(requestOptions.url.href)
    for (let attempt = 0; attempt < MAX_CUSTOM_RETRIES_LOW; attempt++) {
      try {
        requestOptions.options.headers = { ...requestOptions.options.headers, ...headersTemplate }
        const res = await job.runtime.got(requestOptions.url, {
          ...requestOptions.options,
          timeout: { request: 20000 },
          http2: false,
        })
        if (res.statusCode === 403) throw new Error('403')
        const parsed = safeParse(res.body)
        if (!parsed) throw new Error('parse_error')
        return parsed
      } catch (e) {
        if (attempt + 1 >= MAX_CUSTOM_RETRIES_LOW) throw e
        await randomWait(800, 1800)
        await refreshHeaders(requestOptions.url.href)
      }
    }
    throw new Error('unreachable_fetch_state')
  }

  const payload = await fetchJSON()
  let dealerId: string | undefined
  let companyId: string | undefined
  let premium: boolean | undefined
  let packet: number | undefined
  let price: number | undefined
  let objectsInfo
  let devObjects

  if (payload.development) {
    dealerId = payload.development.broker_id?.toString()
    companyId = payload.development.company_id?.toString()
    premium = payload.development.premium === true || payload.development.premium === 't'
    packet = payload.development.packet
    objectsInfo = payload.development.objects_info
    devObjects = payload.devObjects
  } else if (payload.object) {
    dealerId = payload.object.broker_id?.toString()
    companyId = payload.object.company_id?.toString()
    premium = payload.object.premium
    packet = payload.object.packet
    const priceRaw = Number(payload.object.price)
    price = isNaN(priceRaw) ? undefined : priceRaw
  } else {
    // Wrapper
  }

  const dealerType = companyId === '237' ? DealerType.private : DealerType.dealer

  const enrichment: Record<string, string | boolean | number | object> = {
    companyId,
    premium,
    packet,
    isProcessed: true,
  }

  if (objectsInfo) {
    enrichment.objectsInfo = {
      roomsMin: objectsInfo.rooms_min,
      roomsMax: objectsInfo.rooms_max,
      areaMin: objectsInfo.area_min,
      areaMax: objectsInfo.area_max,
      priceMin: objectsInfo.price_min,
      priceMax: objectsInfo.price_max,
      objectCount: objectsInfo.object_count,
    }
  }

  if (devObjects?.length) {
    enrichment.devObjects = devObjects.map((o) => ({
      objectId: o.object_id,
      price: o.object_price,
      rooms: o.object_rooms,
      area: o.object_area_total,
      floor: o.object_floor,
      floors: o.num_floors,
      newPrice: o.new_price,
    }))
  }

  const partialListing: Result = {
    type: ResultType.item,
    data: {
      itemId: job.data.listing.itemId,
      inventoryId,
      iso: job.data.iso,
      dealerId,
      dealerType,
      ...(price ? { price, currency: 'EUR' } : {}),
      data: {
        ...job.data.listing.data,
        ...enrichment,
      },
    },
  } as Result
  return [partialListing]
}

export async function scheduleDealers(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  let { headersGot: headers } = await fetchFingerprintingParams(runtime, LISTINGS_URL, 'desktop', undefined, 10000)
  const requestOptions = brokerSearchRequestOptions(0)
  const retry_selector = MAX_CUSTOM_RETRIES_HIGH
  let count: number

  for (let current_retry = 0; current_retry < retry_selector; current_retry++) {
    try {
      requestOptions.options.headers = Object.assign({}, requestOptions.options.headers, headers)

      count = await runtime.got(requestOptions.url, requestOptions.options).then((response) => {
        if (response.statusCode === 403) throw new Error('403 Forbidden')
        let parsedBody: BrokerSearchResponse
        try {
          parsedBody = JSON.parse(response.body)
        } catch (error) {
          throw new ResponseDetailsError('Unable to parse JSON:' + error.message, response)
        }
        if (!parsedBody.counts || isNaN(Number(parsedBody.counts)))
          throw new ResponseDetailsError('Invalid or absent total count', response)

        return Number(parsedBody.counts)
      })
      break
    } catch (error) {
      if (current_retry + 1 >= retry_selector) throw error
      await randomWait(2000, 3000)
      const { headersGot } = await fetchFingerprintingParams(runtime, LISTINGS_URL, 'desktop', undefined, 10000)
      headers = headersGot
    }
  }
  if (!count) throw new Error('Unable to extract count')
  return splitRangeSize(0, count, BROKER_SEARCH_SIZE).map((range) => ({
    ...job,
    data: {
      iso,
      ...range,
    },
  }))
}

export async function extractDealers(job: JobInstance) {
  let { headersGot: headers } = await fetchFingerprintingParams(job.runtime, BASE_URL, 'desktop', undefined, 10000)

  const requestOptions = brokerSearchRequestOptionsHTML(job.data.rangeMin)

  let dealers: Result[]

  for (let current_retry = 0; current_retry < MAX_CUSTOM_RETRIES; current_retry++) {
    try {
      requestOptions.options.headers = Object.assign({}, requestOptions.options.headers, headers)

      dealers = await job.runtime.got(requestOptions.url, requestOptions.options).then((response) => {
        if (response.statusCode === 403) throw new Error('Error 403, retry limit reached')

        const { document } = new JSDOM(response.body).window

        const tableRows = document.querySelectorAll('#search-results tbody tr')
        if (!tableRows?.length) {
          throw new ResponseDetailsError('No dealer rows found in HTML table', response)
        }

        return Array.from(tableRows).flatMap((row) => {
          const dealerLink = row.querySelector('td:first-child a[href*="/maakler/"]')
          if (!dealerLink) return []

          const dealerName = dealerLink.textContent?.trim()
          const dealerProfileUrl = dealerLink.getAttribute('href')
          const dealerId = dealerProfileUrl?.split('/maakler/')[1] || 'unknown'

          const companyCell = row.querySelector('td.hide-on-mobile a[href*="/firma/"]')
          let companyName = 'unknown'
          let companyId = 'unknown'

          if (companyCell) {
            companyName = companyCell.textContent?.trim() || 'unknown'
            const companyUrl = companyCell.getAttribute('href')
            companyId = companyUrl?.split('/firma/')[1] || 'unknown'
          } else {
            const companyCellText = row.querySelector('td.hide-on-mobile')?.textContent?.trim()
            if (companyCellText && companyCellText !== dealerName) {
              companyName = companyCellText
            }
          }

          const listingCountCell = row.querySelector('td:nth-child(3)')
          const adCount = Number(listingCountCell?.textContent?.trim()) || 0

          const contactButton = row.querySelector('a[href*="/maakler/contact/"]')
          const contactUrl = contactButton?.getAttribute('href')
          const contactId = contactUrl?.split('/contact/')[1] || 'unknown'

          const dealerType = MarketplacesDealerType.dealer
          const itemId = getItemId(DataType.dealers, ProjectType.realestates, dealerId)
          const { packageName, advertisingCost } = determinePackageTier(adCount)

          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId,
              iso: job.data.iso,
              url: `https://www.kv.ee${dealerProfileUrl}`,
              dataType: DataType.dealers,
              dealerId,
              dealerType,
              isActive: true,
              isPaying: dealerType === MarketplacesDealerType.dealer,
              productType: packageName,
              data: {
                adCount,
                packageName,
                advertisingCost,
                dealerName,
                companyName,
                companyId,
                contactId,
                isProcessed: false,
              },
            },
          }
        })
      })
      break
    } catch (error) {
      if (current_retry + 1 >= MAX_CUSTOM_RETRIES) throw error
      await randomWait(2000, 3000)
      const { headersGot } = await fetchFingerprintingParams(job.runtime, BASE_URL, 'desktop', undefined, 10000)
      headers = headersGot
    }
  }

  return dealers
}

export async function scheduleDealerDetails(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  await runtime.collection<Item>('item').updateMany(
    {
      projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
      iso,
    },
    {
      $unset: {
        'data.listingIds': 1,
      },
    },
  )

  const dealers = await runtime
    .collection<Item>('item')
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        'data.isProcessed': false,
      },
      {
        projection: {
          _id: 0,
          itemId: 1,
          dealerId: 1,
          url: 1,
          data: 1,
        },
      },
    )
    .toArray()
  if (!dealers?.length) throw new Error('No unprocessed dealers')

  return dealers.map((dealer) => ({
    ...job,
    data: {
      iso,
      dealer,
    },
  }))
}

export async function extractDealerDetails(job: JobInstance) {
  const listingIds: string[] = []
  let offsetLimit = 1

  await job.runtime.page.setViewport({
    width: 1920,
    height: 1080,
    isMobile: false,
    isLandscape: false,
    hasTouch: false,
  })
  paginationLoop: for (let offset = 0; offset < offsetLimit; offset += 50) {
    retryLoop: for (let current_retry = 0; current_retry < MAX_CUSTOM_RETRIES; current_retry++) {
      try {
        const response = await job.runtime.page.goto(job.data.dealer.url, { waitUntil: 'networkidle2' })
        await randomWait(3000, 5000)
        const html = await job.runtime.page.content()

        if (response.status() === 403) throw new Error('Error 403, retry limit reached')
        const { document } = new JSDOM(html).window

        const totalListings = Number(
          document.querySelector('a[class="type-all active"] > span').textContent.trim() ?? 0,
        )

        if (totalListings === 0) throw new Error('Expected; no listings found')
        if (totalListings > LISTINGS_LIMIT) throw new Error('Invalid job size, too many listings, investigate')

        offsetLimit = totalListings
        const articles = Array.from(document.querySelectorAll('.results-default > article[class^="default"]'))

        if (!articles.length) throw new Error('No listing articles found in HTML')

        const listings = articles.map((article) => ({
          object_id: article.getAttribute('data-object-id'),
        }))

        if (!listings?.length) throw new Error('Unable to extract listings')
        listingIds.push(...listings.map((listing) => listing.object_id.toString()))

        if (listings.length < 50) {
          break paginationLoop
        }

        continue paginationLoop
      } catch (error) {
        if (['Expected; no listings found'].includes(error.message)) {
          job.addErrorToLog(error)
          return []
        }

        if (['Invalid total count', 'Invalid job size, too many listings, investigate'].includes(error.message))
          throw error

        if (current_retry + 1 >= MAX_CUSTOM_RETRIES) {
          error.message = 'Current offset: ' + offset.toString() + ' ;' + error.message
          job.addErrorToLog(error)
          continue paginationLoop
        }
        continue retryLoop
      }
    }
  }

  return [
    {
      type: ResultType.item,
      data: {
        itemId: job.data.dealer.itemId,
        dealerId: job.data.dealer.dealerId,
        iso: job.data.iso,
        data: {
          ...job.data.dealer.data,
          listingIds,
          isProcessed: true,
        },
      },
    },
  ]
}

export async function scheduleCompanyDetails(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const dealers = Object.entries(
    (
      await runtime
        .collection('item')
        .find(
          {
            projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
            iso,
            updatedAt: { $gte: startOfISOWeek(new Date()) },
            'data.companyId': { $exists: true },
          },
          {
            projection: {
              _id: 0,
              itemId: 1,
              data: 1,
            },
          },
        )
        .toArray()
    ).reduce((accumulator, dealer) => {
      const companyId = dealer.data.companyId.toString()
      if (accumulator[companyId]) {
        accumulator[companyId].push(dealer)
      } else {
        accumulator[companyId] = [dealer]
      }
      return accumulator
    }, {}),
  )
  if (!dealers?.length) throw new Error('No unprocessed dealers')

  return dealers.map((dealer) => ({
    ...job,
    data: {
      iso,
      companyId: dealer[0],
      dealers: dealer[1],
      isProcessed: true,
    },
  }))
}

function determinePropertyAttributes(object_type: string, businessType: RealEstatesBusinessType) {
  let propertyScope = RealEstatesPropertyScope.other
  let propertyType = RealEstatesPropertyType.other
  let stockType = RealEstatesStockType.other

  if (object_type.match(/apartment/i)) {
    propertyScope = RealEstatesPropertyScope.residential
    propertyType = RealEstatesPropertyType.apartment
    stockType =
      businessType === RealEstatesBusinessType.sale
        ? RealEstatesStockType.residential_sales
        : RealEstatesStockType.residential_lettings
  } else if (object_type.match(/house/i)) {
    propertyScope = RealEstatesPropertyScope.residential
    propertyType = RealEstatesPropertyType.house
    stockType =
      businessType === RealEstatesBusinessType.sale
        ? RealEstatesStockType.residential_sales
        : RealEstatesStockType.residential_lettings
  } else if (object_type.match(/garages/i)) {
    propertyScope = RealEstatesPropertyScope.commercial
    propertyType = RealEstatesPropertyType.parking
    stockType =
      businessType === RealEstatesBusinessType.sale
        ? RealEstatesStockType.commercial_sales
        : RealEstatesStockType.commercial_lettings
  } else if (object_type.match(/commercial/i)) {
    propertyScope = RealEstatesPropertyScope.commercial
    propertyType = RealEstatesPropertyType.office
    stockType =
      businessType === RealEstatesBusinessType.sale
        ? RealEstatesStockType.commercial_sales
        : RealEstatesStockType.commercial_lettings
  } else if (object_type.match(/land/i)) {
    propertyScope = RealEstatesPropertyScope.land
  }

  return { propertyType, propertyScope, stockType, businessType }
}

function determinePackageTier(adCount: number) {
  switch (true) {
    case adCount < 1:
      throw new Error('Invalid ad count')
    case adCount <= 2:
      return {
        packageName: 'TAVAPAKETT',
        advertisingCost: 79,
      }
    case adCount <= 8:
      return {
        packageName: 'TÄISPAKETT',
        advertisingCost: 129,
      }
    case adCount <= 15:
      return {
        packageName: 'PREMIUMPAKETT',
        advertisingCost: 189,
      }
    case adCount <= 20:
      return {
        packageName: 'PROFIPAKETT',
        advertisingCost: 259,
      }
    case adCount > 20:
      return {
        packageName: 'BULK',
        advertisingCost: 0,
      }
    default:
      return {
        packageName: 'NONE',
        advertisingCost: 0,
      }
  }
}
