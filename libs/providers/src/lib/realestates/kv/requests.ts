import { MAX_RETRIES } from './constants'
export const BASE_URL = 'https://www.kv.ee'
// export const PARISH_URL = 'https://www.kv.ee/et/api/search/form'
// export const PARISH_REQUEST_OPTIONS = {
//   headers: {
//     Referer: 'https://www.kv.ee/',
//     'X-Requested-With': 'XMLHttpRequest',
//     Cookie: '',
//   },
//   searchParams: { county: '1' },
//   retry: {
//     limit: MAX_RETRIES,
//     statusCodes: [403],
//   },
// }

export const LISTINGS_URL = 'https://www.kv.ee/en/search/'

export const LISTINGS_OPTIONS = {
  method: 'GET' as const,
  retry: {
    limit: MAX_RETRIES,
    statusCodes: [403],
  },
  headers: {
    'User-Agent': '',
    Accept: 'application/json, text/javascript, */*; q=0.01',
    'Accept-Language': 'en,en-US;q=0.5',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    Cookie: '',
  },
}

export const listingDetailsRequestOptions = (id: string) => ({
  url: new URL(`https://www.kv.ee/et/external/swed/swedObjectView&object_id=${id}`),
  options: {
    method: 'GET' as const,
    retry: {
      limit: MAX_RETRIES,
      statusCodes: [403],
    },
    headers: {
      Accept: 'application/json, text/javascript, */*; q=0.01',
      'Accept-Encoding': 'gzip, deflate, br, zstd',
    },
  },
})

export const newDevelopmentsRequestOptions = (dev_type_id: string | undefined, offset: number) => {
  const url = new URL('https://www.kv.ee/en/kinnisvara/uusarendused')

  url.searchParams.set('start', offset.toString())
  if (dev_type_id) url.searchParams.set('dev_type_id', dev_type_id)

  return {
    url,
    options: {
      method: 'GET' as const,
      retry: {
        limit: MAX_RETRIES,
        statusCodes: [403],
      },
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'en,en-US;q=0.5',
        Referer: 'https://www.kv.ee/en/kinnisvara/uusarendused',
        'X-Requested-With': 'XMLHttpRequest',
      },
    },
  }
}

export const newDevelopmentsDetailsRequestOptions = (id: number | string, locale: 'et' | 'en' = 'et') => {
  const url = new URL(`https://www.kv.ee/${locale}/kinnisvara/uusarendused/${id}`)
  return {
    url,
    options: {
      method: 'GET' as const,
      retry: {
        limit: MAX_RETRIES,
        statusCodes: [403],
      },
      headers: {
        accept: 'application/json, text/javascript, */*; q=0.01',
        'x-requested-with': 'XMLHttpRequest',
      },
    },
  }
}

export const brokerSearchRequestOptionsHTML = (offset: number) => {
  const url = new URL('https://www.kv.ee/et/maaklerid')

  url.searchParams.set('start', offset.toString())
  return {
    url,
    options: {
      method: 'GET' as const,
      retry: {
        limit: MAX_RETRIES,
        statusCodes: [403],
      },
      headers: {
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        Referer: 'https://www.kv.ee/maaklerid',
        'X-Requested-With': 'XMLHttpRequest',
        DNT: '1',
        'Sec-GPC': '1',
        Connection: 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        Priority: 'u=0',
        TE: 'trailers',
      },
    },
  }
}
export const brokerSearchRequestOptions = (offset: number) => {
  const url = new URL('https://www.kv.ee/et/maaklerid')

  url.searchParams.set('start', offset.toString())
  return {
    url,
    options: {
      method: 'GET' as const,
      retry: {
        limit: MAX_RETRIES,
        statusCodes: [403],
      },
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        Referer: 'https://www.kv.ee/maaklerid',
        'X-Requested-With': 'XMLHttpRequest',
        DNT: '1',
        'Sec-GPC': '1',
        Connection: 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        Priority: 'u=0',
        TE: 'trailers',
      },
    },
  }
}

export const brokerDetailsRequestOptions = (offset: number, brokerId: string) => {
  const url = new URL(`https://www.kv.ee/maakler/${brokerId}`)

  if (offset > 0) {
    url.searchParams.set('start', offset.toString())
  }

  return {
    url,
    options: {
      method: 'GET' as const,
      retry: {
        limit: MAX_RETRIES,
        statusCodes: [403],
      },
      headers: {
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'en,en-US;q=0.5',
        DNT: '1',
        'Sec-GPC': '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        Priority: 'u=0, i',
      },
    },
  }
}

export const companyDetailsRequestOptions = (companyId: string) => {
  const url = new URL(`https://www.kv.ee/en/firma/${companyId}`)

  return {
    url,
    options: {
      method: 'GET' as const,
      retry: {
        limit: MAX_RETRIES,
        statusCodes: [403],
      },
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
      },
    },
  }
}
