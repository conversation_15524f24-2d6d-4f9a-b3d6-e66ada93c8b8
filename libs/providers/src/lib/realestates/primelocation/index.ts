import { <PERSON><PERSON><PERSON> } from 'jsdom'
import UserAgent from 'user-agents'

import {
  DataType,
  MarketplacesDealerType,
  ProjectType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesPropertyType,
  RealEstatesStockType,
  ResultType,
} from '@datagatherers/datagatherers'
import { getItemId, wait } from '@datagatherers/worker-utils'

import { CLOUDFLARE_SELECTOR, ITEMS_PER_PAGE, MAX_RETRIES } from './constants'

import type { Listing, QueryData } from './types'
import type { ControllerRuntime, Iso3166Alpha2, JobDefinition, JobInstance } from '@datagatherers/datagatherers'

export async function scheduler(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const codesCollection = await runtime
    .collection('postalcode')
    .find(
      {
        project: ProjectType.postalcode,
        provider: 'postalcode',
        iso,
      },
      {
        projection: {
          _id: 0,
          'data.code': 1,
        },
      },
    )
    .toArray()

  const codes = codesCollection.map((code) => code.data.code.toLowerCase() as string)

  const jobs: JobDefinition[] = []
  const queryData: QueryData[] = []

  for (const code of codes) {
    queryData.push(...generateQueriesResidential(code), ...generateQueriesCommercial(code))
  }

  for (const qData of queryData) {
    jobs.push({
      ...job,
      data: {
        iso,
        ...qData,
      },
    })
  }
  return jobs
}

export async function extract(job: JobInstance) {
  const listings: Listing[] = []
  const results = []

  for (let pageNr = 1; pageNr <= 10000; pageNr++) {
    await wait(300)
    const listingsResponse = await getListings(job, pageNr)
    if (!listingsResponse.length) break

    listings.push(...listingsResponse)

    if (listingsResponse.length < ITEMS_PER_PAGE) break
  }

  const dealersMap: Map<
    string,
    {
      dealerId: string
      dealerType: MarketplacesDealerType
      isActive: boolean
      isPaying: boolean
    }
  > = new Map()

  const resultsInventory = listings.map((listing) => {
    const dealerId = listing.dealerId
    const dealerType = MarketplacesDealerType.dealer
    const isActive = true
    const isPaying = true

    dealersMap.set(dealerId, {
      dealerId,
      dealerType,
      isActive,
      isPaying,
    })

    const price = listing.price

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: getItemId(DataType.inventory, ProjectType.realestates, listing.inventoryId),
        dataType: DataType.inventory,
        iso: job.data?.iso,
        inventoryId: listing.inventoryId,
        stockType:
          job.data?.businessType === RealEstatesBusinessType.rent
            ? job.data?.propertyScope === RealEstatesPropertyScope.residential
              ? RealEstatesStockType.residential_lettings
              : RealEstatesStockType.commercial_lettings
            : job.data?.propertyScope === RealEstatesPropertyScope.residential
              ? RealEstatesStockType.residential_sales
              : RealEstatesStockType.commercial_sales,
        dealerId,
        dealerType,
        ...(listing.price && { price }),
        currency: 'GBP',
        businessType: job.data?.businessType,
        propertyScope: job.data?.propertyScope,
        propertyType: job.data?.propertyType,
      },
    }
  })
  results.push(...resultsInventory)

  const resultsDealers = [...dealersMap.values()].map((dealer) => ({
    type: ResultType.item,
    data: {
      project: ProjectType.realestates,
      itemId: getItemId(DataType.dealers, ProjectType.realestates, dealer.dealerId),
      iso: job.data?.iso,
      dataType: DataType.dealers,
      dealerId: dealer.dealerId,
      dealerType: dealer.dealerType,
      isActive: dealer.isActive,
      isPaying: dealer.isPaying,
    },
  }))
  results.push(...resultsDealers)

  return results
}

function generateQueriesResidential(code: string): QueryData[] {
  const crawlData = {
    baseUrl: new URL(
      'https://www.primelocation.com/for-sale/houses/nw2/?q=nw2&results_sort=newest_listings&radius=0&floor_area_units=sq_metres&search_source=refine&view_type=list&page_size=50&pn=1',
    ),
    businessTypes: {
      sale: 'for-sale',
      rent: 'to-rent',
    },
    propertyTypes: {
      house: 'houses',
      apartment: 'flats',
    },
  }

  const queryResults: QueryData[] = []

  for (const businessType in crawlData.businessTypes) {
    for (const propertyType in crawlData.propertyTypes) {
      const queryData: QueryData = {
        query: '',
        businessType: '',
        propertyScope: '',
        propertyType: '',
      }

      const pathname =
        crawlData.businessTypes[businessType] + '/' + crawlData.propertyTypes[propertyType] + '/' + code + '/'

      const constructedURL = new URL(crawlData.baseUrl)
      constructedURL.pathname = pathname
      constructedURL.searchParams.set('q', code)

      queryData.query = constructedURL.href
      queryData.businessType = businessType
      queryData.propertyScope = RealEstatesPropertyScope.residential
      queryData.propertyType = propertyType
      queryResults.push(queryData)
    }
    const queryData: QueryData = {
      query: '',
      businessType: '',
      propertyScope: '',
      propertyType: '',
    }
    const pathname = crawlData.businessTypes[businessType] + '/property/' + code + '/'

    const constructedURL = new URL(crawlData.baseUrl)
    constructedURL.pathname = pathname
    constructedURL.searchParams.set('q', code)
    constructedURL.searchParams.set('property_type', RealEstatesPropertyScope.land)

    queryData.query = constructedURL.href
    queryData.businessType = businessType
    queryData.propertyScope = RealEstatesPropertyScope.land
    queryData.propertyType = RealEstatesPropertyType.other
    queryResults.push(queryData)
  }

  return queryResults
}

function generateQueriesCommercial(code: string): QueryData[] {
  const crawlData = {
    baseUrl: new URL(
      'https://www.primelocation.com/for-sale/commercial/offices/nw2/?floor_area_units=sq_metres&page_size=50&q=nw2&radius=0&results_sort=newest_listings&search_source=refine&view_type=list&pn=1',
    ),
    businessTypes: {
      sale: 'for-sale',
      rent: 'to-rent',
    },
    propertyTypes: {
      office: 'offices',
      retail: 'retail-premises',
      factories: 'industrial-sites',
      other: 'hospitality',
    },
  }

  const queryResults: QueryData[] = []

  for (const businessType in crawlData.businessTypes) {
    for (const propertyType in crawlData.propertyTypes) {
      const queryData: QueryData = {
        query: '',
        businessType: '',
        propertyScope: '',
        propertyType: '',
      }

      const pathname =
        crawlData.businessTypes[businessType] +
        '/commercial/' +
        crawlData.propertyTypes[propertyType] +
        '/' +
        code +
        '/'

      const constructedURL = new URL(crawlData.baseUrl)
      constructedURL.pathname = pathname
      constructedURL.searchParams.set('q', code)

      queryData.query = constructedURL.href
      queryData.businessType = businessType
      queryData.propertyScope = RealEstatesPropertyScope.commercial
      queryData.propertyType = propertyType
      queryResults.push(queryData)
    }
    const queryData: QueryData = {
      query: '',
      businessType: '',
      propertyScope: '',
      propertyType: '',
    }
    const pathname = crawlData.businessTypes[businessType] + '/commercial/property/' + code + '/'

    const constructedURL = new URL(crawlData.baseUrl)
    constructedURL.pathname = pathname
    constructedURL.searchParams.set('q', code)
    constructedURL.searchParams.set('property_type', RealEstatesPropertyScope.land)

    queryData.query = constructedURL.href
    queryData.businessType = businessType
    queryData.propertyScope = RealEstatesPropertyScope.land
    queryData.propertyType = RealEstatesPropertyType.other
    queryResults.push(queryData)
  }

  return queryResults
}

async function getListings(job: JobInstance, pageNr: number): Promise<Listing[]> {
  const url = new URL(job.data?.query)
  const listings: Listing[] = []

  url.searchParams.set('pn', pageNr.toString())

  let attempts = 0
  let htmlContent = ''

  await job.runtime.page.goto('https://www.primelocation.com', { waitUntil: 'networkidle2' })
  while (attempts < MAX_RETRIES) {
    try {
      await job.runtime.page.goto(url.href, { waitUntil: 'networkidle2' })
      const challengeElement = await job.runtime.page.$(CLOUDFLARE_SELECTOR)
      if (challengeElement) {
        throw new Error('Cloudflare challenge detected')
      }
      htmlContent = await job.runtime.page.content()
      break
    } catch {
      attempts++
      if (attempts >= MAX_RETRIES) {
        job.addErrorToLog(new Error(`Failed to fetch page after ${MAX_RETRIES} attempts`))
        return []
      }
      const userAgent = new UserAgent({ deviceCategory: 'desktop' }).toString()
      await job.runtime.page.setUserAgent(userAgent)
      await job.runtime.page.goto('https://www.primelocation.com', { waitUntil: 'networkidle2' })
      await wait(1000)
    }
  }

  if (!htmlContent) return []

  const dom = new JSDOM(htmlContent)

  const listingNodes = dom.window.document.querySelectorAll('div[id^="listing_"]')
  if (!listingNodes?.length) return []

  listingNodes.forEach((node) => {
    const idAttr = node.getAttribute('id') || ''
    const inventoryId = idAttr.replace(/^listing_/, '')
    if (!inventoryId) return

    const priceRaw = node.querySelector('[data-testid="listing-price"]')?.textContent || ''
    const price = Number((priceRaw.match(/\d[\d,]*/)?.[0] || '').replace(/,/g, '')) || 0

    const agentLogoSrc = node.querySelector('img[src*="agent_logo_"]')?.getAttribute('src') || ''
    const dealerIdMatch = agentLogoSrc.match(/\((\d+)\)/)
    const dealerId = dealerIdMatch ? dealerIdMatch[1] : ''

    listings.push({
      inventoryId,
      dealerId,
      price,
    })
  })

  return listings
}
