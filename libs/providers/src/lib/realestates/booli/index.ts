import { compareAsc, isSameWeek, startOfISOWeek } from 'date-fns'

import {
  GeoJSONTypes,
  ResultType,
  ProjectType,
  MarketplacesDealerType,
  RealEstatesBusinessType,
  DataType,
  RealEstatesStockType,
  RealEstatesPropertyType,
  RealEstatesPropertyScope,
  Iso3166Alpha2,
} from '@datagatherers/datagatherers'
import { getProjectProviderDataType, getREArea } from '@datagatherers/worker-utils'

import { defaultHeaders, searchForSaleQuery } from './constants'

import type { BooliListing, BooliProject, AdViewsDaysActiveHistoryEntry } from './types'
import type {
  GeoJSONPoint,
  JobDefinition,
  ControllerRuntime,
  JobInstance,
  RealEstatesDealerItem,
  Result,
  Town,
  WorldCity,
} from '@datagatherers/datagatherers'

export async function scheduleExtractAdViews(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2 = Iso3166Alpha2.SE,
): Promise<JobDefinition[]> {
  const listings = await runtime
    .collection('item')
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(ProjectType.realestates, 'booli', DataType.inventory),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        $or: [
          { 'data.adViewsUpdatedAt': { $exists: false } },
          { 'data.adViewsUpdatedAt': { $lt: startOfISOWeek(new Date()) } },
        ],
      },
      { projection: { _id: 0, itemId: 1, inventoryId: 1 } },
    )
    .toArray()

  if (!listings?.length) throw new Error('No listings found')

  return listings.map((listing) => ({ ...job, data: { iso, ...listing } }))
}

export async function extractAdViews(job: JobInstance) {
  const { iso, itemId, inventoryId } = job.data
  const existingListing = await job.runtime.collection('item').findOne(
    {
      itemId,
      projectProviderDataType: getProjectProviderDataType(ProjectType.realestates, 'booli', DataType.inventory),
      iso,
    },
    { projection: { 'data.adViewsDaysActiveHistory': 1, 'data.daysActive': 1 } },
  )

  const pageViews = await job.runtime
    .got('https://www.booli.se/graphql', {
      method: 'POST',
      headers: {
        'x-apollo-operation-id': 'f96dfd81b4f69443681b82a2657889f1ba1191690a31c41f809444ad6ce6a07d',
        'x-apollo-operation-name': 'GetListingDetailById',
        'api-client': 'android-app',
        'content-type': 'application/json',
        'user-agent': 'okhttp/5.0.0-alpha.2',
      },
      json: {
        query:
          'query GetListingDetailById( $listingId: ID! ) { property: propertyByListingId(listingId: $listingId) { ...ListingDetailsFragment } } fragment ListingDetailsFragment on Listing { pageviews }',
        operationName: 'GetListingDetailById',
        variables: {
          listingId: `${inventoryId}`,
        },
      },
      retry: { limit: 3, errorCodes: ['ERR_HTTP2_STREAM_ERROR'] },
    })
    .json<{ data: { property: { pageviews: number } } }>()
    .then((res) => res.data?.property?.pageviews)

  if (!pageViews) throw new Error('No page views found')

  const existingHistory = existingListing?.data?.adViewsDaysActiveHistory || []
  const daysActive = existingListing?.data?.daysActive || null
  const updatedHistory = compileAdViewsDaysActiveHistory(pageViews, daysActive, existingHistory)
  const { isRepostedAdViews, isRepostedDaysActive } = assessIsReposted(updatedHistory)
  return [
    {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        provider: 'booli',
        iso,
        itemId,
        'data.adViews': pageViews,
        'data.adViewsUpdatedAt': new Date(),
        'data.adViewsDaysActiveHistory': updatedHistory,
        'data.isRepostedAdViews': isRepostedAdViews,
        'data.isRepostedDaysActive': isRepostedDaysActive,
      },
    },
  ]
}

export async function scheduleTowns(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const statesArr = await runtime
    .collection<WorldCity>('worldcities')
    .aggregate([
      { $match: { iso2: iso, admin_code: { $exists: true, $ne: '' } } },
      { $group: { _id: { name: '$admin_name', code: '$admin_code' } } },
    ])
    .toArray()
  return statesArr.map((item) => {
    return {
      ...job,
      data: {
        ...job.data,
        stateName: item._id.name,
        stateCode: item._id.code,
        iso,
      },
    }
  })
}

export async function extractTowns(job: JobInstance) {
  const suggestions = await job.runtime
    .got(`https://api.booli.se/suggestions`, {
      method: 'GET',
      headers: {
        'user-agent': 'BooliProd/3.8.6 (com.booli.iphone; build:7; iOS 15.0.2) Alamofire/3.8.6',
        accept: 'application/vnd.booli-v3+json',
        'accept-language': 'pt-PT;q=1.0, en-GB;q=0.9',
        'accept-encoding': 'gzip; q = 1.0, compress; q = 0.5',
      },
      searchParams: { callerId: 'ios_universal', q: job.data?.stateName },
    })
    .json<
      {
        id: number
        suggestion: string
        suggestionSynonym: string
        type: string
        parentId: number
        parent: string
        parentType: string
        parentSynonym?: string
        ancestorId: number
        ancestor: string
        quality: number
        qualityListings: number
        qualityTransactions: number
        nr: number
      }[]
    >()
    .catch((err) => job.addErrorToLog(err))
  if (typeof suggestions === 'undefined' || !suggestions?.length) return []
  return suggestions.flatMap((item) => {
    if (item?.type !== 'Län') return []
    return {
      type: ResultType.town,
      data: {
        project: ProjectType.realestates,
        itemId: `${job.data?.iso}_${item.id}`,
        iso: job.data?.iso,
        data: {
          ...job.data,
          ...item,
        },
      },
    }
  })
}

export async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const towns = await runtime
    .collection<Town>('town')
    .aggregate([{ $match: { project: job.project, provider: job.provider, iso } }])
    .toArray()
  const jobs = []
  jobs.push(
    ...towns.map((town) => ({
      ...job,
      data: {
        ...job.data,
        ...town.data,
      },
    })),
  )
  return jobs
}

export async function extract(job: JobInstance) {
  const results = await job.runtime.got.paginate.all<
    Result,
    {
      data: {
        search: {
          pages: number
          totalCount: number
          result: Array<BooliListing | BooliProject>
        }
      }
    }
  >(`https://www.booli.se/graphql`, {
    method: 'POST',
    headers: defaultHeaders,
    responseType: 'json',
    json: {
      query: searchForSaleQuery,
      variables: {
        input: {
          filters: [{ key: 'upcomingSale', value: '' }],
          areaId: job.data?.id,
          sort: 'published',
          page: 1,
          ascending: false,
        },
      },
      operationName: 'searchForSale',
    },
    retry: {
      limit: 5,
      errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR', 'ERR_HTTP2_STREAM_ERROR'],
    },
    pagination: {
      backoff: 500,
      paginate: ({ response }) => {
        if (!response?.request?.options?.body) return false
        const responseBody = JSON.parse(response?.request?.options?.body)
        if (!responseBody) return false

        const page = responseBody.variables.input.page + 1
        if (page >= response?.body?.data?.search?.pages) {
          return false
        }

        if (!response?.body?.data?.search?.result?.length) {
          return false
        }

        return {
          json: {
            ...responseBody,
            variables: {
              ...responseBody.variables,
              input: {
                ...responseBody.variables.input,
                page,
              },
            },
          },
        }
      },
      transform: ({ body }) => {
        if (!body.data?.search?.result?.length) {
          return []
        }
        const resultsProjectListings = []
        const dealersMap: Map<
          string,
          Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'>
        > = new Map()
        const resultsInventory = body.data.search.result.flatMap((item) => {
          if (!item?.booliId) return []
          let dealer = getProjectDealer(item as BooliProject)
          if (!dealer) dealer = getListingDealer(item as BooliListing)
          const dealerId = dealer?.dealerId ? dealer.dealerId : 'unknown'
          const dealerType = dealer?.dealerType ? dealer.dealerType : MarketplacesDealerType.private
          if (dealer && !dealersMap.has(dealer.dealerId)) {
            try {
              dealersMap.set(dealer.dealerId, dealer)
            } catch (error) {
              console.error(error)
            }
          }
          const url = `https://www.booli.se${
            (item as BooliProject).booliUrl ? (item as BooliProject).booliUrl : item.url
          }`
          const state = getState(job)
          const price = getPrice(item as BooliListing)
          const area = getArea(item as BooliListing)
          const stockType = getStockType(item as BooliListing)
          const businessType = RealEstatesBusinessType.sale
          const propertyScope = getPropertyScope(item as BooliListing)
          const propertyType = getPropertyType(item as BooliListing)
          const daysActive = (item as BooliListing).daysActive
          const geoJson = getGeoJson(item)

          // logo appears only when agency.thumbnail is present at the listing root; agent.agency.thumbnail alone does not render a logo, so counting it inflates presence?
          const hasAgencyLogo = item.__typename === 'Listing' && !!(item as BooliListing).agency?.thumbnail

          const adType =
            item.__typename === 'Project'
              ? 'Project'
              : (item as BooliListing).projectId && (item as BooliListing).projectId !== ''
                ? 'ProjectUnit'
                : undefined
          if ((item as BooliProject).projectListings?.listings?.length) {
            resultsProjectListings.push(...getProjectListings(job, dealersMap, item as BooliProject))
          }
          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId: `listing_${job.data?.iso}_${job.project}_${item.booliId}`,
              iso: job.data?.iso,
              dataType: DataType.inventory,
              inventoryId: item.booliId,
              url,
              stockType,
              dealerId,
              dealerType,
              ...(state && { state }),
              ...(price && { price, currency: 'SEK' }),
              ...(area && { area }),
              ...(businessType && { businessType }),
              ...(propertyScope && { propertyScope }),
              ...(propertyType && { propertyType }),
              ...(daysActive && { daysActive }),
              ...(geoJson && { geoJson }),
              data: {
                ...(daysActive && { daysActive }),
                ...item,
                ...(adType && { adType }),
                isCrossposted: false,
                hasAgencyLogo,
                hasImages: item.images?.length > 0,
              },
            },
          }
        })

        const resultsDealers = [...dealersMap.values()].flatMap((item) => {
          if (!item?.dealerId) return []
          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId: `dealer_${job.data?.iso}_${job.project}_${item.dealerId}`,
              iso: job.data?.iso,
              dataType: DataType.dealers,
              dealerId: item.dealerId,
              dealerType: item.dealerType,
              name: item.name,
              isActive: item.isActive,
              isPaying: item.isPaying,
              data: {
                ...item.data,
              },
            },
          }
        })
        return [...resultsInventory, ...resultsProjectListings, ...resultsDealers]
      },
    },
  })

  return results
}

function getState(job: JobInstance) {
  return job.data?.stateCode
}

function getStockType(item: BooliListing): RealEstatesStockType {
  switch (item.objectType) {
    case 'Lägenhet':
    case 'Villa':
    case 'Parhus':
    case 'Radhus':
    case 'Kedjehus':
      return RealEstatesStockType.residential_sales
    case 'Fritidshus':
      return RealEstatesStockType.commercial_sales
    case 'Gård':
      return RealEstatesStockType.commercial_sales
    case 'Tomt/Mark':
      return RealEstatesStockType.residential_sales
    default:
      break
  }
  return RealEstatesStockType.other
}

function getArea(item: BooliListing) {
  return item.livingArea?.raw ? getREArea(item.livingArea.raw) : item.plotArea?.raw ? getREArea(item.plotArea.raw) : 0
}

function getPrice(item: BooliListing) {
  return item.listPrice?.raw
}

function getPropertyScope(item: BooliListing): RealEstatesPropertyScope {
  if (item.objectType) {
    switch (item.objectType) {
      case 'Lägenhet':
      case 'Villa':
      case 'Parhus':
      case 'Radhus':
      case 'Kedjehus':
        return RealEstatesPropertyScope.residential
      case 'Fritidshus':
        return RealEstatesPropertyScope.commercial
      case 'Gård':
        return RealEstatesPropertyScope.land
      case 'Tomt/Mark':
        return RealEstatesPropertyScope.land
      default:
        break
    }
  }
  return RealEstatesPropertyScope.other
}

function getPropertyType(item: BooliListing): RealEstatesPropertyType {
  if (item.objectType) {
    switch (item.objectType) {
      case 'Lägenhet':
        return RealEstatesPropertyType.apartment
      case 'Villa':
      case 'Parhus':
      case 'Radhus':
      case 'Kedjehus':
      case 'Fritidshus':
        return RealEstatesPropertyType.house
      case 'Gård':
        return RealEstatesPropertyType.agriculture
      case 'Tomt/Mark':
        return RealEstatesPropertyType.urban
      default:
        return RealEstatesPropertyType.other
    }
  }
  return RealEstatesPropertyType.other
}

function getProjectDealer(
  item: BooliProject,
): Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'> | undefined {
  if (item.developer?.id) {
    return {
      dealerId: `developer_${item.developer.id}`,
      dealerType: MarketplacesDealerType.dealer,
      isActive: true,
      isPaying: true,
      name: item.developer?.name,
      data: {
        ...item.developer,
      },
    }
  }
  return undefined
}

function getListingDealer(
  item: BooliListing,
): Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'> | undefined {
  if (item.agent?.id) {
    return {
      dealerId: `agent_${item.agent.id}`,
      dealerType: MarketplacesDealerType.dealer,
      isActive: true,
      isPaying: true,
      name: item.agent.name,
      data: {
        ...item.agent,
      },
    }
  } else if (item.agency?.id) {
    return {
      dealerId: `agency_${item.agency.id}`,
      dealerType: MarketplacesDealerType.dealer,
      isActive: true,
      isPaying: true,
      name: item.agency.name,
      data: {
        ...item.agency,
      },
    }
  }
  return undefined
}

function getGeoJson(item: BooliProject | BooliListing): GeoJSONPoint | undefined {
  if (!item.latitude || !item.longitude) return
  return {
    type: GeoJSONTypes.Point,
    coordinates: [Number(item.longitude), Number(item.latitude)],
  }
}

function getProjectListings(
  job: JobInstance,
  dealersMap: Map<
    string,
    Pick<RealEstatesDealerItem, 'dealerId' | 'dealerType' | 'name' | 'isActive' | 'isPaying' | 'data'>
  >,
  item: BooliProject,
) {
  if (!item?.projectListings?.listings?.length) return []
  return item.projectListings.listings.flatMap((item: BooliListing) => {
    if (!item?.booliId) return []
    const dealer = getListingDealer(item)
    const dealerId = dealer?.dealerId ? dealer.dealerId : 'unknown'
    const dealerType = dealer?.dealerType ? dealer.dealerType : MarketplacesDealerType.private
    if (dealer && !dealersMap.has(dealer.dealerId)) {
      try {
        dealersMap.set(dealer.dealerId, dealer)
      } catch (error) {
        console.error(error)
      }
    }
    const url = `https://www.booli.se${item.url}`
    const state = getState(job)
    const price = getPrice(item)
    const area = getArea(item)
    const stockType = getStockType(item)
    const businessType = RealEstatesBusinessType.sale
    const propertyScope = getPropertyScope(item)
    const propertyType = getPropertyType(item)
    const daysActive = item?.daysActive
    const geoJson = getGeoJson(item)
    const adType = 'ProjectUnit'
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: `listing_${job.data?.iso}_${job.project}_${item.booliId}`,
        iso: job.data?.iso,
        dataType: DataType.inventory,
        inventoryId: item.booliId,
        url,
        stockType,
        dealerId,
        dealerType,
        ...(state && { state }),
        ...(price && { price, currency: 'SEK' }),
        ...(area && { area }),
        ...(businessType && { businessType }),
        ...(propertyScope && { propertyScope }),
        ...(propertyType && { propertyType }),
        ...(daysActive && { daysActive }),
        ...(geoJson && { geoJson }),
        data: {
          daysActive,
          ...item,
          ...(adType && { adType }),
        },
      },
    }
  })
}

export function compileAdViewsDaysActiveHistory(
  rawAdViews: unknown,
  rawDaysActive: unknown,
  existingHistory: AdViewsDaysActiveHistoryEntry[],
): AdViewsDaysActiveHistoryEntry[] {
  const now = new Date()
  const currentWeekStart = startOfISOWeek(now)

  const existingWeekEntryIndex = existingHistory.findIndex((entry) =>
    isSameWeek(entry.weekStart || entry.date, currentWeekStart),
  )

  const adViews =
    typeof rawAdViews === 'string' ? (!isNaN(Number(rawAdViews)) ? Number(rawAdViews) : rawAdViews) : rawAdViews

  const daysActive =
    typeof rawDaysActive === 'string'
      ? !isNaN(Number(rawDaysActive))
        ? Number(rawDaysActive)
        : rawDaysActive
      : rawDaysActive

  const newEntry: AdViewsDaysActiveHistoryEntry = {
    date: now,
    adViews,
    daysActive,
    weekStart: currentWeekStart,
  }

  if (existingWeekEntryIndex !== -1) {
    const updatedHistory = [...existingHistory]
    updatedHistory[existingWeekEntryIndex] = newEntry
    return updatedHistory
  } else {
    return [...existingHistory, newEntry]
  }
}

export function assessIsReposted(history: AdViewsDaysActiveHistoryEntry[]) {
  const isRepostedAdViews = history
    .flatMap(({ adViews, date }) => {
      if (typeof adViews !== 'number') return []
      return [{ adViews, date }]
    })
    .sort(({ date: entryDateA }, { date: entryDateB }) => compareAsc(entryDateA, entryDateB))
    .some(({ adViews: currentAdViews }, currentIndex, sortedHistory) => {
      if (currentIndex === 0) return false
      const { adViews: previousAdViews } = sortedHistory[currentIndex - 1]
      return currentAdViews < previousAdViews
    })

  const isRepostedDaysActive = history
    .flatMap(({ daysActive, date }) => {
      if (typeof daysActive !== 'number') return []
      return [{ daysActive, date }]
    })
    .sort(({ date: entryDateA }, { date: entryDateB }) => compareAsc(entryDateA, entryDateB))
    .some(({ daysActive: currentDaysActive }, currentIndex, sortedHistory) => {
      if (currentIndex === 0) return false
      const { daysActive: previousDaysActive } = sortedHistory[currentIndex - 1]
      return currentDaysActive < previousDaysActive
    })

  return {
    isRepostedAdViews,
    isRepostedDaysActive,
  }
}
