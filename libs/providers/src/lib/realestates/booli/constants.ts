export const searchForSaleQuery =
  'query searchForSale($input: SearchRequest) { search: searchForSale(input: $input) { pages totalCount result { __typename ...ListingDetailsFragment ...ProjectDetailsFragment } __typename } } fragment ListingDetailsFragment on Listing { booliId descriptiveAreaName livingArea { raw } listPrice { raw } latitude longitude daysActive objectType propertyType rent { raw } operatingCost { raw } estimate { price { raw } } rooms { raw } images { id } streetAddress url isNewConstruction biddingOpen upcomingSale mortgageDeed tenureForm plotArea { raw } agentId agent { id email name phone overallRating sellerFavorite premium reviewCount url listingStatistics { startDate endDate publishedCount publishedValue { raw } } agency { id name url thumbnail } } agencyId agency { id name url thumbnail } source { id name type } projectId } fragment ProjectDetailsFragment on Project { booliId name url booliUrl numberOfListingsForSale location { namedAreas } developer { name id } latitude longitude created projectListings { count totalCount listings { ...ListingDetailsFragment } } }'

export const defaultHeaders = {
  'content-type': 'application/json',
  accept: '*/*',
  'apollographql-client-version': '4.3.7-1',
  'accept-encoding': 'gzip, deflate, br',
  'if-none-match': 'W/"19a1-3qiPbl/RawdkqGp2D0WgEmx/j3s"',
  'accept-language': 'pt-PT,pt;q=0.9',
  'api-client': 'ios-app',
  'x-apollo-operation-type': 'query',
  'user-agent': 'BooliProd/1 CFNetwork/1335.0.3 Darwin/21.6.0',
  'apollographql-client-name': 'com.booli.iphone-apollo-ios',
  'x-apollo-operation-name': 'searchForSale',
}
