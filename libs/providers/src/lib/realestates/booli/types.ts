export interface AdViewsDaysActiveHistoryEntry {
  date: Date
  adViews: unknown
  daysActive: unknown
  weekStart: Date
}

export interface BooliListing {
  __typename: string
  booliId: string
  descriptiveAreaName: string
  livingArea?: Value
  listPrice?: Value
  latitude: number
  longitude: number
  daysActive: number
  objectType: string
  propertyType: string
  rent?: Value
  operatingCost?: Value
  estimate?: Estimate
  rooms: Value
  streetAddress: string
  url: string
  isNewConstruction: boolean
  biddingOpen: number
  upcomingSale: boolean
  mortgageDeed?: null
  tenureForm: string
  plotArea?: Value
  agentId: string
  agent: Agent
  agencyId: string
  agency: Agency
  source: Source
  projectId: string
  images?: { id: string }[] | null
}
interface Value {
  raw: number
}
interface Estimate {
  price: Value
}
interface Agent {
  id: string
  email: string
  name: string
  phone: string
  overallRating: number
  sellerFavorite: boolean
  premium: boolean
  reviewCount: number
  url: string
  listingStatistics: ListingStatistics
  agency: Agency
}
interface ListingStatistics {
  startDate: string
  endDate: string
  publishedCount: number
  publishedValue: Value
}
interface Agency {
  thumbnail?: string | null
  id: string
  name: string
  url: string
}
interface Source {
  id: string
  name: string
  type: string
}

export interface BooliProject {
  __typename: string
  booliId: string
  name: string
  url: string
  booliUrl: string
  numberOfListingsForSale: number
  location: Location
  developer: Developer
  latitude: number
  longitude: number
  created: string
  projectListings: ProjectListings
  images?: { id: string }[] | null
}
interface Location {
  namedAreas?: string[] | null
}
interface Developer {
  name: string
  id: string
}
interface ProjectListings {
  count: number
  totalCount: number
  listings?: BooliListing[] | null
}
