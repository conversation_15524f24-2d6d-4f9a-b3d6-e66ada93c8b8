import { ObjectId } from 'mongodb'
import UserAgent from 'user-agents'

import {
  DealerType,
  ClassifiedsStockType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  ResultType,
  VehicleType,
  MarketplacesDealerType,
} from '@datagatherers/datagatherers'
import { getItemId } from '@datagatherers/worker-utils'

import { LIMIT } from './constants'
import { postcodes } from './gbPostcodes'

import type { AutoTraderData, AutotraderUsResponse } from './types'
import type {
  ControllerRuntime,
  JobDefinition,
  JobInstance,
  JobRuntime,
  Town,
  ResultItem,
  Job,
} from '@datagatherers/datagatherers'

export async function scheduleExtractUS(job: JobDefinition, runtime: ControllerRuntime): Promise<JobDefinition[]> {
  const iso = Iso3166Alpha2.US
  const collection = runtime.collection<Town>('town')
  const filter = {
    project: job.project,
    provider: job.provider,
    iso,
  }
  const results = await collection.find(filter).toArray()
  if (!results?.length) return []

  return results
    .filter((item) => item?.data)
    .map((item) => {
      const res = {
        ...job,
        data: {
          ...job.data,
          ...item.data,
          iso,
        },
      }
      return res
    })
}

export async function extractUS(job: JobInstance) {
  const searchRadius = job.data?.distanceToNearest > 0 ? Math.ceil(job.data?.distanceToNearest) : 10

  let createSubJobs = false

  let makes = []
  let models = []
  let listingTypes = []

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sellersMap: Map<string, any> = new Map()

  const listings = await job.runtime.got.paginate.all<ResultItem, AutotraderUsResponse>(
    'https://www.autotrader.com/rest/lsc/listing',
    {
      headers: {
        'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
      },
      searchParams: {
        searchRadius,
        zip: job.data.code,
        newSearch: true,
        includeSuggested: false,
        numRecords: LIMIT,
        firstRecord: 0,
        sortBy: 'distanceASC',
        ...(job.data.make && { makeCode: job.data.make }),
        ...(job.data.model && { modelCode: job.data.model }),
        ...(job.data.listingType && { listingType: job.data.listingType }),
      },
      responseType: 'json',
      retry: {
        limit: 3,
        errorCodes: ['ERR_BODY_PARSE_FAILURE'],
      },
      pagination: {
        paginate: ({ response }) => {
          if (!response?.body?.listings?.length || createSubJobs) return false

          const currentSearchParams = response.request?.options?.searchParams as URLSearchParams
          if (!currentSearchParams) return false

          return {
            searchParams: {
              ...currentSearchParams,
              firstRecord: Number(currentSearchParams?.get('firstRecord') ?? 0) + LIMIT,
            },
          }
        },

        transform: ({ body }) => {
          if (!body?.listings?.length) return []

          const totalListings = body.totalResultCount
          if (totalListings > 400 && !createSubJobs && (!job.data.listingType || job.data.listingType === undefined)) {
            createSubJobs = true
            if (!job.data.make)
              makes =
                body.filters.makeCode?.options?.filter((option) => option.count).map((option) => option.value) ?? []
            else if (!job.data.model)
              models =
                body.filters.modelCode?.options?.filter((option) => option.count).map((option) => option.value) ?? []
            else if (!job.data.listingType)
              listingTypes =
                body.filters.listingType?.options?.filter((option) => option.count).map((option) => option.value) ?? []
            return []
          }

          return body.listings.flatMap((listing) => {
            if (!listing?.id) return []

            const inventoryId = listing.id.toString()
            const dealerId = listing.owner.id.toString()
            const dealerType = listing.owner.privateSeller ? DealerType.private : DealerType.dealer

            if (dealerId && !sellersMap.has(dealerId)) {
              try {
                sellersMap.set(dealerId, {
                  dealerId,
                  dealerName: listing.owner.name,
                  dealerType,
                  contractDealerLevel: listing.owner.contractDealerLevel,
                  isActive: true,
                  isPaying: dealerType === DealerType.dealer,
                })
              } catch (e) {
                console.error(e)
              }
            }

            const data = {
              project: job.project,
              dataType: DataType.inventory,
              stockType: listing.listingType === 'NEW' ? ClassifiedsStockType.new : ClassifiedsStockType.used,
              itemId: getItemId(DataType.inventory, ProjectType.cars, inventoryId),
              iso: job.data.iso,
              inventoryId,
              price: listing.pricingDetail.salePrice ?? 0,
              vehicleType: VehicleType.car,
              productType: listing.owner.contractDealerLevel,
              currency: 'USD',
              dealerId,
              dealerType,
            }

            return {
              type: ResultType.item,
              data,
            }
          })
        },
      },
    },
  )

  if (createSubJobs) {
    await subdivideJob(job, makes, models, listingTypes)
    return []
  }

  if (!listings?.length) return []

  const dealers = [...sellersMap.values()].map((item) => {
    const data = {
      project: job.project,
      itemId: getItemId(DataType.dealers, job.project, item.dealerId),
      iso: job.data?.iso,
      dataType: DataType.dealers,
      dealerId: item.dealerId,
      dealerType: item.dealerType,
      productType: item.contractDealerLevel,
      isActive: item.isActive,
      isPaying: item.isPaying,
    }

    return {
      type: ResultType.item,
      data,
    }
  })

  return [...listings, ...dealers]
}

export async function scheduleExtractGB(job: JobDefinition): Promise<JobDefinition[]> {
  return postcodes.map((postcode) => ({ ...job, data: { postcode } }))
}

export async function extractGB(job: JobInstance) {
  const accessToken = await getAccessToken(job.runtime)
  if (!accessToken) return []

  const isSubdividedLimit = job.data?.make !== undefined

  const dealerMap: Map<
    string,
    {
      dealerId: string
      dealerType: MarketplacesDealerType.dealer | MarketplacesDealerType.private
    }
  > = new Map()

  let makes: string[]

  const listings = await job.runtime.got.paginate.all<ResultItem, AutoTraderData>(
    'https://cws.autotrader.co.uk/CoordinatedWebService/application/crs/sss/searchone/adverts',
    {
      responseType: 'json',
      headers: {
        'Access-Token': accessToken,

        'User-Agent': 'Consumer_Android/v7.0',
        //Connection: 'Keep-Alive',
        platform: 'android',
        'platform-version': '7.0',
      },
      searchParams: {
        distance: 5,
        advertising_location: 'at_cars',
        page: 1,
        facet: 'make',
        size: 20,
        postcode: job.data?.postcode,
        ...(job.data?.make && { make: job.data?.make }),
      },
      retry: {
        limit: 5,
        errorCodes: [
          'ERR_BODY_PARSE_FAILURE',
          'ERR_GOT_REQUEST_ERROR',
          'ERR_SSL_DECRYPTION_FAILED_OR_BAD_RECORD_MAC',
          'ERR_INVALID_CHAR',
        ],
      },
      pagination: {
        backoff: 200,
        paginate: ({ response }) => {
          if (makes?.length) return false
          if (!response?.body?._embedded?.results?.length) return false
          if (!response?.body?.page?.totalPages) return false
          const currentPage = response.body.page.number
          if (!currentPage) return false

          return {
            searchParams: {
              distance: 5,
              advertising_location: 'at_cars',
              page: currentPage + 1,
              facet: 'make',
              size: 20,
              postcode: job.data?.postcode,
              ...(job.data?.make && { make: job.data?.make }),
            },
          }
        },
        transform: ({ body }) => {
          if (body?.page?.totalElements > 2000 && !isSubdividedLimit) {
            makes = body?.facets?.make?.filter((item) => item.count).map((item) => item.value)
            if (makes?.length) {
              return []
            }
          }

          if (!body?._embedded?.results?.length) return []
          return body._embedded.results
            .filter((item) => item?.advertId)
            .map((item) => {
              const inventoryId = item.advertId
              const dealerId = item.advertiser?.retailerId ?? inventoryId
              const dealerType =
                !item.advertiser || item.advertiser?.type === 'Private'
                  ? MarketplacesDealerType.private
                  : MarketplacesDealerType.dealer

              if (!dealerMap.has(dealerId)) {
                dealerMap.set(dealerId, {
                  dealerId,
                  dealerType,
                })
              }

              return {
                type: ResultType.item,
                data: {
                  project: ProjectType.cars,
                  dataType: DataType.inventory,
                  stockType: item.advertDisplayType === 'USED' ? ClassifiedsStockType.used : ClassifiedsStockType.new,
                  itemId: getItemId(DataType.inventory, ProjectType.cars, inventoryId),
                  iso: Iso3166Alpha2.GB,
                  inventoryId,
                  year: item.vehicle?.manufacturedYear,
                  price: item.sale?.pricing?.totalPrice,
                  vehicleType: VehicleType.car,
                  currency: 'GBP',
                  dealerId,
                  dealerType,
                },
              }
            })
        },
      },
    },
  )

  if (makes?.length) {
    await subdivideJob(job, makes)
    return []
  }

  const result = [
    ...listings,
    ...[...dealerMap.values()].map((dealer) => {
      return {
        type: ResultType.item,
        data: {
          project: ProjectType.cars,
          iso: Iso3166Alpha2.GB,
          itemId: getItemId(DataType.dealers, ProjectType.cars, dealer.dealerId),
          dataType: DataType.dealers,
          dealerId: dealer.dealerId,
          dealerType: dealer.dealerType,
          isActive: true,
          isPaying: true,
        },
      }
    }),
  ]

  return result
}

async function subdivideJob(job: JobInstance, makes: string[], models?: string[], listingTypes?: string[]) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) {
    job.addErrorToLog(new Error(`No scheduledJob with id ${job._id}`))
    return
  }

  let jobsToAdd
  if (listingTypes?.length) {
    jobsToAdd = listingTypes.flatMap((listingType) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          listingType,
        },
      }
    })
  } else if (models?.length) {
    jobsToAdd = models.flatMap((model) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          model,
        },
      }
    })
  } else {
    jobsToAdd = makes.flatMap((make) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          make,
        },
      }
    })
  }

  if (jobsToAdd?.length) await job.runtime.addJobs(jobsToAdd)
}

async function getAccessToken(runtime: ControllerRuntime | JobRuntime) {
  const URL =
    'https://cws.autotrader.co.uk/CoordinatedWebService/application/crs/connect/consumerandroid/0diordnaremusnoc2'
  const opts = {
    headers: { 'User-Agent': 'Consumer_Android/v7.0' },
    searchParams: { version: '7.0' },
    retry: { limit: 0 },
  }
  const maxRetries = 5
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    const body = (await runtime.got(URL, opts).text()).trim()
    if (body.startsWith('<!DOCTYPE html') || body.includes('<title>Access denied')) {
      if (attempt === maxRetries) {
        throw new Error(`getAccessToken failed after ${maxRetries + 1} attempts – still got Cloudflare HTML`)
      }
      continue
    }
    return body
  }
}
