import { addWeeks, isBefore, startOfISOWeek, subDays } from 'date-fns'
import { RetryError } from 'got'
import isoCountries from 'i18n-iso-countries'
import { JSDOM } from 'jsdom'
import { ObjectId } from 'mongodb'

import {
  DealerType,
  ResultType,
  DataType,
  ProjectType,
  ClassifiedsStockType,
  CarsStockType,
  VehicleType,
  Iso3166Alpha2,
} from '@datagatherers/datagatherers'
import { getItemId, getProjectProviderDataType, numberRegex } from '@datagatherers/worker-utils'

import {
  countryInternalCodes,
  offers,
  gears,
  peCategories,
  baseUrl,
  baseHeaders,
  mileageRanges,
  registrationRanges,
  sorts,
  descs,
  apiSettings,
  LISTINGS_PER_PAGE,
  queryConfigCH,
  autscout24CHCategoryMapping,
  ProductType,
  vehicleBodyTypes,
  priceRangeDbSwitchCH,
} from './constants'

import type {
  AutoScout24Listing,
  AutoScout24ResponseCH,
  AutoscoutApiResponse,
  ContentEntity,
  EntityId,
  EntityInfo,
} from './types'
import type {
  JobDefinition,
  Result,
  PostalCode,
  Job,
  JobInstance,
  ControllerRuntime,
  Town,
  ControllerConfig,
  Item,
} from '@datagatherers/datagatherers'

export async function scheduleExtractCH(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
  vehicleTypeQuery: string,
): Promise<JobDefinition[]> {
  const jobs = []
  const subcategoryMap = autscout24CHCategoryMapping[vehicleTypeQuery]
  if (!subcategoryMap) throw new Error('No subcategory map found')

  const conditions = ['new', 'oldtimer', 'pre-registered', 'used', 'demonstration']
  for (const subcategory of subcategoryMap) {
    const facetsObject = await runtime
      .got('https://api.autoscout24.ch/v1/listings/facets', {
        ...queryConfigCH(0, vehicleTypeQuery),
        method: 'POST',
        headers: baseHeaders,
        json: {
          query: { vehicleCategories: [vehicleTypeQuery], makeModelVersions: [] },
          facets: [{ name: 'makeKeys' }],
        },
      })
      .json<{
        facets: {
          makeKeys: Record<string, number>
        }
      }>()

    if (!facetsObject?.facets?.makeKeys) throw new Error('No makeModels')

    for (const makeKey of Object.keys(facetsObject.facets.makeKeys)) {
      if (!facetsObject.facets.makeKeys[makeKey]) continue

      const facetObj2 = await runtime
        .got('https://api.autoscout24.ch/v1/listings/facets', {
          ...queryConfigCH(0, vehicleTypeQuery),
          method: 'POST',
          headers: baseHeaders,
          json: {
            query: { vehicleCategories: [vehicleTypeQuery], makeModelVersions: [{ makeKey }] },
            facets: [{ name: 'priceRanges' }],
          },
        })
        .json<{
          facets: {
            priceRanges: Record<string, number>
          }
        }>()
      if (!facetObj2?.facets?.priceRanges) continue

      for (const priceRange of Object.keys(facetObj2.facets.priceRanges)) {
        if (!facetObj2.facets.priceRanges[priceRange]) continue

        const priceRangeSplit = priceRange.split('-')
        const priceFrom = Number(priceRangeSplit[0])
        const priceTo = Number(priceRangeSplit[1])

        jobs.push(
          ...conditions.map((conditionType) => ({
            ...job,
            data: {
              iso,
              bodyTypes: subcategory.bodyTypes,
              vehicleTypeQuery,
              vehicleType: subcategory.vehicleType,
              makeKey,
              priceFrom,
              priceTo,
              conditionType,
            },
          })),
        )
      }
    }
  }

  return jobs
}

export async function extractCH(job: JobInstance) {
  const rawListings = await job.runtime.got.paginate.all<ContentEntity, AutoScout24ResponseCH>(
    'https://api.autoscout24.ch/v1/listings/search',
    {
      ...queryConfigCH(
        0,
        job.data.vehicleTypeQuery,
        job.data.makeKey,
        job.data.priceFrom,
        job.data.priceTo,
        job.data.conditionType,
        job.data.bodyTypes,
      ),
      responseType: 'json',
      pagination: {
        paginate: ({ response }) => {
          if (!response?.body?.content?.length || response?.body?.last) return false

          const currentPage = response.body.number

          return {
            json: {
              query: {
                vehicleCategories: [job.data.vehicleType],
                ...(job.data.priceFrom && { priceFrom: job.data.priceFrom }),
                ...(job.data.priceTo && { priceTo: job.data.priceTo }),
                ...(job.data.makeKey && { makeModelVersions: [{ makeKey: job.data.makeKey }] }),
                ...(job.data.conditionType && { conditionTypes: [job.data.conditionType] }),

                ...(job.data.bodyTypes?.length && { bodyTypes: job.data.bodyTypes }),
              },
              pagination: {
                page: currentPage + 1,
                size: 20,
              },
              sort: [
                {
                  order: 'ASC',
                  type: 'MILEAGE',
                },
              ],
            },
          }
        },
        transform: ({ body }) => body.content,
      },
    },
  )

  const dealersMap: Map<
    string,
    {
      dealerId: string
      dealerType: DealerType
      companyName: string
      sellerFeatures: string[]
    }
  > = new Map()

  const listings = rawListings.map((item) => {
    const dealerId = item.seller?.id.toString() ?? 'unknown'
    const dealerType = item.seller?.type === 'professional' ? DealerType.dealer : DealerType.private

    const sellerFeatures = item.seller?.features?.map((f) => f?.feature) ?? []

    dealersMap.set(dealerId, {
      dealerId,
      dealerType,
      companyName: item?.seller?.name ?? 'unknown',
      sellerFeatures,
    })

    const listingFeatures = item.features?.map((f) => f?.feature) ?? []

    const hasTopList = listingFeatures.includes('top-list')
    const numberOfPhotos = item.images?.length ?? 0
    const createdDate = new Date(item.createdDate)

    const isOlderThan60Days = isBefore(createdDate, subDays(new Date(), 60))
    const isDiscounted = item.previousPrice && item.price ? true : false

    const productType = getCHListingsProductType(numberOfPhotos, isOlderThan60Days, hasTopList, isDiscounted)
    const crossedProductType = getCHDealersProductType(dealerType, sellerFeatures)

    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: getItemId(DataType.inventory, job.project, item.id.toString()),
        dataType: DataType.inventory,
        iso: job.data?.iso,
        inventoryId: item.id,
        price: item.price ?? 0,
        currency: 'CHF',
        stockType: item.conditionType === 'new' ? CarsStockType.new : CarsStockType.used,
        dealerId,
        dealerType,
        vehicleType: job.data.vehicleType,
        productType,
        crossedProductType,
        data: {
          //dealerProductType: getCHDealersProductType(dealerType, sellerFeatures),
          hasTopList,
          isDiscounted,
          sellerFeatures,
          listingFeatures,
          numberOfPhotos,
        },
      },
    }
  })

  const dealers = [...dealersMap.values()].map((item) => {
    const productType = getCHDealersProductType(item.dealerType, item.sellerFeatures)
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: getItemId(DataType.dealers, job.project, item.dealerId),
        iso: job.data.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: true,
        ...(productType && { productType }),
        data: {
          sellerFeatures: item.sellerFeatures,
        },
      },
    }
  })
  return [...listings, ...dealers]
}

function getCHListingsProductType(
  numberOfPhotos: number,
  isOlderThan60Days: boolean,
  hasTopList: boolean,
  isDiscounted: boolean,
) {
  if (hasTopList) {
    if (isDiscounted) {
      if (numberOfPhotos > 16 || isOlderThan60Days) return ProductType.Unlimited
      return ProductType.Premium
    }
    if (numberOfPhotos > 16 || isOlderThan60Days) {
      return ProductType.Unlimited
    } else if (numberOfPhotos > 10 && numberOfPhotos <= 16) {
      return ProductType.Premium
    }
    return ProductType.Plus
  } else if (numberOfPhotos > 6 && numberOfPhotos <= 10) {
    return ProductType.Plus
  }
  return ProductType.Basic
}

function getCHDealersProductType(dealerType: DealerType, sellerFeatures: string[]) {
  if (dealerType === DealerType.private) return undefined
  return sellerFeatures?.find((feature) => feature === 'seller-alternative-listings')
    ? 'Professional Plus'
    : sellerFeatures?.find((feature) => feature === 'seller-presentation-pro')
      ? 'Professional'
      : 'Basic'
}

export async function scheduleCodes(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const iso3 = isoCountries.alpha2ToAlpha3(iso)
  if (!iso3) return []

  const items = await runtime
    .collection<PostalCode>('postalcode')
    .find({
      iso,
    })
    .toArray()

  if (!items?.length) return []
  return items.flatMap((item) => {
    if (!item?.data) return []
    return {
      ...job,
      data: {
        ...job.data,
        ...item.data,
        iso,
        iso3,
        _id: item._id,
        // ...(job.data?.parameters && { parameters: job.data?.parameters }),
      },
    }
  })
}

export async function extractCodes(job: JobInstance) {
  const countryInternalCode = countryInternalCodes[job.data?.iso]

  const data1 = await job.runtime
    .got(`https://geocode.api.autoscout24.com/restapi/api/gis/v2.0/geoautocomplete/${job.data?.iso3}`, {
      searchParams: {
        i: job.data?.code,
        t: 'postcode',
        l: 1,
      },
      headers: baseHeaders,
      retry: {
        limit: 3,
        errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR', 'ERR_SSL_DECRYPTION_FAILED_OR_BAD_RECORD_MAC'],
      },
    })
    .json<EntityId[]>()

  if (!data1?.[0]?.entity?.id) {
    return []
  }

  const entity = data1[0].entity
  const data2 = await job.runtime
    .got(
      `https://geocode.api.autoscout24.com/restapi/api/gis/v2.0/geoautocomplete/${job.data?.iso3}/entity/${entity.id}`,
      {
        searchParams: {
          g: 'WGS84',
        },
        headers: baseHeaders,
        retry: {
          limit: 3,
          errorCodes: [
            'ERR_BODY_PARSE_FAILURE',
            'ERR_GOT_REQUEST_ERROR',
            'ERR_SSL_DECRYPTION_FAILED_OR_BAD_RECORD_MAC',
          ],
        },
      },
    )
    .json<EntityInfo>()

  if (typeof data2 === 'undefined' || !data2?.label || !data2?.geoData) {
    return []
  }

  return [{ ...entity, ...data2 }].flatMap((item) => {
    if (!item?.id) return []
    return {
      type: ResultType.town,
      data: {
        project: job.project,
        itemId: `${job.project}_${job.data?.iso}_${item.id}`,
        iso: job.data?.iso,
        blacklisted: false,
        data: {
          ...item,
          countryInternalCode,
        },
      },
    }
  })
}

export async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const towns = await runtime
    .collection<Town>('town')
    .find({
      project: job.project,
      provider: job.provider === 'autoscout24_test' ? 'autoscout24' : job.provider,
      iso,
      ...(job.provider === 'autoscout24_test' && { 'data.label': { $in: [/03006/, /81667/] } }),
    })
    .toArray()

  if (!towns?.length) return []

  return towns.flatMap((town) =>
    vehicleBodyTypes.flatMap((vehicleBodyType) => {
      return {
        ...job,
        data: {
          ...town.data,
          iso,
          vehicleType: vehicleBodyType.vehicleType,
          atype: vehicleBodyType.atype,
        },
      }
    }),
  )
}
export function scheduleExtractSmyle(job: JobDefinition, iso: Iso3166Alpha2): JobDefinition[] {
  const countryInternalCode = countryInternalCodes[iso]
  const jobs = []
  for (const gear of gears) {
    for (const peCategory of peCategories) {
      for (const bodyType of vehicleBodyTypes.find((item) => item.vehicleType === VehicleType.car).bodyTypes) {
        for (const mileageRange of mileageRanges) {
          for (const registrationRange of registrationRanges) {
            for (const sort of sorts) {
              for (const desc of descs) {
                jobs.push({
                  ...job,
                  data: {
                    iso,
                    gear,
                    peCategory,
                    bodyType,
                    sort,
                    desc,
                    ...(job.data?.parameters && { parameters: job.data?.parameters }),
                    countryInternalCode,
                    ...(mileageRange[0] && { kmfrom: mileageRange[0] }),
                    ...(mileageRange[1] && { kmto: mileageRange[1] }),
                    ...(registrationRange[0] && {
                      fregfrom: registrationRange[0],
                    }),
                    ...(registrationRange[1] && {
                      fregto: registrationRange[1],
                    }),
                  },
                })
              }
            }
          }
        }
      }
    }
  }

  return jobs
}

export async function extractSmyle(job: JobInstance) {
  let subDivide = false
  const maxPages = 20
  const dealersMap: Map<
    string,
    {
      dealerId: string
      dealerType: DealerType
      sellerType: string
      companyName?: string
      sellerCountry: string
      sellerZipcode: string
    }
  > = new Map()
  const size = 20
  const { document } = new JSDOM().window
  const resultsInventory = await job.runtime.got.paginate.all<Result, string>(baseUrl, {
    searchParams: {
      ustate: 'N,U',
      cy: job.data?.countryInternalCode,
      sort: job.provider === 'autoscout24' ? 'distance' : job.data?.sort,
      desc: job.provider === 'autoscout24' ? 0 : job.data?.desc,
      atype: job.data?.atype,
      powertype: 'kw',
      size,
      page: 1,
      ...(job.data.bodyType && { body: job.data?.bodyType }),
      ocs_listing: job.provider === 'autoscout24' ? 'exclude' : 'ocs-only',
      ...(job.data?.kmfrom && { kmfrom: job.data?.kmfrom }),
      ...(job.data?.kmto && { kmto: job.data?.kmto }),
      ...(job.provider === 'autoscout24' && {
        lon: job.data?.geoData.lon,
        zipr: getDefaultRadius(job),
        lat: job.data?.geoData.lat,
        zip: job.data?.label,
        offer: job.data?.offer,
        ...(job.data?.custtype && { custtype: job.data?.custtype }),
      }),
      ...(job.provider === 'autoscout24_smyle' && {
        pe_category: job.data?.peCategory,
        gear: job.data?.gear,
        ...(job.data?.fregfrom && { fregfrom: job.data?.fregfrom }),
        ...(job.data?.fregto && { fregto: job.data?.fregto }),
      }),
    },
    retry: {
      limit: 5,
      errorCodes: ['ERR_GOT_REQUEST_ERROR'],
    },
    pagination: {
      // backoff: 100,
      paginate: ({ response }) => {
        const currentPage = Number(response.requestUrl.searchParams.get('page'))
        if (isNaN(currentPage) || currentPage === maxPages) return false

        // if (currentPage > maxPages) {
        //   // let pageProps
        //   // const dom = new JSDOM(response.body)?.window?.document.getElementById('__NEXT_DATA__')?.textContent
        //   // if (dom) {
        //   //   const jsonData = JSON.parse(dom)
        //   //   if (jsonData) pageProps = jsonData.props?.pageProps
        //   // }
        //   job.addErrorToLog(new Error('job reached provider page limit, subdivision needed.')
        //   subDivide = true
        //   return false
        // }

        const dom = document?.getElementById('__NEXT_DATA__')?.textContent
        if (!dom) return false

        const pageProps = JSON.parse(dom)?.props?.pageProps
        if (!pageProps?.listings?.length) return false

        if (pageProps.numberOfResults / size > maxPages) {
          job.addErrorToLog(
            `job reached provider page limit, subdivision needed. Number of results: ${pageProps.numberOfResults}`,
          )
          subDivide = true
          return false
        }

        return {
          searchParams: {
            ...response.requestUrl.searchParams,
            page: currentPage + 1,
          },
        }
      },
      transform: ({ body }) => {
        document.body.innerHTML = body
        const dom = document.getElementById('__NEXT_DATA__')?.textContent
        if (!dom) return []

        const pageProps = JSON.parse(dom)?.props?.pageProps
        if (!pageProps?.listings?.length || pageProps.numberOfResults / size > maxPages) return []

        return pageProps.listings.flatMap((item: AutoScout24Listing) => {
          if (!item?.id || item.leasing) return []

          const dealerId = item.seller?.id
          const dealerType =
            item.seller?.type === 'D' || item.seller?.type === 'Dealer' ? DealerType.dealer : DealerType.private

          if (dealerId && !dealersMap.has(dealerId)) {
            dealersMap.set(dealerId, {
              dealerId,
              dealerType,
              sellerType: item.seller?.type,
              ...(item.seller?.companyName && { companyName: item.seller.companyName }),
              sellerCountry: item.location?.countryCode,
              sellerZipcode: item.location?.zip,
            })
          }

          const stockType =
            item.vehicle?.offerType === 'N' || item.vehicle?.offerType === 'New'
              ? ClassifiedsStockType.new
              : ClassifiedsStockType.used
          const priceRaw = item.price.priceFormatted.replace(numberRegex, '').replace('.', '').trim()
          const price = isNaN(Number(priceRaw)) ? 0 : Number(priceRaw)
          return {
            type: ResultType.item,
            data: {
              project: ProjectType.cars,
              itemId: getItemId(DataType.inventory, job.project, item.id),
              url: `https://www.autoscout24.com${item.url}`,
              dataType: DataType.inventory,
              iso: job.data?.iso,
              inventoryId: item.id,
              ...(price && { price }),
              ...(price && { currency: 'EUR' }),
              stockType,
              dealerId: item.seller?.id,
              dealerType,
              zipCode: item.location?.zip,
              vehicleType: VehicleType.car,
            },
          }
        })
      },
    },
  })
  const resultsDealers = [...dealersMap.values()].map((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: `dealer_${job.data?.iso}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: true,
      },
    }
  })

  if (subDivide) {
    await subdivideByKm(job)
  }
  return [...resultsInventory, ...resultsDealers]
}

function getBody(job: JobInstance, page: number) {
  return {
    ...apiSettings.search.graphql,
    variables: {
      ...apiSettings.search.graphql.variables,
      query: getQuery(job, page),
      smyleQuery: getQuery(job, page),
      includeOcs: job.provider === 'autoscout24_smyle',
    },
  }
}

function getDefaultRadius(_job: JobInstance) {
  //return job.data.iso === Iso3166Alpha2.BE ? 4 : 3
  return 4
}

function getQuery(job: JobInstance, page: number) {
  if (job.provider === 'autoscout24_smyle') {
    return new URLSearchParams({
      ustate: 'N,U',
      cy: job.data?.countryInternalCode,
      sort: job.data?.sort,
      desc: job.data?.desc,
      atype: job.data?.atype,
      powertype: 'kw',
      size: LISTINGS_PER_PAGE.toString(),
      page: page.toString(),
      ocs_listing: 'ocs-only',
      pe_category: job.data?.peCategory,
      gear: job.data?.gear,
      ...(job.data.bodyType && { body: job.data?.bodyType }),
      ...(job.data?.make && { mmm: `${job.data?.make}|${job.data.model ? job.data.model : ''}|` }),
      ...(job.data?.kmfrom && job.data?.kmfrom > 0 && { kmfrom: job.data?.kmfrom }),
      ...(job.data?.kmto && job.data?.kmfrom > 0 && { kmto: job.data?.kmto }),
      ...(job.data?.fregfrom && { fregfrom: job.data?.fregfrom }),
      ...(job.data?.fregto && { fregto: job.data?.fregto }),
    }).toString()
  }

  return new URLSearchParams({
    ustate: 'N,U',
    cy: job.data?.countryInternalCode,
    sort: 'age',
    desc: '1',
    atype: job.data?.atype,
    powertype: 'kw',
    size: LISTINGS_PER_PAGE.toString(),
    page: page.toString(),
    ocs_listing: 'exclude',
    lon: job.data?.geoData.lon,
    zipr: getDefaultRadius(job).toString(),
    lat: job.data?.geoData.lat,
    zip: job.data?.label,
    ...(job.data.offer && { offer: job.data?.offer }),
    ...(job.data.bodyType && { body: job.data?.bodyType }),
    ...(job.data?.make && { mmmv: `${job.data?.make}|${job.data.model ? job.data.model : ''}||` }),
    ...(job.data?.kmfrom && job.data?.kmfrom > 0 && { kmfrom: job.data?.kmfrom }),
    ...(job.data?.kmto && job.data?.kmto > 0 && { kmto: job.data?.kmto }),
    ...(job.data?.custtype && { custtype: job.data?.custtype }),
  }).toString()
}

export async function extract(job: JobInstance) {
  let subDivide = false
  const dealersMap: Map<
    string,
    {
      dealerId: string
      dealerType: DealerType
      sellerType: string
      companyName?: string
      sellerCountry: string
    }
  > = new Map()
  const isSubJobLastStep =
    job.data?.kmfrom !== undefined &&
    job.data?.kmto !== undefined &&
    job.data?.kmfrom >= 0 &&
    job.data?.kmto >= 0 &&
    job.data?.kmfrom >= job.data?.kmto

  const listings = await job.runtime.got.paginate.all<Result, AutoscoutApiResponse>(
    'https://listing-search.api.autoscout24.com/graphql',
    {
      method: 'POST',
      headers: apiSettings.headers,
      json: getBody(job, 1),
      responseType: 'json',
      retry: {
        limit: 5,
        errorCodes: ['ERR_GOT_REQUEST_ERROR'],
      },
      pagination: {
        // backoff: 100,
        paginate: ({ response }) => {
          if (!response?.body?.data?.search?.listingsByQueryString?.listings?.length) return false

          const currentPage = response?.body?.data?.search?.listingsByQueryString?.metadata?.currentPage
          const totalPages = response?.body?.data?.search?.listingsByQueryString?.metadata?.totalPages
          const totalItems = response?.body?.data?.search?.listingsByQueryString?.metadata?.totalItems

          if (!currentPage || !totalPages || !totalItems) return false

          if (!isSubJobLastStep && totalPages * LISTINGS_PER_PAGE < totalItems) {
            subDivide = true
            return false
          }

          if (currentPage + 1 > totalPages) return false

          return {
            json: getBody(job, currentPage + 1),
          }
        },
        transform: ({ body }) => {
          if (!body?.data?.search?.listingsByQueryString?.metadata) return []
          if (
            !isSubJobLastStep &&
            body?.data?.search?.listingsByQueryString?.metadata?.totalPages * LISTINGS_PER_PAGE <
              body?.data?.search?.listingsByQueryString?.metadata?.totalItems
          )
            return []
          if (!body?.data?.search?.listingsByQueryString?.listings?.length) return []

          return body?.data?.search?.listingsByQueryString?.listings
            ?.filter((item) => item?.listingData?.identifier?.id)
            ?.map((item) => {
              const inventoryId = item?.listingData?.identifier?.id
              const dealerId = item.listingData.seller?.id
              const dealerType = item.listingData.seller?.type === 'Dealer' ? DealerType.dealer : DealerType.private

              if (dealerId && !dealersMap.has(dealerId)) {
                dealersMap.set(dealerId, {
                  dealerId,
                  dealerType,
                  sellerType: item.listingData.seller?.type,
                  ...(item.listingData.seller?.companyName && { companyName: item.listingData.seller.companyName }),
                  sellerCountry: item.listingData.location.countryCode,
                })
              }

              const stockType =
                item.listingData.vehicle?.legalCategories?.[0]?.formatted === 'New'
                  ? ClassifiedsStockType.new
                  : ClassifiedsStockType.used
              let price = item.listingData.prices.public.amountInEUR.raw
              if (price === 999_999_999 || price === 123_456_789 || price === 9_999_999) price = 0

              return {
                type: ResultType.item,
                data: {
                  project: ProjectType.cars,
                  itemId: `inventory_${job.data?.iso}_${inventoryId}`,
                  url: item.listingData.webPage,
                  dataType: DataType.inventory,
                  iso: job.data?.iso,
                  inventoryId,
                  ...(price && { price, currency: 'EUR' }),
                  stockType,
                  dealerId,
                  dealerType,
                  zipCode: item.listingData.location?.zip,
                  vehicleType: job.data.vehicleType,
                },
              }
            })
        },
      },
    },
  )

  if (subDivide) {
    await subdivide(job)
    return []
  }

  const dealers =
    [...dealersMap.values()]?.map((item) => {
      return {
        type: ResultType.item,
        data: {
          project: ProjectType.cars,
          itemId: `dealer_${job.data?.iso}_${item.dealerId}`,
          iso: job.data?.iso,
          dataType: DataType.dealers,
          dealerId: item.dealerId,
          dealerType: item.dealerType,
          isActive: true,
          isPaying: true,
        },
      }
    }) ?? []

  return [...listings, ...dealers]
}

async function subdivide(job: JobInstance) {
  if (!job.data.offer) return await subdivideByOffer(job)
  if (!job.data.bodyType) return await subdivideByBodyType(job)
  if (!job.data.make) return await subdivideByMake(job)
  if (!job.data.model) return await subdivideByModel(job)
  await subdivideByKm(job)
}

async function subdivideByOffer(job: JobInstance) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) {
    throw new Error(`No scheduledJob with id ${job._id}`)
  }

  const jobs = offers?.map((offer) => {
    return {
      controllerId: scheduledJob?.controllerId,
      createdAt: new Date(),
      updatedAt: new Date(),
      project: job.project,
      provider: job.provider,
      type: job.type,
      data: {
        ...job.data,
        offer,
      },
    }
  })
  await job.runtime.addJobs(jobs)
}

async function subdivideByBodyType(job: JobInstance) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) {
    throw new Error(`No scheduledJob with id ${job._id}`)
  }

  const bodyTypes = vehicleBodyTypes.find((item) => item.atype === job.data.atype)?.bodyTypes

  if (bodyTypes?.length) {
    const jobs = bodyTypes?.map((bodyType) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          bodyType,
        },
      }
    })
    await job.runtime.addJobs(jobs)
  } else return await subdivideByMake(job)
}

async function subdivideByMake(job: JobInstance) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) {
    throw new Error(`No scheduledJob with id ${job._id}`)
  }

  const { document } = new JSDOM().window

  const makes = await job.runtime
    .got('https://www.autoscout24.com/refinesearch', {
      searchParams: {
        atype: job.data.atype,
      },
      retry: {
        limit: 5,
        statusCodes: [403, 441],
        errorCodes: ['ERR_GOT_REQUEST_ERROR'],
      },
      hooks: {
        afterResponse: [
          (response) => {
            if (typeof response.body !== 'string') throw new Error('Malformed response body')

            document.body.innerHTML = response.body

            const nextData = document.getElementById('__NEXT_DATA__')?.textContent
            if (!nextData) {
              throw new RetryError(response.request)
            }

            response.rawBody = Buffer.from(nextData)
            return response
          },
        ],
      },
    })
    .text()
    .then((res) => {
      const makeLabels = Object.keys(JSON.parse(res)?.props?.pageProps?.taxonomy?.makeLabels)
      if (!makeLabels?.length) throw new Error('Unable to extract makes, JSON parsing failed\n' + res)

      return makeLabels
    })

  if (makes?.length) {
    const jobs = makes?.map((make) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          make,
        },
      }
    })
    await job.runtime.addJobs(jobs)
  }
}

async function subdivideByModel(job: JobInstance) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) {
    throw new Error(`No scheduledJob with id ${job._id}`)
  }

  const { document } = new JSDOM().window

  const models = await job.runtime
    .got('https://www.autoscout24.com/refinesearch', {
      searchParams: {
        atype: job.data.atype,
        mmmv: `${job.data.make}|||`,
      },
      retry: {
        limit: 5,
        statusCodes: [403, 441],
        errorCodes: ['ERR_GOT_REQUEST_ERROR'],
      },
      hooks: {
        afterResponse: [
          (response) => {
            if (typeof response.body !== 'string') throw new Error('Malformed response body')

            document.body.innerHTML = response.body

            const nextData = document.getElementById('__NEXT_DATA__')?.textContent
            if (!nextData) {
              throw new RetryError(response.request)
            }

            response.rawBody = Buffer.from(nextData)
            return response
          },
        ],
      },
    })
    .text()
    .then((res) => {
      const models = JSON.parse(res)?.props?.pageProps?.taxonomy?.models?.[job.data.make]
      return models?.map((model) => model.value)
    })

  if (models?.length) {
    const jobs = models?.map((model) => {
      return {
        controllerId: scheduledJob?.controllerId,
        createdAt: new Date(),
        updatedAt: new Date(),
        project: job.project,
        provider: job.provider,
        type: job.type,
        data: {
          ...job.data,
          model,
        },
      }
    })
    await job.runtime.addJobs(jobs)
  }
}

async function subdivideByKm(job: JobInstance) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) {
    throw new Error(`No scheduledJob with id ${job._id}`)
  }

  const kmfrom = job.data?.kmfrom && !isNaN(job.data?.kmfrom) ? job.data?.kmfrom : -1
  const kmto = job.data?.kmto && !isNaN(job.data?.kmto) ? job.data?.kmto : -1

  let kmMiddle: number
  let kmsArray: { kmfrom?: number; kmto?: number }[]
  if (kmfrom < 0 && kmto < 0) {
    kmsArray = [
      { kmfrom, kmto: 25000 },
      { kmfrom: 25001, kmto: 50000 },
      { kmfrom: 50001, kmto },
    ]
  } else {
    if (kmfrom >= 0 && kmto >= 0 && kmfrom >= kmto) {
      job.addErrorToLog(new Error('No more km subdivision'))
      return
    }

    if (kmto > 0 && kmfrom > 0) {
      kmMiddle = Math.floor((kmfrom + kmto) / 2)
    } else if (kmto > 0) kmMiddle = Math.floor(kmto / 2)
    else if (kmfrom > 0) {
      kmMiddle = kmfrom + Math.floor(kmfrom / 2)
    }

    if (isNaN(kmMiddle)) {
      job.addErrorToLog(new Error(`Error in km range: ${JSON.stringify(job.data)}`))
      return
    } else
      kmsArray = [
        { kmfrom, kmto: Math.max(kmfrom, kmMiddle) },
        { kmfrom: Math.min(kmMiddle + 1, kmto), kmto },
      ]
  }

  const jobs = kmsArray.flatMap((item) => {
    if (!Object.keys(item).length) return []
    return {
      controllerId: scheduledJob?.controllerId,
      createdAt: new Date(),
      updatedAt: new Date(),
      project: job.project,
      provider: job.provider,
      type: job.type,
      data: {
        ...job.data,
        ...item,
      },
    }
  })
  await job.runtime.addJobs(jobs)
}

function getPriceRangeDbSwitch(iso: Iso3166Alpha2) {
  if (iso === Iso3166Alpha2.CH) return priceRangeDbSwitchCH
  return undefined
}

export async function setListingsPriceRanges(
  config: ControllerConfig,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
  dateArg: string | Date = new Date(),
) {
  const $switch = getPriceRangeDbSwitch(iso)
  if (!$switch) return

  const date = new Date(dateArg)
  await runtime.collection<Item>('item').updateMany(
    {
      projectProviderDataType: getProjectProviderDataType(config.project, config.provider, DataType.inventory),
      iso,
      updatedAt: { $gte: startOfISOWeek(date), $lt: startOfISOWeek(addWeeks(date, 1)) },
    },
    [
      {
        $set: {
          priceRange: {
            $switch,
          },
        },
      },
    ],
  )
}
