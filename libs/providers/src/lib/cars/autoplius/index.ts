import { startOfISOWeek } from 'date-fns'
import { J<PERSON><PERSON> } from 'jsdom'
import UserAgent from 'user-agents'

import {
  CarsStockType,
  AuctionsDealerType,
  ProjectType,
  DataType,
  ResultType,
  VehicleType,
  MarketplacesDealerType,
} from '@datagatherers/datagatherers'
import { getItemId, wait } from '@datagatherers/worker-utils'

import { CLOUDFLARE_SELECTOR, MAX_LISTINGS_PER_JOB, MAX_RETRIES, PACKAGE_DIC } from './constants'

import type {
  JobDefinition,
  ControllerRuntime,
  DealerType,
  JobInstance,
  Iso3166Alpha2,
  Item,
} from '@datagatherers/datagatherers'

export async function scheduleExtractDealerDetails(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  return (
    await runtime
      .collection<Item>(ResultType.item)
      .find(
        {
          projectProviderDataType: 'cars_autoplius_dealers',
          dealerType: 'dealer',
          iso,
          updatedAt: { $gte: startOfISOWeek(new Date()) },
        },
        {
          projection: {
            _id: 0,
            itemId: 1,
            dealerId: 1,
            productType: 1,
            data: 1,
          },
        },
      )
      .toArray()
  ).map((dealer) => ({
    ...job,
    data: {
      ...dealer,
      iso,
    },
  }))
}

export async function extractDealerDetails(job: JobInstance) {
  await job.runtime.page.goto('https://m.autoplius.lt/', { waitUntil: 'networkidle2' })

  let packageRaw: string

  for (let attempts = 0; attempts <= MAX_RETRIES; attempts++) {
    try {
      await wait(1500)

      await job.runtime.page.goto(job.data.dealerId, {
        waitUntil: 'networkidle2',
      })

      const challengeElement = await job.runtime.page.$(CLOUDFLARE_SELECTOR)
      if (challengeElement) {
        throw new Error('Cloudflare challenge detected')
      }

      const htmlImport = await job.runtime.page.content()
      if (!htmlImport) {
        throw new Error('No HTML content')
      }

      const dom = new JSDOM(htmlImport)
      const document = dom.window.document

      packageRaw = document.querySelector('.award-level')?.textContent?.trim()

      break
    } catch (error) {
      attempts++
      if (attempts >= MAX_RETRIES) {
        throw new Error(
          `Failed to process dealer ${job.data.dealerId} after ${MAX_RETRIES} attempts, message: ${error.message}`,
        )
      } else {
        const userAgent = new UserAgent({ deviceCategory: 'desktop' })
        await job.runtime.page.setUserAgent(userAgent.toString())

        await wait(1000)
      }
    }
  }
  const productType = packageRaw ? (PACKAGE_DIC[packageRaw] ?? PACKAGE_DIC.unknown) : PACKAGE_DIC.default

  return [
    {
      type: ResultType.item,
      data: {
        itemId: job.data.itemId,
        dealerId: job.data.dealerId,
        iso: job.data.iso,
        productType,
        data: {
          ...job.data.data,
          previousProductType: job.data.productType,
          productType,
          packageRaw,
        },
      },
    },
  ]
}

export async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const jobs: JobDefinition[] = []

  const { document } = new JSDOM().window

  for (const key of Object.keys(CarsStockType)) {
    const cities = await runtime
      .got('https://m.autoplius.lt/paieska/reiksmes-parinkimas/naudoti-automobiliai', {
        searchParams: {
          category_id: 2,
          fk_place_countries_id: 1,
          is_condition_new: key === CarsStockType.new ? 1 : 0,
          field_name: 'fk_place_cities_id',
          qt: '',
          order_by: 1,
          order_direction: 'DESC',
        },
        retry: {
          limit: 10,
          statusCodes: [403],
        },
      })
      .then((response) => {
        document.body.innerHTML = response.body
        return Array.from(document.querySelectorAll('ul.search-field-selector li[id]')).flatMap((cityElem) => {
          const cityId = cityElem?.getAttribute('id')?.split('_')[1]
          if (!cityId) return []
          const name = cityElem.querySelector('a')?.getAttribute('title')
          if (!name) return []

          const nrListings = Number(cityElem.querySelector('span')?.textContent?.replace(/\D/g, ''))
          if (!nrListings) return []

          return { cityId, name, nrListings }
        })
      })

    if (!cities?.length) continue

    for (const city of cities) {
      if (city?.nrListings < MAX_LISTINGS_PER_JOB) {
        jobs.push({
          ...job,
          data: {
            iso,
            cityId: city.cityId,
            name: city.name,
            stockType: key,
            nrListings: city.nrListings,
          },
        })
        continue
      }

      const makes = await runtime
        .got('https://m.autoplius.lt/paieska/reiksmes-parinkimas/naudoti-automobiliai', {
          searchParams: {
            category_id: 2,
            fk_place_countries_id: 1,
            fk_place_cities_id: city.cityId,
            is_condition_new: key === CarsStockType.new ? 1 : 0,
            field_name: 'make_id',
            qt: '',
            order_by: 1,
            order_direction: 'DESC',
          },
          retry: {
            limit: 10,
            statusCodes: [403],
          },
        })
        .then((response) => {
          document.body.innerHTML = response.body
          return Array.from(document.querySelectorAll('ul.search-field-selector li[id^="item"]')).flatMap(
            (makeElem) => {
              const makeId = makeElem?.getAttribute('id')?.split('_')[1]
              if (!makeId) return []
              const nrListings = Number(makeElem.querySelector('span')?.textContent?.replace(/\D/g, ''))
              if (!nrListings) return []

              return { makeId, nrListings }
            },
          )
        })
      jobs.push(
        ...makes
          .filter((make) => make?.nrListings)
          .map((make) => ({
            ...job,
            data: {
              iso,
              cityId: city.cityId,
              name: city.name,
              stockType: key,
              makeId: make.makeId,
              nrListings: make.nrListings,
            },
          })),
      )
    }
  }
  return jobs
}

export async function extract(job: JobInstance) {
  const itemDataList: itemDataType[] = []
  let lastPage = 1

  for (let pageNr = 1; pageNr <= lastPage; pageNr++) {
    let attempts = 0

    while (attempts < MAX_RETRIES) {
      try {
        await wait(1000)

        const searchParams = new URLSearchParams({
          category_id: '2',
          fk_place_countries_id: '1',
          fk_place_cities_id: job.data?.cityId?.toString() || '',
          is_condition_new: job.data?.stockType === CarsStockType.new ? '1' : '0',
          qt: '',
          order_by: '1',
          order_direction: 'DESC',
          page_nr: pageNr.toString(),
          ...(job.data?.makeId && { [`make_id[${job.data?.makeId}]`]: '0' }),
        })

        await job.runtime.page.goto(`https://m.autoplius.lt/skelbimai/naudoti-automobiliai?${searchParams}`, {
          waitUntil: 'networkidle2',
        })

        const challengeElement = await job.runtime.page.$(CLOUDFLARE_SELECTOR)
        if (challengeElement) {
          throw new Error('Cloudflare challenge detected')
        }

        const htmlImport = await job.runtime.page.content()
        if (!htmlImport) {
          throw new Error('No HTML content')
        }

        const dom = new JSDOM(htmlImport)
        const document = dom.window.document

        if (pageNr === 1) {
          const paginationText = document.querySelector('div.paging')?.textContent
          const newLastPage = Number(paginationText?.split('/')?.[1]?.trim())
          if (newLastPage) {
            lastPage = newLastPage
          }
        }

        const listItems = Array.from(document.querySelectorAll('.list-items a'))
        if (!listItems?.length) {
          throw new Error('No list items found')
        }

        listItems.forEach((element) => {
          let price = Number(
            element
              .querySelector('div.pricing-container > strong')
              ?.firstChild?.nodeValue?.replace(/[^0-9.]/g, '')
              .trim(),
          )
          if (!price) {
            price = Number(
              element
                .querySelector('div.pricing-container > strong > span.promotion > span.promo-price')
                ?.firstChild?.nodeValue?.replace(/[^0-9.]/g, '')
                .trim(),
            )
          }
          const url = element.getAttribute('href')
          const id = element.getAttribute('data-id')

          let dealerType: MarketplacesDealerType
          let dealerId: string | undefined

          const dealerElement = element.querySelector('div[class*="js-item-owner"]')
          if (dealerElement && dealerElement.getAttribute('data-url')) {
            dealerId = dealerElement.getAttribute('data-url')
            dealerType = MarketplacesDealerType.dealer
          } else {
            dealerType = MarketplacesDealerType.private
          }

          if (id) {
            itemDataList.push({ price, url, id, dealerId, dealerType })
          }
        })

        break
      } catch {
        attempts++
        if (attempts >= MAX_RETRIES) {
          job.addErrorToLog(new Error(`Failed to process page ${pageNr} after ${MAX_RETRIES} attempts`))
        } else {
          const userAgent = new UserAgent({ deviceCategory: 'desktop' })
          await job.runtime.page.setUserAgent(userAgent.toString())

          await wait(1000)
        }
      }
    }
  }

  if (!itemDataList?.length) {
    return []
  }

  const dealersMap: Map<string, { dealerId: string; dealerType: DealerType }> = new Map()

  const resultsInventory = itemDataList.flatMap((item) => {
    if (!item.id) return []
    const dealerType = item.dealerType
    if (item.dealerId) {
      if (!dealersMap.has(item.dealerId)) {
        dealersMap.set(item.dealerId, {
          dealerId: item.dealerId,
          dealerType: dealerType as DealerType,
        })
      }
    }
    const data = {
      project: ProjectType.cars,
      dataType: DataType.inventory,
      iso: job.data?.iso,
      itemId: getItemId(DataType.inventory, job.project, item.id),
      inventoryId: item.id,
      ...(item.dealerId && { dealerId: item.dealerId }),
      dealerType,
      stockType: job.data?.stockType,
      ...(item.price && { price: item.price, currency: 'EUR' }),
      url: item.url,
      vehicleType: VehicleType.car,
    }

    return {
      type: ResultType.item,
      data,
    }
  })

  const resultsDealers = [...dealersMap.values()].map((item) => {
    const data = {
      project: ProjectType.cars,
      itemId: getItemId(DataType.dealers, job.project, item.dealerId),
      iso: job.data?.iso,
      dataType: DataType.dealers,
      dealerId: item.dealerId,
      dealerType: item.dealerType,
      isActive: true,
      isPaying: item.dealerType === AuctionsDealerType.dealer,
    }

    return {
      type: ResultType.item,
      data,
    }
  })

  return [...resultsInventory, ...resultsDealers]
}
interface itemDataType {
  price: number
  url: string
  id?: string
  dealerType: string
  dealerId?: string
}
