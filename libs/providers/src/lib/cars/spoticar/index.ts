import {
  DealerType,
  ClassifiedsStockType,
  DataType,
  ProjectType,
  ResultType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { getItemId } from '@datagatherers/worker-utils'

import type { SpoticarVehiclesResponse } from './types'
import type { ControllerRuntime, Iso3166Alpha2, JobDefinition, JobInstance, Result } from '@datagatherers/datagatherers'

export async function scheduleListings(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const models = await runtime
    .got(`https://www.spoticar.${iso.toLowerCase()}/api/vehicleoffers/paginate/search`, {
      method: 'GET',
      searchParams: {
        page: 1,
      },
      retry: {
        limit: 5,
        statusCodes: [403],
      },
    })
    .json<SpoticarVehiclesResponse>()
    .then((res) => res.aggregation.select_filter.model)

  if (typeof models === 'undefined' || !models?.length) return []

  const jobs = []

  for (const model of models) {
    if (!model?.doc_count) continue
    if (model.doc_count < 4000) {
      jobs.push({ ...job, data: { iso, model } })
      continue
    }

    const energies = await runtime
      .got(`https://www.spoticar.${iso.toLowerCase()}/api/vehicleoffers/paginate/search`, {
        method: 'GET',
        searchParams: {
          page: 1,
          'filters[0][model]': model.key,
        },
        retry: {
          limit: 5,
          statusCodes: [403],
        },
      })
      .json<SpoticarVehiclesResponse>()
      .then((res) => res.aggregation.checkbox_filter.energy)

    if (typeof energies === 'undefined' || !energies?.length) {
      jobs.push({ ...job, data: { iso, model } })
    } else {
      jobs.push(
        ...energies.flatMap((energy) => {
          if (!energy?.doc_count) return []

          return { ...job, data: { iso, model, energy } }
        }),
      )
    }
  }

  return jobs
}

export async function extractListings(job: JobInstance) {
  const iso = job.data?.iso ?? undefined
  if (!iso) return []

  const dealersMap: Map<
    string,
    {
      dealerId: string
      dealerType: DealerType
    }
  > = new Map()

  const inventory = await job.runtime.got.paginate.all<Result, SpoticarVehiclesResponse>(
    `https://www.spoticar.${iso.toLowerCase()}/api/vehicleoffers/paginate/search`,
    {
      method: 'GET',
      searchParams: {
        page: 1,
        sort: 'year_asc',
        ...(job.data?.model?.key && { 'filters[0][model]': job.data?.model.key }),
        ...(job.data?.model?.key && job.data?.energy?.key && { 'filters[1][energy]': job.data?.energy.key }),
      },
      responseType: 'json',
      retry: {
        limit: 10,
        statusCodes: [403],
      },
      pagination: {
        backoff: 200,
        paginate: ({ response }) => {
          if (!response?.body?.hits?.length) {
            return false
          }

          const lastPage = response.body.lastPage
          const currentPage = Number(response.requestUrl.searchParams.get('page'))
          if (isNaN(currentPage) || currentPage === lastPage) {
            return false
          }

          return {
            searchParams: {
              ...response.requestUrl.searchParams,
              page: currentPage + 1,
            },
          }
        },
        transform: ({ body }) => {
          if (!body?.hits?.length) return []

          return body.hits.flatMap((item) => {
            if (!item?._source?.field_vo_refbase?.length) return []

            const dealerId = item._source.field_pdv_geo_id[0] ?? undefined
            const dealerType = DealerType.dealer

            if (dealerId && !dealersMap.has(dealerId)) {
              dealersMap.set(dealerId, {
                dealerId,
                dealerType,
              })
            }

            const stockType = ClassifiedsStockType.used
            const price = isNaN(Number(item._source.field_vo_prix_base[0]))
              ? 0
              : Number(item._source.field_vo_prix_base[0])

            return {
              type: ResultType.item,
              data: {
                project: ProjectType.cars,
                itemId: getItemId(DataType.inventory, ProjectType.cars, item._source.field_vo_refbase[0].toString()),
                ...(item._source.url?.[0] && {
                  url: `https://www.spoticar.${iso.toLowerCase()}${item._source.url[0]}`,
                }),
                dataType: DataType.inventory,
                iso,
                inventoryId: item._source.field_vo_refbase[0].toString(),
                ...(price && {
                  price,
                  ...(item._source.field_vo_pf_devise?.[0] && { currency: item._source.field_vo_pf_devise[0] }),
                }),
                stockType,
                dealerId,
                dealerType,
                vehicleType: VehicleType.car,
              },
            }
          })
        },
      },
    },
  )

  const dealers = [...dealersMap.values()].map((item) => {
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.cars,
        itemId: getItemId(DataType.dealers, job.project, item.dealerId),
        iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        isActive: true,
        isPaying: true,
      },
    }
  })

  return [...inventory, ...dealers]
}
