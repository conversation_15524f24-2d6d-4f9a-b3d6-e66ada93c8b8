import {
  ResultType,
  DataType,
  ProjectType,
  ClassifiedsStockType,
  DealerType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { getItemId } from '@datagatherers/worker-utils'

import { configuration } from './constants'

import type { SpotawheelResponse } from './types'
import type { JobDefinition, Iso3166Alpha2, Result, JobInstance } from '@datagatherers/datagatherers'

export function scheduler(job: JobDefinition, iso: Iso3166Alpha2): JobDefinition[] {
  return [{ ...job, data: { iso } }]
}

export async function extract(job: JobInstance) {
  return job.runtime.got.paginate.all<Result, SpotawheelResponse>(
    `https://www.spotawheel.${job.data?.iso.toLowerCase()}/buy`,
    {
      method: 'GET',
      searchParams: configuration.searchParams,
      headers: configuration.headers,
      retry: configuration.retry,
      responseType: 'json',
      pagination: {
        backoff: configuration.backoff,
        paginate: ({ response }) => {
          if (
            !response?.body?.props?.classifiedsActive?.meta?.current_page ||
            !response?.body?.props?.classifiedsActive?.meta?.last_page
          )
            return false

          const currentPage = response?.body?.props?.classifiedsActive?.meta?.current_page
          const lastPage = response?.body?.props?.classifiedsActive?.meta?.last_page
          if (currentPage >= lastPage) return false

          return {
            searchParams: {
              ...configuration.searchParams,
              page: currentPage + 1,
            },
          }
        },
        transform: ({ body }) => {
          if (!body?.props?.classifiedsActive?.data?.length) return []
          return body.props.classifiedsActive.data
            .filter((item) => item?.active && item?.id)
            .map((item) => {
              return {
                type: ResultType.item,
                data: {
                  project: ProjectType.cars,
                  itemId: getItemId(DataType.inventory, job.project, item.id.toString()),
                  url: item.permalink,
                  dataType: DataType.inventory,
                  iso: job.data?.iso,
                  inventoryId: item.id.toString(),
                  price: item.display_final_price ?? 0,
                  currency: 'EUR',
                  stockType: ClassifiedsStockType.used,
                  dealerId: 'spotawheel',
                  dealerType: DealerType.dealer,
                  vehicleType: VehicleType.car,
                },
              }
            })
        },
      },
    },
  )
}
