import UserAgent from 'user-agents'

export const configuration = {
  searchParams: {
    page: 1,
    order_by: 'released_at',
    order_method: 'desc',
  },
  headers: {
    'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
    'x-inertia': 'true',
    // 'x-inertia-partial-component': 'Listing',
    // 'x-inertia-partial-data':
    //   'breadcrumbs,classifiedsActive,classifiedsActiveInactiveCount,classifiedsActiveTotalCount,classifiedsDeactivated,classifiedsSuggested,dimension1,filters,productMode,requestParams,requestParamsCount,seo',
    'x-requested-with': 'XMLHttpRequest',
    'x-xsrf-token':
      'eyJpdiI6IkthR2Y4c2tQbTZwNTBIZG5hR3BBV3c9PSIsInZhbHVlIjoieXplZllTRGUyMWpuMkpRSWhEVmdLWnk1bXhxVHpmMHAvTlFCdndVcFRyVlhrRXpuL0huS0gyUjFlbkg0NkNJMWU2U2kvRUgyR2J6WFZXRGpySER0UlUrK1J1YkVFeGpxZjVuL3R4bFJtWjJaN0VhdnkrNXBlRXh1bk9UbmdseFgiLCJtYWMiOiI0MWRiYjFjMTFlM2QzMjNjMWUwMGM1OWQ4MzkzYWU2NDAyY2ZjMDlmOGZiMjVkNzdhYzdhZTk5YWUyYWRjODBhIiwidGFnIjoiIn0=',
  },
  backoff: 200,
  retry: { limit: 5 },
}
