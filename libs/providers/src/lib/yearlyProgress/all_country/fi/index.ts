import type { PxdataResponse } from './types'
import type { JobInstance } from '@datagatherers/datagatherers'

// export function scheduleOfv(job: JobDefinition, startDate?: Date, endDate?: Date): JobDefinition[] {
//   const iso = Iso3166Alpha2.NO
//   const dataType = DataType.yearlyProgress
//   const dates = []

//   for (
//     let date = startDate ? startDate : subMonths(new Date(), 1);
//     isBefore(date, endDate ? endDate : startOfMonth(new Date()));
//     date = addMonths(date, 1)
//   ) {
//     dates.push(date)
//   }

//   return dates.flatMap((date) => {
//     if (!date || !isValid(date)) return []
//     return {
//       ...job,
//       data: {
//         iso,
//         dataType,
//         date,
//       },
//     }
//   })
// }

// export async function extractOfv(job: JobInstance) {
//   const { document } = new JSDOM().window
//   const stockTypes = ['new_registration', 'used_registration']
//   const snapDate = new Date(job.data?.date)

//   const defaultLastSnapshot = {
//     project: job.project,
//     provider: job.provider,
//     iso: job.data?.iso,
//     dataType: job.data?.dataType,
//     snapTime: format(snapDate, 'yyyy/MM'),
//     snapTimeUnit: TimeUnit.months,
//   }

//   const snapshot = await job.runtime.collection<Snapshot>('snapshot').findOne(defaultLastSnapshot)
//   const snapshotStats = snapshot?.stats?.filter((item: { stockType: string }) => stockTypes.includes(item?.stockType))

//   if (snapshotStats?.length === stockTypes.length) return []

//   const localeMonth = format(snapDate, 'MMMM yyyy', { locale: nb })

//   const data = await job.runtime
//     .got(`https://ofv.no/bilsalget/bilsalget-i-${localeMonth.toLowerCase().replace(' ', '-')}`, {
//       method: 'GET',
//     })
//     .text()

//   if (!data) {
//     return []
//   }

//   document.body.innerHTML = data

//   const { strategy, url } = chooseStrategy(document)

//   let newStats: { count: number; stockType: string }[] = []
//   switch (strategy) {
//     case 'extractOriginalPage': {
//       newStats = getNewStatsFromPage(document, stockTypes)
//       await updateSnapshot(job, newStats, defaultLastSnapshot)
//       break
//     }
//     case 'extractIframe': {
//       await job.runtime.page.goto(url, { waitUntil: 'networkidle0' })
//       const iframeData = await job.runtime.page.content()

//       if (!iframeData) throw new Error('Could no get iframe data from puppeteer')

//       document.body.innerHTML = iframeData

//       newStats = getNewStatsFromIframe(document)
//       await updateSnapshot(job, newStats, defaultLastSnapshot)
//       break
//     }
//     default:
//       throw new Error('No strategy found')
//   }

//   return []
// }

// function chooseStrategy(document: Document) {
//   const iframeSource = document.querySelector('iframe')?.getAttribute('src')

//   if (!iframeSource) {
//     return {
//       strategy: 'extractOriginalPage',
//       url: null,
//     }
//   }

//   return {
//     strategy: 'extractIframe',
//     url: iframeSource,
//   }
// }

// async function updateSnapshot(
//   job: JobInstance,
//   newStats: { count: number; stockType: string }[],
//   defaultLastSnapshot: MatchKeysAndValues<Snapshot>,
// ) {
//   if (newStats.length && !job.runtime.skipStoreData) {
//     await job.runtime.collection<Snapshot>('snapshot').updateOne(
//       {
//         ...defaultLastSnapshot,
//         controllerId: '-1',
//       },
//       {
//         $setOnInsert: {
//           ...defaultLastSnapshot,
//           controllerId: '-1',
//           createdAt: new Date(),
//         },
//         $set: {
//           updatedAt: new Date(),
//         },
//         $addToSet: {
//           stats: {
//             $each: newStats,
//           },
//         },
//       },
//       { upsert: true, writeConcern: { w: 'majority' } },
//     )
//   }
// }

// function getNewStatsFromPage(document: Document, stockTypes: string[]) {
//   const tables = document.querySelectorAll('article[class*="content"] > div > div')
//   if (!tables?.length) throw new Error('Number of tables is zero')

//   const stats: { count: number; stockType: string }[] = []

//   for (const table of Array.from(tables)) {
//     const category = table.querySelector('h3')?.textContent?.trim()
//     if (!category?.length || !category.match('Her er hovedtallene for ')) continue

//     const rows = table.querySelectorAll('tr')
//     if (!rows?.length) continue

//     for (const row of Array.from(rows)) {
//       const rowValues = Array.from(row.querySelectorAll('td'))
//       if (!rowValues?.length) continue

//       const rowTitle = rowValues[0]?.textContent?.trim()
//       if (!rowTitle || !['Nye', 'Eierskifter'].includes(rowTitle)) continue

//       const stockType = rowTitle === 'Nye' ? 'new_registration' : 'used_registration'
//       const count = Number(rowValues[1]?.textContent?.replace(/[^0-9]/g, '').trim())
//       if (!isNaN(count) && count) {
//         stats.push({ count, stockType })
//       }
//       if (stats?.length === stockTypes.length) break
//     }
//     if (stats?.length === stockTypes.length) break
//   }

//   if (stats.length) return stats

//   throw new Error('Unable to get stats from original page')
// }

// function getNewStatsFromIframe(document: Document) {
//   const stats: { count: number; stockType: string }[] = []

//   const NyeCount = Number(
//     document
//       .querySelector('div[title="Nye"]')
//       ?.parentElement?.nextElementSibling?.textContent?.replace(/\D/, '')
//       .trim(),
//   )
//   const EierskifterCount = Number(
//     document
//       .querySelector('div[title="Eierskifter"]')
//       ?.parentElement?.nextElementSibling?.textContent?.replace(/\D/, '')
//       .trim(),
//   )

//   if (!isNaN(NyeCount) && NyeCount) {
//     stats.push({ count: NyeCount, stockType: 'new_registration' })
//   }
//   if (!isNaN(EierskifterCount) && EierskifterCount) {
//     stats.push({ count: EierskifterCount, stockType: 'used_registration' })
//   }

//   if (stats.length) return stats

//   throw new Error('Unable to get stats from iframe')
// }

// ./worker realestates all_country reGraphsNO -L
export async function extractReGraphsFI(job: JobInstance) {
  // hardcoded cookie below
  await job.runtime
    .got('https://pxdata.stat.fi/PxWeb/api/v1/en/StatFin/ashi/statfin_ashi_pxt_13ms.px', {
      method: 'POST',
      json: {
        query: [
          {
            code: 'Alue',
            selection: {
              filter: 'item',
              values: ['ksu'],
            },
          },
          {
            code: 'Tiedot',
            selection: {
              filter: 'item',
              values: ['lkm_julk_kvkl', 'myyntiaika'], // lkm_julk_kvkl = number of sales, myyntiaika = avg days to sell
            },
          },
        ],
        response: {
          format: 'json-stat2',
        },
      },
    })
    .json<PxdataResponse>()
    .then((res) => {
      const months = Object.keys(res.dimension.Kuukausi.category.index).map((item) => ({
        key: item,
        snapshotValue: item.replaceAll('M', '/'),
        index: res.dimension.Kuukausi.category.index[item],
      }))
      console.log(months)
      const values = res.value
      //const tiedot = res.dimension.Tiedot.category.index

      console.log(months, values)
    })

  //   await job.runtime.collection<Snapshot>('snapshot').updateOne(
  //     {
  //       dataType: DataType.yearlyProgress,
  //       project: ProjectType.realestates,
  //       provider: 'all_country',
  //       iso: Iso3166Alpha2.NO,
  //       snapTimeUnit: TimeUnit.months,
  //       snapTime: format(monthlyDate, 'yyyy/MM'),
  //     },
  //     {
  //       $setOnInsert: {
  //         controllerId: '-1',
  //         dataType: DataType.yearlyProgress,
  //         project: ProjectType.realestates,
  //         provider: 'all_country',
  //         iso: Iso3166Alpha2.NO,
  //         snapTimeUnit: TimeUnit.months,
  //         snapTime: format(monthlyDate, 'yyyy/MM'),
  //         createdAt: new Date(),
  //       },
  //       $set: { updatedAt: new Date() },
  //       $addToSet: {
  //         stats: { $each: parsedMonth.payload },
  //       },
  //     },
  //     { upsert: true, writeConcern: { w: 'majority' } },
  //   )
  // }
  return []
}
