import { DataType, Iso3166Alpha2, TimeUnit, type JobInstance, type Snapshot } from '@datagatherers/datagatherers'

import type { PxdataResponse } from './types'

// ./worker realestates all_country reGraphsFI -s
export async function extractReGraphsFI(job: JobInstance) {
  const snapshots: Array<{ snapTime: string; stats: { count: number; stockType: string }[] }> = []

  await job.runtime
    .got('https://pxdata.stat.fi/PxWeb/api/v1/en/StatFin/ashi/statfin_ashi_pxt_13ms.px', {
      method: 'POST',
      json: {
        query: [
          {
            code: 'Alue',
            selection: {
              filter: 'item',
              values: ['ksu'],
            },
          },
          {
            code: 'Tiedot',
            selection: {
              filter: 'item',
              values: ['lkm_julk_kvkl', 'myyntiaika'], // lkm_julk_kvkl = number of sales, myyntiaika = avg days to sell
            },
          },
        ],
        response: {
          format: 'json-stat2',
        },
      },
    })
    .json<PxdataResponse>()
    .then((res) => {
      const months = Object.keys(res.dimension.Kuukausi.category.index).map((item) => ({
        key: item,
        snapshotValue: item.replaceAll('M', '/'),
        index: res.dimension.Kuukausi.category.index[item],
        targetIndexes: [
          res.dimension.Kuukausi.category.index[item] * 2,
          res.dimension.Kuukausi.category.index[item] * 2 + 1,
        ],
      }))
      const values = res.value

      for (const month of months) {
        snapshots.push({
          snapTime: month.snapshotValue,
          stats: [
            { count: values[month.targetIndexes[0]], stockType: 'volume_sold' },
            { count: values[month.targetIndexes[1]], stockType: 'avg_days_to_sell' },
          ],
        })
      }
    })

  if (snapshots?.length && !job.runtime.skipStoreData) {
    for (const snapshot of snapshots) {
      await job.runtime.collection<Snapshot>('snapshot').updateOne(
        {
          dataType: DataType.yearlyProgress,
          project: job.project,
          provider: 'all_country',
          iso: Iso3166Alpha2.FI,
          snapTimeUnit: TimeUnit.months,
          snapTime: snapshot.snapTime,
        },
        {
          $setOnInsert: {
            controllerId: '-1',
            dataType: DataType.yearlyProgress,
            project: job.project,
            provider: 'all_country',
            iso: Iso3166Alpha2.FI,
            snapTimeUnit: TimeUnit.months,
            snapTime: snapshot.snapTime,
            createdAt: new Date(),
          },
          $set: { updatedAt: new Date(), stats: snapshot.stats },
        },
        { upsert: true, writeConcern: { w: 'majority' } },
      )
    }
  }
  return []
}
