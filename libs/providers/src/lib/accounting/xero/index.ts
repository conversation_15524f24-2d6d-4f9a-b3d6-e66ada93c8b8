import { DataType, Iso3166Alpha2, ResultType } from '@datagatherers/datagatherers'
import { DirectSnapshotGenerator } from '@datagatherers/worker-utils'

import { isos } from './constants'

import type { JobDefinition, JobInstance, ResultItem } from '@datagatherers/datagatherers'

// const ITEMS_PER_PAGE = 10

// export async function scheduleAdvisors(
//   job: JobDefinition,
//   runtime: ControllerRuntime,
//   iso: Iso3166Alpha2,
// ): Promise<JobDefinition[]> {
//   const URL =
//     iso === Iso3166Alpha2.WW
//       ? `https://www.xero.com/advisors/find-advisors/` //?type=advisors&orderBy=ADVISOR_RELEVANCE&sort=ASC&pageNumber=`
//       : `https://www.xero.com/${iso.toLowerCase()}/advisors/find-advisors/` //?type=advisors&orderBy=ADVISOR_RELEVANCE&sort=ASC&pageNumber=`

//   const adsTotal = Number(
//     await runtime
//       .got(URL + 1, {
//         retry: {
//           limit: 3,
//           errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
//         },
//       })
//       .text()
//       .then((res) => {
//         if (!res) throw new Error('No Response')
//         const { document } = new JSDOM().window
//         document.body.innerHTML = res

//         return document
//           .querySelector('.advisor-search-tally')
//           ?.textContent?.split('of')?.[1]
//           ?.replace(numberRegex, '')
//           ?.trim()
//       }),
//   )
//   const pageTotal = Math.ceil(adsTotal / ITEMS_PER_PAGE)

//   if (isNaN(pageTotal)) throw new Error('Unable to extract page total')
//   return splitRangeSize(1, pageTotal, 10).map((range) => ({
//     ...job,
//     data: { iso, range, URL },
//   }))
// }

export async function scheduleAdvisorsTotals(job: JobDefinition): Promise<JobDefinition[]> {
  return isos.map((iso) => ({
    ...job,
    data: {
      iso,
      URL:
        iso === Iso3166Alpha2.WW
          ? `https://www.xero.com/advisors/find-advisors/`
          : `https://www.xero.com/${iso.toLowerCase()}/advisors/find-advisors/`,
    },
  }))
}

export async function extractAdvisorsTotals(job: JobInstance) {
  const directSnapshot = new DirectSnapshotGenerator(job, job.provider, DataType.partner, job.data.iso)
  const baseUrl = new URL(job.data.URL)
  baseUrl.searchParams.set('type', 'advisors')
  baseUrl.searchParams.set('orderBy', 'ADVISOR_RELEVANCE')
  baseUrl.searchParams.set('sort', 'ASC')
  baseUrl.searchParams.set('pageNumber', '1')

  // Navigate to the page with retry logic
  await job.runtime.page.goto(baseUrl.toString(), {
    waitUntil: 'networkidle2',
    timeout: 15000,
  })

  // Wait for advisors to load
  await job.runtime.page
    .waitForSelector('.advisor-search-tally', {
      timeout: 10000,
    })
    .catch(() => {
      // If no advisors found, might be end of results
      throw new Error('No advisors found on page')
    })

  const count = await job.runtime.page.$eval('.advisor-search-tally', (el) => {
    const text = el.textContent?.split('of')?.[1]?.trim()
    return Number(text?.replace(/\D/g, ''))
  })

  if (!count) throw new Error('Could not get advisors count from page')

  await directSnapshot.save({ count })

  return []
}

// export async function extractAdvisors(job: JobInstance) {
//   const results = []
//   const { iso } = job.data
//   const page = job.runtime.page

//   // Navigate to the first page
//   const baseUrl = new URL(job.data.URL)
//   baseUrl.searchParams.set('type', 'advisors')
//   baseUrl.searchParams.set('orderBy', 'ADVISOR_RELEVANCE')
//   baseUrl.searchParams.set('sort', 'ASC')
//   baseUrl.searchParams.set('pageNumber', '1')

//   let currentPage = 1
//   let hasMorePages = true
//   let retryCount = 0
//   const maxRetries = 5

//   while (hasMorePages) {
//     try {
//       // Set current page number
//       baseUrl.searchParams.set('pageNumber', currentPage.toString())

//       console.log(baseUrl)

//       // Navigate to the page with retry logic
//       await page.goto(baseUrl.toString(), {
//         waitUntil: 'networkidle2',
//         timeout: 15000,
//       })

//       // Wait for advisors to load
//       await page
//         .waitForSelector('h3[class="title-3 advisors-result-card-title"]', {
//           timeout: 10000,
//         })
//         .catch(() => {
//           // If no advisors found, might be end of results
//           throw new Error('No advisors found on page')
//         })

//       // Extract advisors from current page
//       const advisorsData = await page.evaluate(() => {
//         const advisorElements = Array.from(document.querySelectorAll('h3[class="title-3 advisors-result-card-title"]'))

//         if (!advisorElements?.length) {
//           throw new Error('Unable to extract advisors')
//         }

//         return advisorElements
//           .map((advisor) => {
//             const companyName = advisor?.textContent?.trim()
//             return companyName || null
//           })
//           .filter(Boolean)
//       })

//       // Convert to result items
//       const pageResults = advisorsData.map((companyName) => ({
//         type: ResultType.item,
//         data: {
//           dataType: DataType.partner,
//           provider: job.provider,
//           project: ProjectType.accounting,
//           itemId: getItemId(DataType.partner, job.project, companyName),
//           iso,
//           companyName,
//         },
//       }))

//       results.push(...pageResults)

//       // Check if there's a next page
//       const hasNextButton = await page.$('button[class="pagination-direction pagination-next"]')
//       hasMorePages = hasNextButton !== null

//       if (hasMorePages) {
//         currentPage++
//         // Moving to next page

//         // Add delay between pages
//         await wait(1500) //new Promise((resolve) => setTimeout(resolve, 1500))
//       }

//       // Reset retry count on successful page
//       retryCount = 0
//     } catch (error) {
//       retryCount++

//       if (retryCount >= maxRetries) {
//         console.error(`Max retries (${maxRetries}) reached for page ${currentPage}:`, error)
//         throw error
//       }

//       console.warn(`Retry ${retryCount}/${maxRetries} for page ${currentPage}:`, error.message)

//       await wait(2000 * retryCount)
//       // Wait before retrying
//       //await new Promise((resolve) => setTimeout(resolve, 2000 * retryCount))
//     }
//   }

//   return results
// }

export async function extractApps(job: JobInstance, iso: Iso3166Alpha2) {
  const url = `https://apps.xero.com/api/apps/${iso === Iso3166Alpha2.WW ? 'global' : iso}`
  return job.runtime.got.paginate.all<
    ResultItem,
    {
      items: { appId: string; slug: string; name: string }[]
      pagination: { page: number; pageSize: number; pageCount: number; itemCount: number }
    }
  >(url, {
    searchParams: {
      pageSize: 1000,
      page: 1,
    },
    retry: {
      limit: 3,
      errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
    },
    responseType: 'json',
    pagination: {
      paginate: ({ response }) => {
        if (!response?.body?.items?.length) return false

        const pagination = response.body.pagination
        if (!pagination || pagination.page === pagination.pageCount) return false

        return {
          searchParams: {
            ...response.request.options.searchParams,
            page: pagination.page + 1,
          },
        }
      },
      transform: ({ body }) => {
        if (!body?.items?.length) return []
        return body.items
          .filter((item) => item?.appId)
          .map((item) => {
            const itemId = '/' + item.slug

            return {
              type: ResultType.item,
              data: {
                project: job.project,
                dataType: DataType.app,
                id: item.appId,
                itemId,
                iso,
                url: `https://apps.xero.com/app/${item.slug}`,
                appName: item.name,
              },
            }
          })
      },
    },
  })
}
