import { DealerType, ClassifiedsStockType, ProjectType, Iso3166Alpha2, VehicleType } from '@datagatherers/datagatherers'
import { config } from '@datagatherers/worker-utils'

import type { SkelbiuCategory } from './types'
import type { JobConfig } from '@datagatherers/datagatherers'

export const extractProviderConfig: JobConfig = {
  ...config,
  browser: {
    userAgent: {
      deviceCategory: 'desktop',
    },
    defaultTimeout: 30_000,
  },
  concurrency: 30,
  perJobTimeout: 60 * 60 * 1000,
  overrideDeadlineSeconds: 60 * 60 * 24 * 3,
  constrains: { countries: [Iso3166Alpha2.LT] },
}

export const configuration = {
  retry: {
    limit: 10,
    statusCodes: [403],
    errorCodes: ['ERR_GOT_REQUEST_ERROR'],
  },
  backoff: 200,
  limPages: 100,
  pageSplitThreshold: 5,
  priceLimit: 500_000,
  retryBackoff: 500,
  itemsPerPage: 24,
}

export const cityArray = [
  122, 211, 516, 453, 273, 134, 241, 265, 16, 266, 351, 234, 253, 316, 170, 2, 53, 27, 372, 337, 465, 4, 563, 169, 86,
  153, 466, 398, 43, 364, 304, 542, 463, 42, 63, 430, 98, 529, 407, 212, 564, 120, 566, 328, 3, 221, 565, 442, 386, 562,
  284, 295, 143, 491, 417, 119, 121, 194, 504, 44,
]

export const catUrl = 'https://www.skelbiu.lt/json/categories'
export const mainUrl = 'https://www.skelbiu.lt/skelbimai'

export const searchCategories: SkelbiuCategory[] = [
  {
    project: ProjectType.marketplaces,
    categoryId: '4354',
    category: '/skelbimai/auginantiems-vaikus/',
    stockType: ClassifiedsStockType.kids,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '4263',
    category: '/skelbimai/drabuziai-avalyne/moterims/',
    stockType: ClassifiedsStockType.women,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '4264',
    category: '/skelbimai/drabuziai-avalyne/vyrams/',
    stockType: ClassifiedsStockType.men,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '38',
    category: '/skelbimai/transportas/dalys-aksesuarai/',
    stockType: ClassifiedsStockType.car_parts,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '8',
    category: '/skelbimai/buitis/',
    stockType: ClassifiedsStockType.other,
  },
  {
    project: ProjectType.cars,
    categoryId: '31',
    category: '/skelbimai/transportas/automobiliai/', // ... except: ['automobiliu-supirkimas', 'kita']
    exceptStrings: ['134'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.car,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '7',
    category: '/skelbimai/pramogos/',
    stockType: ClassifiedsStockType.leisure,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '188',
    category: '/skelbimai/drabuziai-avalyne/avalyne/',
    stockType: ClassifiedsStockType.fashion,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '4355',
    category: '/skelbimai/drabuziai-avalyne/avalyne/',
    stockType: ClassifiedsStockType.fashion,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '1301',
    category: '/skelbimai/drabuziai-avalyne/avalyne/',
    stockType: ClassifiedsStockType.fashion,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '1493',
    category: '/skelbimai/drabuziai-avalyne/avalyne/',
    stockType: ClassifiedsStockType.fashion,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '5',
    category: '/skelbimai/technika/',
    stockType: ClassifiedsStockType.electronics,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '6',
    category: '/skelbimai/komunikacijos/',
    stockType: ClassifiedsStockType.electronics,
  },
  {
    project: ProjectType.marketplaces,
    categoryId: '4',
    category: '/skelbimai/kompiuterija/',
    stockType: ClassifiedsStockType.electronics,
  },
  {
    project: ProjectType.jobs,
    categoryId: '53',
    category: '/skelbimai/paslaugos-darbas/siulo-darba/',
    stockType: ClassifiedsStockType.full_time,
  },

  {
    project: ProjectType.cars,
    categoryId: '32',
    category: '/skelbimai/transportas/moto/', // ... except: ['automobiliu-supirkimas', 'kita']
    exceptStrings: ['134'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.motorcycle,
  },

  {
    project: ProjectType.cars,
    categoryId: '34',
    category: '/skelbimai/transportas/autobusai/', // ... except: ['automobiliu-supirkimas', 'kita']
    exceptStrings: ['134'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.commercial,
  },

  {
    project: ProjectType.cars,
    categoryId: '33',
    category: '/skelbimai/transportas/dviraciai/', // ... except: ['automobiliu-supirkimas', 'kita']
    exceptStrings: ['31965', '11165', '612', '30607', '31659'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.other,
  },
  {
    project: ProjectType.cars,
    categoryId: '36',
    category: '/skelbimai/transportas/krovininiai-automobiliai/', // ... except: ['automobiliu-supirkimas', 'kita']
    exceptStrings: ['7328'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.other,
  },
  {
    project: ProjectType.cars,
    categoryId: '37',
    category: '/skelbimai/transportas/vandens-transportas/', // ... except: ['automobiliu-supirkimas', 'kita']
    exceptStrings: ['12205', '13759'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.boat,
  },
  {
    project: ProjectType.cars,
    categoryId: '35',
    category: '/skelbimai/transportas/zemes-ukio-technika/', // ... except: ['automobiliu-supirkimas', 'kita']
    //exceptStrings: ['134'],
    stockType: ClassifiedsStockType.used,
    vehicleType: VehicleType.other,
  },
]

export const ranges = [
  { cost_min: '', cost_max: 9.99 },
  { cost_min: 10, cost_max: 19.99 },
  { cost_min: 20, cost_max: 29.99 },
  { cost_min: 30, cost_max: 39.99 },
  { cost_min: 40, cost_max: 49.99 },
  { cost_min: 50, cost_max: 59.99 },
  { cost_min: 60, cost_max: 74.99 },
  { cost_min: 75, cost_max: 89.99 },
  { cost_min: 90, cost_max: 109.99 },
  { cost_min: 110, cost_max: 149.99 },
  { cost_min: 150, cost_max: 199.99 },
  { cost_min: 200, cost_max: '' },
]

export const dealerTypes: { code: number; dealerType: DealerType }[] = [
  { code: 1, dealerType: DealerType.private },
  { code: 2, dealerType: DealerType.dealer },
]
