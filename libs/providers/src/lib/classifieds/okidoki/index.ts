import { startOfISOWeek } from 'date-fns'
import { RetryError } from 'got'
import { JSDOM } from 'jsdom'
import { ObjectId } from 'mongodb'
import UserAgent from 'user-agents'

import {
  ResultType,
  DataType,
  DealerType,
  ProjectType,
  RealEstatesBusinessType,
  VehicleType,
} from '@datagatherers/datagatherers'
import { snapshot as snapshotBase, chunks, getProjectProviderDataType, wait } from '@datagatherers/worker-utils'

import { itemsPerPage, maxItemsPerCategory, pagesPerJob, searchCategories } from './constants'

import type { DealerlistingData } from './constants'
import type {
  JobDefinition,
  ControllerRuntime,
  JobConfig,
  ClassifiedsStockType,
  JobInstance,
  Item,
  ControllerConfig,
  ResultItem,
  Iso3166Alpha2,
  Job,
} from '@datagatherers/datagatherers'

async function addJobs(job: JobInstance, document: Document) {
  const scheduledJob = await job.runtime.collection<Job>('job').findOne({ _id: new ObjectId(job._id) })
  if (!scheduledJob) return

  const { iso, stockType } = job.data

  const searchCategory = searchCategories.filter(
    (item) => item.project === job.project && (!stockType || item.stockType === stockType),
  )
  const jobs: JobDefinition[] = []

  for (const cat of searchCategory) {
    const subCategories = cat.category
    for (const subCat of subCategories) {
      await wait(500)

      const url = `https://www.okidoki.ee/buy/${subCat}/`
      const searchParams = { p: 1, pp: itemsPerPage, ...(cat.adtype && { adtype: cat.adtype }) }

      await job.runtime
        .got(url, {
          method: 'GET',
          retry: {
            limit: 5,
            statusCodes: [441, 403],
            errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR', 'ERR_RETRYING'],
          },
          headers: {
            'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
          },
          searchParams,
          hooks: {
            afterResponse: [
              (response) => {
                if (typeof response.body !== 'string') throw new Error('Malformed response body')

                document.body.innerHTML = response.body

                const itemsNum = Number(
                  document
                    .querySelector('#category_header .found')
                    ?.textContent?.replace(/[^0-9]/g, '')
                    .trim(),
                )

                if (isNaN(itemsNum)) {
                  throw new RetryError(response.request)
                }
                return response
              },
            ],
          },
        })
        .text()
        .then(() => {
          const itemsNum = Number(
            document
              .querySelector('#category_header .found')
              ?.textContent?.replace(/[^0-9]/g, '')
              .trim(),
          )

          if (isNaN(itemsNum)) {
            throw new Error(`Failed to extract items number for category ${subCat}.`)
          }

          let maxNum = Math.ceil(itemsNum / itemsPerPage)
          if (maxNum > Math.ceil(maxItemsPerCategory / itemsPerPage)) {
            const newCats = Array.from(
              document.querySelectorAll(`#browse-categories > li[class^="category"] > ul > li`),
            ).map((el) => {
              return Number(
                el
                  ?.querySelector('a')
                  ?.getAttribute('href')
                  ?.replace(/\?.*/g, '')
                  ?.replace(/[^0-9]/g, ''),
              )
            })

            if (newCats?.length) {
              newCats.map((category) => subCategories.push(category))
            } else {
              maxNum = Math.ceil(maxItemsPerCategory / itemsPerPage)
            }
          }

          for (let n = 1; n <= maxNum; n += pagesPerJob) {
            jobs.push({
              ...job,
              data: {
                ...job.data,
                url,
                pMin: n,
                pMax: Math.min(maxNum, n + pagesPerJob - 1),
                params: searchParams,
                project: cat.project,
                stockType: cat.stockType,
                ...(cat.project === ProjectType.realestates && {
                  businessType: cat.adtype === 3 ? RealEstatesBusinessType.rent : RealEstatesBusinessType.sale,
                  propertyType: cat.propertyType,
                  propertyScope: cat.propertyScope,
                }),
                iso,
              },
            })
          }
        })
    }
  }

  if (jobs?.length) {
    await job.runtime.addJobs(jobs)
  }
}

export async function scheduleExtract(
  job: JobDefinition,
  _runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
  stockType?: ClassifiedsStockType,
): Promise<JobDefinition[]> {
  return [
    {
      ...job,
      data: {
        ...job.data,
        iso,
        stockType,
      },
    },
  ]

  // const searchCategory = searchCategories.filter(
  //   (item) => item.project === job.project && (!stockType || item.stockType === stockType),
  // )
  // const jobs: JobDefinition[] = []
  // const { document } = new JSDOM().window

  // for (const cat of searchCategory) {
  //   const subCategories = cat.category
  //   for (const subCat of subCategories) {
  //     await wait(200)

  //     const url = `https://www.okidoki.ee/buy/${subCat}/`
  //     const searchParams = { p: 1, pp: itemsPerPage, ...(cat.adtype && { adtype: cat.adtype }) }

  //     await runtime
  //       .got(url, {
  //         method: 'GET',
  //         retry: {
  //           limit: 5,
  //           statusCodes: [441, 403],
  //           errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR', 'ERR_RETRYING'],
  //         },
  //         headers: {
  //           'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
  //         },
  //         searchParams,
  //         hooks: {
  //           afterResponse: [
  //             (response) => {
  //               if (typeof response.body !== 'string') throw new Error('Malformed response body')

  //               document.body.innerHTML = response.body

  //               const itemsNum = Number(
  //                 document
  //                   .querySelector('#category_header .found')
  //                   ?.textContent?.replace(/[^0-9]/g, '')
  //                   .trim(),
  //               )

  //               if (isNaN(itemsNum)) {
  //                 throw new RetryError(response.request)
  //               }
  //               return response
  //             },
  //           ],
  //         },
  //       })
  //       .text()
  //       .then(() => {
  //         const itemsNum = Number(
  //           document
  //             .querySelector('#category_header .found')
  //             ?.textContent?.replace(/[^0-9]/g, '')
  //             .trim(),
  //         )

  //         if (isNaN(itemsNum)) {
  //           throw new Error(`Failed to extract items number for category ${subCat}.`)
  //         }

  //         let maxNum = Math.ceil(itemsNum / itemsPerPage)
  //         if (maxNum > Math.ceil(maxItemsPerCategory / itemsPerPage)) {
  //           const newCats = Array.from(
  //             document.querySelectorAll(`#browse-categories > li[class^="category"] > ul > li`),
  //           ).map((el) => {
  //             return Number(
  //               el
  //                 ?.querySelector('a')
  //                 ?.getAttribute('href')
  //                 ?.replace(/\?.*/g, '')
  //                 ?.replace(/[^0-9]/g, ''),
  //             )
  //           })

  //           if (newCats?.length) {
  //             newCats.map((category) => subCategories.push(category))
  //           } else {
  //             maxNum = Math.ceil(maxItemsPerCategory / itemsPerPage)
  //           }
  //         }

  //         for (let n = 1; n <= maxNum; n += pagesPerJob) {
  //           jobs.push({
  //             ...job,
  //             data: {
  //               url,
  //               pMin: n,
  //               pMax: Math.min(maxNum, n + pagesPerJob - 1),
  //               params: searchParams,
  //               project: cat.project,
  //               stockType: cat.stockType,
  //               ...(cat.project === ProjectType.realestates && {
  //                 businessType: cat.adtype === 3 ? RealEstatesBusinessType.rent : RealEstatesBusinessType.sale,
  //                 propertyType: cat.propertyType,
  //                 propertyScope: cat.propertyScope,
  //               }),
  //               iso,
  //             },
  //           })
  //         }
  //       })
  //   }
  // }

  // return jobs
}

export async function extractListings(job: JobInstance) {
  const { document } = new JSDOM().window

  if (!job.data.pMin) {
    await addJobs(job, document)
    return []
  }

  return await job.runtime.got.paginate.all<ResultItem, string>(job.data.url, {
    method: 'GET',
    searchParams: {
      ...job.data.params,
      p: job.data.pMin,
    },
    headers: {
      'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
    },
    responseType: 'text',
    retry: {
      limit: 3,
      errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
    },
    pagination: {
      backoff: 200,
      paginate: ({ response }) => {
        const items = document?.querySelectorAll('.classifieds__item')
        if (!items?.length) return false

        const currentPage = Number(response.requestUrl.searchParams.get('p'))
        const nextPage = currentPage + 1
        if (nextPage > job.data.pMax) return false

        return {
          searchParams: {
            ...job.data.params,
            p: nextPage,
          },
        }
      },
      transform: ({ body }) => {
        document.body.innerHTML = body
        const items = document?.querySelectorAll('.classifieds__item')
        if (!items?.length) return []

        return Array.from(items)?.flatMap((element) => {
          if (!element) return []

          const url = element.querySelector('a')?.getAttribute('href')
          const id = element
            .querySelector('a')
            ?.getAttribute('href')
            ?.replace(/(\/)(?!.*\/)/, '')
            .replace(/.*\//, '')
          if (!id || !url) return []

          const priceUnit = element.querySelector('.horiz-offer-card__price-unit')?.textContent
          const priceValue = element.querySelector('.horiz-offer-card__price-value')?.textContent
          const priceTxt = priceUnit ? priceValue.replace(priceUnit, '') : priceValue
          let price = Number(priceTxt?.split('€')[0].replace(/[^0-9.]/g, '') || 0)
          if (isNaN(price)) price = 0
          const stockType = job.data?.stockType
          let area = 0
          if (job.project === ProjectType.realestates) {
            const areaChild = element.querySelector(`img[src='/assets/svg/offers/realty/area.svg']`)
            if (areaChild) area = Number(areaChild?.closest('li')?.textContent?.replace(/[^0-9.]/g, ''))
          }
          return {
            type: ResultType.item,
            data: {
              project: job.project,
              itemId: `listing_${job.project}_${job.data?.iso}_${id}`,
              iso: job.data?.iso,
              dataType: DataType.inventory,
              stockType,
              inventoryId: id,
              price,
              currency: 'EUR',
              ...(url && { url }),
              ...(job.project === ProjectType.realestates && {
                businessType: job.data?.businessType,
                propertyType: job.data?.propertyType,
                propertyScope: job.data?.propertyScope,
              }),
              ...(area && { area }),
              ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
            },
          }
        })
      },
    },
  })
}

export async function scheduleDealers(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const collection = runtime.collection<Item>(ResultType.item)
  const items = await collection
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        dealerId: { $exists: false },
      },
      {
        projection: {
          _id: 0,
          inventoryId: 1,
          itemId: 1,
        },
      },
    )
    .toArray()

  const jobs = []
  if (items?.length !== 0) {
    const itemsChunks = [...chunks(items, 1)]
    for (const chunk of itemsChunks) {
      jobs.push({
        ...job,
        data: {
          ...job.data,
          iso,
          listings: chunk,
          ...(job.data?.parameters && { parameters: job.data?.parameters }),
        },
      })
    }
  }

  return jobs
}

export async function extractDealers(job: JobInstance) {
  const listingsUpdate = []
  const listingsData: DealerlistingData[] = []
  const { document } = new JSDOM().window

  for (const listing of job.data.listings) {
    try {
      const url = `https://www.okidoki.ee/item/${listing.inventoryId}`
      await job.runtime
        .got(url, {
          method: 'GET',
          headers: {
            'user-agent': new UserAgent({ deviceCategory: 'desktop' }).toString(),
          },
          retry: {
            limit: 5,
            errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
          },
        })
        .text()
        .then((res) => {
          document.body.innerHTML = res
          const dealerId = !document
            .querySelector('div.user-info > div.user > div:nth-child(1) > a')
            ?.getAttribute('href')
            ? listing.inventoryId
            : document
                .querySelector('div.user-info > div.user > div:nth-child(1) > a')
                ?.getAttribute('href')
                ?.replace(/.*=/, '')

          const price = Number(document.querySelector('p.price')?.textContent?.replace(/[^0-9.]/g, ''))
          const dealerT = document.querySelector('div.user > div:nth-child(1) > ul > li:nth-child(1)')?.textContent

          const listingData = {
            itemId: listing.itemId,
            iso: job.data?.iso,
            dealerId,
            ...(price && { price }),
            ...(dealerT && { data: { dealerType: dealerT } }),
          }

          listingsData.push(listingData)
          listingsUpdate.push({
            type: ResultType.item,
            data: listingData,
          })
        })
      // const dataCard = await fetchWithCloudflareRetry(job.runtime, url, 5)

      // document.body.innerHTML = dataCard

      // const dealerId = !document.querySelector('div.user-info > div.user > div:nth-child(1) > a')?.getAttribute('href')
      //   ? listing.inventoryId
      //   : document
      //       .querySelector('div.user-info > div.user > div:nth-child(1) > a')
      //       ?.getAttribute('href')
      //       ?.replace(/.*=/, '')

      // const price = Number(document.querySelector('p.price')?.textContent?.replace(/[^0-9.]/g, ''))
      // const dealerT = document.querySelector('div.user > div:nth-child(1) > ul > li:nth-child(1)')?.textContent

      // const listingData = {
      //   itemId: listing.itemId,
      //   iso: job.data?.iso,
      //   dealerId,
      //   ...(price && { price }),
      //   ...(dealerT && { data: { dealerType: dealerT } }),
      // }

      // listingsData.push(listingData)
      // listingsUpdate.push({
      //   type: ResultType.item,
      //   data: listingData,
      // })
    } catch (error) {
      job.addErrorToLog(new Error(`Error processing item ${listing.inventoryId}: ${error.message}, skipping`))
      continue
    }

    await wait(200)
  }

  if (!listingsData.length) {
    job.addErrorToLog(new Error('No valid listings data could be processed'))
    return []
  }

  const results = listingsData
    .filter((a) => a?.dealerId)
    .map((item) => {
      const dealerType = getListingSellerType(job.project, item)
      const data = {
        project: job.project,
        itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
        iso: job.data?.iso,
        dataType: DataType.dealers,
        dealerId: item.dealerId,
        dealerType,
        isActive: true,
        isPaying: false,
      }
      return {
        type: ResultType.item,
        data,
      }
    })

  if (!results?.length) {
    job.addErrorToLog(new Error('No valid dealer results could be processed'))
    return []
  }

  return [...results, ...listingsUpdate]
}

export async function updateExistingDealers(job: JobInstance, iso: Iso3166Alpha2) {
  const col = job.runtime.collection('item')
  const dealerIdsDB = await col
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
      },
      {
        projection: {
          _id: 0,
          dealerId: 1,
        },
      },
    )
    .toArray()

  if (!dealerIdsDB.length) {
    return []
  }

  const result = dealerIdsDB
    .filter((item) => item?.dealerId)
    .map((item) => {
      const data = {
        iso,
        itemId: `dealer_${job.project}_${iso}_${item.dealerId}`,
      }
      return {
        type: ResultType.item,
        data,
      }
    })

  return result
}

export async function snapshot(iso: Iso3166Alpha2, config: ControllerConfig, runtime: ControllerRuntime) {
  await snapshotBase(iso, config, runtime)
  // if (config.project === ProjectType.realestates) await snapshots.realestatesFull(iso, config, runtime)
  // if (config.project === ProjectType.cars) await snapshots.carsFull(iso, config, runtime)
  // if (config.project === ProjectType.jobs) await snapshots.jobsFull(iso, config, runtime)
  // if (config.project === ProjectType.marketplaces) await snapshots.marketplacesFull(iso, config, runtime)
}

function getListingSellerType(project: ProjectType, item: DealerlistingData): DealerType {
  if (project === ProjectType.realestates || project === ProjectType.cars || project === ProjectType.marketplaces) {
    return item.dealerT && item.dealerT === 'Eraisik' ? DealerType.private : DealerType.dealer
  } else {
    return item.dealerT && item.dealerT === 'Eraisik' ? DealerType.private : DealerType.company
  }
}

// async function fetchWithCloudflareRetry(
//   runtime: ControllerRuntime | JobInstance['runtime'],
//   url: string,
//   maxRetries = 10,
//   waitTime = 500,
// ): Promise<string> {
//   let html = ''
//   let retryCount = 0

//   while (retryCount < maxRetries) {
//     try {
//       await runtime.page.goto(url, { waitUntil: 'networkidle2' })

//       const challengeElement = await runtime.page.$(CLOUDFLARE_SELECTOR)
//       if (challengeElement) {
//         throw new Error('Cloudflare challenge detected')
//       }

//       html = await runtime.page.content()
//       if (!html) {
//         throw new Error('No HTML content')
//       }

//       if (html.includes('Just a moment...')) {
//         throw new Error('Cloudflare challenge detected in HTML content')
//       }

//       return html
//     } catch (error) {
//       retryCount++
//       if (retryCount >= maxRetries) {
//         throw new Error(`Failed to process url ${url} after ${maxRetries} attempts: ${error.message}`)
//       } else {
//         const userAgent = new UserAgent({ deviceCategory: 'desktop' })
//         await runtime.refreshProxies()
//         await runtime.page.setUserAgent(userAgent.toString())
//         await runtime.page.setViewport({
//           width: 1920,
//           height: 1080,
//           isMobile: false,
//           isLandscape: false,
//           hasTouch: false,
//         })
//         await runtime.page.goto('https://okidoki.ee/', { waitUntil: 'networkidle2' })

//         await wait(waitTime)
//       }
//     }
//   }
// }

export const extractProviderConfig: JobConfig = {
  concurrency: 20,
  // browser: {
  //   userAgent: {
  //     deviceCategory: 'desktop',
  //   },
  // },
  // constrains: { countries: [Iso3166Alpha2.NL, Iso3166Alpha2.PL, Iso3166Alpha2.SE] },
}
