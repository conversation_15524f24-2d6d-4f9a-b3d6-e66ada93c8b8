import {
  format,
  startOfISOWeek,
  startOfMonth,
  subWeeks,
  fromUnixTime,
  isBefore,
  startOfYear,
  subYears,
  isAfter,
  addWeeks,
} from 'date-fns'
import { json2csv } from 'json-2-csv'

import {
  DealerType,
  DataType,
  Iso3166Alpha2,
  ProjectType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesPropertyType,
  RealEstatesStockType,
  ResultType,
  TimeUnit,
  GeoJSONTypes,
  MarketplacesDealerType,
} from '@datagatherers/datagatherers'
import {
  GDrive,
  accountConfigs,
  arraySlicer,
  chunks,
  getItemId,
  getProjectProviderDataType,
  getREArea,
  snapshots,
  wait,
} from '@datagatherers/worker-utils'

import {
  androidAutoCompleteLocationsQuery,
  countiesQuery,
  housingFormGroups,
  listingsCap,
  listingsQuery,
  maxLimit,
  pBrokersListingsQuery,
  pBrokersQuery,
  packagePricesQuery,
  priceRangeDbSwitch,
  productCodes,
  statsProductTypes,
} from './constants'
import { compileAdViewsDaysActiveHistory, assessIsReposted } from '../../realestates/booli'

import type {
  Broker,
  BrokerAgency,
  County,
  HemnetListingCountPerDealerRange,
  HemnetListingResponse,
  HemnetListings,
  HemnetStatistikResponse,
  Hits,
  PremiumBrokerListResponse,
  PremiumBrokerResponse,
  PricesEntity,
} from './types'
import type { AdViewsDaysActiveHistoryEntry } from '../../realestates/booli/types'
import type {
  ControllerConfig,
  ControllerRuntime,
  Item,
  JobDefinition,
  JobInstance,
  Result,
  Snapshot,
  Town,
} from '@datagatherers/datagatherers'

export async function scheduleTowns(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2 = Iso3166Alpha2.SE,
): Promise<JobDefinition[]> {
  return runtime
    .got('https://www.hemnet.se/graphql', {
      method: 'POST',
      headers: {
        'hemnet-application-version': 'android-4.24.2',
        'user-agent': 'Hemnet-Android/4.24.2',
        'content-type': 'application/json',
      },
      json: {
        query: countiesQuery,
        operationName: 'AndroidCounties',
      },
    })
    .json<{ data: { counties: County[] } }>()
    .then((res) => {
      if (!res?.data?.counties) return []

      return res?.data?.counties.flatMap((item) => {
        if (!item?.name) return []
        return {
          ...job,
          data: {
            ...job.data,
            query: item.name,
            iso,
          },
        }
      })
    })
    .catch((err) => {
      console.error(err)
      return []
    })
}

export async function extractTowns(job: JobInstance) {
  const hits = await job.runtime
    .got('https://www.hemnet.se/graphql', {
      method: 'POST',
      headers: {
        'hemnet-application-version': 'android-4.24.2',
        'user-agent': 'Hemnet-Android/4.24.2',
        'content-type': 'application/json',
      },
      json: {
        query: androidAutoCompleteLocationsQuery,
        variables: { query: job.data?.query, limit: 1000 },
        operationName: 'AndroidAutoCompleteLocations',
      },
    })
    .json<{ data: { autocompleteLocations: { hits: Hits[] } } }>()
    .then((res) => res.data?.autocompleteLocations?.hits)
    .catch((error) => job.addErrorToLog(error))
  if (typeof hits === 'undefined' || !hits?.length) {
    return []
  }
  return hits.flatMap((item) => {
    if (!item?.location?.id || item.location.type !== 'MUNICIPALITY') return []
    return {
      type: ResultType.town,
      data: {
        project: job.project,
        iso: job.data?.iso,
        itemId: `${item.location.id}_${item.location.shortName}`,
        data: {
          ...item.location,
        },
      },
    }
  })
}

export async function extractInventoryCount(job: JobInstance, iso: Iso3166Alpha2, timeUnit: TimeUnit) {
  const now = new Date()
  const collection = job.runtime.collection('item')
  const updatedAt = { $gte: timeUnit === TimeUnit.weeks ? startOfISOWeek(now) : startOfMonth(now) }

  const dealerIds = (
    await collection
      .find(
        {
          projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
          iso,
          updatedAt,
          dealerId: /Agency/g,
        },
        {
          projection: {
            _id: 0,
            dealerId: 1,
          },
        },
      )
      .toArray()
  ).map((item) => item.dealerId)

  const res = await collection
    .aggregate([
      {
        $match: {
          projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
          iso: iso,
          updatedAt,
          dealerId: { $in: dealerIds },
        },
      },
      {
        $group: {
          _id: '$dealerId',
          count: {
            $sum: 1,
          },
        },
      },
    ])
    .toArray()

  const result = res.map((item) => {
    const data = {
      iso,
      itemId: `dealer_SE_${job.project}_${item._id}`,
    }
    data[`dealerListingsCount.${timeUnit}`] = {
      value: item.count,
      range: getListingCountRange(item.count),
    }
    return {
      type: ResultType.item,
      data,
    }
  })
  return result
}

export async function extractInventoryStats(job: JobInstance, iso: Iso3166Alpha2) {
  const statsMap = new Map()

  for (const timeUnit of [TimeUnit.weeks, TimeUnit.months]) {
    for (const stockType of statsProductTypes) {
      await job.runtime
        .got('https://www.hemnet.se/graphql', {
          method: 'POST',
          json: {
            query:
              'query publicMetric( $metric: PublicMetric! $resolution: MetricTimeResolution! $filter: MetricFilter! ) { publicMetric(metric: $metric, resolution: $resolution, filter: $filter) {  ...StatisticPublicMetricsFields } } fragment StatisticPublicMetricsFields on TimeSeriesMetric { formattedUnit unit values {  recordedAt  value } } ',
            operationName: 'publicMetric',
            variables: {
              metric: stockType,
              resolution: timeUnit === TimeUnit.months ? 'MONTH' : 'WEEK',
              filter: {
                type: 'ALL',
                locationId: '17691',
                rooms: 'ALL',
                successionType: 'PRE_OWNED',
                marketState: 'ALL',
              },
            },
          },
        })
        .json<HemnetStatistikResponse>()
        .then((res) => {
          if (res?.data?.publicMetric?.values?.length) {
            for (const item of res.data.publicMetric.values) {
              const currItemDate = fromUnixTime(Number(item.recordedAt))
              if (isBefore(subYears(startOfYear(new Date()), 4), currItemDate)) {
                const snapTime = format(currItemDate, timeUnit === TimeUnit.weeks ? 'RRRR/II' : 'yyyy/MM')
                const key = `${snapTime}_${timeUnit}`

                if (!statsMap.has(key)) {
                  statsMap.set(key, {
                    snapTime,
                    snapTimeUnit: timeUnit,
                    stats: [],
                  })
                }

                let normalizedStockType: string
                if (stockType === 'LISTING_SUPPLY') {
                  normalizedStockType = 'property_supply'
                } else if (stockType === 'PUBLISHED_LISTINGS') {
                  normalizedStockType = 'published_listings'
                } else if (stockType === 'ASKING_PRICES') {
                  normalizedStockType = 'asking_prices'
                } else {
                  normalizedStockType = stockType.toLowerCase()
                }

                statsMap.get(key).stats.push({
                  count: item.value ?? 0,
                  stockType: normalizedStockType,
                  ...(normalizedStockType === 'published_listings' && { isNew: true }),
                })
              }
            }
          }
        })
        .catch((err) => job.addErrorToLog(err))
    }
  }

  await wait(1500)

  const html = await job.runtime
    .got('https://www.hemnet.se/statistik/finansiell-rapportering', { responseType: 'text' })
    .text()

  const scriptMatch = html.match(/<script\s+id="__NEXT_DATA__"[^>]*>([\s\S]*?)<\/script>/)

  if (!scriptMatch?.[1]) {
    throw new Error('extractInventoryStats: unable to locate __NEXT_DATA__ JSON on financial report page')
  }
  const __NEXT_DATA__ = JSON.parse(scriptMatch[1])
  if (__NEXT_DATA__?.props?.pageProps?.data?.values?.length) {
    for (const item of __NEXT_DATA__.props.pageProps.data.values) {
      if (item.value == null) {
        continue
      }

      const currItemDate = fromUnixTime(Number(item.recordedAt))
      if (isBefore(subYears(startOfYear(new Date()), 4), currItemDate) && !isAfter(currItemDate, new Date())) {
        const timeUnit = TimeUnit.months
        const snapTime = format(currItemDate, 'yyyy/MM')
        const key = `${snapTime}_${timeUnit}`

        if (!statsMap.has(key)) {
          statsMap.set(key, {
            snapTime,
            snapTimeUnit: timeUnit,
            stats: [],
          })
        }

        statsMap.get(key).stats.push({
          count: item.value ?? 0,
          stockType: 'financial_report',
        })
      }
    }
  }

  const now = new Date()
  const collection = job.runtime.collection<Snapshot>('snapshot')

  for (const snapData of statsMap.values()) {
    await collection.updateOne(
      {
        project: job.project,
        controllerId: '-1',
        provider: job.provider,
        iso,
        snapTime: snapData.snapTime,
        snapTimeUnit: snapData.snapTimeUnit,
        dataType: DataType.yearlyProgress,
      },
      {
        $setOnInsert: {
          project: job.project,
          controllerId: '-1',
          provider: job.provider,
          iso,
          snapTime: snapData.snapTime,
          snapTimeUnit: snapData.snapTimeUnit,
          dataType: DataType.yearlyProgress,
          createdAt: now,
        },
        $set: {
          updatedAt: now,
        },
        $pull: {
          stats: { stockType: { $in: snapData.stats.map((s: { stockType: string }) => s.stockType) } },
        },
      },
      { upsert: true, writeConcern: { w: 'majority' } },
    )

    await collection.updateOne(
      {
        project: job.project,
        controllerId: '-1',
        provider: job.provider,
        iso,
        snapTime: snapData.snapTime,
        snapTimeUnit: snapData.snapTimeUnit,
        dataType: DataType.yearlyProgress,
      },
      {
        $push: {
          stats: { $each: snapData.stats },
        },
      },
    )
  }

  return []
}

export async function extractPremiumBrokers(job: JobInstance) {
  return job.runtime.got.paginate.all<Result, PremiumBrokerListResponse>('https://www.hemnet.se/graphql', {
    method: 'POST',
    json: {
      query: pBrokersQuery,
      variables: {
        limit: 100,
        offset: 0,
        searchParams: {
          locationIds: job.data?.id,
        },
      },
    },
    responseType: 'json',
    retry: { limit: 2 },
    pagination: {
      backoff: 2500,
      requestLimit: 500,
      paginate: ({ response }) => {
        if (
          !response.request.options.body ||
          !response?.body?.data?.searchBrokers?.brokers?.length ||
          response.body.data.searchBrokers.total === 0
        )
          return false

        const parsedBody = JSON.parse(response.request.options.body.toString())
        parsedBody.variables.offset += parsedBody.variables.limit

        return {
          json: parsedBody,
        }
      },
      transform: (response) => {
        if (!response?.body?.data?.searchBrokers?.brokers?.length) return []
        return response.body.data.searchBrokers.brokers.flatMap((listing) => {
          if (!listing?.broker.id) return []

          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId: `dealers_${job.project}_${listing.broker.id}`,
              iso: Iso3166Alpha2.SE,
              dataType: DataType.dealers,
              dealerId: listing.broker.id,
              dealerType: DealerType.dealer,
              isActive: listing.broker.forSale?.total ? true : false,
              isPaying: true,
              data: { isPremium: listing.activeProducts.enhancedBrokerCard ? true : false },
            },
          }
        })
      },
    },
  })
}

export async function schedulePremiumBrokersInventory(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2 = Iso3166Alpha2.SE,
): Promise<JobDefinition[]> {
  return runtime
    .collection('item')
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.dealers),
        iso,
      },
      {
        projection: {
          _id: 0,
          dealerId: 1,
          'data.isPremium': 1,
        },
      },
    )
    .toArray()
    .then((dealerList) =>
      dealerList.flatMap((dealer) => {
        if (!dealer?.data?.isPremium) return []
        return {
          ...job,
          data: {
            iso,
            dealerId: dealer.dealerId,
          },
        }
      }),
    )
}

export async function extractPremiumBrokersInventory(job: JobInstance) {
  return job.runtime.got.paginate.all<Result, PremiumBrokerResponse>('https://www.hemnet.se/graphql', {
    method: 'POST',
    responseType: 'json',
    json: {
      query: pBrokersListingsQuery,
      variables: {
        brokerProfileId: job.data?.dealerId,
        limit: 500,
        offset: 0,
      },
      operationName: 'iOSBrokerProfile',
    },
    retry: { limit: 2 },
    pagination: {
      backoff: 2500,
      requestLimit: 500,
      paginate: ({ response }) => {
        if (
          !response.request.options.body ||
          !response?.body?.data?.broker?.forSale?.listings?.length ||
          response.body.data.broker.forSale.total === 0
        )
          return false

        const parsedBody = JSON.parse(response.request.options.body.toString())
        parsedBody.variables.offset += parsedBody.variables.limit
        return {
          json: parsedBody,
        }
      },
      transform: (response) => {
        if (!response?.body?.data?.broker?.forSale?.listings?.length) return []

        return response.body.data.broker.forSale.listings.flatMap((listing) => {
          if (!listing?.id) return []
          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId: `listing_SE_${job.project}_${listing.id}`,
              dataType: DataType.inventory,
              iso: Iso3166Alpha2.SE,
              inventoryId: `${listing.id}`,
              stockType: getStockType(listing as HemnetListings),
              dealerId: job.data?.dealerId,
              dealerType: DealerType.dealer,
              ...(listing.askingPrice?.amount && { price: listing.askingPrice?.amount }),
              ...(listing.askingPrice?.amount &&
                listing.askingPrice?.currency?.code && { currency: listing.askingPrice?.currency?.code }),
              priceRange:
                listing.askingPrice?.amount && !isNaN(listing.askingPrice?.amount)
                  ? getPriceRange(listing.askingPrice.amount)
                  : undefined,
              productType: listing.packagePurchase ?? 'BASIC',
              data: {
                adType: listing.typename,
                housingForm: listing.housingForm,
                brokerAgency: response.body.data.broker.primaryBrokerAgency.id,
                hasBrokerLogo: !!response?.body?.data?.broker?.primaryBrokerAgency?.brokerCustomization?.logo,
                broker: {
                  id: response.body.data.broker.id,
                  name: response.body.data.broker.name,
                  active: true,
                  hasActiveProfile: response.body.data.broker.hasActiveProfile,
                },
              },
            },
          }
        })
      },
    },
  })
}

export async function unflagPremiumBrokers(config: ControllerConfig, runtime: ControllerRuntime) {
  await runtime.collection('item').updateMany(
    {
      projectProviderDataType: getProjectProviderDataType(config.project, config.provider, DataType.dealers),
      iso: Iso3166Alpha2.SE,
    },
    { $unset: { 'data.isPremium': '' } },
    { writeConcern: { w: 'majority' } },
  )
}

export async function snapshot(config: ControllerConfig, runtime: ControllerRuntime, iso: Iso3166Alpha2, date?: Date) {
  if (config.project === ProjectType.realestates) {
    await snapshots.snapRealestates(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date)
    await snapshots.snapRealestates(iso, DataType.inventory, TimeUnit.months, config, runtime, date)

    await snapshots.snapRealestates(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, 'hemnet_agencies')
    await snapshots.snapRealestates(iso, DataType.dealers, TimeUnit.months, config, runtime, date, 'hemnet_agencies')

    await snapshots.realestatesFull(iso, config, runtime, date, 'hemnet_upcoming', {
      'data.isUpcoming': true,
    })

    await snapshots.realestatesFull(iso, config, runtime, date, 'hemnet_for_sale', {
      'data.isUpcoming': false,
      'data.adType': { $nin: ['Project', 'ProjectUnit'] },
    })
    await snapshots.realestatesFull(iso, config, runtime, date, 'hemnet_republished', {
      'data.isRepublished': true,
    })
    await snapshots.realestatesFull(iso, config, runtime, date, 'hemnet_newproduction_projects', {
      'data.adType': 'Project',
    })
    await snapshots.realestatesFull(iso, config, runtime, date, 'hemnet_newproduction_listings', {
      'data.adType': 'ProjectUnit',
    })
  }
}

export async function scheduleRealEstates(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
  startDate?: Date,
): Promise<JobDefinition[]> {
  const statesArray = await runtime
    .collection('worldcities')
    .aggregate([
      {
        $match: {
          iso2: iso,
          admin_code: { $nin: [null, ''] },
        },
      },
      {
        $group: {
          _id: {
            name: '$admin_name',
            code: '$admin_code',
          },
        },
      },
      {
        $project: {
          _id: 0,
          code: '$_id.code',
          name: '$_id.name',
        },
      },
    ])
    .toArray()
  const results = await runtime
    .collection<Town>('town')
    .find({
      project: job.project,
      provider: job.provider,
      iso,
    })
    .toArray()

  return results.flatMap((item) => {
    if (!item?.data) return []
    return [
      ...housingFormGroups.flatMap((housingFormGroup) => ({
        ...job,
        data: {
          ...item.data,
          iso,
          statesArray,
          ...(startDate && { startDate }),
          housingFormGroup,
        },
      })),
    ]
  })
}

export async function extractRealEstates(job: JobInstance) {
  return job.runtime.got.paginate.all<Result, HemnetListingResponse>('https://www.hemnet.se/graphql', {
    method: 'POST',
    headers: {
      'hemnet-application-version': 'android-4.24.2',
      'user-agent': 'Hemnet-Android/4.24.2 (Nexus 7; Android 6.0.1)',
      'content-type': 'application/json',
    },
    responseType: 'json',
    json: {
      variables: {
        limit: maxLimit,
        offset: 0,
        searchInput: {
          balcony: 'INCLUDE',
          biddingStarted: 'INCLUDE',
          deactivatedBeforeOpenHouse: 'INCLUDE',
          elevator: 'INCLUDE',
          foreclosure: 'INCLUDE',
          housingFormGroups: [job.data?.housingFormGroup],
          liveStream: 'INCLUDE',
          locationIds: [job.data?.id],
          owned: 'INCLUDE',
          publishedSince: 'all',
          upcoming: 'INCLUDE',
          newConstruction: 'INCLUDE',
        },
        sort: 'NEWEST',
      },
      query: listingsQuery,
    },
    pagination: {
      backoff: 2000,
      paginate: ({
        response: {
          request: { options },
          body,
        },
      }) => {
        if (!options?.body) return false
        let responseBody
        try {
          responseBody = JSON.parse(options.body.toString())
        } catch {
          //EMPTY
        }
        if (!responseBody) return false

        const offset = responseBody.variables.offset + maxLimit
        if (offset >= listingsCap) {
          return false
        }

        if (body?.data?.searchListings?.total === responseBody.variables.offset) {
          return false
        }

        if (!body.data?.searchListings?.listings?.length || body.data?.searchListings?.listings?.length < maxLimit) {
          return false
        }

        if (offset >= listingsCap) return false
        return {
          json: {
            ...responseBody,
            variables: {
              ...responseBody.variables,
              offset,
            },
          },
        }
      },
      transform: async ({ body }) => {
        if (!body.data?.searchListings?.listings?.length) {
          return []
        }
        const dealersMap: Map<
          string,
          {
            dealerId: string
            dealerType: DealerType
            isActive: boolean
            isPaying: boolean
            dealer?: BrokerAgency | Broker
          }
        > = new Map()

        const resultsInventory = await Promise.all(
          body.data.searchListings.listings.map(async (item) => {
            if (!item?.id) return []

            let dealerId = 'private'
            let dealerType: MarketplacesDealerType = MarketplacesDealerType.private
            let dealer
            let hasBrokerLogo = false
            if (item.brokerAgency) {
              dealer = item.brokerAgency
              dealerId = `Agency_${dealer.id}`
              dealerType = MarketplacesDealerType.dealer
              hasBrokerLogo = !!dealer.brokerCustomization?.resultCardLogo?.length
            } else if (item.broker) {
              dealer = item.broker
              dealerId = `Broker_${dealer.id}`
              dealerType = MarketplacesDealerType.dealer
            }

            if (dealerId && !dealersMap.has(dealerId)) {
              try {
                dealersMap.set(dealerId, {
                  dealerId,
                  dealerType,
                  isActive: true,
                  isPaying: true,
                  ...(dealer && { ...dealer }),
                })
              } catch (e) {
                console.error(e)
              }
            }

            const inventoryId = item.id

            const itemId = `listing_SE_${job.project}_${inventoryId}`
            const stockType = getStockType(item)
            const municipalityLocationId = getLocationId(item, 'MUNICIPALITY')
            const price = item.askingPrice?.amount ? item.askingPrice?.amount : 0
            const currency = item.askingPrice?.currency?.code ? item.askingPrice.currency.code : 'SEK'
            const priceRange = getPriceRange(price)
            const area = item.livingArea ? getREArea(item.livingArea) : 0
            const hasRocket = item.gaPageViewCustomDimensions
              ? item.gaPageViewCustomDimensions.some((e: { identifier: number; value: string }) => {
                  if (e?.identifier === 68) {
                    return e.value
                  }
                  return false
                })
              : false
            const productType = !item.packagePurchase ? 'BASIC' : item.packagePurchase
            const businessType = RealEstatesBusinessType.sale
            const propertyScope = getPropertyScope(item)
            const propertyType = getPropertyType(item)
            const rawAdViews = item.timesViewed
            const adViews = rawAdViews ?? 0
            const daysActive = item?.daysOnHemnet

            const existingHistory = await getExistingAdViewsDaysActiveHistory(job, itemId, job.data?.iso)
            const updatedAdViewsDaysActiveHistory = compileAdViewsDaysActiveHistory(
              rawAdViews,
              daysActive,
              existingHistory,
            )

            const { isRepostedAdViews, isRepostedDaysActive } = assessIsReposted(updatedAdViewsDaysActiveHistory)

            let state: string | undefined
            if (item.locations?.length) {
              let stateName: string | undefined
              const locationState = item.locations?.filter((item) => item.type === 'COUNTY')
              if (locationState?.length) stateName = locationState[0].name
              if (stateName) {
                if (stateName.endsWith('s')) {
                  stateName = stateName.substring(0, stateName.length - 1)
                }
                const stateElem = job.data?.statesArray.filter(
                  (item: { code: string; name: string }) => item.name === stateName,
                )
                if (stateElem?.length === 1) {
                  state = stateElem[0].code
                }
              }
            }

            return {
              type: ResultType.item,
              data: {
                project: ProjectType.realestates,
                iso: job.data?.iso,
                dataType: DataType.inventory,
                inventoryId,
                itemId,
                stockType,
                dealerId,
                dealerType,
                ...(item.coordinates?.long &&
                  item.coordinates?.lat && {
                    geoJson: {
                      type: GeoJSONTypes.Point,
                      coordinates: [item.coordinates.long, item.coordinates.lat],
                    },
                  }),
                ...(price && { price }),
                ...(price && currency && { currency }),
                ...(priceRange && { priceRange }),
                ...(item.postCode && { zipCode: item.postCode }),
                ...(state && { state }),
                ...(area && { area }),
                businessType,
                propertyScope,
                propertyType,
                productType,
                data: {
                  adType: item.listingType,
                  streetAddress: item.streetAddress,
                  ...(municipalityLocationId && { municipalityLocationId }),
                  askingPriceAmount: price,
                  ...(currency && { currency }),
                  housingForm: item.housingForm,
                  brokerAgency: item.brokerAgency,
                  broker: item.broker,
                  hasBrokerLogo,
                  isUpcoming: item.isUpcoming,
                  housingFormGroup: job.data?.housingFormGroup,
                  hasRocket,
                  adViews,
                  adViewsDaysActiveHistory: updatedAdViewsDaysActiveHistory,
                  daysActive,
                  isCrossposted: false,
                  isRepostedAdViews,
                  isRepostedDaysActive,
                },
              },
            }
          }),
        ).then((results) => results.flat())

        const resultsDealers = [...dealersMap.values()].flatMap((item) => {
          if (!item?.dealerId) return []
          return {
            type: ResultType.item,
            data: {
              project: ProjectType.realestates,
              itemId: `dealer_SE_${job.project}_${item.dealerId}`,
              iso: job.data?.iso,
              dataType: DataType.dealers,
              ...item,
            },
          }
        })

        return [...resultsInventory, ...resultsDealers]
      },
    },
  })
}

export async function scheduleRealEstatesPackagePricing(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2 = Iso3166Alpha2.SE,
): Promise<JobDefinition[]> {
  const collection = runtime.collection('item')
  const items = await collection
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, job.provider, DataType.inventory),
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        productType: { $exists: true },
        'data.municipalityLocationId': { $exists: true },
      },
      {
        projection: {
          _id: 0,
          itemId: 1,
          price: 1,
          'data.municipalityLocationId': 1,
          productType: 1,
          area: 1,
          'data.housingFormGroup': 1,
          'data.hasRocket': 1,
        },
      },
    )
    .toArray()
  if (!items?.length) return []
  const itemsPerChunk = 100
  const itemsChunks = [...chunks(items, itemsPerChunk)]
  return itemsChunks.map((chunk) => ({
    ...job,
    data: {
      ...job.data,
      iso,
      listings: chunk,
    },
  }))
}

export async function extractRealEstatesPackagePricing(job: JobInstance) {
  const appendData: { listingId: string; advertisingCost: number; rocketCost: number | undefined }[] = []
  for (const listing of job.data.listings) {
    await wait(1000)
    const variables = {
      locationId: listing.data.municipalityLocationId.toString(),
      productCodes,
    }
    if (listing.price && listing.price > 0) {
      variables['askingPrice'] = listing.price
    } else if (listing.data.housingFormGroup === 'APARTMENTS' && listing.area) {
      variables['housingFormGroup'] = listing.data.housingFormGroup
      variables['livingAreaInSqm'] = listing.area
    } else if (listing.data.housingFormGroup && listing.data.housingFormGroup !== 'APARTMENTS') {
      variables['housingFormGroup'] = listing.data.housingFormGroup
    } else variables['askingPrice'] = 2000000000

    const data = await job.runtime
      .got('https://www.hemnet.se/graphql', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        json: {
          query: packagePricesQuery,
          variables,
        },
      })
      .json<{
        data: {
          sellerMarketingProductPrices: {
            prices: PricesEntity[]
          }
        }
      }>()
      .then((res) => res.data?.sellerMarketingProductPrices?.prices)
      .catch((error) => {
        job.addErrorToLog(error)
      })

    if (typeof data === 'undefined' || !data?.length) {
      continue
    }
    const { packagePrice, rocketPrice } = getAdvertisingCost(listing, data)
    appendData.push({
      listingId: listing.itemId,
      advertisingCost: packagePrice,
      rocketCost: rocketPrice,
    })
  }
  return appendData.flatMap((item) => {
    if (!item?.listingId) return []
    return {
      type: ResultType.item,
      data: {
        itemId: item.listingId,
        iso: job.data?.iso,
        'data.advertisingCost': item.advertisingCost,
        ...(item.rocketCost && { 'data.rocketCost': item.rocketCost }),
      },
    }
  })
}

export async function scheduleRepublished(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2 = Iso3166Alpha2.SE,
): Promise<JobDefinition[]> {
  const newItems = await runtime
    .collection('item')
    .find(
      {
        projectProviderDataType: 'realestates_hemnet_inventory',
        iso,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        geoJson: { $exists: true },
        createdAt: { $gte: startOfISOWeek(new Date()) },
        'data.adType': { $nin: ['Project', 'ProjectUnit'] },
      },
      { projection: { _id: 0, itemId: 1, iso: 1, geoJson: 1, area: 1 } },
    )
    .toArray()
  if (!newItems?.length) return []
  const newItemsSliced = arraySlicer(newItems, 100)
  return newItemsSliced.map((newListings) => ({ ...job, data: { iso, newListings } }))
}

export async function extractRepublished(job: JobInstance) {
  const dataArray = []
  for (const newListing of job.data.newListings) {
    let val
    if (newListing.area) {
      val = await job.runtime
        .collection('item')
        .find({
          projectProviderDataType: 'realestates_hemnet_inventory',
          iso: newListing.iso,
          updatedAt: { $lt: startOfISOWeek(new Date()) },
          geoJson: newListing.geoJson,
          'data.adType': { $nin: ['Project', 'ProjectUnit'] },
          area: newListing.area,
        })
        .toArray()
      if (val?.length) {
        dataArray.push({
          ...newListing,
        })
        continue
      }
    } else {
      val = await job.runtime
        .collection('item')
        .find({
          projectProviderDataType: 'realestates_hemnet_inventory',
          iso: newListing.iso,
          updatedAt: { $lt: startOfISOWeek(new Date()) },
          geoJson: newListing.geoJson,
          'data.adType': { $nin: ['Project', 'ProjectUnit'] },
        })
        .toArray()
      if (val?.length) {
        dataArray.push({
          ...newListing,
        })
      }
    }
  }
  return dataArray.flatMap((item) => {
    if (!item?.itemId) return []
    return {
      type: ResultType.item,
      data: {
        itemId: item.itemId,
        iso: item.iso,
        'data.isRepublished': true,
      },
    }
  })
}

export async function extractRocket(job: JobInstance) {
  const items = await job.runtime
    .collection<{
      inventoryId: string
      price: number
      dealerId: string
      dealerType: string
      productType: string
      data: {
        rocketCost: number
      }
    }>(ResultType.item)
    .find(
      {
        projectProviderDataType: getProjectProviderDataType(job.project, 'hemnet', DataType.inventory),
        iso: Iso3166Alpha2.SE,
        updatedAt: { $gte: startOfISOWeek(new Date()) },
        'data.rocketCost': { $exists: true },
      },
      {
        projection: {
          _id: 0,
          inventoryId: 1,
          price: 1,
          dealerId: 1,
          dealerType: 1,
          'data.rocketCost': 1,
          productType: 1,
        },
      },
    )
    .toArray()

  if (!items?.length) {
    return []
  }
  return items.flatMap((item) => {
    if (!item?.inventoryId) return []
    return {
      type: ResultType.item,
      data: {
        project: ProjectType.realestates,
        itemId: getItemId(DataType.inventory, job.project, item.inventoryId),
        inventoryId: item.inventoryId,
        iso: Iso3166Alpha2.SE,
        dataType: DataType.inventory,
        price: item.price,
        dealerId: item.dealerId,
        dealerType: item.dealerType,
        productType: 'ROCKET',
        'data.advertisingCost': item.data.rocketCost,
        'data.packageType': item.productType,
      },
    }
  })
}

function getStockType(item: HemnetListings): RealEstatesStockType {
  if (item.housingForm?.symbol) {
    switch (item.housingForm?.symbol) {
      case 'APARTMENT':
      case 'HOUSE':
      case 'ROW_HOUSE':
        return RealEstatesStockType.residential_sales
      default:
        return RealEstatesStockType.commercial_sales
    }
  }
  return RealEstatesStockType.other
}

function getLocationId(item: HemnetListings, type: string): string | undefined {
  return item?.locations?.find((location) => location?.type === type)?.id
}

function getAdvertisingCost(listing: { productType: string; data: { hasRocket: boolean } }, data: PricesEntity[]) {
  const basicPrice = data.find((item) => item.code === 'BASIC')?.price.amount || 0
  const plusPrice = data.find((item) => item.code === 'PLUS')?.price.amount || 0
  const premiumPrice = data.find((item) => item.code === 'PREMIUM')?.price.amount || 0
  const maxPrice = data.find((item) => item.code === 'MAX')?.price.amount || 0
  const toplistingPrice = data.find((item) => item.code === 'TOPLISTING')?.price.amount || 0
  // const paidRepublishPrice = data.find((item) => item.code === 'PAID_REPUBLISH')?.price.amount || 0

  switch (listing.productType) {
    case 'BASIC':
      return {
        packagePrice: basicPrice,
        ...(listing.data.hasRocket && {
          rocketPrice: toplistingPrice,
        }),
      }
    case 'PLUS':
      return {
        packagePrice: basicPrice + plusPrice,
        ...(listing.data.hasRocket && {
          rocketPrice: toplistingPrice,
        }),
      }
    case 'PREMIUM':
      return {
        packagePrice: basicPrice + premiumPrice,
        ...(listing.data.hasRocket && {
          rocketPrice: toplistingPrice,
        }),
      }
    case 'MAX':
      return {
        packagePrice: basicPrice + maxPrice,
        ...(listing.data.hasRocket && {
          rocketPrice: toplistingPrice,
        }),
      }
    default:
      return {
        packagePrice: 0,
        rocketPrice: undefined,
      }
  }
}

function getPriceRange(price: number): string {
  if (price < 300000) return '0 - 299,999'
  if (price >= 300000 && price < 600000) return '300,000 - 599,999'
  if (price >= 600000 && price < 1100000) return '600,000 - 1,099,999'
  if (price >= 1100000 && price < 1600000) return '1,100,000 - 1,599,999'
  if (price >= 1600000 && price < 2100000) return '1,600,000 - 2,099,999'
  if (price >= 2100000 && price < 2600000) return '2,100,000 - 2,599,999'
  if (price >= 2600000 && price < 3100000) return '2,600,000 - 3,099,999'
  if (price >= 3100000 && price < 4100000) return '3,100,000 - 4,099,999'
  if (price >= 4100000 && price < 5100000) return '4,100,000 - 5,099,999'
  if (price >= 5100000 && price < 6100000) return '5,100,000 - 6,099,999'
  if (price >= 6100000 && price < 8100000) return '6,100,000 - 8,099,999'
  if (price >= 8100000 && price < 10100000) return '8,100,000 - 10,099,999'
  if (price >= 10100000 && price < 11100000) return '10,100,000 - 11,099,999'
  if (price >= 11100000 && price < 12100000) return '11,100,000 - 12,099,999'
  if (price >= 12100000 && price < 13100000) return '12,100,000 - 13,099,999'
  if (price >= 13100000 && price < 14100000) return '13,100,000 - 14,099,999'
  if (price >= 14100000 && price < 15100000) return '14,100,000 - 15,099,999'
  if (price >= 15100000 && price < 16100000) return '15,100,000 - 16,099,999'
  if (price >= 16100000 && price < 17100000) return '16,100,000 - 17,099,999'
  if (price >= 17100000 && price < 18100000) return '17,100,000 - 18,099,999'
  if (price >= 18100000 && price < 19100000) return '18,100,000 - 19,099,999'
  if (price >= 19100000 && price < 20100000) return '19,100,000 - 20,099,999'
  if (price >= 20100000 && price < 21100000) return '20,100,000 - 21,099,999'
  if (price >= 21100000 && price < 22100000) return '21,100,000 - 22,099,999'
  if (price >= 22100000 && price < 23100000) return '22,100,000 - 23,099,999'
  if (price >= 23100000 && price < 24100000) return '23,100,000 - 24,099,999'
  if (price >= 24100000 && price < 25100000) return '24,100,000 - 25,099,999'
  if (price >= 25100000 && price < 26100000) return '25,100,000 - 26,099,999'
  if (price >= 26100000 && price < 27100000) return '26,100,000 - 27,099,999'
  if (price >= 27100000 && price < 28100000) return '27,100,000 - 28,099,999'
  if (price >= 28100000 && price < 29100000) return '28,100,000 - 29,099,999'
  if (price >= 29100000 && price < 30100000) return '29,100,000 - 30,099,999'
  return '30,100,000 +'
}

export async function setListingsPriceRanges(
  config: ControllerConfig,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2 = Iso3166Alpha2.SE,
  dateArg: string | Date = new Date(),
) {
  const date = new Date(dateArg)
  await runtime.collection<Item>('item').updateMany(
    {
      projectProviderDataType: getProjectProviderDataType(config.project, config.provider, DataType.inventory),
      iso,
      updatedAt: { $gte: startOfISOWeek(date), $lt: startOfISOWeek(addWeeks(date, 1)) },
    },
    [
      {
        $set: {
          priceRange: {
            $switch: priceRangeDbSwitch,
          },
        },
      },
    ],
  )
  return []
}

function getListingCountRange(inventoryCount: number) {
  let listingCountRange: HemnetListingCountPerDealerRange = '0 - 5'
  if (inventoryCount >= 6 && inventoryCount <= 10) {
    listingCountRange = '6 - 10'
  } else if (inventoryCount >= 11 && inventoryCount <= 20) {
    listingCountRange = '11 - 20'
  } else if (inventoryCount >= 21 && inventoryCount <= 40) {
    listingCountRange = '21 - 40'
  } else if (inventoryCount >= 41 && inventoryCount <= 100) {
    listingCountRange = '41 - 100'
  } else if (inventoryCount >= 101) {
    listingCountRange = '101 +'
  }
  return listingCountRange
}

function getPropertyType(item: HemnetListings): RealEstatesPropertyType {
  switch (item?.housingForm?.symbol) {
    case 'HOUSE':
    case 'ROW_HOUSE':
    case 'TWIN_HOUSE':
    case 'TERRACED_HOUSE':
    case 'VACATION_HOUSE':
    case 'HOMESTEAD':
    case 'LINKED_HOUSE':
    case 'VACATION_HOME':
    case 'WINTERIZED_VACATION_HOME':
      return RealEstatesPropertyType.house
    case 'APARTMENT':
      return RealEstatesPropertyType.apartment
    case 'FORESTING_ESTATE':
    case 'AGRICULTURAL_ESTATE':
      return RealEstatesPropertyType.agriculture
    case 'ESTATE_WITHOUT_CULTIVATION':
    case 'PLOT':
      return RealEstatesPropertyType.urban
    default:
      return RealEstatesPropertyType.other
  }
}

function getPropertyScope(item: HemnetListings): RealEstatesPropertyScope {
  switch (item?.housingForm?.symbol) {
    case 'HOUSE':
    case 'ROW_HOUSE':
    case 'TWIN_HOUSE':
    case 'TERRACED_HOUSE':
    case 'VACATION_HOUSE':
    case 'HOMESTEAD':
    case 'LINKED_HOUSE':
    case 'VACATION_HOME':
    case 'WINTERIZED_VACATION_HOME':
    case 'APARTMENT':
    case 'ESTATE_WITHOUT_CULTIVATION':
      return RealEstatesPropertyScope.residential
    case 'FORESTING_ESTATE':
    case 'AGRICULTURAL_ESTATE':
    case 'PLOT':
      return RealEstatesPropertyScope.land
    default:
      return RealEstatesPropertyScope.other
  }
}

export async function cleanupMunicipalityPrices(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  await runtime
    .collection<Town>('town')
    .updateMany(
      { project: job.project, provider: job.provider, iso, 'data.type': 'MUNICIPALITY' },
      { $unset: { 'data.municipalityPrices': '' } },
    )
  return []
}

export async function schedulePricesCsv(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  await cleanupMunicipalityPrices(job, runtime, Iso3166Alpha2.SE)

  const municipalitiesArray = await runtime
    .collection<Town>('town')
    .find({ project: job.project, provider: job.provider, iso, 'data.type': 'MUNICIPALITY' })
    .toArray()

  if (!municipalitiesArray?.length) return []
  return municipalitiesArray.flatMap((town) => {
    if (!town?.data?.id) return []
    return {
      ...job,
      data: {
        municipality: town.data,
        iso,
        townItemId: town.itemId,
      },
    }
  })
}

export async function extractMunicipalityPrices(job: JobInstance) {
  // const askingPrices = [
  //   1, 300000, 600000, 1100000, 1600000, 2100000, 2600000, 3100000, 4100000, 5100000, 6100000, 8100000, 10100000,
  // ]
  const startingPricesRaw = [
    1, 0.3, 0.6, 1.1, 1.6, 2.1, 2.6, 3.1, 4.1, 5.1, 6.1, 8.1, 10.1, 11.1, 12.1, 13.1, 14.1, 15.1, 16.1, 17.1, 18.1,
    19.1, 20.1, 21.1, 22.1, 23.1, 24.1, 25.1, 26.1, 27.1, 28.1, 29.1, 30.1,
  ]

  const askingPrices = startingPricesRaw.map((price) => {
    if (price === 1) return 1
    return Math.ceil(1000000 * price)
  })
  const obj = {}
  for (const askingPrice of askingPrices) {
    await wait(1000)
    await job.runtime
      .got('https://www.hemnet.se/graphql', {
        method: 'POST',
        retry: { limit: 2 },
        json: {
          operationName: 'SellerMarketingProductPrices',
          variables: {
            askingPrice,
            locationId: job.data?.municipality.id,
            productCodes,
          },
          query:
            'query SellerMarketingProductPrices($locationId: ID!, $askingPrice: Int, $housingFormGroup: HousingFormGroup, $livingAreaInSqm: Float, $productCodes: [PackagePurchase!]!) { sellerMarketingProductPrices( locationId: $locationId askingPrice: $askingPrice  productCodes: $productCodes housingFormGroup: $housingFormGroup livingAreaInSqm: $livingAreaInSqm ) { formattedValidThrough prices { code price { amount } } } }',
        },
      })
      .json<{
        data: {
          sellerMarketingProductPrices: {
            prices: {
              code: string
              price: {
                amount: number
              }
            }[]
          }
        }
      }>()
      .then((res) => {
        if (!res?.data?.sellerMarketingProductPrices?.prices?.length) return []
        return res?.data?.sellerMarketingProductPrices?.prices.map((price) => {
          let code
          switch (price.code) {
            case 'BASIC':
              code = 'Basic'
              break
            case 'PLUS':
              code = 'Plus'
              break
            case 'PREMIUM':
              code = 'Premium'
              break
            case 'MAX':
              code = 'Max'
              break
            case 'PAID_REPUBLISH':
              code = 'Republishing'
              break
            case 'TOPLISTING':
              code = 'Rocket'
              break
            default:
              code = price.code
              break
          }
          if (!obj[code]) obj[code] = []
          obj[code].push({ value: price.price.amount, askingPrice })
        })
      })
  }
  if (!Object.keys(obj)?.length) return []
  return [
    {
      type: ResultType.town,
      data: {
        project: job.project,
        provider: job.provider,
        iso: job.data?.iso,
        itemId: job.data?.townItemId,
        'data.municipalityPrices': obj,
      },
    },
  ]
}

export async function municipalityPricesCsv(config: ControllerConfig, runtime: ControllerRuntime) {
  const municipalitiesArray = await runtime
    .collection<Town>('town')
    .find({
      project: config.project,
      provider: config.provider,
      iso: Iso3166Alpha2.SE,
      'data.type': 'MUNICIPALITY',
      'data.municipalityPrices': { $exists: true },
    })
    .sort({ 'data.shortName': 1 })
    .project({
      _id: 0,
      name: '$data.shortName',
      fullName: '$data.fullName',
      prices: '$data.municipalityPrices',
    })
    .toArray()
  if (!municipalitiesArray?.length) return

  const csvResults = municipalitiesArray.flatMap((item) => {
    if (!Object.keys(item.prices)?.length) return []
    const res = [{ id: item.name }]
    for (const key of Object.keys(item.prices)) {
      const obj = { id: key }
      for (const prices of item.prices[key]) {
        obj[prices.askingPrice] = prices.value
      }
      res.push(obj)
    }
    res.push({ id: '' })
    return res
  })

  const filename = `Hemnet Municipality Prices ${format(new Date(), 'RRRR/II')}.csv`
  const csv = json2csv(csvResults, { emptyFieldValue: '' })
  const gDriveAccount = 'vor'
  const gDrive = await GDrive(gDriveAccount)
  await gDrive.upsert(
    csv,
    filename,
    accountConfigs[gDriveAccount].defaultFileType,
    accountConfigs[gDriveAccount].folders.hemnetMunicipalityPrices,
  )
}

export async function perRegionData(
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
  field: 'productType' | 'priceRange',
) {
  if (!runtime.collection) return

  const startDate = format(subWeeks(startOfISOWeek(Date.now()), 4), 'RRRR/II')

  const results = await runtime
    .collection<Snapshot>('snapshot')
    .aggregate([
      {
        $match: {
          project: ProjectType.realestates,
          provider: 'hemnet',
          iso,
          dataType: DataType.inventory,
          snapTime: { $gte: startDate },
          snapTimeUnit: TimeUnit.weeks,
        },
      },
      { $unwind: '$stats' },
      { $match: { 'stats.isNew': true } },
      {
        $group: {
          _id: {
            snapTime: '$snapTime',
            state: '$stats.state',
            ...(field === 'priceRange' && { priceRange: '$stats.priceRange' }),
            ...(field === 'productType' && { productType: '$stats.productType' }),
          },
          count: { $sum: '$stats.count' },
        },
      },
      {
        $project: {
          _id: 0,
          state: {
            $switch: {
              branches: [
                { case: { $eq: ['$_id.state', 'SE-K'] }, then: 'Blekinge' },
                { case: { $eq: ['$_id.state', 'SE-W'] }, then: 'Dalarna' },
                { case: { $eq: ['$_id.state', 'SE-I'] }, then: 'Gotland' },
                { case: { $eq: ['$_id.state', 'SE-X'] }, then: 'Gävleborg' },
                { case: { $eq: ['$_id.state', 'SE-N'] }, then: 'Halland' },
                { case: { $eq: ['$_id.state', 'SE-Z'] }, then: 'Jämtland' },
                { case: { $eq: ['$_id.state', 'SE-F'] }, then: 'Jönköping' },
                { case: { $eq: ['$_id.state', 'SE-H'] }, then: 'Kalmar' },
                { case: { $eq: ['$_id.state', 'SE-G'] }, then: 'Kronoberg' },
                { case: { $eq: ['$_id.state', 'SE-BD'] }, then: 'Norrbotten' },
                { case: { $eq: ['$_id.state', 'SE-M'] }, then: 'Skåne' },
                { case: { $eq: ['$_id.state', 'SE-AB'] }, then: 'Stockholm' },
                { case: { $eq: ['$_id.state', 'SE-D'] }, then: 'Södermanland' },
                { case: { $eq: ['$_id.state', 'SE-C'] }, then: 'Uppsala' },
                { case: { $eq: ['$_id.state', 'SE-S'] }, then: 'Värmland' },
                { case: { $eq: ['$_id.state', 'SE-AC'] }, then: 'Västerbotten' },
                { case: { $eq: ['$_id.state', 'SE-Y'] }, then: 'Västernorrland' },
                { case: { $eq: ['$_id.state', 'SE-U'] }, then: 'Västmanland' },
                { case: { $eq: ['$_id.state', 'SE-O'] }, then: 'Västra Götaland' },
                { case: { $eq: ['$_id.state', 'SE-T'] }, then: 'Örebro' },
                { case: { $eq: ['$_id.state', 'SE-E'] }, then: 'Östergötland' },
              ],
              default: 'Unknown',
            },
          },
          snapTime: '$_id.snapTime',
          ...(field === 'priceRange' && {
            priceRange: {
              $cond: [
                { $eq: ['$_id.priceRange', '30,100,000 +'] },
                '30,100,000',
                { $trim: { input: { $arrayElemAt: [{ $split: ['$_id.priceRange', '-'] }, 0] } } },
              ],
            },
            priceRangeIndex: {
              $switch: {
                branches: [
                  { case: { $eq: ['$_id.priceRange', '0 - 299,999'] }, then: 1 },
                  { case: { $eq: ['$_id.priceRange', '300,000 - 599,999'] }, then: 2 },
                  { case: { $eq: ['$_id.priceRange', '600,000 - 1,099,999'] }, then: 3 },
                  { case: { $eq: ['$_id.priceRange', '1,100,000 - 1,599,999'] }, then: 4 },
                  { case: { $eq: ['$_id.priceRange', '1,600,000 - 2,099,999'] }, then: 5 },
                  { case: { $eq: ['$_id.priceRange', '2,100,000 - 2,599,999'] }, then: 6 },
                  { case: { $eq: ['$_id.priceRange', '2,600,000 - 3,099,999'] }, then: 7 },
                  { case: { $eq: ['$_id.priceRange', '3,100,000 - 4,099,999'] }, then: 8 },
                  { case: { $eq: ['$_id.priceRange', '4,100,000 - 5,099,999'] }, then: 9 },
                  { case: { $eq: ['$_id.priceRange', '5,100,000 - 6,099,999'] }, then: 10 },
                  { case: { $eq: ['$_id.priceRange', '6,100,000 - 8,099,999'] }, then: 11 },
                  { case: { $eq: ['$_id.priceRange', '8,100,000 - 10,099,999'] }, then: 12 },
                  { case: { $eq: ['$_id.priceRange', '10,100,000 - 11,099,999'] }, then: 13 },
                  { case: { $eq: ['$_id.priceRange', '11,100,000 - 12,099,999'] }, then: 14 },
                  { case: { $eq: ['$_id.priceRange', '12,100,000 - 13,099,999'] }, then: 15 },
                  { case: { $eq: ['$_id.priceRange', '13,100,000 - 14,099,999'] }, then: 16 },
                  { case: { $eq: ['$_id.priceRange', '14,100,000 - 15,099,999'] }, then: 17 },
                  { case: { $eq: ['$_id.priceRange', '15,100,000 - 16,099,999'] }, then: 18 },
                  { case: { $eq: ['$_id.priceRange', '16,100,000 - 17,099,999'] }, then: 19 },
                  { case: { $eq: ['$_id.priceRange', '17,100,000 - 18,099,999'] }, then: 20 },
                  { case: { $eq: ['$_id.priceRange', '18,100,000 - 19,099,999'] }, then: 21 },
                  { case: { $eq: ['$_id.priceRange', '19,100,000 - 20,099,999'] }, then: 22 },
                  { case: { $eq: ['$_id.priceRange', '20,100,000 - 21,099,999'] }, then: 23 },
                  { case: { $eq: ['$_id.priceRange', '21,100,000 - 22,099,999'] }, then: 24 },
                  { case: { $eq: ['$_id.priceRange', '22,100,000 - 23,099,999'] }, then: 25 },
                  { case: { $eq: ['$_id.priceRange', '23,100,000 - 24,099,999'] }, then: 26 },
                  { case: { $eq: ['$_id.priceRange', '24,100,000 - 25,099,999'] }, then: 27 },
                  { case: { $eq: ['$_id.priceRange', '25,100,000 - 26,099,999'] }, then: 28 },
                  { case: { $eq: ['$_id.priceRange', '26,100,000 - 27,099,999'] }, then: 29 },
                  { case: { $eq: ['$_id.priceRange', '27,100,000 - 28,099,999'] }, then: 30 },
                  { case: { $eq: ['$_id.priceRange', '28,100,000 - 29,099,999'] }, then: 31 },
                  { case: { $eq: ['$_id.priceRange', '29,100,000 - 30,099,999'] }, then: 32 },
                  { case: { $eq: ['$_id.priceRange', '30,100,000 +'] }, then: 101 },
                  { case: { $eq: ['$_id.priceRange', '10,100,000 +'] }, then: 100 },
                ],
                default: 0,
              },
            },
          }),
          ...(field === 'productType' && { productType: '$_id.productType' }),
          count: '$count',
        },
      },
      {
        $sort: {
          state: 1,
          snapTime: 1,
          ...(field === 'priceRange' && { priceRangeIndex: 1 }),
          ...(field === 'productType' && { productType: 1 }),
        },
      },
      {
        $project: {
          priceRangeIndex: 0,
        },
      },
    ])
    .toArray()
  if (!results.length) throw new Error('Mongo Aggregation failed')

  const filename =
    field === 'priceRange'
      ? `Hemnet Price Range Per Region ${format(startOfISOWeek(Date.now()), 'RRRR/II')}.csv`
      : `Hemnet Product Type Per Region ${format(startOfISOWeek(Date.now()), 'RRRR/II')}.csv`
  const csv = json2csv(results, { emptyFieldValue: 0 })
  const account = 'vor'
  const gDrive = await GDrive(account)
  await gDrive.upsert(
    csv,
    filename,
    accountConfigs[account].defaultFileType,
    accountConfigs[account].folders.hemnetMunicipalityPrices,
  )
}

async function getExistingAdViewsDaysActiveHistory(
  job: JobInstance,
  itemId: string,
  iso: string,
): Promise<AdViewsDaysActiveHistoryEntry[]> {
  const existingListing = await job.runtime.collection('item').findOne(
    {
      itemId,
      projectProviderDataType: getProjectProviderDataType(ProjectType.realestates, 'hemnet', DataType.inventory),
      iso,
    },
    { projection: { 'data.adViewsDaysActiveHistory': 1 } },
  )

  return existingListing?.data?.adViewsDaysActiveHistory || []
}
