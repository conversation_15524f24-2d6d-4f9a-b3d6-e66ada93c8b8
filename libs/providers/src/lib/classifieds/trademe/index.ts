import { ClassifiedsStockType, ProjectType } from '@datagatherers/datagatherers'
import { snapshots } from '@datagatherers/worker-utils'

import { LIMIT, locations, paramsPerProject } from './constants'

import type { TrademeApiResponse, TrademeListEntities } from './types'
import type {
  ControllerConfig,
  ControllerRuntime,
  Iso3166Alpha2,
  JobDefinition,
  JobInstance,
} from '@datagatherers/datagatherers'

export function scheduleTrademeListings(job: JobDefinition, iso: Iso3166Alpha2): JobDefinition[] {
  const jobs: JobDefinition[] = []
  locations.map((location) => {
    paramsPerProject.map((param) => {
      if (job.project === param.project) {
        for (const canonicalPath of param.canonicalPaths) {
          if (param.project !== ProjectType.marketplaces) {
            jobs.push({
              ...job,
              data: {
                districtName: location.name,
                districtId: location.id,
                ...(job.data?.parameters && { parameters: job.data?.parameters }),
                iso,
                canonicalPath,
                url: param.url,
              },
            })
          } else {
            for (const condition of [ClassifiedsStockType.used, ClassifiedsStockType.new]) {
              jobs.push({
                ...job,
                data: {
                  districtName: location.name,
                  districtId: location.id,
                  ...(job.data?.parameters && { parameters: job.data?.parameters }),
                  iso,
                  canonicalPath,
                  url: param.url,
                  condition,
                },
              })
            }
          }
        }
      }
    })
  })

  return jobs
}

export async function extractTrademeApiData(job: JobInstance, canonicalPath: string, userDistrict: number) {
  return job.runtime.got.paginate.all<TrademeListEntities, TrademeApiResponse>(
    'https://api.trademe.co.nz/v1/search/general.json',
    {
      responseType: 'json',
      headers: {
        referer: 'https://www.trademe.co.nz/',
      },
      searchParams: {
        sort_order: 'expirydesc',
        rows: LIMIT,
        return_ads: 'true',
        canonical_path: canonicalPath,
        page: '1',
        user_district: userDistrict,
        ...(job.project === ProjectType.marketplaces && { condition: job.data?.condition }),
      },
      pagination: {
        backoff: 100,
        paginate: ({ response, currentItems }) => {
          const previousSearchParams = response.request.options.searchParams as URLSearchParams

          if (!previousSearchParams) return false
          const previousPage = previousSearchParams.get('page')

          if (currentItems.length < LIMIT) {
            return false
          }

          return {
            searchParams: {
              ...previousSearchParams,
              page: Number(previousPage) + 1,
            },
          }
        },
        transform: (response) => {
          const items = response.body.List

          if (!items?.length) return []

          return items
        },
      },
    },
  )
}

export async function snapshot(iso: Iso3166Alpha2, config: ControllerConfig, runtime: ControllerRuntime, date?: Date) {
  if (config.project === ProjectType.jobs) await snapshots.jobsFull(iso, config, runtime, date)
  if (config.project === ProjectType.cars) await snapshot(iso, config, runtime, date)
  if (config.project === ProjectType.realestates) await snapshots.realestatesFull(iso, config, runtime, date)
  if (config.project === ProjectType.marketplaces) await snapshots.marketplacesFull(iso, config, runtime, date)
}
