import { <PERSON><PERSON><PERSON> } from 'jsdom'

import { ResultType, DataType, DealerType, ProjectType, VehicleType } from '@datagatherers/datagatherers'
import { snapshot, wait } from '@datagatherers/worker-utils'

import { categories } from './constants'

import type {
  JobDefinition,
  Iso3166Alpha2,
  JobConfig,
  JobInstance,
  ControllerRuntime,
  ControllerConfig,
} from '@datagatherers/datagatherers'

const lib = {
  scheduleExtract: async (
    job: JobDefinition,
    runtime: ControllerRuntime,
    iso: Iso3166Alpha2,
  ): Promise<JobDefinition[]> => {
    const searchCategory = categories.filter((item) => item.project === job.project)
    const jobs: JobDefinition[] = []

    for (const cat of searchCategory) {
      await wait(1000)
      await runtime.page.setUserAgent(
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
      )

      await runtime.page.setViewport({
        width: 1920,
        height: 1080,
        isMobile: false,
      })

      await runtime.page.goto(cat.url, { waitUntil: 'networkidle2' })

      let totalItems
      try {
        totalItems = await runtime.page.$eval(
          '.count_v',

          (el) => Number(el?.textContent?.replace(/\D/g, '')),
        )
      } catch {
        console.error('failed to get listings total from count_v, attempting another selector.')
      }

      if (!totalItems) {
        try {
          totalItems = await runtime.page.$eval(
            '.count_t',

            (el) => Number(el?.textContent?.replace(/\D/g, '')),
          )
        } catch {
          console.error('failed to get listings total from count_t, cannot get count from provided selectors.')
          break
        }
      }

      const nrPages = Math.ceil(Number(totalItems) / 25)
      const pagesPerJob = 10
      for (let start = 0; start < nrPages; start += pagesPerJob) {
        const end = Math.min(nrPages, start + pagesPerJob - 1)
        jobs.push({
          ...job,
          data: {
            iso,
            start,
            end,
            url: cat.url,
            project: cat.project,
            stockType: cat.stockType,
          },
        })
      }
    }

    return jobs
  },
  extract: async (job: JobInstance) => {
    const appendData: {
      url?: string
      id?: string
      stockType?: string
      dealerType?: string
      price?: number
    }[] = []
    const { document } = new JSDOM().window
    for (let page = job.data?.start; page <= job.data?.end; page++) {
      await wait(2000)
      const dataCards = await job.runtime
        .got(job.data?.url, {
          method: 'GET',
          searchParams: {
            page,
            inf: true,
          },
          headers: {
            'user-agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
          },
        })
        .text()
        .then((res) => res)

      if (!dataCards?.length) {
        return []
      }

      document.body.innerHTML = dataCards

      const items = document.querySelectorAll('a[class*="main_a_c_b"]')
      items.forEach((element) => {
        const url = element.querySelector('a')?.getAttribute('href')
        const id = element.getAttribute('id')
        const price = Number(
          element
            .querySelector('div.desc_m_a_b > div.descriptions_price_b > div.price > span')
            ?.textContent?.replace(/[^0-9.]/g, ''),
        )
        const stockType = job.data?.stockType
        const dealerType = DealerType.private
        appendData.push({
          ...(!!url && { url }),
          ...(!!id && { id }),
          stockType,
          dealerType,
          ...(!!price && { price }),
        })
      })
    }

    const resultsInventory = appendData
      .filter((a) => a?.id && (job.project === ProjectType.jobs || (a.price && !isNaN(a.price) && a.price <= 10000000)))
      .map((item) => {
        return {
          type: ResultType.item,
          data: {
            project: job.project,
            itemId: `listing_${job.project}_${job.data?.iso}_${item.id}`,
            iso: job.data?.iso,
            dataType: DataType.inventory,
            stockType: item.stockType,
            inventoryId: item.id,
            ...(item.price && { price: item.price }),
            ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
            currency: 'EUR',
            dealerType: item.dealerType,
          },
        }
      })

    return resultsInventory
  },
  snapshot: async (iso: Iso3166Alpha2, config: ControllerConfig, runtime: ControllerRuntime) =>
    snapshot(iso, config, runtime),
}

export const extractProviderConfig: JobConfig = {
  browser: true,
  concurrency: 10,
  returnCount: true,
}

export default lib
