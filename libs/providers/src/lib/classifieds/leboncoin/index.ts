import {
  Iso3166Alpha2,
  ClassifiedsStockType,
  DealerType,
  isProjectClassifieds,
  ResultType,
  DataType,
  ProjectType,
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  TimeUnit,
  VehicleType,
} from '@datagatherers/datagatherers'
import { GotProxy, getSnapTime, snapshots, solve, pageFetch, wait, snapshot } from '@datagatherers/worker-utils'

import cars from '../../cars'

import type { JobDefinition, JobInstance, ControllerRuntime, Snapshot, Town } from '@datagatherers/datagatherers'

interface LeboncoinItems {
  total: number
  total_all: number
  total_pro: number
  total_private: number
  total_active: number
  total_inactive: number
  total_shippable: number
  max_pages: number
  referrer_id: string
  pivot: string
  ads?: AdsEntity[] | null
  applied_condition: number
  human_readable_applied_condition: string
}
interface AdsEntity {
  list_id: number
  first_publication_date: string
  index_date: string
  status: string
  category_id: string
  category_name: string
  subject: string
  body: string
  ad_type: string
  url: string
  price?: number[] | null
  price_calendar?: unknown | null
  images: Record<string, unknown>
  attributes: Record<string, unknown>[]
  location: Location
  owner: {
    store_id: string
    user_id: string
    type: string
    name: string
    siren: string
    no_salesmen: boolean
    activity_sector: string
  }
  options: {
    has_option: boolean
    booster: boolean
    photosup: boolean
    urgent: boolean
    gallery: boolean
    sub_toplist: boolean
  }
  has_phone: boolean
}
interface Location {
  country_id: string
  region_id: string
  region_name: string
  department_id: string
  department_name: string
  city_label: string
  city: string
  zipcode: string
  lat: number
  lng: number
  source: string
  provider: string
  is_shape: boolean
  feature: {
    type: string
    geometry: {
      type: string
      coordinates: number[]
    }
    properties: unknown
  }
}

interface LeboncoinLocations {
  locationType: string
  label: string
  zipcode: string
  department_id: string
  region_id: string
  area: Area
  city?: string | null
}
interface Area {
  lat: number
  lng: number
  default_radius: number
}

const limit = 100

const datadome = process.argv.slice(-1)[0]

const provider = {
  scheduleCodes: async (
    job: JobDefinition,
    runtime: ControllerRuntime,
    iso: Iso3166Alpha2,
  ): Promise<JobDefinition[]> => {
    const collection = runtime.collection('postalcode')
    const filter = {
      iso,
    }
    const items = await collection.find(filter).toArray()
    const jobs: JobDefinition[] = items
      .filter((item) => !!item)
      .map((town) => {
        return {
          ...job,
          data: {
            ...town.data,
            iso,
            _id: town._id,
            ...(job.data?.parameters && { parameters: job.data?.parameters }),
          },
        }
      })
    return jobs
  },
  extractLocations: async (job: JobInstance) => {
    const appendData = []
    const preJob = preJobs.find((item) => item.project === job.project)
    if (!preJob || !preJob.categories || preJob.categories.length === 0) return []

    const gotProxy = new GotProxy(true, 2, 2, 10000, true)

    const config = {
      method: 'POST' as const,
      headers: {
        'X-LBC-CC': '7',
        Accept: 'application/json,application/hal+json',
        'User-Agent': 'LBC;Android;6.0.1;Nexus 7;tab;ced879b1c653792b;wifi;********;512300;0',
        'Content-Type': 'application/json; charset=UTF-8',
        'Accept-Encoding': 'gzip',
      },
      json: { context: [], text: job.data?.code },
      responseType: 'json' as const,
    }

    const locations = await gotProxy
      .retryWithCaptcha<
        LeboncoinLocations[]
      >(job, `https://api.leboncoin.fr/api/parrot/v4/complete/location`, config, 'geetest-puzzle', job.runtime.proxyPlan)
      .catch((error) => job.addErrorToLog(error))

    if (!locations || locations.length === 0) {
      return []
    }

    appendData.push({
      locations: [locations[0]],
      iso: job.data?.iso,
      zip: job.data?.code,
    })

    const results = appendData
      .filter((item) => !!item.zip && !!item.iso)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .map((item: any) => {
        const itemId = `${job.project}_${job.data?.iso}_${item.zip}`
        const data = {
          project: job.project,
          itemId,
          iso: job.data?.iso,
          blacklisted: false,
          data: {
            ...item,
          },
        }

        return {
          type: ResultType.town,
          data,
        }
      })

    return results
  },
  scheduleExtract: async (
    job: JobDefinition,
    runtime: ControllerRuntime,
    iso: Iso3166Alpha2,
  ): Promise<JobDefinition[]> => {
    const collection = runtime.collection<Town>('town')
    const filter = {
      project: job.project,
      provider: job.provider,
      iso,
    }
    const jobs: JobDefinition[] = []
    const limit = 20000
    const preJob = preJobs.find((item) => item.project === job.project)
    if (!preJob || !preJob.categories || preJob.categories.length === 0) return []
    for (let skip = 0; ; skip += limit) {
      const items = await collection.find(filter).skip(skip).limit(limit).toArray()
      if (!!items && items.length !== 0) {
        const category = preJob.categories.find(
          (item: { categoryId: string }) => !job.data?.categoryId || item.categoryId === job.data?.categoryId,
        )
        if (!category?.enums) {
          for (const item of items) {
            jobs.push({
              ...job,
              data: {
                ...item.data,
                categoryId: category?.categoryId,
                iso,
                enums: {},
                ...(job.data?.parameters && { parameters: job.data?.parameters }),
              },
            })
          }
        } else {
          for (const key of Object.keys(category.enums)) {
            const values = category.enums[key]
            if (!values || values.length === 0) continue
            for (const value of values) {
              const enums = {}
              enums[key] = [value]
              for (const item of items) {
                jobs.push({
                  ...job,
                  data: {
                    ...item.data,
                    categoryId: category.categoryId,
                    iso,
                    enums,
                    ...(job.data?.parameters && { parameters: job.data?.parameters }),
                  },
                })
              }
            }
          }
        }
      } else {
        break
      }
    }

    return jobs
  },
  extract: async (job: JobInstance) => {
    const appendData = []
    const gotProxy = new GotProxy(true, 2, 2, 10000, true)

    if (!isProjectClassifieds(job.project)) return []
    for (let offset = 0; ; offset += limit) {
      await wait(500)
      const headers = {
        api_key: 'ba0c2dad52b3ec',
        'X-LBC-CC': '7',
        Accept: 'application/json,application/hal+json',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'LBC;Android;6.0.1;Nexus 7;tab;ced879b1c653792b;wifi;********;512300;0',
        'Content-Type': 'application/json; charset=UTF-8',
      }
      let enums = {
        ad_type: ['offer'],
      }
      if (job.data?.enums) {
        enums = {
          ...enums,
          ...job.data?.enums,
        }
      }
      const config = {
        method: 'POST' as const,
        headers,
        json: {
          limit,
          offset,
          pivot: '0,0,0',
          sort_by: 'time',
          sort_order: 'desc',
          limit_alu: 1,
          owner_type: 'all',
          filters: {
            category: {
              id: job.data?.categoryId,
            },
            enums,
            keywords: {
              type: 'all',
            },
            location: {
              area: {
                lat: 0.0,
                lng: 0.0,
                radius: 0,
              },
              shippable: false,
              locations: job.data?.locations,
            },
            owner: {
              no_salesmen: false,
            },
            ranges: {},
          },
        },
        responseType: 'json' as const,
      }

      const items: LeboncoinItems = await gotProxy.retryWithCaptcha<LeboncoinItems>(
        job,
        `https://api.leboncoin.fr/api/adfinder/v1/search`,
        config,
        'geetest-puzzle',
        job.runtime.proxyPlan,
      )

      if (!items?.ads?.length) {
        break
      }

      appendData.push(...items.ads)

      if (items.ads?.length < 100) {
        break
      }
    }

    const dealersMap = new Map()
    const resultsInventory = appendData
      .filter((a) => !!a && !!a.list_id)
      .map((item) => {
        const stockType = getStockType(job, item)
        const dealerType = getDealerType(job.project, item)
        const address = item.location
        const dealer = item.owner
        let price = 0
        if (!!item.price && item.price.length !== 0) {
          price = item.price[0]
        }
        let dealerId
        if (dealer) {
          dealerId = dealer.store_id
        }
        if (dealerId) {
          if (!dealersMap.has(dealerId)) {
            try {
              dealersMap.set(dealerId, {
                dealerId,
                dealerType,
                dealer,
                options: item.options,
              })
            } catch (e) {
              console.error(e)
            }
          }
          if (job.project === ProjectType.realestates && dealersMap.has(dealerId)) {
            // Just to be safe
            const value = dealersMap.get(dealerId)
            if (value) {
              let stockTypeArr: Set<ClassifiedsStockType> = new Set()
              if (value.stockTypes) {
                stockTypeArr = new Set(value.stockTypes)
              }
              if (!stockTypeArr.has(stockType)) {
                stockTypeArr.add(stockType)
                value.stockTypes = [...stockTypeArr.values()]
                dealersMap.set(dealerId, value)
              }
            }
          }
        }

        let isSecurePayment
        let isShippable
        if (item.attributes?.length !== 0) {
          for (const attribute of item.attributes) {
            if (!attribute.key) {
              continue
            }
            if (attribute.key === 'vehicle_is_eligible_p2p') {
              isSecurePayment = attribute.value === '2'
              continue
            }
            if (attribute.key === 'shippable') {
              isShippable = attribute.value === 'true'
            }
          }
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let data: any = {
          project: job.project,
          itemId: `listing_${job.project}_${job.data?.iso}_${item.list_id}`,
          iso: job.data?.iso,
          dataType: DataType.inventory,
          inventoryId: `${item.list_id}`,
          dealerId,
          stockType,
          dealerType,
          price,
          ...(job.project !== ProjectType.marketplaces && {
            data: {
              ...item,
              ...(!!isSecurePayment && { isSecurePayment }),
            },
          }),
          ...(job.project === ProjectType.marketplaces && !!isShippable && { isShippable }),
        }

        if (job.project !== ProjectType.marketplaces && !!address) {
          data = {
            ...data,
            state: address.region_name,
            zipCode: address.zipcode,
          }
        }

        return {
          type: ResultType.item,
          data,
        }
      })
    const resultsDealers = [...dealersMap.values()]
      .filter((a) => !!a.dealerId)
      .map((item) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const data: any = {
          project: job.project,
          itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
          iso: job.data?.iso,
          dataType: DataType.dealers,
          dealerId: item.dealerId,
          dealerType: item.dealerType,
          ...(!!item.stockTypes && { stockTypes: item.stockTypes }),
          isActive: true,
          isPaying: item.dealerType !== DealerType.private,
          ...(job.project !== ProjectType.marketplaces && { data: item.dealer }),
        }

        return {
          type: ResultType.item,
          data,
        }
      })
    return [...resultsInventory, ...resultsDealers]
  },
  extractFetch: async (job: JobInstance) => {
    await job.runtime.page.goto('https://www.leboncoin.fr/', { waitUntil: 'networkidle2' })

    if (await job.runtime.page.$('iframe[src*="geo.captcha-delivery.com/captcha"]')) {
      await solve(job.runtime.page, 'geetest-puzzle')
    }

    const appendData = []

    if (!isProjectClassifieds(job.project)) return []
    for (let offset = 0; ; offset += limit) {
      await wait(600)
      const headers = {
        api_key: 'ba0c2dad52b3ec',
        'Content-Type': 'application/json',
      }
      let enums = {
        ad_type: ['offer'],
      }
      if (job.data?.enums) {
        enums = {
          ...enums,
          ...job.data?.enums,
        }
      }
      const config = {
        method: 'POST',
        headers,
        body: JSON.stringify({
          limit,
          offset,
          pivot: '0,0,0',
          sort_by: 'time',
          sort_order: 'desc',
          limit_alu: 1,
          owner_type: 'all',
          filters: {
            category: {
              id: job.data?.categoryId,
            },
            enums,
            keywords: {
              type: 'all',
            },
            location: {
              area: {
                lat: 0.0,
                lng: 0.0,
                radius: 0,
              },
              shippable: false,
              locations: job.data?.locations,
            },
            owner: {
              no_salesmen: false,
            },
            ranges: {},
          },
        }),
      }
      const data: string | undefined = await pageFetch(
        job.runtime.page,
        'https://api.leboncoin.fr/finder/search',
        config,
      )

      if (!data) {
        break
      }

      let result: LeboncoinItems
      try {
        result = JSON.parse(data)
      } catch {
        throw new Error('Failed to parse JSON response!')
      }

      if (!result?.ads?.length) {
        break
      }

      appendData.push(...result.ads)

      // if (result.ads?.length < 100) {
      //   break
      // }
    }

    const dealersMap = new Map()
    const resultsInventory = appendData
      .filter((a) => !!a && !!a.list_id)
      .map((item) => {
        const stockType = getStockType(job, item)
        const dealerType = getDealerType(job.project, item)
        const address = item.location
        const dealer = item.owner
        let price = 0
        if (!!item.price && item.price.length !== 0) {
          price = item.price[0]
        }
        let dealerId
        if (dealer) {
          dealerId = dealer.store_id
        }
        if (dealerId) {
          if (!dealersMap.has(dealerId)) {
            try {
              dealersMap.set(dealerId, {
                dealerId,
                dealerType,
                dealer,
                options: item.options,
              })
            } catch (e) {
              console.error(e)
            }
          }
          if (job.project === ProjectType.realestates && dealersMap.has(dealerId)) {
            // Just to be safe
            const value = dealersMap.get(dealerId)
            if (value) {
              let stockTypeArr: Set<ClassifiedsStockType> = new Set()
              if (value.stockTypes) {
                stockTypeArr = new Set(value.stockTypes)
              }
              if (!stockTypeArr.has(stockType)) {
                stockTypeArr.add(stockType)
                value.stockTypes = [...stockTypeArr.values()]
                dealersMap.set(dealerId, value)
              }
            }
          }
        }

        let isSecurePayment
        let isShippable
        if (item.attributes?.length !== 0) {
          for (const attribute of item.attributes) {
            if (!attribute.key) {
              continue
            }
            if (attribute.key === 'vehicle_is_eligible_p2p') {
              isSecurePayment = attribute.value === '2'
              continue
            }
            if (attribute.key === 'shippable') {
              isShippable = attribute.value === 'true'
            }
          }
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let data: any = {
          project: job.project,
          itemId: `listing_${job.project}_${job.data?.iso}_${item.list_id}`,
          iso: job.data?.iso,
          dataType: DataType.inventory,
          inventoryId: `${item.list_id}`,
          dealerId,
          stockType,
          dealerType,
          price,
          ...(job.project !== ProjectType.marketplaces && {
            data: {
              ...item,
              ...(!!isSecurePayment && { isSecurePayment }),
            },
          }),
          ...(job.project === ProjectType.marketplaces && !!isShippable && { isShippable }),
        }

        if (job.project !== ProjectType.marketplaces && !!address) {
          data = {
            ...data,
            state: address.region_name,
            zipCode: address.zipcode,
          }
        }

        return {
          type: ResultType.item,
          data,
        }
      })
    const resultsDealers = [...dealersMap.values()]
      .filter((a) => !!a.dealerId)
      .map((item) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const data: any = {
          project: job.project,
          itemId: `dealer_${job.project}_${job.data?.iso}_${item.dealerId}`,
          iso: job.data?.iso,
          dataType: DataType.dealers,
          dealerId: item.dealerId,
          dealerType: item.dealerType,
          ...(!!item.stockTypes && { stockTypes: item.stockTypes }),
          isActive: true,
          isPaying: item.dealerType !== DealerType.private,
          ...(job.project !== ProjectType.marketplaces && { data: item.dealer }),
        }

        return {
          type: ResultType.item,
          data,
        }
      })
    return [...resultsInventory, ...resultsDealers]
  },
  snapshot: async (iso: Iso3166Alpha2, config, runtime) => {
    if (config.project === ProjectType.realestates) {
      await snapshots.realestatesFull(iso, config, runtime)
    } else if (config.project === ProjectType.cars) {
      await snapshot(iso, config, runtime)
      if (iso === Iso3166Alpha2.FR) {
        await snapshot(iso, config, runtime, undefined, undefined, 'leboncoin_secure_payment', {
          'data.isSecurePayment': true,
        })
      }
    } else if (config.project === ProjectType.jobs) {
      await snapshots.jobsFull(iso, config, runtime)
    } else if (config.project === ProjectType.marketplaces) {
      await snapshots.marketplacesFull(iso, config, runtime)
      if (iso === Iso3166Alpha2.FR) {
        await snapshots.snapMarketplaces(
          iso,
          DataType.inventory,
          TimeUnit.weeks,
          config,
          runtime,
          undefined,
          `leboncoin_shippable`,
          { isShippable: true },
        )
        await snapshots.snapMarketplaces(
          iso,
          DataType.inventory,
          TimeUnit.months,
          config,
          runtime,
          undefined,
          `leboncoin_shippable`,
          { isShippable: true },
        )
      }
    }
  },
  scheduleInventoryForDealers: async (
    job: JobDefinition,
    runtime: ControllerRuntime,
    iso: Iso3166Alpha2,
  ): Promise<JobDefinition[]> => {
    return cars.scheduleInventoryForDealers(job, runtime, iso)
  },
  extractInventoryCount: async (job: JobInstance) => {
    return cars.extractInventoryCount(job)
  },
  concurrency: (): number => {
    return 20
  },
  extractListingsNumber: async (job: JobInstance, iso: Iso3166Alpha2) => {
    let totalPrivate = 0
    let totalDealers = 0
    const gotProxy = new GotProxy(true, 2, 2, 10000, true)

    for (const id of job.data.categoryId) {
      const enums = getListingParameters(job.provider, job.project, job.data?.stockType, id)
      const config = {
        method: 'POST' as const,
        headers: {
          api_key: 'ba0c2dad52b3ec',
          'X-LBC-CC': '7',
          Accept: 'application/json,application/hal+json',
          'Accept-Encoding': 'gzip',
          'User-Agent': 'LBC;Android;6.0.1;Nexus 7;tab;ced879b1c653792b;wifi;********;512300;0',
          'Content-Type': 'application/json; charset=UTF-8',
          ...(datadome !== 'no-cookie' && { cookie: `datadome=${datadome}` }),
        },
        json: {
          filters: {
            category: { id: id },
            enums: {
              ad_type: ['offer'],
              real_estate_type: enums?.realestateType,
              lease_type: enums?.leaseType,
              jobtime: enums?.jobTime,
              vehicle_type: enums?.vehicleType,
              vehicle_is_eligible_p2p: enums?.securePayment,
            },
            location: {},
            keywords: {},
            ranges: {},
          },
          limit: 1,
          limit_alu: 0,
          owner_type: 'all',
        },
        responseType: 'json' as const,
      }
      const data = await gotProxy
        .retryWithCaptcha<LeboncoinItems>(
          job,
          `https://api.leboncoin.fr/api/adfinder/v1/search`,
          config,
          'geetest-puzzle',
          job.runtime.proxyPlan,
        )
        .catch((error) => job.addErrorToLog(error))

      if (!data) {
        throw new Error("The API call didn't return any data, the weekly count will not be accurate")
      }
      totalDealers += data.total_pro
      totalPrivate += data.total_private
    }

    const snapTime = getSnapTime(new Date(), TimeUnit.weeks)
    const collection = job.runtime.collection<Snapshot>('snapshot')
    await collection.updateOne(
      {
        controllerId: '-1',
        project: job.project,
        snapTimeUnit: TimeUnit.weeks,
        snapTime,
        iso,
        provider: job.provider,
        dataType: DataType.inventory,
      },
      {
        $pull: { stats: { stockType: job.data?.stockType } },
        $set: {
          updatedAt: new Date(),
        },
      },
      { writeConcern: { w: 'majority' } },
    )
    await collection.updateOne(
      {
        controllerId: '-1',
        project: job.project,
        snapTimeUnit: TimeUnit.weeks,
        snapTime,
        iso,
        provider: job.provider,
        dataType: DataType.inventory,
      },
      {
        $setOnInsert: {
          controllerId: '-1',
          project: job.project,
          snapTimeUnit: TimeUnit.weeks,
          snapTime,
          iso,
          provider: job.provider,
          dataType: DataType.inventory,
          createdAt: new Date(),
        },
        $addToSet: {
          stats: {
            $each: [
              {
                count: totalDealers,
                stockType: job.data?.stockType,
                dealerType: DealerType.dealer,
                ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
              },
              {
                count: totalPrivate,
                stockType: job.data?.stockType,
                dealerType: DealerType.private,
                ...(job.project === ProjectType.cars && { vehicleType: VehicleType.car }),
              },
            ],
          },
        },
        $set: {
          updatedAt: new Date(),
        },
      },
      { upsert: true, writeConcern: { w: 'majority' } },
    )
    return []
  },
  extractDealersNumber: async (job: JobInstance, iso: Iso3166Alpha2) => {
    const gotProxy = new GotProxy(true, 2, 2, 10000, true)

    const config = {
      method: 'POST' as const,
      headers: {
        api_key: 'ba0c2dad52b3ec',
        'X-LBC-CC': '7',
        Accept: 'application/json,application/hal+json',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'LBC;Android;6.0.1;Nexus 7;tab;ced879b1c653792b;wifi;********;512300;0',
        'Content-Type': 'application/json; charset=UTF-8',
        ...(datadome !== 'no-cookie' && { cookie: `datadome=${datadome}` }),
      },
      json: {
        limit: 30,
        offset: 0,
        filters: { location: null, category: { id: job.data?.id } },
      },
      responseType: 'json' as const,
    }

    const data = await gotProxy
      .retryWithCaptcha<LeboncoinItems>(
        job,
        `https://api.leboncoin.fr/api/onlinestores/v1/online_stores`,
        config,
        'geetest-puzzle',
        job.runtime.proxyPlan,
      )
      .catch((error) => job.addErrorToLog(error))

    if (!data) {
      throw new Error("The API call didn't return any data, the weekly count will not be accurate")
    }

    const total = data.total
    const snapTime = getSnapTime(new Date(), TimeUnit.weeks)
    const collection = job.runtime.collection<Snapshot>('snapshot')
    const stockType = job.data?.stockType

    await collection.updateOne(
      {
        controllerId: '-1',
        project: job.project,
        snapTimeUnit: TimeUnit.weeks,
        snapTime,
        iso,
        provider: job.provider,
        dataType: DataType.dealers,
      },
      {
        $pull: {
          stats: {
            dealerType: job.data?.dealerType,
            isActive: true,
            isPaying: true,
            ...(!!stockType && { stockType }),
          },
        },
        $set: {
          updatedAt: new Date(),
        },
      },
      { writeConcern: { w: 'majority' } },
    )
    await collection.updateOne(
      {
        controllerId: '-1',
        project: job.project,
        snapTimeUnit: TimeUnit.weeks,
        snapTime,
        iso,
        provider: job.provider,
        dataType: DataType.dealers,
      },
      {
        $setOnInsert: {
          controllerId: '-1',
          project: job.project,
          snapTimeUnit: TimeUnit.weeks,
          snapTime,
          iso,
          provider: job.provider,
          dataType: DataType.dealers,
          createdAt: new Date(),
        },
        $addToSet: {
          stats: {
            count: total,
            dealerType: job.data?.dealerType,
            isActive: true,
            isPaying: true,
            ...(!!stockType && { stockType }),
          },
        },
        $set: {
          updatedAt: new Date(),
        },
      },
      { upsert: true, writeConcern: { w: 'majority' } },
    )

    return []
  },
}

export default provider

interface leboncoinJobItem {
  project: ProjectType
  categories: Array<{ categoryId: string; enums?: Record<string, string[]> }>
}

const preJobs: leboncoinJobItem[] = [
  {
    project: ProjectType.cars,
    categories: [
      {
        categoryId: '2',
        enums: {
          vehicle_type: ['4x4', 'berline', 'break', 'cabriolet', 'citadine', 'coupe', 'minibus', 'monospace', 'autre'],
        },
      },
    ],
  },
  {
    project: ProjectType.realestates,
    categories: [
      { categoryId: '9', enums: { real_estate_type: ['1', '2', '3', '4', '5'] } },
      { categoryId: '10', enums: { real_estate_type: ['1', '2', '3', '4', '5'] } },
      { categoryId: '11' },
      { categoryId: '13', enums: { lease_type: ['sell', RealEstatesBusinessType.rent] } },
    ],
  },
  {
    project: ProjectType.jobs,
    categories: [{ categoryId: '33', enums: { jobcontract: ['1', '2', '3', '4', '5', '6', '7'] } }],
  },
  {
    project: ProjectType.marketplaces,
    categories: [
      { categoryId: '6' }, // Car parts

      // '14', // Electronics
      { categoryId: '15' },
      {
        categoryId: '43',
        enums: { video_game_type: ['console', 'jeux', 'accessoires'] },
      },
      { categoryId: '16' },
      {
        categoryId: '17',
        enums: { phone_memory: ['8go', '16go', '32go', '64go', '128go', '256go', '512go', '999999'] },
      },

      // '72', // Fashion
      {
        categoryId: '22',
        enums: {
          clothing_tag: [
            'robe',
            'manteau',
            'haut',
            'pantalon',
            'pull',
            'jean',
            'chemise',
            'costume',
            'short',
            'sport',
            'maillot',
            'lingerie',
            'sousvetement',
            'deguisement',
            'mariage',
          ],
        },
      }, // add clothing_tag to enums below ad_type
      {
        categoryId: '53',
        enums: {
          shoe_category_a: [
            'basket',
            'lacets',
            'scratch',
            'mocassin',
            'bottine',
            'botte',
            'escarpin',
            'sandale',
            'chausson',
            'ballerine',
            'autre',
          ],
        },
      }, // add shoe_category_a to enums below ad_type
      {
        categoryId: '47',
        enums: {
          accessories_type: [
            'accessoirepourcheveux',
            'bonnetbobberet',
            'bretelles',
            'cartable',
            'casquette',
            'ceinture',
            'chaleetetole',
            'chapeau',
            'cravateetnoeudpapillon',
            'echarpeetfoulard',
            'etuitelephone',
            'Ggantsmitainesetmoufles',
            'houssedetabletteetordinateur',
            'lunettes',
            'mallette',
            'parapluie',
            'perruque',
            'pochette',
            'portecartes',
            'portechequier',
            'portecles',
            'portedocumentsserviette',
            'portehabits',
            'portemonnaieetportefeuille',
            'sacados',
            'sacamain',
            'sacdevoyage',
            'sacenbandouliere',
            'sacweekender',
            'sacoche',
            'totebag',
            'trousse',
            'valisecabine',
            'valisemoyenneetgrandetaille',
            'autre',
          ],
        },
      }, // add accessories_type to enums below ad_type
      {
        categoryId: '42',
        enums: {
          watches_jewels_type: [
            'bague',
            'bijoudetelephone',
            'bijoudetete',
            'bouclesdoreilles',
            'boutonsdemanchette',
            'bracelet',
            'broche',
            'chaine',
            'charm',
            'chevaliere',
            'collierpendentif',
            'gourmette',
            'montreagousset',
            'monteautomatique',
            'montreconnectee',
            'montredesport',
            'montremecanique',
            'montrequartz',
            'montrebague',
            'montrependentif',
            'parure',
            'piercing',
            'autre',
          ],
        },
      }, // add watches_jewels_type to enums below ad_type
      {
        categoryId: '23',
        enums: {
          baby_equipment_type: [
            'babyphone',
            'baignoire',
            'barrieredesecurite',
            'bavoir',
            'berceau',
            'biberon',
            'chaisehaute',
            'chanceliere',
            'chauffebiberon',
            'couffin',
            'coussinallaitement',
            'gigoteuse',
            'humidificateur',
            'litbebe',
            'litparapluie',
            'matelas',
            'nacelle',
            'nidange',
            'parc',
            'portebebe',
            'poubellecouches',
            'pousette',
            'potsiegereducteur',
            'rehausseur',
            'robotcuisine',
            'saclanger',
            'securitedomestique',
            'securiteexterieur',
            'siegeauto',
            'sterilisateur',
            'sucette',
            'tablealanger',
            'tapiseveil',
            'tetine',
            'thermometre',
            'tirelait',
            'transatbalancelle',
            'trotteur',
            'vaisselle',
            'veilleuse',
            'autre',
          ],
        },
      }, // add baby_equipment_type to enums below ad_type
      {
        categoryId: '54',
        enums: {
          baby_clothing_category_a: [
            'bodies',
            'tshirt',
            'bermuda',
            'pantalon',
            'jean',
            'pyjama',
            'pull',
            'robe',
            'manteau',
            'legging',
            'deguisement',
            'ensemble',
            'bonnet',
            'maillot',
          ],
        },
      }, // add baby_clothing_category_a to enums below ad_type

      // '18', // House Appliances
      {
        categoryId: '19',
        enums: {
          furniture_type: [
            'accessoire',
            'armoire',
            'bar',
            'bainbaignoire',
            'bibliothequeetetagere',
            'buffet',
            'bureau',
            'canape',
            'canapeconvertible',
            'chaisetabouretetbanc',
            'coffreetmalle',
            'coiffeuse',
            'commode',
            'console',
            'dressingetpenderie',
            'fauteuil',
            'lit',
            'litpourenfant',
            'luminaire',
            'matelas',
            'meubledecuisine',
            'meubledejardin',
            'meublederangement',
            'meubledesalledebain',
            'meubletv',
            'porte',
            'portemanteau',
            'sommier',
            'tabledappoint',
            'tabledesalleamanger',
            'tablebasse',
            'tabledechevet',
            'tapis',
            'vaisselier',
            'autre',
          ],
        },
      },
      { categoryId: '20' },
      {
        categoryId: '45',
        enums: {
          table_art_material: [
            'acrylique',
            'alu',
            'argenterie',
            'bambou',
            'bois',
            'caoutchouc',
            'carton',
            'ceramique',
            'coton',
            'cristal',
            'cuivre',
            'faience',
            'ferforge',
            'gres',
            'inox',
            'metal',
            'papier',
            'pierre',
            'plastique',
            'polyester',
            'porcelaine',
            'pvc',
            'resine',
            'terrecuite',
            'verre',
            'autre',
          ],
        },
      },
      {
        categoryId: '39',
        enums: {
          decoration_type: [
            'abatjour',
            'rangement',
            'salle_de_bain',
            'applique',
            'bibelot',
            'bougeoir',
            'bouquet',
            'cadre',
            'cendrier',
            'coussin',
            'dame_jeanne',
            'guirlande',
            'horloge',
            'lampadaire',
            'lampe_poser',
            'lampe_pied',
            'lustre',
            'miroir',
            'panier',
            'paravent',
            'pele_mele',
            'rideaux',
            'sculpture',
            'suspension',
            'tableau',
            'tapis',
            'vase',
            'autre',
          ],
        },
      },
      {
        categoryId: '46',
        enums: {
          linens_type: ['lingedelit', 'lingedebain', 'decotextile', 'lingedetable', 'equipementdulit', 'autre'],
        },
      },
      {
        categoryId: '21',
        enums: { item_condition: ['1', '2', '3', '7', '5'] },
      },
      {
        categoryId: '52',
        enums: { item_condition: ['1', '2', '3', '7', '5'] },
      },

      // '24', // Leisure
      { categoryId: '25' },
      { categoryId: '26' },
      { categoryId: '27' },
      {
        categoryId: '55',
        enums: {
          bicycle_type: [
            'enfant',
            'vtt',
            'vtc',
            'bmx',
            'course',
            'electrique',
            'pliant',
            'ville',
            'fixie',
            'appartement',
            'tandem',
            'piece',
            'equipement',
            'autre',
          ],
        },
      },
      { categoryId: '29' },
      { categoryId: '30' },
      { categoryId: '40' },
      {
        categoryId: '41',
        enums: {
          toy_type: [
            'cuisinetedinettes',
            'doudousetpeluches',
            'jeuxdeconstruction',
            'jeuxdesociete',
            'jeuxdimitationetdeguisements',
            'jeuxeducatifs',
            'jeuxradiocommandes',
            'jouetseveil',
            'loisirscreatifs',
            'porteurstrotteursetdraisiennes',
            'poupeesetaccessoires',
            'puzzle',
            'trainsbateauxetavions',
            'voituresetcircuits',
            'autre',
          ],
        },
      },
      { categoryId: '48' },
    ],
  },
]

function getStockType(job: JobInstance, item: AdsEntity): ClassifiedsStockType {
  if (job.project === ProjectType.cars) {
    return ClassifiedsStockType.used
  }
  if (job.project === ProjectType.marketplaces) {
    if (job.data?.categoryId === '6') {
      return ClassifiedsStockType.car_parts
    } else if (['15', '43', '16', '17'].includes(job.data?.categoryId)) {
      return ClassifiedsStockType.electronics
    } else if (['22', '53', '47', '42', '23', '54'].includes(job.data?.categoryId)) {
      return ClassifiedsStockType.fashion
    } else if (['19', '20', '45', '39', '46', '21', '52'].includes(job.data?.categoryId)) {
      return ClassifiedsStockType.house_appliances
    } else if (['25', '26', '27', '55', '29', '30', '40', '41', '48'].includes(job.data?.categoryId)) {
      return ClassifiedsStockType.leisure
    }
    return ClassifiedsStockType.other
  }
  let result: ClassifiedsStockType = ClassifiedsStockType.other
  if (!item.attributes || item.attributes.length === 0) {
    return result
  }
  let realEstateType
  let leaseType
  let jobTime
  for (const attribute of item.attributes) {
    if (attribute.key === 'real_estate_type') {
      realEstateType = attribute.value
    } else if (attribute.key === 'lease_type') {
      leaseType = attribute.value
    } else if (attribute.key === 'jobtime') {
      jobTime = attribute.value
    }
  }
  if (job.data?.categoryId === '11') {
    result = ClassifiedsStockType.residential_lettings
    if (leaseType === 'sell') {
      result = ClassifiedsStockType.residential_sales
    }
  } else if (job.data?.categoryId === '13') {
    result = ClassifiedsStockType.commercial_lettings
    if (leaseType === 'sell') {
      result = ClassifiedsStockType.commercial_sales
    }
  } else if (job.data?.categoryId === '9' || job.data?.categoryId === '10') {
    if (realEstateType) {
      if (realEstateType === '1' || realEstateType === '2') {
        // residential
        if (leaseType === 'sell' || job.data?.categoryId === '9') result = ClassifiedsStockType.residential_sales
        else result = ClassifiedsStockType.residential_lettings
      } else {
        // commercial
        if (leaseType === 'sell' || job.data?.categoryId === '9') result = ClassifiedsStockType.commercial_sales
        else result = ClassifiedsStockType.commercial_lettings
      }
    }
  } else if (job.data?.categoryId === '33') {
    if (jobTime === '1') {
      result = ClassifiedsStockType.full_time
    } else if (jobTime === '2') {
      result = ClassifiedsStockType.part_time
    }
  }
  return result
}

function getDealerType(project: ProjectType, item: AdsEntity): DealerType {
  if (!item.owner || !item.owner.type) {
    return DealerType.private
  }
  if (item.owner.type === DealerType.private) {
    return DealerType.private
  }
  if (project === ProjectType.jobs) {
    if (!!item.owner.activity_sector && item.owner.activity_sector === '8') {
      return DealerType.recruiter
    }
    return DealerType.company
  }
  return DealerType.dealer
}

function getListingParameters(provider: string, project: string, stockType: string, id: string) {
  let realestateType: string[] | null = null
  let leaseType: string[] | null = null
  let jobTime: string[] | null = null
  let vehicleType: string[] | null = null
  let securePayment: string[] | null = null

  if (project === ProjectType.realestates) {
    if (stockType.includes(RealEstatesPropertyScope.residential)) {
      if (id === '9' || id === '10') {
        realestateType = ['1', '2']
      }
    }
    if (stockType === ClassifiedsStockType.commercial_sales) {
      if (id === '9') {
        realestateType = ['3', '4', '5']
      }
      if (id === '13') {
        leaseType = ['sell']
      }
    }
    if (stockType === ClassifiedsStockType.commercial_lettings) {
      if (id === '10') {
        realestateType = ['3', '4', '5']
      }
      if (id === '13') {
        leaseType = [RealEstatesBusinessType.rent]
      }
    }

    return {
      leaseType,
      realestateType,
    }
  }

  if (project === ProjectType.jobs) {
    if (stockType === ClassifiedsStockType.full_time) {
      jobTime = ['1']
    }
    if (stockType === ClassifiedsStockType.part_time) {
      jobTime = ['2']
    }
    return {
      jobTime,
    }
  }
  if (project === ProjectType.cars) {
    vehicleType = ['4x4', 'berline', 'break', 'cabriolet', 'citadine', 'coupe', 'minibus', 'monospace', 'autre']
    if (provider === 'leboncoin_secure_payment') {
      securePayment = ['2']
    }

    return {
      vehicleType,
      securePayment,
    }
  }
}
