import type { ClassifiedsDataType, DealerType, Iso3166Alpha2 } from '@datagatherers/datagatherers'

export interface OikotieDealers {
  itemId: string
  dealerId: string
  dealerType: DealerType
  isActive: boolean
  isPaying: boolean
  iso: Iso3166Alpha2
  dataType: ClassifiedsDataType
}

export interface OikotieJobsListingsResponse {
  items_found: number
  items_returned: number
  items: { cardData: OikotieCard }[]
}

export interface OikotieREListingsResponse {
  found: number
  start: number
  cards: OikotieRealestatesCard[]
}

export interface OikotieUser {
  cuid: string
  token: string
  time: number
}

interface OikotieCard {
  cardViewType: number
  listingCardType: number
  sourceType: number
  id: number
  url: string
  description: string
  rooms: number
  roomConfiguration: string
  price: string
  nextViewing?: null
  newDevelopment: boolean
  published: string
  size: number
  sizeMin?: null
  sizeMax?: null
  sizeLot?: null
  cardType: number
  contractType: number
  onlineOffer?: null
  isOnlineOffer: boolean
  extraVisibility: boolean
  extraVisibilityString?: null
  buildingData: BuildingData
  coordinates: Coordinates
  brand?: Brand
  priceChanged?: null
  visits: number
  visits_weekly: number
  cardSubType?: number[] | null
  status: number
  sellStatus?: null
  title: string[]
  subtitle?: string[]
  isNew: boolean
}

interface BuildingData {
  address: string
  district: string
  city: string
  country: string
  year?: null
  buildingType: number
}
interface Coordinates {
  latitude: number
  longitude: number
}
interface Brand {
  image: string
  name?: string
  id?: number
}

export interface OikotieRealestatesCard {
  cardId: number
  cardType: number
  cardSubType: number
  status: number //only interested in '1'
  url: string
  location: {
    address: string
    district: string
    city: string
    country: string
    zipCode: string
    latitude: number
    longitude: number
  }
  meta: {
    published: string
    contractType: number
    listingType: number //1 regular, 2 plus, 4 loisto ?
    cardViewType: number //1 small, 2 medium, 4 large
    vendorAdId: string
    vendorCompanyId: string
  }
  company: {
    companyId: number
    companyName: string
    realtorName: string
  }
  data: {
    description: string
    rooms: number
    price: string
    size: string
    sizeLot: number | null
    sizeMin: number | null
    sizeMax: number | null
    newDevelopment: boolean
    isOnlineOffer: boolean
    extraVisibility: boolean
    visits: number
    visitsWeekly: number
  }
}
