import {
  ClassifiedsStockType,
  DealerType,
  RealEstatesPropertyScope,
  RealEstatesBusinessType,
  RealEstatesStockType,
  RealEstatesPropertyType,
  ResultType,
  DataType,
  ProjectType,
  GeoJSONTypes,
} from '@datagatherers/datagatherers'
import { getREArea } from '@datagatherers/worker-utils'

import { categoryIds, configuration, listingsUrls, locationUrls } from './constants'

import type {
  OikotieDealers,
  OikotieJobsListingsResponse,
  OikotieRealestatesCard,
  OikotieREListingsResponse,
  OikotieUser,
} from './types'
import type {
  Town,
  JobDefinition,
  PostalCode,
  GeoJSONPoint,
  JobInstance,
  ControllerRuntime,
  ResultItem,
  Iso3166Alpha2,
} from '@datagatherers/datagatherers'
import type { Filter, WithId } from 'mongodb'

function locationParams(job: JobInstance) {
  if (job.project === ProjectType.realestates) {
    return {
      query: job.data?.code,
    }
  }
  return {}
}

export async function scheduleLocations(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  if (job.project === ProjectType.jobs) {
    return [
      {
        ...job,
        data: {
          ...job.data,
          iso,
        },
      },
    ]
  }
  const collection = runtime.collection<PostalCode>('postalcode')
  const filter: Filter<WithId<PostalCode>> = {
    iso,
  }
  if (job.data?.itemId) {
    filter.itemId = job.data?.itemId
  }
  const results = await collection.find(filter).toArray()
  const jobs: JobDefinition[] = results
    .filter((item) => item.data.code)
    .map((item) => {
      const res = {
        ...job,
        data: {
          code: item.data.code,
          iso: item.data.iso,
        },
      }
      return res
    })
  return jobs
}

export async function extractLocations(job: JobInstance) {
  const locationUrl = locationUrls[job.project]
  if (!locationUrl) {
    return []
  }
  const params = locationParams(job)
  const locations = await job.runtime
    .got(locationUrl, {
      ...(params && { params }),
      headers: {
        'client-id': '58f098d7-6271-42ea-98ec-13a812586cb1',
        'user-agent': 'asus Nexus 7 - 1.9.11 b391',
      },
    })
    .json<{ id?: number; name: string; location_id?: number; location_type?: number }[]>()
  if (!locations?.length) {
    return []
  }

  const results = locations
    .filter((item) =>
      job.project === ProjectType.jobs ? item?.id : item?.location_id && item?.location_type && item.name,
    )
    .map((item) => {
      const location =
        job.project === ProjectType.jobs ? `${item.id}` : `${item.location_id},${item.location_type},${item.name}`
      const itemId = `${job.project}_${job.data?.iso}_${location}`
      const data = {
        project: job.project,
        itemId,
        iso: job.data?.iso,
        blacklisted: false,
        data: {
          ...item,
          location,
        },
      }
      return {
        type: ResultType.town,
        data,
      }
    })
  return results
}

export async function scheduleExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
): Promise<JobDefinition[]> {
  const listingsUrl = listingsUrls[job.project]
  if (!listingsUrl) {
    return []
  }
  const user = await getAuth(runtime)
  if (!user) return []
  const collection = runtime.collection<Town>('town')
  const filter: Filter<WithId<Town>> = {
    project: job.project,
    provider: job.provider,
    iso,
  }
  if (job.data?.itemId) {
    filter.itemId = job.data?.itemId
  }
  const results = (await collection.find(filter).toArray()).filter((item) => item.data?.location)
  const jobs = []
  for (const result of results) {
    for (const categoryId of categoryIds[job.project]) {
      jobs.push({
        ...job,
        data: {
          ...job.data,
          ...result.data,
          iso,
          categoryId,
          listingsUrl,
          user,
        },
      })
    }
  }
  return jobs
}

export async function extractJobs(job: JobInstance) {
  const dealersMap: Map<string, OikotieDealers> = new Map()
  const listings = await job.runtime.got.paginate.all<ResultItem, OikotieJobsListingsResponse>(job.data.listingsUrl, {
    responseType: 'json',
    headers: {
      'client-id': '58f098d7-6271-42ea-98ec-13a812586cb1',
      'user-agent': 'asus Nexus 7 - 1.9.11 b391',
    },
    searchParams: {
      pageSize: configuration.listingsPerPage,
      page: 1,
      'locationIDs[]': job.data?.location,
      'categoryIDs[]': job.data?.categoryId,
    },
    retry: {
      limit: 3,
    },
    pagination: {
      paginate: ({ response }) => {
        if (!response?.body?.items_returned || response.body.items_returned < configuration.listingsPerPage)
          return false

        const previousSearchParams = response.request.options.searchParams as URLSearchParams
        const page = Number(previousSearchParams?.get('page'))
        if (!page) return false

        return {
          searchParams: {
            ...previousSearchParams,
            page: page + 1,
          },
        }
      },
      transform: ({ body }) => {
        if (!body?.items?.length) return []

        return body.items
          .filter((item) => item.cardData?.id)
          .map((item) => {
            const stockType = getStockType(job, item)
            let dealerId = 'private'
            if (item.cardData?.subtitle?.length === 2) {
              dealerId = item.cardData.subtitle[1]
            }
            if (item.cardData?.brand?.name) {
              dealerId = item.cardData.brand.name as string
            }
            const dealerType = getDealerType(job.project, dealerId)
            if (dealerId && dealerType !== DealerType.private && !dealersMap.has(dealerId)) {
              try {
                dealersMap.set(dealerId, {
                  dealerId,
                  itemId: `dealer_${job.project}_${job.data.iso}_${dealerId}`,
                  iso: job.data.iso,
                  dataType: DataType.dealers,
                  dealerType: DealerType.dealer,
                  isActive: true,
                  isPaying: true,
                })
              } catch (e) {
                console.error(e)
              }
            }
            const inventoryId = item.cardData.id.toString()
            const data = {
              project: job.project,
              itemId: `listing_${job.project}_${job.data?.iso}_${inventoryId}`,
              iso: job.data?.iso,
              dataType: DataType.inventory,
              inventoryId,
              dealerId,
              stockType,
              dealerType,
              url: item.cardData.url,
            }
            return {
              type: ResultType.item,
              data,
            }
          })
      },
    },
  })

  if (!listings?.length) return []

  const dealers = [...dealersMap.values()]
    .filter((a) => a.dealerId)
    .map((item) => {
      const data = {
        project: job.project,
        ...item,
      }

      return {
        type: ResultType.item,
        data,
      }
    })
  return [...listings, ...dealers]
}

export async function extractRE(job: JobInstance) {
  const dealersMap: Map<string, OikotieDealers> = new Map()
  const listings = await job.runtime.got.paginate.all<ResultItem, OikotieREListingsResponse>(
    `https://asunnot.oikotie.fi/api/search`,
    {
      responseType: 'json',
      headers: {
        // 'user-agent':
        //   'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36',
        'ota-loaded': job.data?.user?.time,
        'ota-cuid': job.data?.user?.cuid,
        'ota-token': job.data?.user?.token,
      },
      searchParams: {
        offset: 0,
        limit: configuration.listingsPerPage,
        locations: `[[${job.data?.location_id},${job.data?.location_type},"${job.data?.name}"]]`,
        sortBy: 'published_sort_desc',
      },
      retry: {
        limit: 3,
        errorCodes: ['ERR_BODY_PARSE_FAILURE', 'ERR_GOT_REQUEST_ERROR'],
      },
      pagination: {
        paginate: ({ response }) => {
          if (!response?.body?.cards?.length || !response?.body?.found) {
            return false
          }

          const previousSearchParams = response.request.options.searchParams as URLSearchParams
          const offset = Number(previousSearchParams?.get('offset'))
          if (isNaN(offset)) return false

          if (response.body.found < offset + configuration.listingsPerPage) return false

          return {
            searchParams: {
              ...previousSearchParams,
              offset: offset + configuration.listingsPerPage,
            },
          }
        },
        transform: ({ body }) => {
          if (!body?.cards?.length) return []

          return body.cards
            .filter((item) => item?.cardId)
            .map((item) => {
              const inventoryId = item.cardId.toString()
              const dealerId = item.company?.companyId?.toString()
              const dealerType = getRealEstateDealerType(item)

              const stockType = getRealEstateStockType(job, item)
              const businessType = getRealEstateBusinessType(job, item)
              const propertyScope = getRealEstatePropertyScope(job, item)
              const propertyType = getRealEstatePropertyType(job, item)
              const price = getPrice(item)
              const area = getArea(item)
              const productType = getProductType(item)
              let geoJson: GeoJSONPoint | undefined
              if (item.location?.latitude && item.location?.longitude) {
                geoJson = {
                  type: GeoJSONTypes.Point,
                  coordinates: [item.location.longitude, item.location.latitude],
                }
              }
              if (dealerId && dealerType !== DealerType.private && !dealersMap.has(dealerId)) {
                try {
                  dealersMap.set(dealerId, {
                    dealerId,
                    itemId: `dealer_${job.project}_${job.data?.iso}_${dealerId}`,
                    iso: job.data?.iso,
                    ...(item.company?.companyName && { name: item.company.companyName }),
                    dataType: DataType.dealers,
                    dealerType: DealerType.dealer,
                    isActive: true,
                    isPaying: true,
                  })
                } catch (e) {
                  console.error(e)
                }
              }

              const data = {
                project: job.project,
                itemId: `listing_${job.project}_${job.data?.iso}_${inventoryId}`,
                iso: job.data?.iso,
                dataType: DataType.inventory,
                inventoryId,
                dealerId,
                stockType,
                dealerType,
                ...(price && { price }),
                ...(area && { area }),
                ...(businessType && { businessType }),
                ...(propertyScope && { propertyScope }),
                ...(propertyType && { propertyType }),
                ...(geoJson && { geoJson }),
                ...(productType && { productType }),
                url: item.url,
                data: {
                  visits: item.data.visits,
                  visitsWeekly: item.data.visitsWeekly,
                  //sourceType: item.data.sourceType,
                  contractType: item.meta.contractType,
                  listingType: item.meta.listingType,
                  cardViewType: item.meta.cardViewType,
                  vendorAdId: item.meta.vendorAdId,
                  vendorCompanyId: item.meta.vendorCompanyId,
                  isNewDevelopment: item.data.newDevelopment,
                  isOnlineOffer: item.data.isOnlineOffer,
                  extraVisibility: item.data.extraVisibility,
                },
              }
              return {
                type: ResultType.item,
                data,
              }
            })
        },
      },
    },
  )

  if (!listings.length) {
    return []
  }

  const dealers = [...dealersMap.values()]
    .filter((a) => a.dealerId)
    .map((item) => {
      const data = {
        project: job.project,
        ...item,
      }
      return {
        type: ResultType.item,
        data,
      }
    })
  return [...listings, ...dealers]
}

function getStockType(job: JobInstance, item: { cardData: { cardType: number | undefined } }): ClassifiedsStockType {
  let stockType: ClassifiedsStockType = ClassifiedsStockType.other
  if (job.project === ProjectType.jobs) {
    if (job.data?.categoryId === 1) {
      stockType = ClassifiedsStockType.full_time
    } else if (job.data?.categoryId === 2) {
      stockType = ClassifiedsStockType.part_time
    }
  } else if (job.project === ProjectType.realestates) {
    const cardType: number | undefined = item.cardData.cardType
    if (cardType) {
      switch (cardType) {
        case 101:
          {
            stockType = ClassifiedsStockType.residential_lettings
          }
          break
        case 100:
        case 200:
          {
            stockType = ClassifiedsStockType.residential_sales
          }
          break
        case 103:
        case 109:
          {
            stockType = ClassifiedsStockType.commercial_lettings
          }
          break
        case 102:
        case 104:
        case 107:
        case 108:
          {
            stockType = ClassifiedsStockType.commercial_lettings
          }
          break
        default: {
          stockType = ClassifiedsStockType.other
        }
      }
    }
  }
  return stockType
}

function getRealEstateStockType(_job: JobInstance, item: OikotieRealestatesCard): RealEstatesStockType {
  let stockType: RealEstatesStockType = RealEstatesStockType.other
  if (item?.cardType) {
    switch (item?.cardType) {
      case 101:
        {
          stockType = RealEstatesStockType.residential_lettings
        }
        break
      case 100:
      case 200:
        {
          stockType = RealEstatesStockType.residential_sales
        }
        break
      case 103:
      case 109:
      case 106:
        {
          stockType = RealEstatesStockType.commercial_lettings
        }
        break
      case 102:
      case 104:
      case 107:
      case 108:
      case 105:
        {
          stockType = RealEstatesStockType.commercial_sales
        }
        break
      default: {
        stockType = RealEstatesStockType.other
      }
    }
  }
  return stockType
}

function getDealerType(project: ProjectType, dealerId: string): DealerType {
  let dealerType: DealerType = DealerType.private
  if (dealerId !== DealerType.private) {
    if (project === ProjectType.realestates) {
      dealerType = DealerType.dealer
    } else {
      dealerType = DealerType.recruiter
    }
  }
  return dealerType
}

function getRealEstateDealerType(item: OikotieRealestatesCard): DealerType {
  return !item.company?.companyId ? DealerType.private : DealerType.dealer
}

function getRealEstateBusinessType(
  job: JobInstance,
  item: OikotieRealestatesCard,
): RealEstatesBusinessType | undefined {
  if (job.project !== ProjectType.realestates || !item.cardType) return undefined
  switch (item.cardType) {
    case 101:
    case 103:
    case 109:
    case 106:
      return RealEstatesBusinessType.rent
    case 100:
    case 200:
    case 102:
    case 104:
    case 107:
    case 108:
    case 105:
      return RealEstatesBusinessType.sale
    default:
      return undefined
  }
}

function getRealEstatePropertyScope(
  job: JobInstance,
  item: OikotieRealestatesCard,
): RealEstatesPropertyScope | undefined {
  if (job.project !== ProjectType.realestates || !item.cardType) return undefined
  switch (item.cardType) {
    case 200:
    case 100:
    case 101:
      return RealEstatesPropertyScope.residential
    case 102:
    case 103:
    case 108:
    case 109:
    case 105:
    case 106:
      return RealEstatesPropertyScope.commercial
    case 104:
    case 107:
      return RealEstatesPropertyScope.land
    default:
      return RealEstatesPropertyScope.other
  }
}

function getRealEstatePropertyType(
  job: JobInstance,
  item: OikotieRealestatesCard,
): RealEstatesPropertyType | undefined {
  if (job.project !== ProjectType.realestates || !item.cardType) return undefined
  switch (item.cardType) {
    case 200:
    case 100:
    case 101: {
      if (item?.cardSubType) {
        switch (item.cardSubType) {
          case 256:
            return RealEstatesPropertyType.apartment
          case 2:
          case 4:
          case 8:
          case 32:
          case 128:
          case 512:
            return RealEstatesPropertyType.house
          default:
            break
        }
      }
      return RealEstatesPropertyType.other
    }
    case 102:
    case 103:
      return RealEstatesPropertyType.other
    case 108:
    case 109:
      return RealEstatesPropertyType.parking
    case 105:
    case 106:
      return RealEstatesPropertyType.retail
    case 104:
      return RealEstatesPropertyType.urban
    case 107:
      return RealEstatesPropertyType.agriculture
    default:
      return RealEstatesPropertyType.other
  }
}

function getPrice(item: OikotieRealestatesCard): number | undefined {
  return item?.data?.price ? Number(item.data.price.replace(/[^0-9.]/g, '').trim()) : 0
}

function getArea(item: OikotieRealestatesCard): number | undefined {
  return item.data.size ? getREArea(Number(item.data.size.replace(/[^0-9.]/g, '').trim()) || 0) : 0
}

function getProductType(item: OikotieRealestatesCard): string | undefined {
  if (item.meta.listingType === 4) {
    return 'loisto'
  }
  if (item.meta.listingType === 2) {
    if (item.meta.cardViewType === 4) {
      return 'large+plus'
    }
    return 'plus'
  }
  if (item.meta.cardViewType === 4 || item.meta.cardViewType === 3) {
    return 'large'
  }
  if (item.meta.cardViewType === 2) {
    return 'medium'
  }
  if (item.meta.cardViewType === 1) {
    return 'small'
  }
  return undefined
}

async function getAuth(runtime: ControllerRuntime): Promise<OikotieUser | undefined> {
  return await runtime
    .got('https://asunnot.oikotie.fi/user/get', {
      headers: {
        accept: 'application/json',
      },
      searchParams: {
        format: 'json',
      },
      retry: {
        limit: 3,
      },
    })
    .json<{ user: OikotieUser }>()
    .then((res) => res.user)
}
