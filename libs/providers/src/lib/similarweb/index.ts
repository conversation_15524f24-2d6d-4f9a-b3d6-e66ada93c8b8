import { addMonths, endOfMonth, format, isAfter, isBefore, parse, startOfMonth, subMonths, subYears } from 'date-fns'
import { got, RetryError } from 'got'
import isoCountries from 'i18n-iso-countries'

import {
  Iso3166Alpha2,
  IntegrationSnapshotDataType,
  TimeUnit,
  ProjectType,
  SimilarWebGranularity,
  IntegrationType,
} from '@datagatherers/datagatherers'
import { getRunningControllers, round } from '@datagatherers/worker-utils'

import { isoIgnoreProviders, similarWebAPIkey } from './constants'

import type {
  SimilarwebCapabilitiesResponse,
  SimilarWebCountryShareResponse,
  SimilarWebPageviewsResponse,
  SimilarWebVisitsResponse,
  SimilarWebDeduplicatedAudienceResponse,
  SimilarWebScheduleShareKey,
  SimilarWebScheduleShareValue,
} from './types'
import type {
  JobDefinition,
  ControllerRuntime,
  JobInstance,
  Project,
  SimilarWeb,
  IntegrationSnapshotSimilarweb,
  Job,
} from '@datagatherers/datagatherers'

export async function scheduleIsDataUpdated(job: JobDefinition, runtime: ControllerRuntime): Promise<JobDefinition[]> {
  const isRunning = await checkRunningStatus(runtime)
  if (isRunning) return []

  const capabilities = await getCapabilities()
  if (capabilities) {
    const similarWebEndDate = startOfMonth(new Date(capabilities.end_date))
    const maximumEndDate = startOfMonth(subMonths(new Date(), 1))
    if (!isBefore(similarWebEndDate, maximumEndDate)) {
      return [{ ...job, data: { continueChain: true } }]
    }
  }
  return []
}

export async function schedulerExtract(
  job: JobDefinition,
  runtime: ControllerRuntime,
  dataType: IntegrationSnapshotDataType,
  snapTimeUnit: TimeUnit,
  isFullCrawl: boolean,
  isBlacklisted = false,
): Promise<JobDefinition[]> {
  const isRunning = await checkRunningStatus(runtime)
  if (isRunning) return []

  const capabilities = await getCapabilities()
  if (!capabilities) return []

  const collection = runtime.collection<SimilarWeb>('similarWeb')
  const aggr = []
  const $match = {}

  if (job.data?.project) {
    $match['project'] = job.data?.project
  }
  if (job.data?.provider) {
    $match['provider'] = job.data?.provider
  }
  if (job.data?.iso) {
    $match['iso'] = job.data?.iso
  }
  if (Object.keys($match)?.length) aggr.push({ $match })

  aggr.push({
    $unwind: '$domains',
  })

  if (!isFullCrawl) {
    aggr.push({
      $match: {
        ...(isBlacklisted && { 'domains.blacklistedDataTypes': dataType }),
        ...(!isBlacklisted && { 'domains.blacklistedDataTypes': { $ne: dataType } }),
      },
    })
  }

  aggr.push(
    ...[
      {
        $group: {
          _id: {
            project: '$project',
            provider: '$provider',
            iso: '$iso',
            webpage: '$webpage',
            domain: '$domains',
            range: '$range',
          },
        },
      },
      {
        $project: {
          _id: 0,
          project: '$_id.project',
          provider: '$_id.provider',
          isolist: '$_id.iso',
          webpage: '$_id.webpage',
          domain: '$_id.domain',
          range: '$_id.range',
        },
      },
    ],
  )

  const similarWebDomains = await collection.aggregate<SimilarWebExtended>(aggr).toArray()
  if (!similarWebDomains?.length) return []

  const startDateStr = isFullCrawl ? capabilities.start_date : undefined
  const jobs: JobDefinition[] = []
  const defaultEndDate = endOfMonth(getEndDate(capabilities.end_date))
  const defaultStartDate = endOfMonth(new Date(capabilities.start_date))

  for (const similarWebDomain of similarWebDomains) {
    const end =
      similarWebDomain?.range?.end && isBefore(new Date(similarWebDomain?.range?.end), defaultEndDate)
        ? endOfMonth(new Date(similarWebDomain?.range?.end))
        : defaultEndDate
    const start = await getStartDate(runtime, similarWebDomain, dataType, end, isFullCrawl, startDateStr)
    if (!start?.length) continue

    const addJobs: JobDefinition[] = start.flatMap((startObj) => {
      if (isBefore(end, startObj.startDate)) return []

      return {
        ...job,
        data: {
          ...similarWebDomain,
          startDate: format(
            isBefore(startObj.startDate, defaultStartDate) ? defaultStartDate : startObj.startDate,
            'yyyy-MM',
          ),
          endDate: format(end, 'yyyy-MM'),
          isolist: startObj.isolist,
          dataType,
          snapTimeUnit,
          blacklisted: isFullCrawl || isBlacklisted,
        },
      }
    })
    if (addJobs?.length) jobs.push(...addJobs)
  }
  return jobs
}

export async function schedulerExtractShare(
  job: JobDefinition,
  runtime: ControllerRuntime,
  dataType?: IntegrationSnapshotDataType,
): Promise<JobDefinition[]> {
  const isRunning = await checkRunningStatus(runtime)
  if (isRunning) return []

  const minSnapTime = format(subYears(subMonths(new Date(), 1), 3), 'yyyy/MM')
  const collection = runtime.collection<IntegrationSnapshotSimilarweb>('integrationSnapshot')
  const aggr = [
    {
      $match: {
        integration: IntegrationType.similarWeb,
        ...(job.data?.project && { project: job.data?.project }),
        ...(job.data?.provider && { provider: job.data?.provider }),
        ...(job.data?.iso && { iso: job.data.iso }),
        dataType: !dataType
          ? {
              $in: [
                IntegrationSnapshotDataType.similarWebPageviews,
                IntegrationSnapshotDataType.similarWebUsers,
                IntegrationSnapshotDataType.similarWebVisits,
              ],
            }
          : dataType,
        snapTime: { $gte: minSnapTime },
      },
    },
    {
      $unwind: '$stats',
    },
    {
      $match: {
        iso: { $exists: true, $nin: [Iso3166Alpha2.WW, Iso3166Alpha2.EU, ''] },
        'stats.hasCountryShare': { $exists: false },
      },
    },
    {
      $group: {
        _id: {
          project: '$project',
          provider: '$provider',
          snapTime: '$snapTime',
          snapTimeUnit: '$snapTimeUnit',
          domain: '$stats.domain',
          webpage: '$stats.webpage',
        },
        isolist: { $addToSet: '$iso' },
        datatypes: { $addToSet: '$dataType' },
      },
    },
    {
      $group: {
        _id: {
          snapTime: '$_id.snapTime',
          snapTimeUnit: '$_id.snapTimeUnit',
          domain: '$_id.domain',
        },
        data: {
          $addToSet: {
            project: '$_id.project',
            provider: '$_id.provider',
            isolist: '$isolist',
            datatypes: '$datatypes',
            webpage: '$_id.webpage',
          },
        },
      },
    },
  ]

  const arr = (await collection.aggregate(aggr).toArray())?.reduce(
    (acc: Map<SimilarWebScheduleShareKey, SimilarWebScheduleShareValue>, curr) => {
      const snapTimeUnit = curr._id.snapTimeUnit as TimeUnit
      const date = parse(curr._id.snapTime, snapTimeUnit === TimeUnit.months ? 'yyyy/MM' : 'RRRR/II', new Date())

      const monthlySnapTime = format(date, 'yyyy/MM')

      const key = {
        monthlySnapTime,
        snapTimeUnit,
      }

      const mapEntry = acc.get(key) ?? {
        domain: curr._id.domain,
        data: curr.data.flatMap(
          (item: {
            project: ProjectType
            provider: string
            isolist: Iso3166Alpha2[]
            datatypes: IntegrationSnapshotDataType[]
            webpage: string
          }) => ({ ...item, snaptimes: [] }),
        ),
      }

      mapEntry.data.map((item: { snaptimes: string[] }) => {
        if (!item.snaptimes?.includes(curr._id.snapTime)) item.snaptimes.push(curr._id.snapTime)
      })
      acc.set(key, mapEntry)

      return acc
    },
    new Map<SimilarWebScheduleShareKey, SimilarWebScheduleShareValue>(),
  )

  const keysArray = Array.from(arr.keys()) as SimilarWebScheduleShareKey[]

  if (!keysArray?.length) return []

  const jobs = keysArray.flatMap((key) => {
    if (!key?.monthlySnapTime || !key?.snapTimeUnit || !arr.get(key)) return []
    return {
      ...job,
      data: {
        ...key,
        ...arr.get(key),
      },
    }
  })

  return jobs
}

export async function extractUsers(job: JobInstance) {
  const snapTimeUnit: TimeUnit = job.data?.snapTimeUnit as TimeUnit
  const granularity = snapTimeUnit === TimeUnit.months ? SimilarWebGranularity.monthly : SimilarWebGranularity.daily
  const url = getUrl(job.data?.domain.url, job.data?.dataType)
  if (!url) return []

  let addToBlacklisted = false

  const results = await got(url, {
    method: 'GET',
    searchParams: {
      api_key: similarWebAPIkey,
      start_date: job.data?.startDate,
      end_date: job.data?.endDate,
      granularity,
      main_domain_only: 'false',
      format: 'json',
    },
    retry: {
      limit: 3,
      statusCodes: [429],
    },
  })
    .json<SimilarWebDeduplicatedAudienceResponse>()
    .then((res) => {
      if (res?.meta?.error_code) {
        if (res.meta.error_message) throw new SimilarWebError(res.meta.error_code, res.meta.error_message)
        else throw new Error(res.meta.error_message)
      }

      if (!res?.data?.length) return []

      return res.data.flatMap((item) => {
        if (!item?.dedup_data?.total_deduplicated_audience) return []

        return job.data?.isolist.flatMap((iso) => {
          const snapTime = format(new Date(item.date), 'yyyy/MM')
          const itemId = getItemId(job.data?.dataType, job.data?.project, job.data?.provider, snapTime, iso)
          return {
            project: job.data?.project as Project,
            provider: job.data?.provider,
            iso,
            itemId,
            snapTime,
            snapTimeUnit,
            dataType: job.data?.dataType as IntegrationSnapshotDataType,
            stats: [
              {
                count: round(
                  item.dedup_data.total_deduplicated_audience *
                    (1 + item.dedup_data.desktop_and_mobile_web_audience_share),
                  0,
                ),
                deduped: round(item.dedup_data.total_deduplicated_audience, 0),
                ...(!!job.data?.webpage && { webpage: job.data?.webpage }),
                ...(!!job.data?.domain.url && { domain: job.data?.domain.url }),
              },
            ],
          }
        })
      })
    })
    .catch((err) => {
      const processed = processError(err)
      if (processed?.addToBlacklisted) {
        addToBlacklisted = true
      }
      return []
    })
  if (!job.runtime.skipStoreData) {
    await store(job, addToBlacklisted, results)
  }
  return []
}

export async function extractVisits(job: JobInstance) {
  const snapTimeUnit: TimeUnit = job.data?.snapTimeUnit as TimeUnit
  const granularity = snapTimeUnit === TimeUnit.months ? SimilarWebGranularity.monthly : SimilarWebGranularity.daily
  const url = getUrl(job.data?.domain.url, job.data?.dataType)
  if (!url) return []

  let addToBlacklisted = false
  const results = await got(url, {
    method: 'GET',
    searchParams: {
      api_key: similarWebAPIkey,
      start_date: job.data?.startDate,
      end_date: job.data?.endDate,
      granularity,
      main_domain_only: 'false',
      format: 'json',
    },
    retry: {
      limit: 3,
      statusCodes: [429],
    },
  })
    .json<SimilarWebVisitsResponse>()
    .then((res) => {
      if (res?.meta?.error_code) {
        if (res.meta.error_message) throw new SimilarWebError(res.meta.error_code, res.meta.error_message)
        else throw new Error(res.meta.error_message)
      }

      if (!res?.visits?.length) return []

      return res.visits.flatMap((item) => {
        if (!item?.visits) return []

        return job.data?.isolist.flatMap((iso) => {
          const snapTime = format(new Date(item.date), 'yyyy/MM')
          const itemId = getItemId(job.data?.dataType, job.data?.project, job.data?.provider, snapTime, iso)
          return {
            project: job.data?.project as Project,
            provider: job.data?.provider,
            iso,
            itemId,
            snapTime,
            snapTimeUnit,
            dataType: job.data?.dataType as IntegrationSnapshotDataType,
            stats: [
              {
                count: round(item.visits, 0),
                ...(!!job.data?.webpage && { webpage: job.data?.webpage }),
                ...(!!job.data?.domain.url && { domain: job.data?.domain.url }),
              },
            ],
          }
        })
      })
    })
    .catch((err) => {
      const processed = processError(err)
      if (processed?.addToBlacklisted) {
        addToBlacklisted = true
      }
      return []
    })

  if (!job.runtime.skipStoreData) {
    await store(job, addToBlacklisted, results)
  }
  return []
}

export async function extractPageviews(job: JobInstance) {
  const snapTimeUnit: TimeUnit = job.data?.snapTimeUnit as TimeUnit
  const granularity = snapTimeUnit === TimeUnit.months ? SimilarWebGranularity.monthly : SimilarWebGranularity.daily
  const url = getUrl(job.data?.domain.url, job.data?.dataType)
  if (!url) return []

  let addToBlacklisted = false
  const results = await got(url, {
    method: 'GET',
    searchParams: {
      api_key: similarWebAPIkey,
      start_date: job.data?.startDate,
      end_date: job.data?.endDate,
      granularity,
      main_domain_only: 'false',
      format: 'json',
    },
    retry: {
      limit: 3,
      statusCodes: [429],
    },
  })
    .json<SimilarWebPageviewsResponse>()
    .then((res) => {
      if (res?.meta?.error_code) {
        if (res.meta.error_message) throw new SimilarWebError(res.meta.error_code, res.meta.error_message)
        else throw new Error(res.meta.error_message)
      }
      if (!res?.pages_views?.length) return []

      return res.pages_views.flatMap((item) => {
        if (!item?.pages_views) return []

        return job.data?.isolist.flatMap((iso) => {
          const snapTime = format(new Date(item.date), 'yyyy/MM')
          const itemId = getItemId(job.data?.dataType, job.data?.project, job.data?.provider, snapTime, iso)
          return {
            project: job.data?.project as Project,
            provider: job.data?.provider as string,
            iso,
            itemId,
            snapTime,
            snapTimeUnit,
            dataType: job.data?.dataType as IntegrationSnapshotDataType,
            stats: [
              {
                count: round(item.pages_views, 0),
                ...(!!job.data?.webpage && { webpage: job.data?.webpage }),
                ...(!!job.data?.domain.url && { domain: job.data?.domain.url }),
              },
            ],
          }
        })
      })
    })
    .catch((err) => {
      const processed = processError(err)
      if (processed?.addToBlacklisted) {
        addToBlacklisted = true
      }
      return []
    })

  if (!job.runtime.skipStoreData) {
    await store(job, addToBlacklisted, results)
  }

  return []
}

function getShareRequestsLimit(numberOfCountries?: number) {
  return Math.max(100, numberOfCountries)
}

export async function extractShare(job: JobInstance) {
  const monthlySnapTime: string = job.data?.monthlySnapTime
  const domain: string = job.data?.domain
  if (!monthlySnapTime || !domain) return []
  const limit = getShareRequestsLimit(
    [
      ...new Set(
        job.data?.data?.flatMap((item) => {
          if (!item?.isolist?.length) return []
          return item.isolist
        }) ?? [],
      ),
    ].length,
  )

  const snapTimeUnit: TimeUnit = job.data?.snapTimeUnit as TimeUnit
  const granularity =
    snapTimeUnit === TimeUnit.months
      ? SimilarWebGranularity.monthly
      : snapTimeUnit === TimeUnit.weeks
        ? SimilarWebGranularity.weekly
        : SimilarWebGranularity.daily

  const countryDistribution = await getCountryDistribution(monthlySnapTime, domain, granularity, limit)

  if (!countryDistribution?.length) {
    return []
  }

  const res = []
  const dataObjArray: {
    isolist: Iso3166Alpha2[]
    datatypes: IntegrationSnapshotDataType[]
    snaptimes: string[]
    project: ProjectType
    provider: string
    webpage?: string
  }[] = job.data?.data

  for (const dataObj of dataObjArray) {
    const isoList = !isoIgnoreProviders.includes(dataObj.provider)
      ? dataObj.isolist
      : [
          ...new Set([
            ...dataObj.isolist,
            ...countryDistribution.flatMap((item) => {
              if (!item?.country) return []

              const iso = isoCountries.numericToAlpha2(item.country)
              if (!iso || (iso === Iso3166Alpha2.SE && ['bill.com', 'ramp', 'brex'].includes(dataObj.provider)))
                return []

              return iso
            }),
          ]),
        ]

    if (!isoList?.length) continue
    const countryRes = []

    for (const iso of isoList) {
      if (!iso || iso === Iso3166Alpha2.WW || iso === Iso3166Alpha2.EU) continue

      const countryCode = Number(isoCountries.alpha2ToNumeric(iso))
      if (!countryCode || isNaN(countryCode)) continue

      const countryShare = countryDistribution?.find((item) => item.country === countryCode)?.share ?? 0

      for (const dataType of dataObj.datatypes) {
        if (!dataType) continue

        for (const snapTime of dataObj.snaptimes) {
          const itemId = getItemId(dataType, dataObj.project, dataObj.provider, snapTime, iso)
          if (!itemId) continue

          let similarWebSnap = await job.runtime
            .collection<IntegrationSnapshotSimilarweb>('integrationSnapshot')
            .findOne({
              integration: IntegrationType.similarWeb,
              provider: dataObj.provider,
              iso,
              itemId,
            })

          if (
            !similarWebSnap &&
            isoIgnoreProviders.includes(dataObj.provider) &&
            (iso !== Iso3166Alpha2.SE || !['bill.com', 'ramp', 'brex'].includes(dataObj.provider))
          ) {
            similarWebSnap = await job.runtime
              .collection<IntegrationSnapshotSimilarweb>('integrationSnapshot')
              .findOne({
                integration: IntegrationType.similarWeb,
                provider: dataObj.provider,
                iso: Iso3166Alpha2.WW,
                itemId: getItemId(dataType, dataObj.project, dataObj.provider, snapTime, Iso3166Alpha2.WW),
              })
          }

          if (!similarWebSnap) {
            continue
          }

          const oldStats = []
          if (similarWebSnap.stats) oldStats.push(...similarWebSnap.stats)

          if (!oldStats?.length) continue

          const newStats = oldStats.flatMap((item) => {
            if (item.domain !== domain || item.webpage !== dataObj.webpage) return item
            return {
              ...item,
              count: round(item.count * countryShare, 0),
              ...(item.deduped && { deduped: round(item.deduped * countryShare, 0) }),
              countryShare,
              hasCountryShare: true,
            }
          })

          countryRes.push({
            type: 'integrationSnapshot',
            data: {
              integration: IntegrationType.similarWeb,
              project: similarWebSnap.project,
              provider: similarWebSnap.provider,
              iso,
              itemId,
              snapTime: similarWebSnap.snapTime,
              snapTimeUnit: similarWebSnap.snapTimeUnit,
              dataType: similarWebSnap.dataType,
              stats: newStats,
              updatedAt: new Date(),
            },
          })
        }
      }
    }
    res.push(...countryRes)
  }

  return res
}

async function getCountryDistribution(
  snapTime: string,
  domain: string,
  granularity: SimilarWebGranularity,
  limit?: number,
) {
  if (!domain || !snapTime || !granularity) return []
  let date = parse(snapTime, granularity === 'monthly' ? 'yyyy/MM' : 'RRRR/II', new Date())
  const baseDate = startOfMonth(subYears(subMonths(new Date(), 1), 3))
  if (!date || isBefore(date, baseDate)) date = baseDate
  const formattedDate = format(date, 'yyyy-MM')

  const searchParams = {
    api_key: similarWebAPIkey,
    start_date: formattedDate,
    end_date: formattedDate,
    granularity: 'monthly',
    main_domain_only: 'false',
    format: 'json',
    limit: limit ?? 50,
    sort: 'share',
    asc: 'false',
  }
  const url = `https://api.similarweb.com/v4/website/${domain.trim()}/geo/total-traffic-by-country`.trim()

  return got(url, {
    searchParams,
    timeout: {
      request: 10_000,
    },
    retry: {
      limit: 3,
      statusCodes: [429],
    },
  })
    .json<SimilarWebCountryShareResponse>()
    .then((res) => {
      if (res?.meta?.error_code && res.meta.error_message) {
        throw new SimilarWebError(res.meta.error_code, res.meta.error_message)
      }
      return res?.records
    })
}

function getItemId(
  dataType: IntegrationSnapshotDataType,
  project: ProjectType,
  provider: string,
  snapTime: string,
  iso: string,
): string {
  if (!dataType) {
    return ''
  }
  return `${dataType}_${project}_${provider}_${iso}_${snapTime}`
}

function getEndDate(end_date: string): Date {
  // Format YYYY-MM-DD
  let endDate: Date

  if (!end_date) {
    endDate = new Date()
    if (endDate.getDay() < 6) {
      endDate = subMonths(endDate, 1)
    }
    endDate = endOfMonth(subMonths(endDate, 1))
  } else {
    endDate = endOfMonth(new Date(end_date))
  }
  return endDate
}

interface SimilarWebExtended extends SimilarWeb {
  domain: { url: string; blacklistedDataTypes?: IntegrationSnapshotDataType[] }
  isolist: Iso3166Alpha2[]
}

async function getStartDate(
  runtime: ControllerRuntime,
  similarWebDomain: SimilarWebExtended,
  dataType: IntegrationSnapshotDataType,
  endDate: Date,
  isFullCrawl: boolean,
  startDateStr?: string,
): Promise<{ startDate: Date; isolist: Iso3166Alpha2[] }[]> {
  const rootDate = subYears(new Date(), 3)
  let startDate = subYears(endDate, 3)
  if (isBefore(startDate, rootDate)) startDate = rootDate
  if (similarWebDomain.range?.start && isAfter(new Date(similarWebDomain.range?.start), startDate))
    startDate = new Date(similarWebDomain.range?.start)
  if (isFullCrawl) {
    if (startDateStr) {
      startDate = endOfMonth(new Date(startDateStr))
      if (similarWebDomain.range?.start && isAfter(new Date(similarWebDomain.range?.start), startDate))
        startDate = new Date(similarWebDomain.range?.start)
    }
    return [{ startDate, isolist: similarWebDomain.isolist }]
  } else {
    const last = await runtime
      .collection('integrationSnapshot')
      .aggregate([
        {
          $match: {
            integration: IntegrationType.similarWeb,
            project: similarWebDomain.project,
            provider: similarWebDomain.provider,
            dataType,
            ...(!!similarWebDomain.webpage && { 'stats.webpage': similarWebDomain.webpage }),
            ...(!!similarWebDomain.domain && { 'stats.domain': similarWebDomain.domain.url }),
          },
        },
        {
          $sort: {
            snapTime: -1,
          },
        },
        {
          $group: {
            _id: '$iso',
            snapTime: { $first: '$snapTime' },
          },
        },
        {
          $group: {
            _id: '$snapTime',
            isolist: {
              $push: '$_id',
            },
          },
        },
      ])
      .toArray()

    if (!last?.length) return [{ startDate, isolist: similarWebDomain.isolist }]

    const result = []

    const existingDataIsos = last.flatMap((item) => {
      if (!item?.isolist?.length) return []
      return item.isolist
    })

    const noDataIsos = similarWebDomain.isolist.filter((iso) => !existingDataIsos.includes(iso))

    if (noDataIsos?.length) {
      const tempStartDate =
        similarWebDomain.range?.start && isAfter(new Date(similarWebDomain.range?.start), startDate)
          ? new Date(similarWebDomain.range?.start)
          : endOfMonth(startDate)
      result.push({ startDate: tempStartDate, isolist: noDataIsos })
    }

    result.push(
      ...last.flatMap((lastObj) => {
        if (!lastObj?._id || !lastObj?.isolist?.length) return []

        const tempStartDate = parse(lastObj._id, 'yyyy/MM', new Date())
        const tempStartDate2 =
          similarWebDomain.range?.start && isAfter(new Date(similarWebDomain.range?.start), tempStartDate)
            ? new Date(similarWebDomain.range?.start)
            : addMonths(endOfMonth(tempStartDate), 1)

        return {
          startDate: tempStartDate2,
          isolist: lastObj.isolist,
        }
      }),
    )

    return result
  }
}

async function getCapabilities() {
  return got({
    url: 'https://api.similarweb.com/capabilities',
    searchParams: {
      api_key: similarWebAPIkey,
    },
  })
    .json<SimilarwebCapabilitiesResponse>()
    .then((res) => res?.web_desktop_data?.snapshot_interval)
    .catch((error) => {
      console.error(error)
      return undefined
    })
}

function getUrl(domain: string, dataType: IntegrationSnapshotDataType) {
  switch (dataType) {
    // case 'similarWebTraffic': {
    //   if (source === RealEstatesDataType.desktop) {
    //     return `https://api.similarweb.com/v1/website/${domain}/traffic-sources/overview-share`
    //   } else if (source === RealEstatesDataType.mobile) {
    //     return ` https://api.similarweb.com/v5/website/${domain}/mobile-traffic-sources/mobile-overview-share`
    //   }
    //   return undefined
    // }
    case IntegrationSnapshotDataType.similarWebUsers: {
      return `https://api.similarweb.com/v1/website/${domain}/dedup/deduplicated-audiences`
    }
    case IntegrationSnapshotDataType.similarWebVisits: {
      return `https://api.similarweb.com/v1/website/${domain}/total-traffic-and-engagement/visits`
    }
    case IntegrationSnapshotDataType.similarWebPageviews: {
      return `https://api.similarweb.com/v1/website/${domain}/total-traffic-and-engagement/page-views`
    }
    default: {
      return undefined
    }
  }
}

export async function checkRunningStatus(runtime: ControllerRuntime) {
  const runningControllers = await getRunningControllers(runtime.k8sApi)
  const controllers = runningControllers.filter(
    (controller) =>
      controller.metadata.labels?.project === ProjectType.similarweb &&
      controller.metadata.labels?.type !== 'checkAvailability',
  )

  if (!controllers?.length) return false

  const lastJob = await runtime
    .collection<Job>('job')
    .find({ controllerId: { $in: controllers.map((item) => item.metadata.labels.id) }, finishedAt: { $exists: false } })
    .sort({ _id: -1 })
    .limit(1)
    .toArray()
  return !!lastJob?.length
}

class SimilarWebError extends Error {
  code: number
  constructor(code: number, message: string) {
    super(message)
    this.code = code
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function processError(err: any): { addToBlacklisted?: boolean } | undefined {
  try {
    if (JSON.parse(err.response?.body)?.meta?.error_code === 401) {
      return { addToBlacklisted: true }
    }
    if (JSON.parse(err.response?.body)?.meta?.error_code === 429) {
      throw new RetryError(err.request)
    }
  } catch {
    //EMPTY
  }
  throw err
}

// STORAGE

async function storeResults(results: IntegrationSnapshotSimilarweb[], job: JobInstance) {
  const collection = job.runtime.collection<IntegrationSnapshotSimilarweb>('integrationSnapshot')
  for (const result of results) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const res1 = await collection.updateOne(
      {
        integration: IntegrationType.similarWeb,
        project: result.project,
        provider: result.provider,
        iso: result.iso,
        itemId: result.itemId,
        snapTime: result.snapTime,
        snapTimeUnit: result.snapTimeUnit,
        dataType: result.dataType,
      },
      {
        $pull: {
          stats: {
            domain: job.data?.domain.url,
            ...(job.data?.webpage && { webpage: job.data?.webpage }),
          },
        },
      },
      { writeConcern: { w: 'majority' } },
    )

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const res2 = await collection.updateOne(
      {
        integration: IntegrationType.similarWeb,
        project: result.project,
        provider: result.provider,
        iso: result.iso,
        itemId: result.itemId,
        snapTime: result.snapTime,
        snapTimeUnit: result.snapTimeUnit,
        dataType: result.dataType,
      },
      {
        $setOnInsert: {
          project: result.project,
          provider: result.provider,
          iso: result.iso,
          itemId: result.itemId,
          snapTime: result.snapTime,
          snapTimeUnit: result.snapTimeUnit,
          dataType: result.dataType,
          createdAt: new Date(),
        },
        $addToSet: {
          stats: {
            $each: result.stats,
          },
        },
        $set: {
          updatedAt: new Date(),
        },
      },
      { upsert: true, writeConcern: { w: 'majority' } },
    )

    //console.log(res1, res2)
  }
}

async function updateBlacklistedDataTypes(job: JobInstance, addToBlacklisted: boolean) {
  const collection = job.runtime.collection<SimilarWeb>('similarWeb')

  const instruction = {}

  if (addToBlacklisted) {
    instruction['$addToSet'] = {
      'domains.$[element].blacklistedDataTypes': job.data.dataType,
    }
  } else if (!addToBlacklisted) {
    instruction['$pull'] = { 'domains.$[element].blacklistedDataTypes': job.data.dataType }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const res = await collection.updateOne(
    {
      project: job.data.project,
      provider: job.data.provider,
      ...(job.data.webpage && { webpage: job.data.webpage }),
    },
    instruction,
    { arrayFilters: [{ 'element.url': job.data.domain.url }] },
  )
  // console.log(res)
}

async function store(job: JobInstance, addToBlacklisted: boolean, results?: IntegrationSnapshotSimilarweb[]) {
  if (results?.length) {
    await storeResults(results, job)
  }

  if (job.data?.blacklisted !== undefined && addToBlacklisted !== job.data.blacklisted) {
    await updateBlacklistedDataTypes(job, addToBlacklisted)
  }
}

export async function getOverviewShare(domain: string, startDate: Date, endDate: Date, keyFormat = 'MMM/yy') {
  const start_date = format(startDate, 'yyyy-MM')
  const end_date = format(endDate, 'yyyy-MM')

  const months: string[] = []
  for (let d = startDate; !isAfter(d, endDate); d = addMonths(d, 1)) {
    months.push(format(d, 'yyyy/MM'))
  }

  const desktopUrl = `https://api.similarweb.com/v1/website/${domain}/traffic-sources/overview-share`
  const desktopData = await got(desktopUrl, {
    searchParams: {
      api_key: similarWebAPIkey,
      granularity: 'Monthly',
      main_domain_only: 'false',
      format: 'json',
      start_date,
      end_date,
    },
  }).json<{
    visits: Record<
      string,
      Array<{ source_type: string; visits: Array<{ date: string; organic: number; paid: number }> }>
    >
  }>()

  const mobileUrl = `https://api.similarweb.com/v5/website/${domain}/mobile-traffic-sources/mobile-overview-share`
  const mobileData = await got(mobileUrl, {
    searchParams: {
      api_key: similarWebAPIkey,
      granularity: 'Monthly',
      main_domain_only: 'false',
      format: 'json',
      start_date,
      end_date,
    },
  }).json<{
    visits: Record<string, Array<{ source_type: string; visits: Array<{ date: string; visits: number }> }>>
  }>()

  const outputJson = {}

  for (const m of months) {
    //const col = format(parse(m, 'yyyy/MM', new Date()), 'MMM/yy')

    // desktop totals for month m
    const dList = desktopData.visits[domain] || []
    const desktopSearch = dList.find((s) => s.source_type === 'Search')
    const desktopPaidSearch = (desktopSearch?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + (v.paid ?? 0), 0)
    const desktopOrganicSearch = (desktopSearch?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + (v.organic ?? 0), 0)
    const desktopSocial = (dList.find((s) => s.source_type === 'Social')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + ((v.organic ?? 0) + (v.paid ?? 0)), 0)
    const desktopDirect = (dList.find((s) => s.source_type === 'Direct')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + ((v.organic ?? 0) + (v.paid ?? 0)), 0)
    const desktopMail = (dList.find((s) => s.source_type === 'Mail')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + ((v.organic ?? 0) + (v.paid ?? 0)), 0)
    const desktopDisplayAds = (dList.find((s) => s.source_type === 'Display Ads')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + ((v.organic ?? 0) + (v.paid ?? 0)), 0)
    const desktopReferrals = (dList.find((s) => s.source_type === 'Referrals')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + ((v.organic ?? 0) + (v.paid ?? 0)), 0)
    const desktopTotal = dList.reduce((sum, s) => {
      const monthVisits = s.visits.filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      return sum + monthVisits.reduce((sub, v) => sub + ((v.organic ?? 0) + (v.paid ?? 0)), 0)
    }, 0)

    // mobile totals for month m
    const mList = mobileData.visits[domain] || []
    const mobilePaidSearch = (mList.find((s) => s.source_type === 'Paid Search')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileOrganicSearch = (mList.find((s) => s.source_type === 'Organic Search')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileSocial = (mList.find((s) => s.source_type === 'Social')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileDirect = (mList.find((s) => s.source_type === 'Direct')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileMail = (mList.find((s) => s.source_type === 'Mail')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileDisplayAds = (mList.find((s) => s.source_type === 'Display Ads')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileReferrals = (mList.find((s) => s.source_type === 'Referrals')?.visits || [])
      .filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      .reduce((sum, v) => sum + v.visits, 0)
    const mobileTotal = mList.reduce((sum, s) => {
      const monthVisits = s.visits.filter((v) => format(parse(v.date, 'yyyy-MM-dd', new Date()), 'yyyy/MM') === m)
      return sum + monthVisits.reduce((sub, v) => sub + v.visits, 0)
    }, 0)

    const totalPaidSearch = desktopPaidSearch + mobilePaidSearch
    const totalOrganicSearch = desktopOrganicSearch + mobileOrganicSearch
    const totalSocial = desktopSocial + mobileSocial
    const totalDirect = desktopDirect + mobileDirect
    const totalMail = desktopMail + mobileMail
    const totalDisplayAds = desktopDisplayAds + mobileDisplayAds
    const totalReferrals = desktopReferrals + mobileReferrals
    const totalAll = desktopTotal + mobileTotal

    const monthFormatted = format(parse(m, 'yyyy/MM', new Date()), keyFormat)

    outputJson[monthFormatted] = {
      paidSearch: {
        desktop: {
          absolute: desktopPaidSearch,
          relative: totalAll > 0 ? round((desktopPaidSearch / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobilePaidSearch,
          relative: totalAll > 0 ? round((mobilePaidSearch / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalPaidSearch,
          relative: totalAll > 0 ? round((totalPaidSearch / totalAll) * 100, 2) : 0,
        },
      },
      organicSearch: {
        desktop: {
          absolute: desktopOrganicSearch,
          relative: totalAll > 0 ? round((desktopOrganicSearch / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileOrganicSearch,
          relative: totalAll > 0 ? round((mobileOrganicSearch / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalOrganicSearch,
          relative: totalAll > 0 ? round((totalOrganicSearch / totalAll) * 100, 2) : 0,
        },
      },
      social: {
        desktop: {
          absolute: desktopSocial,
          relative: totalAll > 0 ? round((desktopSocial / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileSocial,
          relative: totalAll > 0 ? round((mobileSocial / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalSocial,
          relative: totalAll > 0 ? round((totalSocial / totalAll) * 100, 2) : 0,
        },
      },
      direct: {
        desktop: {
          absolute: desktopDirect,
          relative: totalAll > 0 ? round((desktopDirect / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileDirect,
          relative: totalAll > 0 ? round((mobileDirect / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalDirect,
          relative: totalAll > 0 ? round((totalDirect / totalAll) * 100, 2) : 0,
        },
      },
      mail: {
        desktop: {
          absolute: desktopMail,
          relative: totalAll > 0 ? round((desktopMail / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileMail,
          relative: totalAll > 0 ? round((mobileMail / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalMail,
          relative: totalAll > 0 ? round((totalMail / totalAll) * 100, 2) : 0,
        },
      },
      displayAds: {
        desktop: {
          absolute: desktopDisplayAds,
          relative: totalAll > 0 ? round((desktopDisplayAds / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileDisplayAds,
          relative: totalAll > 0 ? round((mobileDisplayAds / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalDisplayAds,
          relative: totalAll > 0 ? round((totalDisplayAds / totalAll) * 100, 2) : 0,
        },
      },
      referrals: {
        desktop: {
          absolute: desktopReferrals,
          relative: totalAll > 0 ? round((desktopReferrals / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileReferrals,
          relative: totalAll > 0 ? round((mobileReferrals / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalReferrals,
          relative: totalAll > 0 ? round((totalReferrals / totalAll) * 100, 2) : 0,
        },
      },
      total: {
        desktop: {
          absolute: desktopTotal,
          relative: totalAll > 0 ? round((desktopTotal / totalAll) * 100, 2) : 0,
        },
        mobile: {
          absolute: mobileTotal,
          relative: totalAll > 0 ? round((mobileTotal / totalAll) * 100, 2) : 0,
        },
        total: {
          absolute: totalAll,
          relative: totalAll > 0 ? round((totalAll / totalAll) * 100, 2) : 0,
        },
      },
    }
  }

  return outputJson
}
