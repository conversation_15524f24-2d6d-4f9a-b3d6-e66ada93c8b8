import { BatchV1Api } from '@kubernetes/client-node'
import { Injectable } from '@nestjs/common'
import Bottleneck from 'bottleneck'
import chalk from 'chalk'
import { Db, ObjectId } from 'mongodb'

import { InjectDb } from '@datagatherers/database'
import {
  JobService,
  Job,
  JobRuntime,
  ProviderJobSchedule,
  JobDefinition,
  ControllerConfig,
  ControllerUtils,
  ControllerRuntime,
  AfterWithCount,
  AfterWithResume,
  JobConfig,
  collectionMap,
  parseError,
  JobStats,
  ControllerStats,
  ProjectIssueService,
  type ControllerStatsSnapshot,
  defaultQaAnalysis,
} from '@datagatherers/datagatherers'
import { KubernetesService } from '@datagatherers/infrastructure'
import { getProvider } from '@datagatherers/providers'
import {
  generateProxyPlan,
  applyProxyPlanToAxios,
  applyProxyPlanToGot,
  cleanProcess,
  setupBrowser,
  parseForBrowser,
  fingerprintForBrowser,
  durationFormatterNarrow,
  sizeFormatter,
  oncefy,
} from '@datagatherers/worker-utils'

import { SchedulerHandler } from './scheduler.handler'
import { SlackLog } from './utils'
import { WorkerHandler } from './worker.handler'

function resumeLog(runtime: ControllerRuntime, remaining = false) {
  let res = `${chalk.magenta(runtime.stats.todo)} of ${runtime.stats.jobs} (${runtime.stats.progress}%)`
  res += ` | ${chalk.green(runtime.stats.completed)} - ${chalk.red(runtime.stats.errored)}`

  if (remaining) {
    res += ` (${durationFormatterNarrow(runtime.stats.remainingTime)})`
  }

  res += ` < ${chalk.cyan(runtime.stats.items + ' items')} | ${runtime.stats.jobsPerSecond.toFixed(2)} jps | ${
    runtime.stats.requests
  } req | ${chalk.green(runtime.stats.requestsPerSecond.toFixed(2) + ' rps')} | ${
    runtime.stats.meanRequestTime
  } meanRT | ${chalk.red(runtime.stats.proxyErrors + ' Perr')} | ${chalk.red(
    runtime.stats.timeoutErrors + ' Terr',
  )} | ${runtime.proxyPlan.country} | ${sizeFormatter(runtime.stats.dataTransfered)} | ${(
    runtime.stats.requests / runtime.stats.completed
  ).toFixed(2)} rpj ${
    runtime.stats.newJobsCoeficient() !== 1 ? `| ${runtime.stats.newJobsCoeficient().toFixed(2)} jcc ` : ''
  }>`
  return res
}

let statsSnap: ControllerStatsSnapshot
function monitor(runtime: ControllerRuntime) {
  if (process.env.DEBUG === 'true' || process.env.MONITOR === 'true') {
    const newSnapshot = runtime.stats.snapshot()

    const jobsPerSecondSince = runtime.stats.jobsPerSecondSince(statsSnap).toFixed(2)
    const requestsPerSecond = runtime.stats.requestsPerSecondSince(statsSnap).toFixed(2)
    const meanRequestTimeSince = runtime.stats.meanRequestTimeSince(statsSnap)
    const newJobsCoeficientSince = runtime.stats.newJobsCoeficientSince(statsSnap)

    let message = '\n\n================ Monitor ================'
    message += `\n    Period: ${durationFormatterNarrow(runtime.stats.durationSince(statsSnap))} | ${chalk.blue(
      durationFormatterNarrow(runtime.stats.duration),
    )}`
    message += `\n  Progress: ${chalk.magenta(runtime.stats.todo)} of ${runtime.stats.jobs} (${chalk.blue(
      runtime.stats.progress + '%',
    )})`
    message += `\nEstimation: ${durationFormatterNarrow(runtime.stats.remainingTime)} (${chalk.blue(
      durationFormatterNarrow(runtime.stats.remainingTimeSince(statsSnap)),
    )})`
    message += `\n      Jobs: ${runtime.stats.completedSince(statsSnap)} - ${chalk.green(
      runtime.stats.successfulSince(statsSnap),
    )} - ${chalk.red(runtime.stats.erroredSince(statsSnap))} | ${runtime.stats.jobsPerSecond} (${chalk.blue(
      jobsPerSecondSince,
    )}) jps ${newJobsCoeficientSince !== 1 ? `| ${newJobsCoeficientSince.toFixed(2)} jcc ` : ''}`
    message += `\n     Items: ${runtime.stats.items} (${chalk.cyan(runtime.stats.itemsSince(statsSnap))})`
    message += `\n  Requests: ${runtime.stats.requests} (${chalk.blue(
      runtime.stats.requestsSince(statsSnap),
    )}) | ${chalk.green(requestsPerSecond + ' rps')} | ${meanRequestTimeSince} meanRT`
    message += `\n Bandwidth: ${sizeFormatter(runtime.stats.dataTransfered)} (${chalk.blue(
      sizeFormatter(runtime.stats.dataTransferedSince(statsSnap)),
    )}) | ${sizeFormatter(runtime.stats.dataTransferedPerSecond)}/s (${chalk.blue(
      sizeFormatter(runtime.stats.dataTransferedPerSecondSince(statsSnap)) + '/s',
    )})`
    message += `\nTimeoutErr: ${runtime.stats.timeoutErrors} (${chalk.red(
      runtime.stats.timeoutErrorsSince(statsSnap),
    )})`
    message += `\n  ProxyErr: ${runtime.stats.proxyErrors} (${chalk.red(runtime.stats.proxyErrorsSince(statsSnap))})`
    message += `\n ProxyPlan: ${runtime.proxyPlan.country} | ${runtime.proxyPlan.provider}`
    message += '\n=========================================\n'
    // eslint-disable-next-line no-console
    console.info(message)

    statsSnap = newSnapshot
  }
}

const refreshProxies = (runtime: ControllerRuntime) => {
  return async () => {
    runtime.proxyPlan = generateProxyPlan(runtime.proxyPlan.constrains)

    const proxyStats = runtime.stats.newProxyStats(runtime.proxyPlan)

    runtime.axios = await applyProxyPlanToAxios(runtime.proxyPlan, proxyStats)
    runtime.got = await applyProxyPlanToGot(runtime.proxyPlan, proxyStats)

    if (runtime.browserProxy) {
      runtime.browserProxy.proxies = { DEFAULT: parseForBrowser(runtime.proxyPlan) }
      runtime.proxyPlanFingerprint = fingerprintForBrowser(runtime.proxyPlan)
    }
  }
}

@Injectable()
export class ControllerHandler {
  // connection = new Bottleneck.RedisConnection({
  //   clientOptions: {
  //     db: 'controller_jobs',
  //     host:
  //       process.env.NODE_ENV === 'production' && process.env.IN_CLUSTER === 'true'
  //         ? 'cluster-redis-master.default.svc.cluster.local'
  //         : undefined,
  //     password:
  //       process.env.NODE_ENV === 'production' && process.env.IN_CLUSTER === 'true'
  //         ? 'qQLYWV2FofNgcMiiLcydZRoq'
  //         : undefined,
  //     retry_strategy: (options: { attempt: number }) => {
  //       // reconnect after
  //       return Math.max(options.attempt * 100, 3000)
  //     },
  //   },
  // })

  schedulerSlack = new SlackLog(process.env.SLACK_SCHEDULER_WEBHOOK_URL)
  controllerSlack = new SlackLog(process.env.SLACK_CONTROLLER_WEBHOOK_URL)

  runtime: ControllerRuntime

  constructor(
    @InjectDb() readonly db: Db,
    @InjectDb('data') readonly dbData: Db,
    @InjectDb('datalake') readonly dbDataLake: Db,
    readonly jobService: JobService,
    readonly scheduler: SchedulerHandler,
    readonly worker: WorkerHandler,
    readonly k8s: KubernetesService,
    readonly projectIssueService: ProjectIssueService,
  ) {}

  async handler(config: ControllerConfig & { id: string }, schedule = true) {
    if (process.env.DEBUG === 'true') {
      // eslint-disable-next-line no-console
      console.log(config)
    }

    if (schedule) {
      const existingJobsForController = await this.jobService.count({ controllerId: config.id })
      if (existingJobsForController === 0) {
        try {
          await this.schedule(config)
        } catch (error) {
          await this.schedulerSlack.log(
            config,
            `:boom: Error scheduling controller${this.runtime?.proxyPlan?.country ? ` with Server in ${this.runtime?.proxyPlan?.country}` : ''}`,
            error,
          )
          throw error
        }
      }
    }

    return this.controller(config)
  }

  async controller(config: ControllerConfig & { id: string; jobList?: Job[] }, utils?: ControllerUtils) {
    const provider = await getProvider(config.project, config.provider)

    const func = provider[config.type]
    if (typeof func === 'undefined') {
      throw new Error(`Job type ${config.type} not defined for provider ${config.provider}!`)
    }

    func.config.concurrency = config.concurrency || func.config.concurrency

    const runtime = await this.generateRuntime(config, func)

    if (!func.bottleneck?.length) {
      func.bottleneck = [
        {
          maxConcurrent: func.config.concurrency,
          minTime: 10,
        },
      ]
    }

    const limiters: Bottleneck[] = []
    const limiter = new Bottleneck({
      // timeout: 5 * 60 * 1000,
      // datastore:
      //   process.env.KUBERNETES === 'true' && process.env.NODE_ENV === 'production' && process.env.IN_CLUSTER === 'true'
      //     ? 'redis'
      //     : 'local',
      // connection:
      //   process.env.KUBERNETES === 'true' && process.env.NODE_ENV === 'production' && process.env.IN_CLUSTER === 'true'
      //     ? this.connection
      //     : undefined,
      id: config.id ?? 'local',
      ...func.bottleneck[0],
    })

    limiters.push(limiter)

    if (func.bottleneck.length > 1) {
      for (let i = 1; i < func.bottleneck.length; i++) {
        const extraLimiter = new Bottleneck({
          // timeout: 5 * 60 * 1000,
          // datastore:
          //   process.env.KUBERNETES === 'true' &&
          //   process.env.NODE_ENV === 'production' &&
          //   process.env.IN_CLUSTER === 'true'
          //     ? 'redis'
          //     : 'local',
          // connection:
          //   process.env.KUBERNETES === 'true' &&
          //   process.env.NODE_ENV === 'production' &&
          //   process.env.IN_CLUSTER === 'true'
          //     ? this.connection
          //     : undefined,
          id: `${config.id ?? 'local'}-${i}`,
          ...func.bottleneck[i],
        })
        limiter.chain(extraLimiter)
        limiters.push(extraLimiter)
      }
    }

    const wrapped = limiter.wrap(run)

    let debugInterval: NodeJS.Timeout | undefined
    if (process.env.DEBUG === 'true') {
      debugInterval = setInterval(() => {
        // eslint-disable-next-line no-console
        console.info(`Controller ${config.id || 'local'}`, limiter.counts())
      }, 60 * 1000)
    }

    statsSnap = runtime.stats.snapshot()
    const monitorInterval = setInterval(() => monitor(runtime), 10 * 1000)

    if ((config as ControllerConfig & { jobList?: Job[] }).jobList) {
      runtime.stats.jobs = (config as ControllerConfig & { jobList?: Job[] }).jobList.length
    } else {
      runtime.stats.jobs = await this.jobService.count({
        ...(config.jobs ? config.jobs : { controllerId: config.id }),
        finishedAt: { $exists: false },
      })
    }

    if (utils && utils.spinner && utils.spinner.text) {
      limiter.on('done', () => (utils.spinner.text = resumeLog(runtime, true)))
      limiter.on('depleted', () => (utils.spinner.text = resumeLog(runtime, true)))

      utils.spinner.text = resumeLog(runtime)
    } else {
      // eslint-disable-next-line no-console
      limiter.on('done', () => console.info(resumeLog(runtime, true)))
      // eslint-disable-next-line no-console
      limiter.on('depleted', () => console.info(resumeLog(runtime, true)))
      // eslint-disable-next-line no-console
      console.info(resumeLog(runtime))
    }

    if (process.env.DEBUG === 'true') {
      limiter.on('failed', function (error, jobInfo) {
        // eslint-disable-next-line no-console
        console.log('==== Debug ====')

        console.error(error, jobInfo)
        // eslint-disable-next-line no-console
        console.log('===============')
      })
    }

    if (runtime.stats.jobs > 0) {
      await this.runExisting(wrapped, config, runtime, func)
    }

    try {
      await this.runAfterControllerFinishedJob(config, runtime, func)
    } catch (error) {
      await this.controllerSlack.log(config, ':boom: Error after controller finished', error)
    }

    try {
      await this.runScheduleAfterFinished(config, runtime, func)
    } catch (error) {
      await this.controllerSlack.log(config, ':boom: Error schedule after finished', error)
    }

    try {
      await this.runSnapshot(config, runtime, func)
    } catch (error) {
      await this.controllerSlack.log(config, ':boom: Error snapshot', error)
    }

    try {
      // await this.runQA(config, runtime, func)
    } catch (error) {
      await this.controllerSlack.log(config, ':boom: Error QA', error)
    }

    for (const limiter of limiters) {
      await limiter.disconnect()
    }

    clearInterval(monitorInterval)

    if (typeof debugInterval !== 'undefined') {
      clearInterval(debugInterval)
    }

    await cleanProcess()

    return runtime
  }

  async schedule(config: ControllerConfig) {
    const provider = await getProvider(config.project, config.provider)

    const func = provider[config.type]
    if (typeof func === 'undefined') {
      throw new Error(`Job type ${config.type} not defined for provider ${config.provider}!`)
    }

    let jobs: JobDefinition[] = []

    if (typeof config.skipFunction === 'undefined' || !config.skipFunction) {
      if (typeof func.schedule !== 'function') {
        jobs = [
          {
            project: config.project,
            provider: config.provider,
            type: config.type,
            data: config.data,
          },
        ]
      } else {
        func.config.concurrency = config.concurrency || func.config.concurrency

        const runtime = await this.generateRuntime(config, func)

        jobs = await func.schedule(
          {
            project: config.project,
            provider: config.provider,
            type: config.type,
            data: config.data,
          },
          runtime,
        )
      }
    }

    if (!config.skipStoreJobs) {
      await this.jobService.createMany(jobs.map((job) => ({ ...job, controllerId: config.id })))
    }

    return jobs
  }

  private async generateRuntime(config: ControllerConfig, func: ProviderJobSchedule): Promise<ControllerRuntime> {
    if (this.runtime) {
      return this.runtime
    }

    if (typeof func.config.maxProxyRetriesPerRequest === 'undefined') {
      process.env.MAX_PROXY_RETRIES_PER_REQUEST = func.config.maxProxyRetriesPerRequest
    }
    if (typeof func.config.maxTimeoutRetriesPerRequest === 'undefined') {
      process.env.MAX_TIMEOUT_RETRIES_PER_REQUEST = func.config.maxTimeoutRetriesPerRequest
    }

    const proxyPlan = generateProxyPlan(func.config.constrains, func.region)

    const stats = new ControllerStats()

    const proxyStats = stats.newProxyStats(proxyPlan)

    const runtime: Partial<ControllerRuntime> = {
      collection: (collection: string) => {
        const dbName = collectionMap(collection)

        if (typeof dbName === 'undefined') {
          throw new Error(`Collection ${collection} not defined!`)
        }

        if (dbName === 'datalake') {
          return this.dbDataLake.collection(collection)
        }

        if (dbName === 'data') {
          return this.dbData.collection(collection)
        }

        return this.db.collection(collection)
      },
      k8sApi: this.k8s.api(BatchV1Api),
      proxyPlan,
      axios: await applyProxyPlanToAxios(proxyPlan, proxyStats),
      got: await applyProxyPlanToGot(proxyPlan, proxyStats),
      schedule: this.schedule,

      skipStoreData: config.skipStoreData,
      skipStoreJobs: config.skipStoreJobs,

      stats,
    }

    runtime.refreshProxies = oncefy(refreshProxies(runtime as ControllerRuntime), async () => {
      if (runtime.browserProxy) {
        await runtime.page.close()

        runtime.page = await runtime.createPage(runtime as ControllerRuntime)

        if (process.env.DEBUG_PROXY === 'true') {
          const page = await runtime.createPage(runtime as ControllerRuntime)
          await page.goto('https://api.my-ip.io/v2/ip.json', { waitUntil: 'networkidle2' })
          const el = await page.$('body')
          const reponse = await el?.evaluate((el) => el.textContent)
          // eslint-disable-next-line no-console
          console.info('browser', JSON.parse(reponse))
          await page.close()
        }
      }
    })

    if (func.config.browser) {
      await setupBrowser(runtime as ControllerRuntime, func.config)
      runtime.page = await runtime.createPage(runtime as ControllerRuntime)

      if (process.env.DEBUG_PROXY === 'true') {
        const page = await runtime.createPage(runtime as ControllerRuntime)
        await page.goto('https://api.my-ip.io/v2/ip.json', { waitUntil: 'networkidle2' })
        const el = await page.$('body')
        const reponse = await el?.evaluate((el) => el.textContent)
        // eslint-disable-next-line no-console
        console.info('browser', JSON.parse(reponse))
        await page.close()
      }
    }

    this.runtime = runtime as ControllerRuntime

    return this.runtime
  }

  private async runExisting(
    wrapped: {
      withOptions: (
        options: Bottleneck.JobOptions,
        arg1: Job,
        arg2: ControllerRuntime,
        arg6: ProviderJobSchedule,
        arg7: JobService,
        arg8: WorkerHandler,
      ) => Promise<unknown>
    },
    config: ControllerConfig & { jobList?: Job[] },
    runtime: ControllerRuntime,
    func: ProviderJobSchedule,
  ) {
    if (config.skipFunction === true) {
      console.warn('\nSkipped running Job function.')
      return
    }

    let jobs: Job[]
    if ((config as ControllerConfig & { jobList?: Job[] }).jobList) {
      jobs = (config as ControllerConfig & { jobList?: Job[] }).jobList
      delete (config as ControllerConfig & { jobList?: Job[] }).jobList
    } else {
      jobs = await this.jobService.find(
        {
          finishedAt: { $exists: false },
          ...(config.jobs ? config.jobs : { controllerId: config.id }),
        },
        {
          limit: config.maxNumberOfJobs || 999,
        },
      )

      if (process.env.DEBUG === 'true') {
        // eslint-disable-next-line no-console
        console.log('Query', {
          ...(config.jobs ? config.jobs : { controllerId: config.id }),
          finishedAt: { $exists: false },
        })
        // eslint-disable-next-line no-console
        console.log('Number of jobs: ' + jobs.length)
      }
    }

    if (!jobs.length) {
      return Promise.resolve()
    }

    const jobsQueue: Promise<unknown>[] = []
    for (const job of jobs) {
      jobsQueue.push(
        wrapped
          .withOptions(
            {
              expiration: func.config.perJobTimeout ?? 15 * 60 * 1000, // 15 minutes
              id: job._id?.toString(),
            },
            job,
            runtime,
            func,
            this.jobService,
            this.worker,
          )
          .catch(async (error) => {
            const err = parseError(error)
            console.error(err)
            console.warn('\nAffected Job:', job)

            if (!runtime.skipStoreJobs && job._id) {
              await this.jobService.updateOne(
                {
                  _id: new ObjectId(job._id),
                },
                {
                  $set: {
                    ...(job.errorLog && { errorLog: job.errorLog }),
                    error: err,
                    stats: runtime.stats.serialize,
                    finishedAt: new Date(),
                  },
                },
              )
            }

            runtime.stats.increment('errored')
          }),
      )
    }

    await Promise.all(jobsQueue)

    if (!config.maxNumberOfJobs || jobs.length < config.maxNumberOfJobs) {
      return this.runExisting(wrapped, config, runtime, func)
    }
  }

  private async runAfterControllerFinishedJob(
    config: ControllerConfig,
    runtime: ControllerRuntime,
    func: ProviderJobSchedule,
  ) {
    if (typeof func.afterControllerFinished === 'undefined') {
      return
    }

    if (config.skipAfterControllerFinished === true) {
      console.warn('\nSkipped running After Controller Finished function.')
      return
    }

    return await func.afterControllerFinished(config, runtime)
  }

  private async runScheduleAfterFinished(
    config: ControllerConfig,
    runtime: ControllerRuntime,
    func: ProviderJobSchedule,
  ) {
    if (typeof func.scheduleAfterFinished === 'undefined') {
      return
    }

    if (config.skipScheduleAfterFinished === true) {
      console.warn('\nSkipped running Schedule After Finished function.')
      return
    }

    if (typeof func.scheduleAfterFinished === 'function') {
      const schedules = await func.scheduleAfterFinished(config, runtime)
      if (Array.isArray(schedules)) {
        for (const schedule of schedules) {
          await this.scheduler.createController(await this.completeControllerConfig(schedule))
        }
        return
      } else {
        return this.scheduler.createController(await this.completeControllerConfig(schedules))
      }
    } else if (Array.isArray(func.scheduleAfterFinished)) {
      for (const schedule of func.scheduleAfterFinished) {
        await this.scheduler.createController(await this.completeControllerConfig(schedule))
      }
      return
    } else {
      return this.scheduler.createController(await this.completeControllerConfig(func.scheduleAfterFinished))
    }
  }

  private async completeControllerConfig(config: ControllerConfig & { config?: JobConfig }) {
    if (!config.id) {
      config.id = this.scheduler.generateControllerId(config)
    }

    if (!config.config) {
      const provider = await getProvider(config.project, config.provider)

      const func = provider[config.type]
      if (typeof func === 'undefined') {
        throw new Error(`Job type ${config.type} not defined for provider ${config.provider}!`)
      }

      config.config = func.config
    }

    return config as ControllerConfig & { config: JobConfig }
  }

  private async runSnapshot(config: ControllerConfig, runtime: ControllerRuntime, func: ProviderJobSchedule) {
    if (typeof func.snapshot === 'undefined') {
      return
    }

    if (config.skipSnapshot === true) {
      console.warn('\nSkipped running Snapshot function.')
      return
    }

    return await func.snapshot(config, runtime)
  }

  private async runQA(config: ControllerConfig, runtime: ControllerRuntime, func: ProviderJobSchedule) {
    if (config.skipQA === true) {
      console.warn('\nSkipped running QA function.')
      return []
    }

    const foundIssues = await defaultQaAnalysis(runtime.collection('snapshot'), runtime.collection('job'), {
      project: config.project,
      provider: config.provider,
      jobType: config.type,
      config: func.qaConfig ?? {},
    })

    if (foundIssues?.length) {
      return await this.projectIssueService.createMany(foundIssues)
    }

    if (typeof func.qa === 'undefined') {
      return foundIssues
    }

    const issues = await func.qa(config, runtime, func.qaConfig)

    if (issues?.length) {
      return await this.projectIssueService.createMany(issues)
    }

    return [...foundIssues, ...issues]
  }
}

async function generateJobRuntime(runtime: ControllerRuntime) {
  const stats = new JobStats()

  const proxyStats = stats.newProxyStats(runtime.proxyPlan)

  const jobRuntime: Partial<JobRuntime> = {
    collection: runtime.collection,
    proxyPlan: runtime.proxyPlan,
    proxyPlanFingerprint: runtime.proxyPlanFingerprint,
    axios: await applyProxyPlanToAxios(runtime.proxyPlan, proxyStats),
    got: await applyProxyPlanToGot(runtime.proxyPlan, proxyStats),

    skipStoreData: runtime.skipStoreData,
    skipStoreJobs: runtime.skipStoreJobs,

    browserProxy: runtime.browserProxy,

    proxyStats,
    stats,

    addJobs: async (jobs: JobDefinition[]) => {
      if (jobRuntime.skipStoreJobs) {
        runtime.stats.jobs = jobs.length

        return {
          insertedCount: 0,
          acknowledged: true,
          insertedIds: [],
        }
      }

      const result = await runtime.collection('job').insertMany(jobs)

      runtime.stats.jobs = result.insertedCount

      return result
    },
  }

  jobRuntime.refreshProxies = async () => {
    await runtime.refreshProxies()

    jobRuntime.proxyStats = stats.newProxyStats(runtime.proxyPlan)

    jobRuntime.axios = await applyProxyPlanToAxios(runtime.proxyPlan, jobRuntime.proxyStats)
    jobRuntime.got = await applyProxyPlanToGot(runtime.proxyPlan, jobRuntime.proxyStats)

    if (runtime.browserProxy) {
      await jobRuntime.page.close()

      jobRuntime.page = await runtime.createPage(jobRuntime as JobRuntime)
    }
  }

  if (runtime.createPage) {
    jobRuntime.page = await runtime.createPage(jobRuntime as JobRuntime)
  }

  return jobRuntime as JobRuntime
}

const run = async (
  job: Job,
  runtime: ControllerRuntime,
  func: ProviderJobSchedule,
  jobService: JobService,
  worker: WorkerHandler,
): Promise<void> => {
  if (job.finishedAt) {
    console.warn(`Job ${job._id?.toString() || 'local'} has already been completed!`)
    return
  }
  const jobRuntime = await generateJobRuntime(runtime)

  try {
    const result = await worker.handler(job, jobRuntime, func)

    if ('errorMessage' in result) {
      throw result
    }

    if (typeof func.after === 'function') {
      if ('numberOfItems' in result) {
        await (func.after as AfterWithCount)(job, result, jobRuntime)
      } else {
        await (func.after as AfterWithResume)(job, result, jobRuntime)
      }
    }

    runtime.stats.increment('successful')
  } catch (error) {
    const err = parseError(error)
    console.error(err)
    console.warn('\nAffected Job:', job)

    if (typeof func.catch === 'function') {
      try {
        await func.catch(job, error, jobRuntime)
      } catch (err) {
        console.error(parseError(err))
      }
    }

    if (!jobRuntime.skipStoreJobs && job._id) {
      await jobService.updateOne(
        {
          _id: new ObjectId(job._id),
        },
        {
          $set: {
            ...(job.errorLog && { errorLog: job.errorLog }),
            error: err,
            stats: jobRuntime.stats.serialize,
            finishedAt: new Date(),
          },
        },
      )
    }
    runtime.stats.increment('errored')
  } finally {
    if (jobRuntime.page) {
      await jobRuntime.page.close()
    }

    runtime.stats.merge(jobRuntime.stats)
  }
}
