import type { DefaultConfigs } from './project.entity'

export const defaultConfigs: DefaultConfigs = {
  accounting: {
    additionalFilters: [
      {
        key: 'verticalType',
        entries: [
          {
            value: 'bookkeeping',
            label: 'Bookkeeping',
            providers: [
              'bexio',
              'bokio',
              'bjornlunden',
              'cegid',
              'contaazul',
              'e.conomic',
              'ebp',
              'exact',
              'fiken',
              'finago',
              'fortnox',
              'freee',
              'gestionix',
              'gtaxes',
              'holded',
              'khatabook',
              'lexware',
              'logo',
              'moneyforward',
              'nibo',
              'omie',
              'parasut',
              'quickbooks',
              'reviso',
              'vismaspcs',
              'wehago',
              'wehagot',
              'xero',
              '24sevenoffice',
              'sevdesk',
              'lexoffice',
              'coconut',
              'sinube',
              'taxnote',
              'epsilonnet',
              'pylon',
              'softone',
              'epsilonsmart',
              'prosvasis',
              'kontist',
              'dougs',
              'pennylane',
              'tripletex',
              'szamlazz',
              'dinero',
              'yuki',
              'sage',
              'autocount',
              'deskera',
              'freshbooks',
              'wave',
              'myob',
              'teamsystem',
              'zucchetti',
              'eudonet',
              'mago',
              'scan',
              'konto',
              'odoo',
              'aspia',
              'ludvig',
              'bakertilly',
              'eaccounting',
              '24sevenoffice',
              'poweroffice',
              'poweroffice_mo',
              'duett',
              'pyramid',
              'xledger',
              'briljant',
            ],
          },
          {
            value: 'expenses',
            providers: [
              'rakus',
              'divvy',
              'brex',
              'ramp',
              'airbase',
              'relay',
              'concur',
              'pleo',
              'soldo',
              'xero_expenses',
              'expend',
              'yokoy',
              'spendesk',
              'mynt',
              'paild',
              'upsider',
              'qonto',
              'beanworks',
              'jeeves',
              'fylehq',
              'trulyfinancial',
              'divipay',
              'expensify',
              'mendel',
              'capitalontap',
              'hubdoc',
              'dext',
              'payhawk',
              'getmoss',
              'clara',
              'tribalcredit',
              'mesh',
              'acubiz',
              'zenegy',
              'zenegy_expenses',
              'nexonia',
              'n2f',
              'moss',
              'haslle',
              'pelikin',
              'parpera',
              'fortnox_expenses',
              'srxp',
              'qred',
              'juni',
              'vipps',
            ],
            label: 'Expenses',
          },
          {
            value: 'procurement',
            providers: ['coupa', 'ziphq'],
            label: 'Procurement',
          },
          {
            value: 'accounts_receivable',
            label: 'Accounts Receivable',
            providers: [
              /bill.com/,
              'billtrust',
              'highradius',
              'corcentric',
              'veem',
              'invoice2go',
              'paystand',
              'quickbooks_selfemployed',
              'zoho_books',
              'square_invoices',
              'freshbooks',
              'banqup',
              'jefacture',
              'unifiedpost',
              'crossinx',
              'billtobox',
              'scripturaengage',
              'adminbox',
              'onearchive',
              'nomadesk',
              'akti',
              'leleu',
              'fitekin',
              '21grams',
              'arved.ee',
              'digithera',
              'epay.lt',
              'epay.lv',
              'rekini.lv',
              'banqware',
              'sistemaefactura',
              'zlogin',
              'plateiq',
              'fortnoxpay',
              'billfire',
              'ignitionapp',
              'payoneer',
              'airwallex',
              'wise',
              'bluevine',
              'quickbooks_money',
              'mercury',
              'epsilonpay',
              'enty',
              'fortnoxpay_deprecated',
              'sidetrade',
              'sequencehq',
            ],
          },
          {
            value: 'accounts_payable',
            label: 'Accounts Payable',
            providers: [
              'tfin',
              'melio',
              'tipalti',
              'plooto',
              'corpay',
              /bill.com/,
              'avidxchange',
              'corcentric',
              'mineraltree',
              'veem',
              'airbase',
              'relay',
              'stampli',
              'routable',
              'bottomline',
              'banqup',
              'jefacture',
              'unifiedpost',
              'crossinx',
              'billtobox',
              'scripturaengage',
              'adminbox',
              'onearchive',
              'nomadesk',
              'akti',
              'leleu',
              'fitekin',
              '21grams',
              'arved.ee',
              'digithera',
              'epay.lt',
              'epay.lv',
              'rekini.lv',
              'banqware',
              'sistemaefactura',
              'zlogin',
              'plateiq',
              'freedommerchantservices',
              'plastiq',
              'beanworks',
              'corporatespending',
              'eved',
              'bluevine',
              'brex',
              'zenbill',
              'sparcpay',
              'transcard',
              'quadient',
              'melio',
              'ramp',
              'glean.ai',
              'settle',
              'prospend',
              'lightyear',
              'libeo',
              'yooz',
              'epsilonpay',
            ],
          },
          {
            value: 'e-signing',
            providers: ['cloudsign', 'docusign', 'freeesign', 'cling'],
            label: 'E-Signing',
          },
          {
            value: 'tax',
            providers: ['vertex', 'a-saas', 'hnry', 'diginet'],
            label: 'Tax',
          },
          {
            value: 'payroll',
            providers: [
              'planday',
              'elmo',
              'lexoffice',
              'quickbooks',
              'remote',
              'dailypay',
              'justworks',
              'wave',
              'rippling',
              'zenefits',
              'gusto',
              'payfit',
              'trinet',
              'patriotsoftware',
              'papayaglobal',
              'onpay',
              'wagepoint',
              'paychex',
              'adp',
              'rightnetworks',
              'surepayroll',
              'workful',
              'traxpayroll',
              'eddy',
              'isolved',
              'personio',
              'zenegy',
              'zenegy_payroll',
              'autocount',
              'deskera',
              'bjornlunden',
              'hogia',
              'fortnox',
              'visma_lon',
              'employmenthero',
              'epsilonpay',
            ],
            label: 'Payroll',
          },
          {
            value: 'working_capital',
            providers: [
              'fundingcircle',
              'bluevine',
              'lendio',
              'biz2credit',
              'nav',
              'smartbizloans',
              'lsq',
              'fundera',
              'fundbox',
              'kabbage',
              'ondeck',
              'credibilitycapital',
              'c2fo',
              'settle',
              'resursbank',
              'almi',
            ],
            label: 'Working Capital',
          },
          {
            value: 'human_resources',
            providers: [
              'eddy',
              'bamboohr',
              'personio',
              'crewmeister',
              'clockodo',
              'tamigo',
              'atoss',
              'compleet',
              'dejoris',
              'e2n',
              'gfos',
              'interflex',
              'isgus',
              'oracle',
              'quinyx',
              'zebra',
              'sap',
              'timeplan',
              'ukg',
              'workday',
              'employmenthero',
            ],
            label: 'Human Resources',
          },
          {
            value: 'fpa',
            providers: ['jirav', 'fathom', 'finmark', 'clockwork'],
            label: 'FP&A',
          },
          {
            value: 'e_invoicing',
            providers: ['aade', 'prosvasis_go', 'epsilonsmart', 'kivra'],
            label: 'E-Invoicing',
          },
          {
            value: 'practice_manegement',
            providers: ['karbonhq'],
            label: 'Practice Manegement',
          },
          {
            value: 'accounting_automation',
            providers: ['silverfin', 'botkeeper', 'floqast'],
            label: 'Accounting Automation',
          },
          {
            value: 'proposal',
            providers: ['goproposal', 'ignitionapp'],
            label: 'Proposal',
          },
          {
            value: 'spend_mgmt_as_a_service',
            providers: ['cardlay', 'airwallex'],
            label: 'Spend Mgmt as a service',
          },
        ],
      },
      {
        key: 'marketSegment',
        entries: [
          {
            value: 'sme',
            label: 'SME',
            providers: [
              'bexio',
              'bokio',
              'cegid',
              'e.conomic',
              'ebp',
              'exact',
              'fiken',
              'finago',
              'fortnox',
              'freee',
              'gestionix',
              'gtaxes',
              'holded',
              'khatabook',
              'lexware',
              'logo',
              'moneyforward',
              'nibo',
              'parasut',
              'quickbooks',
              'reviso',
              'vismaspcs',
              'wehago',
              'wehagot',
              'xero',
              'veem',
              /bill.com/,
              'melio',
              'plooto',
              'corpay',
              'sevdesk',
              'lexoffice',
              'coconut',
              'rakus',
              'sinube',
              'gusto',
              'divvy',
              'brex',
              'invoice2go',
              'pleo',
              'soldo',
              'xero_expenses',
              'expend',
              'taxnote',
              'cloudsign',
              'docusign',
              'freeesign',
              'yokoy',
              'remote',
              'dailypay',
              'justworks',
              'wave',
              'rippling',
              'zenefits',
              'gusto',
              'payfit',
              'trinet',
              'patriotsoftware',
              'onpay',
              'wagepoint',
              'paychex',
              'adp',
              'rightnetworks',
              'surepayroll',
              'workful',
              'traxpayroll',
              'fundingcircle',
              'bluevine',
              'lendio',
              'biz2credit',
              'nav',
              'smartbizloans',
              'lsq',
              'fundera',
              'fundbox',
              'kabbage',
              'ondeck',
              'credibilitycapital',
              'eddy',
              'bamboohr',
              'banqup',
              'jefacture',
              'crossinx',
              'billtobox',
              'scripturaengage',
              'adminbox',
              'onearchive',
              'nomadesk',
              'akti',
              'leleu',
              'fitekin',
              '21grams',
              'arved.ee',
              'digithera',
              'epay.lt',
              'epay.lv',
              'rekini.lv',
              'banqware',
              'sistemaefactura',
              'zlogin',
              'spendesk',
              'mynt',
              'c2fo',
              'paild',
              'upsider',
              'qonto',
              'plateiq',
              'freedommerchantservices',
              'personio',
              'jirav',
              'fathom',
              'plastiq',
              'ramp',
              'beanworks',
              'jeeves',
              'fortnoxpay',
              'corporatespending',
              'fylehq',
              'a-saas',
              'billfire',
              'trulyfinancial',
              'epsilonnet',
              'settle',
              'prosvasis',
              'datacommunication',
              'singularlogic',
              'iqom',
              'pcs',
              'csa',
              'digital4u',
              'taxheaven',
              'technolife',
              'epsilonsmart',
              'aade',
              'prosvasis_go',
              'epsilonsmart',
              'finmark',
              'mendel',
              'getmoss',
              'moneybird',
              'e-boekhouden',
              'snelstart',
              'visma',
              'clara',
              'tribalcredit',
              'resursbank',
              'dougs',
              'sage',
              'tripletex',
              'szamlazz',
              'dinero',
              'yuki',
              'stampli',
              'quadient',
              'moss',
              'haslle',
              'hnry',
              'pelikin',
              'wayflyer',
              'ziphq',
              'ignitionapp',
              'silverfin',
              'botkeeper',
              'karbonhq',
              'goproposal',
              'parpera',
              'prospend',
              'almi',
              'airwallex',
              'payoneer',
              'wise',
              'bluevine',
              'quickbooks_money',
              'mercury',
              'visma_lon',
              'employmenthero',
              'pennylane',
              'eaccounting',
              '24sevenoffice',
              'poweroffice',
              'poweroffice_mo',
              'duett',
              'glean.ai',
              'crewmeister',
              'enty',
              'clockodo',
              'tamigo',
              'fortnoxpay_deprecated',
            ],
          },
          {
            value: 'mid',
            label: 'Mid-market',
            providers: [
              'tipalti',
              'contaazul',
              'freee',
              'moneyforward',
              'omie',
              'quickbooks',
              '24sevenoffice',
              'billtrust',
              'planday',
              /bill.com/,
              'avidxchange',
              'mineraltree',
              'elmo',
              'rakus',
              'sinube',
              'gusto',
              'divvy',
              'brex',
              'ramp',
              'expend',
              'docusign',
              'freeesign',
              'yokoy',
              'dailypay',
              'paychex',
              'adp',
              'unifiedpost',
              'mynt',
              'c2fo',
              'paild',
              'upsider',
              'corporatespending',
              'settle',
              'aade',
              'prosvasis_go',
              'epsilonsmart',
              'mesh',
              'clockwork',
              'resursbank',
              'dougs',
              'sage',
              'n2f',
              'stampli',
              'pelikin',
              'mesh',
              'wayflyer',
              'ziphq',
              'parpera',
              'prospend',
              'airwallex',
              'yooz',
              'pyramid',
              'xledger',
              'briljant',
            ],
          },
          {
            value: 'enterprise',
            label: 'Enterprise',
            providers: [
              'billtrust',
              'planday',
              'highradius',
              'corcentric',
              'tipalti',
              'avidxchange',
              'vertex',
              'coupa',
              'ramp',
              'airbase',
              'docusign',
              'yokoy',
              'dailypay',
              'papayaglobal',
              'paychex',
              'adp',
              'c2fo',
              'eved',
              'aade',
              'prosvasis_go',
              'epsilonsmart',
              'clockwork',
              'resursbank',
              'dougs',
              'sage',
              'cegid',
              'n2f',
              'ziphq',
              'scan',
              'atoss',
              'compleet',
              'dejoris',
              'e2n',
              'gfos',
              'interflex',
              'isgus',
              'oracle',
              'quinyx',
              'zebra',
              'sap',
              'timeplan',
              'ukg',
              'workday',
            ],
          },
        ],
      },
    ],
  },
  animals: {},
  ampereTv: {},
  ampere: {},
  auctions: {
    // providerGrouping: {
    //   'i&c': ['proxibid', 'bidspotter', 'ibidder'], // TODO (Realista): check how this should work
    // },
    additionalFilters: [
      {
        key: 'verticalType',
        entries: [
          {
            value: 'nft',
            label: "NFT's",
            providers: ['opensea', 'zora', 'foundation.app', 'niftygateway'],
          },
          {
            value: 'charity',
            label: 'Charities',
            providers: ['charitybuzz'],
          },
          {
            value: 'art_antiques',
            label: 'Art & Antiques',
            providers: [
              'artsy',
              'thesaleroom',
              'liveauctioneers',
              'invaluable',
              'lottissimo',
              'easyliveauction',
              'icollector',
              'bidsquare',
              'christies',
              /^estatesales/,
              'barnebys',
            ],
          },
          {
            value: 'industrial',
            label: 'Industrial',
            providers: [
              'hibid',
              'krank',
              'bidadoo',
              'ironplanet',
              'euroauctions',
              'bidspotter',
              'proxibid',
              'equipmentfacts',
              'ritchiebros',
              'machinerytrader',
              /^estatesales/,
              'allsurplus',
              'liquidation',
              'govdeals',
              'bid4assets',
              'ibidder',
            ],
          },
          {
            value: 'collectibles',
            label: 'Collectibles',
            providers: ['catawiki', 'easyliveauction', 'stacksbowers'],
          },
          {
            value: 'auto',
            label: 'Auto',
            providers: ['acvauctions'],
          },
          {
            value: 'consumer_surplus',
            label: 'Consumer Surplus',
            providers: ['ibidder'],
          },
          {
            value: 'medical',
            label: 'Medical',
            providers: ['compugroup'],
          },
          {
            value: 'planning',
            label: 'Planning',
            providers: ['anaplan'],
          },
          {
            value: 'property',
            label: 'Property',
            providers: ['appfolio', 'yardi', 'realpage', 'costar'],
          },
          {
            value: 'security',
            label: 'Security',
            providers: ['shotspotter', 'alarm.com'],
          },
          {
            value: 'surveys',
            label: 'Surveys',
            providers: ['cint'],
          },
          {
            value: 'websites',
            label: 'Websites',
            providers: ['wix', 'squarespace'],
          },
          {
            value: 'car_repair',
            label: 'Car Repair',
            providers: ['cccportal', 'cccone'],
          },
          {
            value: 'auction',
            label: 'Auction',
            providers: [
              'bidpath',
              'bidlogix',
              'maxanet',
              'nextlot',
              'skeleton',
              'snoofa',
              'globalauctionplatform',
              'auctionmobility',
              'wavebid',
            ],
          },
          {
            value: 'salons',
            label: 'Salons',
            providers: ['slick', 'fresha', 'matchi', 'bokadirekt'],
          },
          {
            value: 'legal',
            label: 'Legal',
            providers: ['dyedurhamdyedurham'],
          },
        ],
      },
    ],
  },
  cars: {},
  delivery: {},
  jobs: {},
  marketplaces: {
    additionalFilters: [
      {
        key: 'verticalType',
        entries: [
          {
            value: 'arts_crafts',
            label: 'Arts & Crafts',
            providers: ['creema', 'minne', 'etsy'],
          },
          {
            value: 'b2b',
            label: 'B2B',
            providers: ['indiamart'],
          },
          {
            value: 'c2c',
            label: 'C2C',
            providers: [
              'ebay',
              'mercari',
              'rakuma',
              'enjoei',
              'vinted',
              'craigslist',
              'offerup',
              'poshmark',
              'enjoei_pro',
            ],
          },
          {
            value: 'dating',
            label: 'Dating',
            providers: ['meetme', 'lovoo', 'skout', 'tagged', 'parship', 'eharmony', 'growlr', 'hi5', 'elitepartner'],
          },
          {
            value: 'digital_mailboxes',
            label: 'Digital Mailboxes',
            providers: ['kivra_fi'],
          },
          {
            value: 'driving_schools',
            label: 'Driving Schools',
            providers: ['clickclickdrive', 'superprawojazdy'],
          },
          {
            value: 'general_goods',
            label: 'General Goods',
            providers: [
              'amazon',
              'ebay',
              'mercadolivre',
              'olx',
              'olx_pay',
              'leboncoin',
              'leboncoin_shippable',
              'ebk',
              'real',
              'allegro',
              'aliexpress',
              'skelbiu',
              'kuldnebors',
              'soov',
              'okidoki',
              'alio',
              'kainos',
              'osta',
            ],
          },
          {
            value: 'healthcare',
            label: 'Healthcare',
            providers: ['doximity', 'health.usnews.com'],
          },
          {
            value: 'home_services',
            label: 'Home Services',
            providers: ['angieslist', 'homeadvisor'],
          },
          {
            value: 'industrial',
            label: 'Industrial',
            providers: ['krank', 'boomandbucket'],
          },
          {
            value: 'mobility',
            label: 'Mobility',
            providers: ['gett'],
          },
          {
            value: 'restaurants',
            label: 'Restaurants',
            providers: ['hotpeppergourmet'],
          },
          {
            value: 'services',
            label: 'Services',
            providers: ['offerta', 'servicefinder', 'blocket_services'],
          },
          {
            value: 'realestates',
            label: 'Realestates',
            providers: [
              'apartments.com',
              'ten-x',
              'costar',
              'loopnet',
              'homes.com',
              'str.com',
              'bizbuysell',
              'businessimmo',
              'costarmanager',
              'thomas_daily',
              'bureauxlocaux',
              'belbex',
              'grecam',
              'land.com',
            ],
          },
          {
            value: 'animals',
            label: 'Animals',
            providers: [
              'pets4homes',
              'puppies_co_uk',
              'petify',
              'foreverpuppy',
              'ukpets',
              'bazoeki',
              'tipaw',
              'dieren_plaats',
              'marktplaats_animals',
              'europuppy',
              'cani',
              'animaleamico',
              'expodog',
              'hastlycka',
              'floccs',
              'travera',
              'svehastar',
              'edogs',
              'deine_tierwelt',
              'snautz',
              'petfinder',
              'puppies_com',
              'puppyspot',
              'pets4you',
              'akc',
            ],
          },
        ],
      },
    ],
  },
  payments: {
    additionalFilters: [
      {
        key: 'verticalType',
        entries: [
          {
            value: 'acquiror_processing',
            label: 'Acquiror Processing',
            providers: ['fis', 'fiserv', 'viva.com'],
          },
          {
            value: 'card_issuing',
            label: 'Card Issuing',
            providers: [
              'americanexpress',
              'chase',
              'bankofamerica',
              'citi',
              'capitalone',
              'wellsfargo',
              'barclays',
              'pncbank',
            ],
          },
          {
            value: 'card_networks',
            label: 'Card Networks',
            providers: ['mastercard', 'visa', 'americanexpress', 'discover'],
          },
          {
            value: 'cross_border',
            label: 'Cross-border',
            providers: [
              'wise',
              'paypal',
              'westernunion',
              'remitly',
              'worldremit',
              'revolut',
              'veem',
              'currencycloud',
              'rapyd',
              'remitly',
              'transfermate',
              'bankingcircle',
              'moneycorp',
              'paysafe',
              'alipay',
              'xoom',
              'ria',
              'atlanticmoney',
              'intermex',
              'riamoney',
              'moneygram',
              'remit2india',
              'mdaq',
              'youtrip',
              'youbiz',
              'thunes',
              'terrapay',
              'ripple',
              'vertofx',
              'global-e',
              'zing',
            ],
          },
          {
            value: 'end2end_networks',
            label: 'End-To-End Networks',
            providers: ['paypal', 'wise', 'venmo', 'intermex', 'riamoney', 'remit2india'],
          },
          {
            value: 'ewallets',
            label: 'eWallets',
            providers: ['applepay', 'googlepay', 'samsungpay', 'paypal', 'venmo', 'vivawallet', 'walley'],
          },
          {
            value: 'issuer_processors',
            label: 'Issuer Processors',
            providers: ['marqeta', 'comdata', 'cambridge', 'wex', 'globalprocessingservices'],
          },
          {
            value: 'merchant_acquiring',
            label: 'Merchant Acquiring',
            providers: [
              'adyen',
              'stripe',
              'worldpay',
              'zettle',
              'square',
              'sumup',
              'checkout',
              'worldline',
              'payu',
              'dlocal',
              'yabie',
            ],
          },
          {
            value: 'neo_banks',
            label: 'Neo-banks',
            providers: [
              'revolut',
              'starling',
              'monzo',
              'n26',
              'bambu',
              'mercury',
              'atombank',
              'oaknorth',
              'rocker',
              'tandem',
              'tide',
              'tinkoff',
              'greendot',
              'sofi',
              'moneylion',
              'chime',
              'current',
              'aspiration',
              'varo',
              'monese',
              'starling',
              'wise',
              'sable',
              'juno',
              'eqbank',
              'yapeal',
              'up',
              'papara',
              'nbgpay',
              'finom',
              'skrill',
              'nubank',
              'indo_bank',
            ],
          },
          {
            value: 'open_banking',
            label: 'Open Banking',
            providers: ['gocardless', 'crezco'],
          },
          {
            value: 'pos',
            label: 'POS',
            providers: ['storyous', 'softpay', 'meawallet', 'yabie'],
          },
          {
            value: 'multi_currency_accounts',
            label: 'Multi-Currency Accounts',
            providers: ['airwallex', 'parpera', 'wise', 'nium'],
          },
          {
            value: 'tech_infrastructure_partners',
            label: 'Tech/Infrastructure Partners',
            providers: ['currencycloud', 'plaid', 'finicity', 'gocardless', 'codat'],
          },
          {
            value: 'cross_border_infrastructure',
            label: 'Cross-border infrastructure',
            providers: [
              'nium',
              'bvnk',
              'crownagentsbank',
              'wise_platform',
              'corpay',
              'payoneer',
              'terrapay',
              'thunes',
              'transfermate',
              'ripple',
              'currencycloud',
              'airwallex',
              'dandelionpayments',
              'wise',
              'ria',
              'renpayments',
              'riamoney',
            ],
          },
          {
            value: 'marketplace_payout',
            label: 'Marketplace Payout',
            providers: ['payoneer'],
          },
          {
            value: 'stablecoins',
            label: 'Stablecoins',
            providers: ['circle'],
          },
          {
            value: 'remittances',
            label: 'Remittances',
            providers: [
              'africhange',
              'wise',
              'revolut',
              'westernunion',
              'intermex',
              'moneygram',
              'paypal',
              'remitly',
              'zing',
              'ria',
              'riamoney',
              'xoom',
              'venmo',
              'mandiri',
              'instarem',
            ],
          },
        ],
      },
    ],
  },
  realestatemarketplaces: {},
  realestates: {},
  saas: {
    additionalFilters: [
      {
        key: 'marketSegment',
        entries: [
          {
            value: 'sme',
            label: 'SME',
            providers: [
              'pushpay',
              'admicom',
              'base',
              'cafe24',
              'shopify',
              'kahoot',
              'chatwork',
              'compugroup',
              'anaplan',
              'appfolio',
              'alarm.com',
              'wix',
              'squarespace',
              'freeesign',
              'slick',
              'fresha',
              'matchi',
              'bokadirekt',
              'booksy',
              'booksybiz',
              'hotpepperbeauty',
            ],
          },
          {
            value: 'mid',
            label: 'Mid-market',
            providers: [
              'pushpay',
              'admicom',
              'nemetscheck',
              'cafe24',
              'kahoot',
              'chatwork',
              'compugroup',
              'anaplan',
              'appfolio',
              'realpage',
              'costar',
              'cint',
              'cccportal',
              'cccone',
              'freeesign',
            ],
          },
          {
            value: 'enterprise',
            label: 'Enterprise',
            providers: [
              'ncino',
              'nemetscheck',
              'compugroup',
              'anaplan',
              'appfolio',
              'yardi',
              'realpage',
              'costar',
              'shotspotter',
              'cint',
              'cccportal',
              'cccone',
              'cobee',
            ],
          },
        ],
      },
      {
        key: 'verticalType',
        entries: [
          {
            value: 'banking',
            label: 'Banking',
            providers: ['ncino'],
          },
          {
            value: 'churches',
            label: 'Churches',
            providers: ['pushpay'],
          },
          {
            value: 'construction',
            label: 'Construction',
            providers: ['admicom', 'nemetscheck'],
          },
          {
            value: 'ecommerce',
            label: 'eCommerce',
            providers: ['base', 'cafe24', 'shopify'],
          },
          {
            value: 'e-conveyancing',
            label: 'E-Conveyancing',
            providers: ['pexa', 'pexa_onboarding', 'pexa_tracker', 'lextech', 'sympli'],
          },
          {
            value: 'education',
            label: 'Education',
            providers: ['kahoot'],
          },
          {
            value: 'groupware',
            label: 'Groupware',
            providers: ['chatwork'],
          },
          {
            value: 'medical',
            label: 'Medical',
            providers: ['compugroup'],
          },
          {
            value: 'planning',
            label: 'Planning',
            providers: ['anaplan'],
          },
          {
            value: 'property',
            label: 'Property',
            providers: ['appfolio', 'yardi', 'realpage', 'costar'],
          },
          {
            value: 'security',
            label: 'Security',
            providers: ['shotspotter', 'alarm.com'],
          },
          {
            value: 'surveys',
            label: 'Surveys',
            providers: ['cint'],
          },
          {
            value: 'websites',
            label: 'Websites',
            providers: ['wix', 'squarespace', 'rakentaja', 'lendo'],
          },
          {
            value: 'car_repair',
            label: 'Car Repair',
            providers: ['cccportal', 'cccone'],
          },
          {
            value: 'auction',
            label: 'Auction',
            providers: [
              'bidpath',
              'bidlogix',
              'maxanet',
              'nextlot',
              'skeleton',
              'snoofa',
              'globalauctionplatform',
              'auctionmobility',
              'wavebid',
            ],
          },
          {
            value: 'salons',
            label: 'Salons',
            providers: ['slick', 'fresha', 'matchi', 'bokadirekt', 'booksy', 'booksybiz', 'hotpepperbeauty'],
          },
          {
            value: 'legal',
            label: 'Legal',
            providers: ['dyedurhamdyedurham'],
          },
          {
            value: 'music',
            label: 'Music',
            providers: ['epidemicsound'],
          },
          {
            value: 'hotel_crm',
            label: 'Hotel CRM',
            providers: ['netera'],
          },
          {
            value: 'banking_as_a_service',
            label: 'Banking as a service',
            providers: ['temenos', 'mambu', 'lhv', 'bpc'],
          },
          {
            value: 'hotel_software',
            label: 'Hotel Software',
            providers: ['hotelavailabilities', 'bookonlinenow', 'hoteloncloud'],
          },
          {
            value: 'sports',
            label: 'Sports',
            providers: ['eliteprospects', 'hockeydb'],
          },
          {
            value: 'digital_mailbox',
            label: 'Digital Mailbox',
            providers: ['kivra', 'e-boks', 'billo'],
          },
        ],
      },
    ],
  },
  vod: {},
  databases: {
    additionalFilters: [
      {
        key: 'verticalType',
        entries: [
          {
            value: 'legal',
            label: 'Legal',
            providers: [
              'notisum',
              'dib',
              'bella',
              'echoline',
              'liaisons_sociales',
              'lamyline_notaire',
              'lamyline',
              'aranzadi',
              'karnov',
              'juno_ai',
              'kaila',
            ],
          },
          {
            value: 'sports',
            label: 'Sports',
            providers: ['eliteprospects', 'hockeydb'],
          },
        ],
      },
    ],
  },
}
