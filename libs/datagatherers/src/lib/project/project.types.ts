import { registerEnumType } from '@nestjs/graphql'

import {
  AccountingProject,
  AmpereProject,
  AnimalsProject,
  CarsProject,
  ClassifiedsProject,
  DeliveryProject,
  VodProject,
  ApptopiaProject,
  DataAiProject,
  TrackersProject,
  OxfordDataplanProject,
  SensortowerProject,
} from '../interfaces'

export enum FrontendProjectTypeBase {
  realestatemarketplaces = 'realestatemarketplaces',
  saas = 'saas',
  payments = 'payments',
  databases = 'databases',
}

registerEnumType(FrontendProjectTypeBase, {
  name: 'FrontendProjectTypeBase',
  description: 'The base type of project',
})

export enum ProjectTypeBase {
  similarweb = 'similarweb',

  postalcode = 'postalcode',
  exchangerate = 'exchangerate',

  non_standard = 'non_standard',
}

registerEnumType(ProjectTypeBase, {
  name: 'ProjectTypeBase',
  description: 'The base type of project',
})

export const FrontendProjectType = {
  ...AccountingProject,
  ...AmpereProject,
  ...AnimalsProject,
  ...CarsProject,
  ...ClassifiedsProject,
  ...DeliveryProject,
  ...VodProject,
  ...FrontendProjectTypeBase,
}

export type FrontendProjectType =
  | AccountingProject
  | AmpereProject
  | AnimalsProject
  | CarsProject
  | ClassifiedsProject
  | DeliveryProject
  | VodProject
  | FrontendProjectTypeBase

registerEnumType(FrontendProjectType, {
  name: 'FrontendProjectType',
  description: 'The type of project',
})

export const ProjectType = {
  ...FrontendProjectType,

  ...ApptopiaProject,
  ...DataAiProject,
  ...TrackersProject,
  ...ProjectTypeBase,
  ...OxfordDataplanProject,
  ...SensortowerProject,
}

export type ProjectType =
  | FrontendProjectType
  | ApptopiaProject
  | DataAiProject
  | TrackersProject
  | ProjectTypeBase
  | OxfordDataplanProject
  | SensortowerProject

registerEnumType(ProjectType, {
  name: 'ProjectType',
  description: 'The type of project',
})

export enum ProjectCardType {
  break = 'break',
  title = 'title',

  // Integrations
  apptopiaActiveUsers = 'apptopiaActiveUsers',
  apptopiaDownloads = 'apptopiaDownloads',

  dataAiActiveUsers = 'dataAiActiveUsers',
  dataAiDownloads = 'dataAiDownloads',

  similarWebUsers = 'similarWebUsers',
  similarWebVisits = 'similarWebVisits',
  similarWebPageviews = 'similarWebPageviews',
  similarWebProvider = 'similarWebProvider',

  oxfordDataplanWeeklyVolume = 'oxfordDataplanWeeklyVolume',
  oxfordDataplanMonthlyVolume = 'oxfordDataplanMonthlyVolume',
  oxfordDataplanMonthlyAdRevenue = 'oxfordDataplanMonthlyAdRevenue',
  oxfordDataplanWeeklyMobilityNaa = 'oxfordDataplanWeeklyMobilityNaa',
  oxfordDataplanMonthlyMobilityNaa = 'oxfordDataplanMonthlyMobilityNaa',

  sensortowerActiveUsers = 'sensortowerActiveUsers',
  sensortowerDownloads = 'sensortowerDownloads',

  // Marketplaces
  listings = 'listings',
  newListings = 'newListings',
  gmvCurrent = 'gmvCurrent',
  avgListingPrice = 'avgListingPrice',
  newListingsYearlyProgress = 'newListingsYearlyProgress',

  // Dealers
  dealerPackageTypes = 'dealerPackageTypes',
  dealersAdvertisingIncome = 'dealersAdvertisingIncome',

  // Accounting
  apiIntegrations = 'apiIntegrations',
  partners = 'partners',
  installations = 'installations',
  fiduciaries = 'fiduciaries',
  customers = 'customers',
  users = 'users',
  invoices = 'invoices',
  analyticsTpv = 'analyticsTpv',
  analyticsTpvEur = 'analyticsTpvEur',
  analyticsCustomers = 'analyticsCustomers',
  analyticsTpvPerCustomer = 'analyticsTpvPerCustomer',
  analyticsTpvEurPerCustomer = 'analyticsTpvEurPerCustomer',

  // Ampere
  quarterlyBroadcasterRevenue = 'quarterlyBroadcasterRevenue',
  quarterlyBroadcasterRevenueSplit = 'quarterlyBroadcasterRevenueSplit',
  quarterlyBroadcasterContentSpend = 'quarterlyBroadcasterContentSpend',
  quarterlyBroadcasterContentSpendSplit = 'quarterlyBroadcasterContentSpendSplit',
  totalAdvertisingWW = 'totalAdvertisingWW',
  totalAdvertisingCountry = 'totalAdvertisingCountry',
  totalAdvertisingCountryPercent = 'totalAdvertisingCountryPercent',
  tvAdvertisingWW = 'tvAdvertisingWW',
  displayAdvertisingWW = 'displayAdvertisingWW',
  onlineVideoAdvertisingWW = 'onlineVideoAdvertisingWW',
  mobileVideoAdvertisingWW = 'mobileVideoAdvertisingWW',
  otherDigitalAdvertisingWW = 'otherDigitalAdvertisingWW',
  otherAdvertisingWW = 'otherAdvertisingWW',
  yearlyBroadcasterRevenue = 'yearlyBroadcasterRevenue',
  yearlyBroadcasterRevenuePercent = 'yearlyBroadcasterRevenuePercent',
  yearlyBroadcasterContentSpend = 'yearlyBroadcasterContentSpend',
  yearlyBroadcasterContentSpendPercent = 'yearlyBroadcasterContentSpendPercent',

  // Auctions
  auctionTypes = 'auctionTypes',
  auctionTypesNewListings = 'auctionTypesNewListings',
  historicalAuctions = 'historicalAuctions',
  historicalListings = 'historicalListings',
  historicalSellers = 'historicalSellers',
  historicalThv = 'historicalThv',
  historicalAvgListingPrice = 'historicalAvgListingPrice',
  // historicalAvgInternetFee = 'historicalAvgInternetFee',
  historicalThvAuctionTypes = 'historicalThvAuctionTypes',
  liveBiddingListings = 'liveBiddingListings',
  liveBiddingThv = 'liveBiddingThv',
  stockTypesThv = 'stockTypesThv',
  stockTypesGmv = 'stockTypesGmv',
  biddingGmv = 'biddingGmv',
  providerThv = 'providerThv',
  historicalGmv = 'historicalGmv',
  conversionRate = 'conversionRate',

  // Cars
  agents = 'agents',
  gmvNewListings = 'gmvNewListings',
  packageTypes = 'packageTypes',
  packageTypesNewListings = 'packageTypesNewListings',
  totalRegisteredDealers = 'totalRegisteredDealers',
  newRegisteredDealers = 'newRegisteredDealers',
  totalCarsSold = 'totalCarsSold',
  newCarsSold = 'newCarsSold',
  totalBidCount = 'totalBidCount',
  newBidCount = 'newBidCount',
  totalAvgBidsPerCar = 'totalAvgBidsPerCar',
  priceRange = 'priceRange',
  priceRangeNewListings = 'priceRangeNewListings',
  usedRegistrationYearlyProgress = 'usedRegistrationYearlyProgress',
  newProductionYearlyProgress = 'newProductionYearlyProgress',
  newRegistrationYearlyProgress = 'newRegistrationYearlyProgress',
  listingsYearlyProgress = 'listingsYearlyProgress',
  usedRegistrationYearlyProgressYoY = 'usedRegistrationYearlyProgressYoY',
  newProductionYearlyProgressYoY = 'newProductionYearlyProgressYoY',
  newRegistrationYearlyProgressYoY = 'newRegistrationYearlyProgressYoY',

  // Delivery
  restaurants = 'restaurants',
  groceries = 'groceries',
  avgDeliveryFee = 'avgDeliveryFee',
  avgMinimumOrder = 'avgMinimumOrder',

  // Jobs
  publishers = 'publishers',
  hemnetEmployees = 'hemnetEmployees',
  inventorySectorTypes = 'inventorySectorTypes',
  inventorySectorTypesNew = 'inventorySectorTypesNew',

  // Marketplaces
  sellers = 'sellers',
  gmvSold = 'gmvSold',
  offertaCompanies = 'offertaCompanies',
  offertaQuotes = 'offertaQuotes',
  offertaReviews = 'offertaReviews',
  offertaDailyUsers = 'offertaDailyUsers',
  offertaNewQuotes = 'offertaNewQuotes',

  // Payments
  analyticsCompanies = 'analyticsCompanies',
  analyticsPayments = 'analyticsPayments',
  analyticsAmount = 'analyticsAmount',

  // Real Estate
  adTypeProject = 'adTypeProject',
  adTypeProjectUnit = 'adTypeProjectUnit',
  dealerListingsCountRange = 'dealerListingsCountRange',
  listingsAdvertisingIncome = 'listingsAdvertisingIncome',
  businessSubscriptionsLogoListings = 'businessSubscriptionsLogoListings',
  hemnetAnalyticsGoogle = 'hemnetAnalyticsGoogle',
  hemnetAnalyticsDeviceDistribution = 'hemnetAnalyticsDeviceDistribution',
  hemnetAnalyticsOrvesto = 'hemnetAnalyticsOrvesto',
  crosspostedListings = 'crosspostedListings',
  propertiesSoldYearlyProgress = 'propertiesSoldYearlyProgress',
  propertiesSoldYearlyProgressYoY = 'propertiesSoldYearlyProgressYoY',
  arplYearlyProgress = 'arplYearlyProgress',
  trafficPageYearlyProgress = 'trafficPageYearlyProgress',
  averagePriceYearlyProgress = 'averagePriceYearlyProgress',
  totalListingsYearlyProgress = 'totalListingsYearlyProgress',
  totalNewListingsYearlyProgress = 'totalNewListingsYearlyProgress',
  priceDevelopmentYearlyProgress = 'priceDevelopmentYearlyProgress',
  volumeSoldYearlyProgress = 'volumeSoldYearlyProgress',
  postedForSaleYearlyProgress = 'postedForSaleYearlyProgress',
  avgDaysActive = 'avgDaysActive',
  financialReportsYearlyProgress = 'financialReportsYearlyProgress',
  apartmentsForSaleYearlyProgress = 'apartmentsForSaleYearlyProgress',
  apartmentsComingSoonYearlyProgress = 'apartmentsComingSoonYearlyProgress',
  apartmentsDaysToSellYearlyProgress = 'apartmentsDaysToSellYearlyProgress',
  housesForSaleYearlyProgress = 'housesForSaleYearlyProgress',
  housesComingSoonYearlyProgress = 'housesComingSoonYearlyProgress',
  housesDaysToSellYearlyProgress = 'housesDaysToSellYearlyProgress',
  absViewsPerDayPerListing = 'absViewsPerDayPerListing',
  avgViewsPerDayPerListing = 'avgViewsPerDayPerListing',
  adRanges = 'adRanges',
  adViews = 'adViews',
  squareMeterPriceYearlyProgress = 'squareMeterPriceYearlyProgress',
  adsPublishedYearlyProgress = 'adsPublishedYearlyProgress',
  clicksFirst8DaysYearlyProgress = 'clicksFirst8DaysYearlyProgress',
  avgNotificationsEmailsYearlyProgress = 'avgNotificationsEmailsYearlyProgress',
  notificationsPushYearlyProgress = 'notificationsPushYearlyProgress',
  notificationsAdsYearlyProgress = 'notificationsAdsYearlyProgress',

  // Vod
  marketPenetration = 'marketPenetration',
  consumerMonthlyUsers = 'consumerMonthlyUsers',
  profit = 'profit',
  contentDuration = 'contentDuration',
  contentDurationCategorized = 'contentDurationCategorized',
  countryContentSpend = 'countryContentSpend',
  globalContentSpend = 'globalContentSpend',
  yearlyCompanyContentSpend = 'yearlyCompanyContentSpend',
  yearlyGlobalCompanyContentSpend = 'yearlyGlobalCompanyContentSpend',
  yearlyContentSpend = 'yearlyContentSpend',
  countryRevenue = 'countryRevenue',
  viewingHours1864 = 'viewingHours1864',
  viewingHours1824 = 'viewingHours1824',
  viewingHours5564 = 'viewingHours5564',
  broadcasterContentSold = 'broadcasterContentSold',
  svodSubscriptions = 'svodSubscriptions',
  productionData = 'productionData',
  newProductionData = 'newProductionData',
  popularityScore = 'popularityScore',

  // Real Estates
  avgListingPriceGeoZone = 'avgListingPriceGeoZone',
  packageTypesGeoZone = 'packageTypesGeoZone',
  packageTypesNewListingsGeoZone = 'packageTypesNewListingsGeoZone',

  //crossed package types
  listingsDealerPackageTypes = 'listingsDealerPackageTypes',
  newListingsDealerPackageTypes = 'newListingsDealerPackageTypes',
  avgListingProductTypePrices = 'avgListingProductTypePrices',
  avgNewListingProductTypePrices = 'avgNewListingProductTypePrices',
}

registerEnumType(ProjectCardType, {
  name: 'ProjectCardType',
  description: 'The type of card',
})
