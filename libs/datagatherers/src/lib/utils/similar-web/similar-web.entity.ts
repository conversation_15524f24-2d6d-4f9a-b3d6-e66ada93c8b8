import { ObjectType, Directive, Field, InputType, GraphQLISODateTime } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'

import { Iso3166Alpha2 } from '@datagatherers/core'
import { MongoDBEntity } from '@datagatherers/database'

import { IntegrationSnapshotDataType } from '../../interfaces'
import { ProjectType } from '../../project'

@ObjectType()
@InputType('SimilarWebRangeInput')
export class SimilarWebRange {
  @Field(() => GraphQLISODateTime, { nullable: true })
  @ApiProperty({ type: Date, required: false })
  start?: Date

  @Field(() => GraphQLISODateTime, { nullable: true })
  @ApiProperty({ type: Date, required: false })
  end?: Date
}

@ObjectType()
@InputType('SimilarWebDomainInput')
export class SimilarWebDomain {
  @Field(() => String)
  @ApiProperty({ type: String, required: true })
  url: string

  @Field(() => [IntegrationSnapshotDataType], { nullable: true })
  @ApiProperty({ enum: IntegrationSnapshotDataType, isArray: true, required: false })
  blacklistedDataTypes?: IntegrationSnapshotDataType[]
}

@ObjectType()
@Directive('@key(fields: "_id")')
export class SimilarWeb extends MongoDBEntity {
  @Field(() => ProjectType)
  @ApiProperty({ enum: ProjectType })
  project: ProjectType

  @Field(() => String)
  @ApiProperty({ type: String })
  provider: string

  @Field(() => [Iso3166Alpha2])
  @ApiProperty({ enum: Iso3166Alpha2, isArray: true })
  iso: Iso3166Alpha2[]

  @Field(() => [SimilarWebDomain])
  @ApiProperty({ type: SimilarWebDomain, isArray: true })
  domains: SimilarWebDomain[]

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  webpage: string

  @Field(() => SimilarWebRange, { nullable: true })
  @ApiProperty({ type: SimilarWebRange, required: false })
  range?: SimilarWebRange
}
