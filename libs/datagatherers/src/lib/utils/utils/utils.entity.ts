import { Directive, Field, GraphQLISODateTime, ObjectType, registerEnumType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'

import { MongoDBEntity } from '@datagatherers/database'

// TODO: Deprecated
export enum Region {
  'eu_west_1' = 'eu-west-1',
  'us_east_1' = 'us-east-1',
  'eu_north_1' = 'eu-north-1',
  'eu_west_3' = 'eu-west-3',
  'eu_west_2' = 'eu-west-2',
  'eu_central_1' = 'eu-central-1',
  'sa_east_1' = 'sa-east-1',
  'ap_northeast_2' = 'ap-northeast-2',
  'me_central_1' = 'me-central-1',
}

export enum ConstrainsRegion {
  EU = 'EU',
  Europe = 'Europe',
  NorthAmerica = 'NorthAmerica',
  SouthAmerica = 'SouthAmerica',
  Americas = 'Americas',
  Asia = 'Asia',
  Africa = 'Africa',
  Oceania = 'Oceania',
  Antarctica = 'Antarctica',
  MiddleEast = 'MiddleEast',
}

// TODO: Deprecated
export const oldRegions: {
  [key in Region]: ConstrainsRegion
} = {
  'eu-west-1': ConstrainsRegion.EU,
  'us-east-1': ConstrainsRegion.NorthAmerica,
  'eu-north-1': ConstrainsRegion.EU,
  'eu-west-3': ConstrainsRegion.EU,
  'eu-west-2': ConstrainsRegion.EU,
  'eu-central-1': ConstrainsRegion.EU,
  'sa-east-1': ConstrainsRegion.SouthAmerica,
  'ap-northeast-2': ConstrainsRegion.Asia,
  'me-central-1': ConstrainsRegion.MiddleEast,
}

export enum UtilType {
  schedulerLastRun = 'schedulerLastRun',
}
registerEnumType(UtilType, { name: 'UtilType', description: 'The type of cost' })

@ObjectType()
@Directive('@key(fields: "_id")')
export class Utils extends MongoDBEntity {
  @Field(() => UtilType)
  @ApiProperty({ enum: UtilType })
  type: UtilType

  @Field(() => GraphQLISODateTime)
  @ApiProperty({ type: Date })
  lastRun: Date
}
