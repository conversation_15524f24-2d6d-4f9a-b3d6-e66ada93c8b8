import { startOfISOWeek, addWeeks, parse, startOfMonth } from 'date-fns'

import { defaultConfigs, type Project, type ProjectType } from '../project'

import type {
  CompositionResult,
  CompositionSeries,
  SegmentationResult,
  SegmentationSeries,
  SegmentationSeriesItem,
  SeriesItem,
  StatsResult,
} from './analysis'
import type { IntegrationSnapshotService } from './integration-snapshot.service'
import type { AmpereServices } from './providers/ampere'
import type { SnapshotService } from './snapshot.service'
import type { UtilsServices } from '../utils'

export interface SnapshotsServices {
  snapshot: SnapshotService
  integrationSnapshot: IntegrationSnapshotService
  ampere: AmpereServices
  utils: UtilsServices
}

export const getLimitedProviders = (project: Project, verticalType: string, marketSegment: string) => {
  let verticalProviders
  let marketSegmentProviders

  if (verticalType) {
    verticalProviders = defaultConfigs[project.type]?.additionalFilters
      ?.filter((item) => item.key === 'verticalType')?.[0]
      ?.entries?.filter((item) => item.value === verticalType)?.[0]?.providers
  }

  if (marketSegment) {
    marketSegmentProviders = defaultConfigs[project.type]?.additionalFilters
      ?.filter((item) => item.key === 'marketSegment')?.[0]
      ?.entries?.filter((item) => item.value === marketSegment)?.[0]?.providers
  }

  const result = []
  if (verticalProviders?.length && marketSegmentProviders?.length) {
    result.push(
      ...verticalProviders.filter((vertical) =>
        marketSegmentProviders.some(
          (mktSegment) =>
            vertical === mktSegment ||
            (vertical instanceof RegExp && typeof mktSegment === 'string' && vertical.test(mktSegment)) ||
            (mktSegment instanceof RegExp && typeof vertical === 'string' && mktSegment.test(vertical)) ||
            (vertical instanceof RegExp && mktSegment instanceof RegExp && vertical.source === mktSegment.source),
        ),
      ),
    )
  } else if (verticalProviders?.length) {
    result.push(...verticalProviders)
  } else if (marketSegmentProviders?.length) {
    result.push(...marketSegmentProviders)
  }

  return result
}

export const zerosToNull = <T extends StatsResult | SegmentationResult | CompositionResult>(c: T): T => ({
  ...c,
  series:
    c.series?.map((s: SeriesItem | SegmentationSeries | CompositionSeries) => ({
      ...s,
      data: s.data.map((v: number | SegmentationSeriesItem) => {
        if (typeof v === 'object') {
          v.value = !v.value ? null : v.value
          return v
        }

        return !v ? null : v
      }),
    })) ?? [],
})

export function parseISOWeekYear(dateString?: string) {
  if (!dateString) {
    return startOfISOWeek(new Date())
  }

  // Split the input string by '/'
  const [yearString, weekString] = dateString.split('/')
  const year = parseInt(yearString, 10)
  const week = parseInt(weekString, 10)

  // Create a date representing the start of the ISO week-based year
  const fourthOfJanuary = new Date(Date.UTC(year, 0, 4)) // January 4th of the given year is always in week 1
  const startOfISOYear = startOfISOWeek(fourthOfJanuary)

  // Add the required number of weeks
  const resultDate = addWeeks(startOfISOYear, week - 1)

  return resultDate
}

export function parseMonthStart(dateString?: string) {
  if (!dateString) {
    return startOfMonth(new Date())
  }

  return startOfMonth(parse(dateString, 'yyyy/MM', new Date()))
}

export function getProductTypeIndex(project: ProjectType, provider: string, fieldToCompare: string) {
  if (provider === 'cargurus') {
    return {
      $switch: {
        branches: [
          { case: { $eq: [fieldToCompare, 'Suspended'] }, then: 1 },
          { case: { $eq: [fieldToCompare, 'Enhanced'] }, then: 2 },
          { case: { $eq: [fieldToCompare, 'Featured'] }, then: 3 },
          { case: { $eq: [fieldToCompare, 'Featured Priority'] }, then: 4 },
        ],
        default: 0,
      },
    }
  }
  if (project !== 'jobs' && provider === 'hemnet') {
    return {
      $switch: {
        branches: [
          { case: { $eq: [fieldToCompare, 'BASIC'] }, then: 1 },
          { case: { $eq: [fieldToCompare, 'PLUS'] }, then: 2 },
          { case: { $eq: [fieldToCompare, 'PREMIUM'] }, then: 3 },
          { case: { $eq: [fieldToCompare, 'MAX'] }, then: 4 },
          { case: { $eq: [fieldToCompare, 'ROCKET'] }, then: 5 },
        ],
        default: 0,
      },
    }
  }
  if (project === 'realestates') {
    switch (provider) {
      case 'realestate.com.au':
        return {
          $switch: {
            branches: [
              { case: { $eq: [fieldToCompare, 'standard'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'midtier'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'feature'] }, then: 3 },
              { case: { $eq: [fieldToCompare, 'premiere'] }, then: 4 },
              { case: { $eq: [fieldToCompare, 'luxe'] }, then: 5 },
            ],
            default: 0,
          },
        }
      case 'apartments.com':
      case 'loopnet':
        return {
          $switch: {
            branches: [
              { case: { $eq: [fieldToCompare, 'BASIC'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'BASIC PLUS'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'SILVER'] }, then: 3 },
              { case: { $eq: [fieldToCompare, 'SILVER PLUS'] }, then: 4 },
              { case: { $eq: [fieldToCompare, 'PREMIUM'] }, then: 5 },
              { case: { $eq: [fieldToCompare, 'PREMIUM PLUS'] }, then: 6 },
              { case: { $eq: [fieldToCompare, 'GOLD'] }, then: 7 },
              { case: { $eq: [fieldToCompare, 'GOLD PLUS'] }, then: 8 },
              { case: { $eq: [fieldToCompare, 'DIAMOND'] }, then: 9 },
              { case: { $eq: [fieldToCompare, 'DIAMOND PLUS'] }, then: 10 },
            ],
            default: 0,
          },
        }
      case 'finn.no':
        return {
          $switch: {
            branches: [
              { case: { $eq: [fieldToCompare, 'small'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'medium'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'large'] }, then: 3 },
            ],
            default: 0,
          },
        }
      case 'homegate':
      case 'immoscout24':
        return {
          $switch: {
            branches: [
              { case: { $eq: [fieldToCompare, 'Basic'] }, then: 0 },
              /// Listing productTypes
              { case: { $eq: [fieldToCompare, 'STANDARD'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'TOP'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'PREMIUM'] }, then: 3 },
              /// Agent productTypes
              { case: { $eq: [fieldToCompare, 'Starter'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'Experienced'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'Professional'] }, then: 3 },
              { case: { $eq: [fieldToCompare, 'Expert'] }, then: 4 },

              { case: { $eq: [fieldToCompare, 'RE Welcome'] }, then: 5 },
              { case: { $eq: [fieldToCompare, 'New Construction'] }, then: 6 },

              //{ case: { $eq: [fieldToCompare, 'Expert'] }, then: 8 },
            ],
            default: 10,
          },
        }
      case 'aruodas':
        return {
          $switch: {
            branches: [
              /// Agent productTypes
              { case: { $eq: [fieldToCompare, 'basic'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'basic extra'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'silver'] }, then: 3 },
              { case: { $eq: [fieldToCompare, 'silver extra'] }, then: 4 },
              { case: { $eq: [fieldToCompare, 'gold'] }, then: 5 },
              { case: { $eq: [fieldToCompare, 'gold extra'] }, then: 6 },
              { case: { $eq: [fieldToCompare, 'platinum'] }, then: 7 },
              { case: { $eq: [fieldToCompare, 'platinum extra'] }, then: 8 },
              { case: { $eq: [fieldToCompare, 'vip'] }, then: 9 },
              { case: { $eq: [fieldToCompare, 'vip extra'] }, then: 10 },
              { case: { $eq: [fieldToCompare, 'vip ultra'] }, then: 11 },
            ],
            default: 0,
          },
        }
      case 'oikotie':
        return {
          $switch: {
            branches: [
              { case: { $eq: [fieldToCompare, 'small'] }, then: 1 },
              { case: { $eq: [fieldToCompare, 'medium'] }, then: 2 },
              { case: { $eq: [fieldToCompare, 'large'] }, then: 3 },
              { case: { $eq: [fieldToCompare, 'large+plus'] }, then: 4 },
              { case: { $eq: [fieldToCompare, 'plus'] }, then: 5 },
              { case: { $eq: [fieldToCompare, 'loisto'] }, then: 6 },
            ],
            default: 0,
          },
        }
    }
  }
  if (provider === 'blocket') {
    return {
      $switch: {
        branches: [
          { case: { $eq: [fieldToCompare, 'Basic'] }, then: 1 },
          { case: { $eq: [fieldToCompare, 'Plus+Premium'] }, then: 2 },
        ],
        default: 0,
      },
    }
  }
  if (provider === 'autoscout24') {
    return {
      $switch: {
        branches: [
          { case: { $eq: [fieldToCompare, 'Private'] }, then: 1 },
          { case: { $eq: [fieldToCompare, 'Basic'] }, then: 2 },
          { case: { $eq: [fieldToCompare, 'Professional'] }, then: 3 },
          { case: { $eq: [fieldToCompare, 'Professional Plus'] }, then: 4 },

          { case: { $eq: [fieldToCompare, 'Plus'] }, then: 5 },
          { case: { $eq: [fieldToCompare, 'Premium'] }, then: 6 },
          { case: { $eq: [fieldToCompare, 'Unlimited'] }, then: 7 },
        ],
        default: 0,
      },
    }
  }
  return undefined
}
