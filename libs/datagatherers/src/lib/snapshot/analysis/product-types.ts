import { Field, InputType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { startOfISOWeek, subYears, isBefore, startOfMonth, isAfter, addWeeks, addMonths } from 'date-fns'
import { Document } from 'mongodb'

import { Iso3166Alpha2 } from '@datagatherers/core'

import { StatsResult } from './stats'
import { VehicleType, DealerType, RealEstatesBusinessType, TimeUnit, RealEstatesPropertyScope } from '../../interfaces'
import { Project, ProjectType, scopeFilter } from '../../project'
import { dateFnsUtils } from '../date-fns.utils'
import { getProductTypeIndex, zerosToNull } from '../utils'

@InputType()
export class ProductTypesFilter {
  @Field(() => TimeUnit)
  @ApiProperty({ enum: TimeUnit })
  timeUnit: TimeUnit

  @Field(() => Iso3166Alpha2)
  @ApiProperty({ enum: Iso3166Alpha2 })
  iso: Iso3166Alpha2

  @Field(() => String)
  @ApiProperty({ type: String })
  dataType: string

  @Field(() => String)
  @ApiProperty({ type: String })
  provider: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  state?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isNew?: boolean

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  stockType?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isHistorical?: boolean

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isPercent?: boolean

  @Field(() => VehicleType, { nullable: true })
  @ApiProperty({ enum: VehicleType, required: false })
  vehicleType?: VehicleType

  @Field(() => DealerType, { nullable: true })
  @ApiProperty({ enum: DealerType, required: false })
  dealerType?: DealerType

  @Field(() => RealEstatesBusinessType, { nullable: true })
  @ApiProperty({ enum: RealEstatesBusinessType, required: false })
  businessType?: RealEstatesBusinessType

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  geoZone?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isCrossed?: boolean

  @Field(() => RealEstatesPropertyScope, { nullable: true })
  @ApiProperty({ enum: RealEstatesPropertyScope, required: false })
  propertyScope?: RealEstatesPropertyScope
}

export const productTypes = {
  aggregation: async (project: Project, filter: ProductTypesFilter) => {
    const productTypeIndex = getProductTypeIndex(
      project.type === ProjectType.realestatemarketplaces ? ProjectType.realestates : project.type,
      filter.provider,
      filter.isCrossed ? '$stats.crossedProductType' : '$stats.productType',
    )
    return [
      {
        $match: scopeFilter(project, {
          project: project.type === ProjectType.realestatemarketplaces ? ProjectType.realestates : project.type,
          snapTimeUnit: filter.timeUnit,
          iso: filter.iso,
          provider: filter.provider,
          dataType: filter.dataType,
          ...(filter.isHistorical && { isHistorical: filter.isHistorical }),
        }),
      },
      {
        $sort: {
          updatedAt: -1,
        },
      },
      {
        $group: {
          _id: {
            project: '$project',
            provider: '$provider',
            iso: '$iso',
            dataType: '$dataType',
            timeFrame: '$snapTime',
          },
          stats: { $first: '$stats' },
        },
      },
      {
        $unwind: {
          path: '$stats',
          includeArrayIndex: 'statsIndex',
        },
      },
      {
        $match: {
          ...(!filter.isCrossed && { 'stats.productType': { $ne: null } }),
          ...(filter.isCrossed && { 'stats.crossedProductType': { $ne: null } }),
          // ...(!['cars', 'realestates'].includes(project.type) && {
          //   'stats.dealerType': project.type === 'jobs' ? 'company' : 'dealer',
          // }),
          // ...(//['cars', 'realestates'].includes(project.type) &&
          // filter.dealerType && { 'stats.dealerType': filter.dealerType }),
          ...(filter.state && { 'stats.state': filter.state }),
          ...(filter.isNew && { 'stats.isNew': filter.isNew }),
          ...(filter.stockType && { 'stats.stockType': filter.stockType }),
          ...(filter.vehicleType && { 'stats.vehicleType': filter.vehicleType }),
          ...(filter.businessType && { 'stats.businessType': filter.businessType }),
          ...(filter.geoZone && { 'stats.geoZone': filter.geoZone }),
          ...(filter.dealerType && { 'stats.dealerType': filter.dealerType }),
          ...(filter.propertyScope && { 'stats.propertyScope': filter.propertyScope }),
        },
      },
      {
        $group: {
          _id: {
            timeFrame: '$_id.timeFrame',
            provider: '$_id.provider',
            iso: '$_id.iso',
            dataType: '$_id.dataType',
            productType: '$stats.productType',
            crossedProductType: '$stats.crossedProductType',
            ...(productTypeIndex && { productTypeIndex }),
          },
          count: {
            $sum: '$stats.count',
          },
        },
      },
      {
        $sort: {
          ...(productTypeIndex !== undefined && { '_id.productTypeIndex': 1 }),
          ...(productTypeIndex === undefined && !filter.isCrossed && { '_id.productType': 1 }),
          ...(productTypeIndex === undefined && filter.isCrossed && { '_id.crossedProductType': 1 }),
        },
      },
      {
        $group: {
          _id: {
            timeFrame: '$_id.timeFrame',
            provider: '$_id.provider',
            iso: '$_id.iso',
            dataType: '$_id.dataType',
          },
          data: {
            $push: {
              productType: '$_id.productType',
              crossedProductType: '$_id.crossedProductType',
              productTypeIndex: '$_id.productTypeIndex',
              count: '$count',
            },
          },
          total: {
            $sum: '$count',
          },
        },
      },
      {
        $sort: {
          '_id.provider': 1,
        },
      },
      {
        $unwind: {
          path: '$data',
          includeArrayIndex: 'dataIndex',
        },
      },
      {
        $group: {
          _id: {
            iso: '$_id.iso',
            provider: '$_id.provider',
          },
          data: {
            $push: {
              timeFrame: '$_id.timeFrame',
              provider: '$_id.provider',
              iso: '$_id.iso',
              productType: '$data.productType',
              crossedProductType: '$data.crossedProductType',
              count: '$data.count',
              total: '$total',
              percent: {
                $cond: {
                  if: { $eq: ['$total', 0] },
                  then: 0,
                  else: { $divide: [{ $multiply: ['$data.count', 100] }, '$total'] },
                },
              },
            },
          },
          startTime: {
            $min: '$_id.timeFrame',
          },
          endTime: {
            $max: '$_id.timeFrame',
          },
          outputGroupCount: {
            $sum: '$count',
          },
          count: {
            $sum: 1,
          },
        },
      },
      {
        $sort: {
          '_id.provider': 1,
          count: -1,
        },
      },
    ]
  },
  format(items: Document[], _project: Project, filter: ProductTypesFilter) {
    return zerosToNull(
      format(items[0], filter.isCrossed ? 'crossedProductType' : 'productType', filter.timeUnit, filter.isPercent),
    )
  },
}

const format = (item: Document, groupBy: string, timeUnit: TimeUnit, isPercent: boolean): StatsResult => {
  const provider = item._id.provider as string
  let startDate: Date
  let endDate: Date
  if (timeUnit === TimeUnit.weeks) {
    endDate = dateFnsUtils.searchWeek(true)
    startDate = startOfISOWeek(subYears(new Date(), 3))
    const itemStartDate = dateFnsUtils.searchWeek(true, item.startTime)
    if (!isBefore(itemStartDate, startDate)) {
      startDate = itemStartDate
    }
  } else {
    endDate = dateFnsUtils.searchMonth(true)
    startDate = startOfMonth(subYears(new Date(), 3))
    const itemStartDate = dateFnsUtils.searchMonth(true, item.startTime)
    if (!isBefore(itemStartDate, startDate)) {
      startDate = itemStartDate
    }
  }

  const interval = []
  for (
    let dateVar = startDate;
    !isAfter(dateVar, endDate);
    dateVar = timeUnit === TimeUnit.weeks ? addWeeks(dateVar, 1) : addMonths(dateVar, 1)
  ) {
    interval.push(dateFnsUtils.format(dateVar, timeUnit))
  }

  const series = item.data.reduce((acc, curr) => {
    const index = interval.indexOf(curr.timeFrame)

    if (acc.find((el) => el.name === curr[groupBy])) {
      return acc.map((el) => {
        if (el.name === curr[groupBy]) {
          return {
            ...el,
            data: el.data.map((d, i) => (i !== index ? d : isPercent ? d + curr.percent : d + curr.count)),
          }
        } else {
          return el
        }
      })
    } else {
      return [
        ...acc,
        {
          name: curr[groupBy],
          type: 'bar',
          stack: 'stacked',
          barWidth: '60%',
          iso: curr.iso,
          data: [...Array(interval.length)].map((_x, i) => (i !== index ? 0 : isPercent ? curr.percent : curr.count)),
        },
      ]
    }
  }, [])

  return {
    interval,
    series: series.map((serie) => {
      if (!serie.name) {
        serie.name = 'Unknown'
      } else {
        switch (serie.name) {
          case 'STANDARD': {
            if (provider === 'homegate' || provider === 'immoscout24') {
              serie.name = 'Classic'
            } else {
              serie.name = 'Standard'
            }
            break
          }
          case 'TOPLISTING_5_DAYS': {
            serie.name = 'Toplisting 5 Days'
            break
          }
          case 'PAID_REPUBLISH': {
            serie.name = 'Paid Republish'
            break
          }
          default: {
            serie.name = serie.name.toLowerCase().replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
          }
        }
      }
      return serie
    }),
    iso: item._id.iso,
    provider,
    // productType: item._id.productType,
    count: item.count2,
    timeUnit,
    isPercent,
  }
}
