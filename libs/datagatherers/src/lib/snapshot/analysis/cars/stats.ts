import { Field, InputType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import {
  addMonths,
  addWeeks,
  endOfYear,
  isAfter,
  isBefore,
  startOfISOWeek,
  startOfMonth,
  startOfYear,
  subYears,
  format as dateFnsFormat,
} from 'date-fns'
import { Document } from 'mongodb'

import { Iso3166Alpha2 } from '@datagatherers/core'

import { CarsStockType, VehicleType, ClassifiedsDataType, DealerType, TimeUnit } from '../../../interfaces'
import { Project, scopeFilter } from '../../../project'
import { dateFnsUtils } from '../../date-fns.utils'
import { DataType } from '../../snapshot.interface'
import { zerosToNull } from '../../utils'
import { StatsResult } from '../stats'

@InputType()
export class CarsStatsFilter {
  @Field(() => TimeUnit)
  @ApiProperty({ enum: TimeUnit })
  timeUnit: TimeUnit

  @Field(() => Iso3166Alpha2)
  @ApiProperty({ enum: Iso3166Alpha2 })
  iso: Iso3166Alpha2

  @Field(() => ClassifiedsDataType, { nullable: true })
  @ApiProperty({ enum: ClassifiedsDataType, required: false })
  dataType?: ClassifiedsDataType

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  provider?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  chartName?: string

  @Field(() => CarsStockType, { nullable: true })
  @ApiProperty({ enum: CarsStockType, required: false })
  stockType?: CarsStockType

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isPaying?: boolean

  @Field(() => DealerType, { nullable: true })
  @ApiProperty({ enum: DealerType, required: false })
  dealerType?: DealerType

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  state?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isNew?: boolean

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isSold?: boolean

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  hasPrice?: boolean

  @Field(() => VehicleType, { nullable: true })
  @ApiProperty({ enum: VehicleType, required: false })
  vehicleType?: VehicleType
}

export const carsStats = {
  aggregation: async (project: Project, filter: CarsStatsFilter) => {
    return [
      {
        $match: scopeFilter(project, {
          project: project.type,
          ...(filter.iso && { iso: filter.iso }),
          snapTimeUnit: filter.timeUnit,
          ...(filter.dataType && { dataType: filter.dataType }),
          ...(filter.provider && { provider: filter.provider }),
        }),
      },
      {
        $sort: {
          updatedAt: -1,
        },
      },
      {
        $group: {
          _id: {
            project: '$project',
            provider: '$provider',
            iso: '$iso',
            dataType: '$dataType',
            timeFrame: '$snapTime',
          },
          stats: { $first: '$stats' },
        },
      },
      {
        $unwind: {
          path: '$stats',
          includeArrayIndex: 'statsIndex',
        },
      },
      {
        $match: {
          ...(filter.stockType && { 'stats.stockType': filter.stockType }),
          ...(filter.isPaying && { 'stats.isPaying': filter.isPaying }),
          ...(filter.dealerType && { 'stats.dealerType': filter.dealerType }),
          ...(filter.state && { 'stats.state': filter.state }),
          ...(filter.isNew && { 'stats.isNew': filter.isNew }),
          ...(filter.isSold && { 'stats.isSold': filter.isSold }),
          ...(filter.hasPrice && { 'stats.hasPrice': filter.hasPrice }),
          ...(filter.vehicleType && {
            ...(filter.dataType === DataType.inventory && { 'stats.vehicleType': filter.vehicleType }),
            ...(filter.dataType === DataType.dealers && { 'stats.vehicleTypes': filter.vehicleType }),
          }),
        },
      },
      {
        $group: {
          _id: {
            timeFrame: '$_id.timeFrame',
            provider: '$_id.provider',
            ...(filter.iso && { iso: '$_id.iso' }),
            dataType: '$_id.dataType',
          },
          count: {
            $sum: '$stats.count',
          },
          gmvCurrent: {
            $sum: '$stats.gmvCurrent',
          },
          gmvSold: {
            $sum: '$stats.gmvSold',
          },
        },
      },
      {
        $sort: {
          '_id.provider': 1,
        },
      },
      {
        $group: {
          _id: {
            iso: '$_id.iso',
          },
          data: {
            $push: {
              timeFrame: '$_id.timeFrame',
              provider: '$_id.provider',
              iso: '$_id.iso',
              ...(!filter.iso && { iso: 'WW' }),
              count: '$count',
              gmvCurrent: '$gmvCurrent',
              gmvSold: '$gmvSold',
              avgListingPrice: {
                $cond: {
                  if: { $eq: ['$count', 0] },
                  then: 0,
                  else: { $divide: ['$gmvCurrent', '$count'] },
                },
              },
              year: { $arrayElemAt: [{ $split: ['$_id.timeFrame', '/'] }, 0] },
              partOfYear: { $arrayElemAt: [{ $split: ['$_id.timeFrame', '/'] }, 1] },
            },
          },
          startTime: {
            $min: '$_id.timeFrame',
          },
          endTime: {
            $max: '$_id.timeFrame',
          },
        },
      },
      {
        $sort: {
          _id: 1,
        },
      },
    ]
  },
  format(items: Document[], _project: Project, filter: CarsStatsFilter) {
    return zerosToNull(
      format(items[0], filter.chartName === 'yearlyProgress' ? 'year' : 'provider', filter.timeUnit, filter.chartName),
    )
  },
}

const format = (item: Document, groupBy: string, timeUnit: TimeUnit, chartName: string): StatsResult => {
  let dateFnsFormatString = timeUnit === TimeUnit.weeks ? 'RRRR/II' : 'yyyy/MM'
  if (chartName === 'yearlyProgress') {
    dateFnsFormatString = timeUnit === TimeUnit.weeks ? 'II' : 'MM'
  }

  let startDate: Date
  let endDate: Date
  if (chartName === 'yearlyProgress') {
    startDate = startOfYear(new Date())
    endDate = endOfYear(new Date())
  } else {
    if (timeUnit === TimeUnit.weeks) {
      endDate = dateFnsUtils.searchWeek(true)
      startDate = startOfISOWeek(subYears(new Date(), 3))
      const itemStartDate = dateFnsUtils.searchWeek(true, item.startTime)
      if (!isBefore(itemStartDate, startDate)) {
        startDate = itemStartDate
      }
    } else {
      endDate = dateFnsUtils.searchMonth(true)
      startDate = startOfMonth(subYears(new Date(), 3))
      const itemStartDate = dateFnsUtils.searchMonth(true, item.startTime)
      if (!isBefore(itemStartDate, startDate)) {
        startDate = itemStartDate
      }
    }
  }

  const interval = []
  for (
    let dateVar = startDate;
    !isAfter(dateVar, endDate);
    dateVar = timeUnit === TimeUnit.weeks ? addWeeks(dateVar, 1) : addMonths(dateVar, 1)
  ) {
    interval.push(dateFnsFormat(dateVar, dateFnsFormatString))
  }

  const series = item.data.reduce((acc, curr) => {
    const index = interval.indexOf(chartName === 'yearlyProgress' ? curr.partOfYear : curr.timeFrame)
    if (acc.find((el) => el.name === curr[groupBy])) {
      return acc.map((el) => {
        if (el.name === curr[groupBy]) {
          return {
            ...el,
            data: el.data.map((d, i) => {
              if (i === index) {
                if (chartName === 'avgListingPrice') {
                  return d + curr.avgListingPrice
                } else if (chartName === 'gmvCurrent') {
                  return d + curr.gmvCurrent
                } else if (chartName === 'gmvSold') {
                  return d + curr.gmvSold
                } else {
                  return d + curr.count
                }
              } else {
                return d
              }
            }),
          }
        } else {
          return el
        }
      })
    } else {
      return [
        ...acc,
        {
          name: curr[groupBy],
          type: 'line',
          ...(curr.iso && { iso: curr.iso }),
          data: [...Array(interval.length)].map((_x, i) => {
            if (i === index) {
              if (chartName === 'avgListingPrice') {
                return curr.avgListingPrice
              } else if (chartName === 'gmvCurrent') {
                return curr.gmvCurrent
              } else if (chartName === 'gmvSold') {
                return curr.gmvSold
              } else {
                return curr.count
              }
            } else {
              return 0
            }
          }),
        },
      ]
    }
  }, [])
  return {
    interval,
    series: series.map((serie) => {
      if (serie.name) serie.name = serie.name.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
      return serie
    }),
    iso: item._id.iso,
    provider: item._id.provider,
    count: item.count2,
    timeUnit,
    ...(chartName === 'yearlyProgress' && { isYearlyProgress: true }),
  }
}
