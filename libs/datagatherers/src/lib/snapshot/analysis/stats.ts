import { Field, InputType, ObjectType, registerEnumType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { addMonths, addWeeks, isAfter, isBefore, startOfISOWeek, startOfMonth, subYears } from 'date-fns'
import { Document } from 'mongodb'

import { Iso3166Alpha2, MarkArea, MarkLine, MarkPoint } from '@datagatherers/core'

import { VehicleType, TimeUnit } from '../../interfaces'
import { Project, ProjectType, scopeFilter } from '../../project'
import { dateFnsUtils } from '../date-fns.utils'
import { DataType } from '../snapshot.interface'
import { getLimitedProviders, zerosToNull } from '../utils'

export enum AnalysisChartType {
  line = 'line',
  bar = 'bar',
  scatter = 'scatter',
  pie = 'pie',
  // radar = 'radar',
  // map = 'map',
  // tree = 'tree',
  // treemap = 'treemap',
  // graph = 'graph',
  // gauge = 'gauge',
  // funnel = 'funnel',
  // parallel = 'parallel',
  // sankey = 'sankey',
  boxplot = 'boxplot',
  // candlestick = 'candlestick',
  // effectScatter = 'effectScatter',
  // lines = 'lines',
  // heatmap = 'heatmap',
  // pictorialBar = 'pictorialBar',
  // themeRiver = 'themeRiver',
  // sunburst = 'sunburst',
}

registerEnumType(AnalysisChartType, {
  name: 'AnalysisChartType',
  description: 'The type of chart',
})

@InputType()
export class StatsFilter {
  @Field(() => TimeUnit)
  @ApiProperty({ enum: TimeUnit })
  timeUnit: TimeUnit

  @Field(() => Iso3166Alpha2)
  @ApiProperty({ enum: Iso3166Alpha2 })
  iso: Iso3166Alpha2

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  dataType?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  stockType?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isPaying?: boolean

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  dealerType?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  metro?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  webpage?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  listingCountRange?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  platform?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  device?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  stockTypes?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  verticalType?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isNew?: boolean

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  submetro?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  productType?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  state?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  marketSegment?: string

  @Field(() => VehicleType, { nullable: true })
  @ApiProperty({ enum: VehicleType, required: false })
  vehicleType?: VehicleType

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isPercent?: boolean

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  crossedProductType?: string
}

@ObjectType()
export class SeriesItem {
  @Field(() => String)
  @ApiProperty({ type: String })
  name: string

  @Field(() => AnalysisChartType)
  @ApiProperty({ enum: AnalysisChartType })
  type: AnalysisChartType

  @Field(() => Iso3166Alpha2)
  @ApiProperty({ enum: Iso3166Alpha2 })
  iso: string

  @Field(() => [Number], { nullable: 'items' })
  @ApiProperty({ type: Number, isArray: true })
  data: number[]

  @Field(() => Number, { nullable: true })
  @ApiProperty({ type: Number, required: false })
  yAxisIndex?: number

  @Field(() => MarkPoint, { nullable: true })
  @ApiProperty({ type: MarkPoint, required: false })
  markPoint?: MarkPoint

  @Field(() => MarkLine, { nullable: true })
  @ApiProperty({ type: MarkLine, required: false })
  markLine?: MarkLine

  @Field(() => MarkArea, { nullable: true })
  @ApiProperty({ type: MarkArea, required: false })
  markArea?: MarkArea

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  stack?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  barWidth?: string
}

@ObjectType()
export class StatsResult {
  @Field(() => [String])
  @ApiProperty({ type: String, isArray: true })
  interval: string[]

  @Field(() => [SeriesItem])
  @ApiProperty({ type: SeriesItem, isArray: true })
  series: SeriesItem[]

  @Field(() => Iso3166Alpha2)
  @ApiProperty({ enum: Iso3166Alpha2 })
  iso: Iso3166Alpha2

  @Field(() => TimeUnit, { nullable: true })
  @ApiProperty({ enum: TimeUnit, required: false })
  timeUnit?: TimeUnit

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  provider?: string

  @Field(() => Number, { nullable: true })
  @ApiProperty({ type: Number, required: false })
  count?: number

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isPercent?: boolean

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isPercentSecondary?: boolean

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  title?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  subtitle?: string

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  keepXLabeling?: boolean
}

export const stats = {
  aggregation: async (project: Project, filter: StatsFilter) => {
    const limitedProviders = getLimitedProviders(project, filter.verticalType, filter.marketSegment)
    return [
      {
        $match: scopeFilter(project, {
          project: project.type === ProjectType.realestatemarketplaces ? ProjectType.realestates : project.type,
          ...(filter.iso && { iso: filter.iso }),
          snapTimeUnit: filter.timeUnit,
          ...(filter.dataType && { dataType: filter.dataType }),
          ...(filter.webpage && { webpage: filter.webpage }),
          ...((limitedProviders.length !== 0 || (filter.verticalType && filter.marketSegment)) && {
            provider: { $in: limitedProviders },
          }),
        }),
      },
      {
        $sort: {
          updatedAt: -1,
        },
      },
      {
        $group: {
          _id: {
            project: '$project',
            provider: '$provider',
            iso: '$iso',
            dataType: '$dataType',
            timeFrame: '$snapTime',
            webpage: '$webpage',
          },
          stats: { $first: '$stats' },
        },
      },
      {
        $unwind: {
          path: '$stats',
          includeArrayIndex: 'statsIndex',
        },
      },
      {
        $match: {
          ...(filter.stockType &&
            (filter.stockType !== 'used' || project.type !== 'cars') && { 'stats.stockType': filter.stockType }),
          ...(filter.stockType &&
            filter.stockType === 'used' &&
            project.type === 'cars' && { 'stats.stockType': { $ne: 'new' } }),
          // ...(!stockType &&
          //   dataType !== 'annie-data' && { 'stats.stockType': { $nin: ['downloads', 'active_users'] } }),
          ...(filter.isPaying && { 'stats.isPaying': filter.isPaying }),
          ...(filter.dealerType && { 'stats.dealerType': filter.dealerType }),
          ...(filter.metro && { 'stats.metro': filter.metro }),
          ...(filter.listingCountRange && { 'stats.listingCountRange': filter.listingCountRange }),
          ...(filter.platform && { 'stats.platform': filter.platform }),
          ...(filter.device && { 'stats.device': filter.device }),
          ...(filter.stockTypes && { 'stats.stockTypes': filter.stockTypes }),
          ...(filter.isNew && { 'stats.isNew': filter.isNew }),
          ...(filter.submetro && { 'stats.submetro': filter.submetro }),
          ...(filter.productType && { 'stats.productType': filter.productType }),
          ...(filter.state && { 'stats.state': filter.state }),
          ...(filter.vehicleType && {
            ...(filter.dataType === DataType.inventory && { 'stats.vehicleType': filter.vehicleType }),
            ...(filter.dataType === DataType.dealers && { 'stats.vehicleTypes': filter.vehicleType }),
          }),
        },
      },
      {
        $group: {
          _id: {
            timeFrame: '$_id.timeFrame',
            provider: '$_id.provider',
            ...(filter.iso && { iso: '$_id.iso' }),
            dataType: '$_id.dataType',
            webpage: '$_id.webpage',
          },
          count: {
            $sum: '$stats.count',
          },
        },
      },
      {
        $sort: {
          '_id.provider': 1,
        },
      },
      {
        $group: {
          _id: {
            iso: '$_id.iso',
          },
          data: {
            $push: {
              timeFrame: '$_id.timeFrame',
              provider: '$_id.provider',
              iso: '$_id.iso',
              ...(!filter.iso && { iso: 'WW' }),
              count: '$count',
            },
          },
          startTime: {
            $min: '$_id.timeFrame',
          },
          endTime: {
            $max: '$_id.timeFrame',
          },
          outputGroupCount: {
            $sum: '$count',
          },
          count: {
            $sum: 1,
          },
        },
      },
      {
        $sort: {
          _id: 1,
          count: -1,
        },
      },
    ]
  },
  format(items: Document[], project: Project, filter: StatsFilter) {
    return zerosToNull(format(items[0], 'provider', project, filter.timeUnit, filter.isPercent))
  },
}

const format = (
  item: Document,
  groupBy: string,
  project: Project,
  timeUnit: TimeUnit,
  isPercent: boolean,
): StatsResult => {
  let startDate: Date
  let endDate: Date
  if (timeUnit === TimeUnit.weeks) {
    endDate = dateFnsUtils.searchWeek(true)
    if (project.type === 'cars' && item.data.iso === 'US') {
      startDate = dateFnsUtils.searchWeek(true, '2019/52')
    } else {
      startDate = startOfISOWeek(subYears(new Date(), 3))
      const itemStartDate = dateFnsUtils.searchWeek(true, item.startTime)
      if (!isBefore(itemStartDate, startDate)) {
        startDate = itemStartDate
      }
    }
  } else {
    endDate = dateFnsUtils.searchMonth(true)
    startDate = startOfMonth(subYears(new Date(), 3))
    const itemStartDate = dateFnsUtils.searchMonth(true, item.startTime)
    if (!isBefore(itemStartDate, startDate)) {
      startDate = itemStartDate
    }
  }

  const interval = []
  for (
    let dateVar = startDate;
    !isAfter(dateVar, endDate);
    dateVar = timeUnit === TimeUnit.weeks ? addWeeks(dateVar, 1) : addMonths(dateVar, 1)
  ) {
    interval.push(dateFnsUtils.format(dateVar, timeUnit))
  }

  const series = item.data.reduce((acc, curr, _i) => {
    const index = interval.indexOf(curr.timeFrame)
    if (acc.find((el) => el.name === curr[groupBy])) {
      return acc.map((el) => {
        if (el.name === curr[groupBy]) {
          return {
            ...el,
            data: el.data.map((d, i) => (i === index ? d + curr.count : d)),
          }
        } else {
          return el
        }
      })
    } else {
      return [
        ...acc,
        {
          name: curr[groupBy],
          type: 'line',
          ...(curr.iso && { iso: curr.iso }),
          data: [...Array(interval.length)].map((_x, i) => (i === index ? curr.count : 0)),
        },
      ]
    }
  }, [])

  return {
    interval,
    series,
    iso: item._id.iso,
    provider: item._id.provider,
    count: item.count2,
    timeUnit,
    isPercent,
  }
}
