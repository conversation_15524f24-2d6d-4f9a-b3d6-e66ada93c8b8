import { ObjectType, Directive, Field } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'

import { JSONObjectScalar } from '@datagatherers/core'

import { ClassifiedsDealerItem, ClassifiedsListingItem, Item } from './item.entity'
import { CarsStockType, VehicleType, ClassifiedsProject } from '../interfaces'

@ObjectType({
  implements: () => [Item],
})
@Directive('@key(fields: "_id")')
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class CarsInventoryItem<T extends object = any> extends ClassifiedsListingItem<T> {
  @Field(() => ClassifiedsProject)
  @ApiProperty({ enum: ClassifiedsProject })
  project: ClassifiedsProject.cars

  @Field(() => VehicleType)
  @ApiProperty({ enum: VehicleType })
  vehicleType: VehicleType

  // @Field(() => Number, { nullable: true })
  // @ApiProperty({ type: Number, required: false })
  // price?: number

  @Field(() => CarsStockType, { nullable: true })
  @ApiProperty({ enum: CarsStockType, required: false })
  stockType?: CarsStockType

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isFeatured?: boolean

  @Field(() => JSONObjectScalar, { nullable: true })
  @ApiProperty({ type: JSONObjectScalar, required: false })
  featuredTypes?: Record<string, unknown>[]

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  vin?: string

  static type = 'mongodb'
}

@ObjectType({
  implements: () => [Item],
})
@Directive('@key(fields: "_id")')
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class CarsDealerItem<T extends object = any> extends ClassifiedsDealerItem<T> {
  @Field(() => ClassifiedsProject)
  @ApiProperty({ enum: ClassifiedsProject })
  project: ClassifiedsProject.cars

  @Field(() => Boolean, { nullable: true })
  @ApiProperty({ type: Boolean, required: false })
  isFeatured?: boolean

  @Field(() => JSONObjectScalar, { nullable: true })
  @ApiProperty({ type: JSONObjectScalar, required: false })
  featuredTypes?: Record<string, unknown>[]

  @Field(() => [VehicleType], { nullable: true })
  @ApiProperty({ enum: VehicleType, isArray: true, required: false })
  vehicleTypes?: VehicleType[]

  static type = 'mongodb'
}

export type CarsItem = CarsInventoryItem | CarsDealerItem
