import { isBefore, startOfDay, subMonths } from 'date-fns'

import { Iso3166Alpha2, ResultType } from '@datagatherers/datagatherers'

import { getProjectProviderDataType, getProjectProviderTrackerType } from './utils'

import type { MongoDBEntity } from '@datagatherers/database'
import type {
  Item,
  JobInstance,
  ResultBase,
  Collection,
  BaseDGEntity,
  ResultIntegrationSnapshot,
  JobRuntime,
} from '@datagatherers/datagatherers'
import type { AnyBulkWriteOperation, BulkWriteResult, Document } from 'mongodb'

const MAX_DB_OPERATIONS_PER_REQUEST = 1000

export async function store<
  Y extends ResultType,
  DTO extends BaseDGEntity | (BaseDGEntity & MongoDBEntity),
  T extends ResultBase<Y, DTO>,
>(job: JobInstance, results: T[]) {
  const operations: {
    [index: string]: AnyBulkWriteOperation<DTO>[]
  } = {}
  for (const result of results) {
    if (!operations[result.type]) {
      operations[result.type] = []
    }

    if (result.type === ResultType.integrationSnapshot) {
      operations[result.type].push(parseIntegrationSnapshotResult(job, result as unknown as ResultIntegrationSnapshot))
    } else {
      operations[result.type].push(parseResult(job, result))
    }
  }

  const res: BulkWriteResult[] = []
  for (const key in operations) {
    if (operations[key]) {
      if (process.env.DEBUG === 'true') {
        // eslint-disable-next-line no-console
        console.log(`Storing ${operations[key].length} ${key} items`)
      }
      const collection = job.runtime.collection<DTO>(key)

      const results = await bulkWriteController<DTO>(job.runtime, collection, operations[key])

      for (const item of results) {
        job.runtime.stats.db.increment('inserted', (item.insertedCount || 0) + (item.upsertedCount || 0))
        job.runtime.stats.db.increment('updated', item.modifiedCount || 0)
        job.runtime.stats.db.increment('matched', item.matchedCount || 0)
        job.runtime.stats.db.increment('deleted', item.deletedCount || 0)
        res.push(item)
      }
    }
  }

  return res
}

export function parseResult<
  Y extends ResultType,
  DTO extends BaseDGEntity | (BaseDGEntity & MongoDBEntity),
  T extends ResultBase<Y, DTO>,
>(job: JobInstance, result: T): AnyBulkWriteOperation<DTO> {
  const { project, provider, dataType, iso, itemId, auctionType, trackerType, ...wrappedResultData } = result.data
  let isoUpdated = iso

  const createdAt = (result.data as MongoDBEntity).createdAt
  const updatedAt = (result.data as MongoDBEntity).updatedAt

  if (result.type === ResultType.transferFees && !isoUpdated) isoUpdated = Iso3166Alpha2.WW

  const now = new Date()
  const updatedAtDate =
    result.type === ResultType.trackers
      ? job.config.keepUpdatedAt && updatedAt
        ? updatedAt
        : now
      : startOfDay(job.config.keepUpdatedAt && updatedAt ? updatedAt : now)

  let providerIsoItemId: string
  if (provider && isoUpdated && itemId) {
    providerIsoItemId = `${provider}_${isoUpdated}_${itemId}`
  }

  let projectProviderDataType: string
  if (project && provider && dataType) {
    projectProviderDataType = getProjectProviderDataType(project, provider, dataType)
  }

  let projectProviderTrackerType: string
  if (project && provider && trackerType) {
    projectProviderTrackerType = getProjectProviderTrackerType(project, provider, trackerType)
  }

  let setToDelete: boolean
  if (job.config.keepUpdatedAt && updatedAt) {
    setToDelete = isBefore(
      updatedAtDate,
      provider === 'hemnet' ? subMonths(startOfDay(now), 6) : subMonths(startOfDay(now), 3),
    )
  }

  const filter = {
    ...(provider && { provider }),
    ...(isoUpdated && { iso: isoUpdated }),
    ...(itemId && { itemId }),
  }

  // delete wrappedResultData.data

  return {
    updateOne: {
      filter,
      update: {
        $set: {
          ...wrappedResultData,
          updatedAt: updatedAtDate,
          ...(!!setToDelete && { setToDelete }),
          ...(dataType && { dataType }),
          ...(createdAt && { createdAt: ResultType.trackers ? createdAt : startOfDay(createdAt) }),
          ...(trackerType && { trackerType }),
        },
        $setOnInsert: {
          ...(typeof createdAt === 'undefined' && { createdAt: ResultType.trackers ? now : startOfDay(now) }),
          ...(project && { project }),
          ...(provider && { provider }),
          ...(isoUpdated && { iso: isoUpdated }),
          ...(itemId && { itemId }),
          ...(projectProviderDataType && { projectProviderDataType }),
          ...(providerIsoItemId && { providerIsoItemId }),
          ...(auctionType && { auctionType }),
          ...(projectProviderTrackerType && { projectProviderTrackerType }),
        },
      },
      upsert: true,
      ...(result.type !== ResultType.integrationAppInfo && { hint: { provider: 1, iso: 1, itemId: 1 } }),
    },
  } as never
}

export function parseIntegrationSnapshotResult<DTO extends BaseDGEntity | (BaseDGEntity & MongoDBEntity)>(
  job: JobInstance,
  result: ResultIntegrationSnapshot,
): AnyBulkWriteOperation<DTO> {
  const {
    project,
    provider,
    dataType,
    iso,
    itemId,
    integration,
    snapTimeUnit,
    snapTime,
    platform,
    appId,
    createdAt,
    updatedAt,
    ...wrappedResultData
  } = result.data

  const now = new Date()
  const updatedAtDate = updatedAt ? updatedAt : now

  let setToDelete: boolean
  if (job.config.keepUpdatedAt && updatedAt) {
    setToDelete = isBefore(
      updatedAtDate,
      provider === 'hemnet' ? subMonths(startOfDay(now), 6) : subMonths(startOfDay(now), 3),
    )
  }

  const filter = {
    integration,
    project,
    provider,
    iso,
    dataType,
    snapTimeUnit,
    snapTime,
    ...(platform && { platform }),
    ...(appId && { appId }),
  }

  // delete wrappedResultData.data

  return {
    updateOne: {
      filter,
      update: {
        $set: {
          ...wrappedResultData,
          updatedAt: updatedAtDate,
          ...(!!setToDelete && { setToDelete }),
          ...(dataType && { dataType }),
        },
        $setOnInsert: {
          createdAt: createdAt || now,
          ...(project && { project }),
          ...(provider && { provider }),
          ...(iso && { iso }),
          ...(itemId && { itemId }),

          ...(integration && { integration }),
          ...(snapTimeUnit && { snapTimeUnit }),
          ...(snapTime && { snapTime }),
          ...(platform && { platform }),
          ...(appId && { appId }),
        },
      },
      upsert: true,
    },
  } as never
}

function* chunks<T>(arr: T[], n: number) {
  for (let i = 0; i < arr.length; i += n) {
    yield arr.slice(i, i + n)
  }
}

async function bulkWriteController<T extends Document>(
  runtime: JobRuntime,
  collection: Collection<T>,
  operations: AnyBulkWriteOperation<T>[],
): Promise<BulkWriteResult[]> {
  const operationsChunks = [...chunks(operations, MAX_DB_OPERATIONS_PER_REQUEST)]

  const res: BulkWriteResult[] = []
  for (const opts of operationsChunks) {
    const startTime = performance.now()
    res.push(await collection.bulkWrite(opts, { writeConcern: { w: 'majority' }, ordered: false }))
    runtime.stats.db.increment('runningTime', Math.round(performance.now() - startTime))
  }

  return res
}

export async function getItem(job: JobInstance) {
  if (!job.runtime.collection) {
    throw new Error('No DB instance to read from!')
  }

  const collection = job.runtime.collection<Item>('item')
  const result = await collection.findOne({
    _id: job.item,
  })

  job.item = result ? result : undefined

  return result
}
