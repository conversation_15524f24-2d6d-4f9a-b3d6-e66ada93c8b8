import { auth, drive_v3 } from '@googleapis/drive'
import { gmail_v1 } from '@googleapis/gmail'

import { emailDirectory } from './emailDirectory'
import { emailRegex } from './regex'
import { wait } from './utils'

export interface GDriveFileResponse {
  kind: string
  id: string
  name: string
  mimeType: string
}

interface AccountConfigs {
  [index: string]: {
    accountName: string
    defaultFileType: string
    folders: {
      default: string
      base: 'root'
      [index: string]: string
    }
    oAuthConfig: {
      client_secret: string
      refresh_token: string
      client_id: string
      redirect_uri?: string
    }
  }
}

export const accountConfigs: AccountConfigs = {
  vor: {
    accountName: 'dg.temp.01',
    defaultFileType: 'text/csv',
    folders: {
      default: 'root',
      base: 'root',
      vor: '1vY5L8ZWAilcIhT4ve4IRjvF7nWMDfvVD',
      csvExports: '1LRIAnCbnBOwHcA5PNJGRJKh4p8nj8Opu',
      archive: '1HNtvR95QlrMli0NWfq3M4XmAx0vZ4CFx',
      nuComSW: '1atJnbU6l2t4PZ2SFuiUNjsuI9Qcg7qD9',
      datingA: '1FnS0t8sTrenCWOrpyz7btTcjvm-e7wcb',
      datingSW: '1QC4_UxaP4asdU34gQxsKFZn0OIAr6clD',
      vodA: '1iKRJrOr3gtLvgDQibPBequftvgAtK1-I',
      vodSW: '1wqe8cl9cbLqoC64-w71ivFgc3zNGv6-c',
      mobileDE: '1j3vW9TB6qGCgMfbM_S2_w2-6r5LXZjrw',
      tFees: '1Ew7rp-8g1tdhlYdPOCQ0PtfFuI7XqXzW',
      foxIntel: '1SQMgq6L_c7i0YScPFSQTTRXfYXlXVQfn',
      liveauctioneersGMV: '1n3yN4QXjlJdzSl7zpkWrIWVDFEeaTuNi',
      liveauctioneersBonhams: '1eWl5w5owik2PSb6ZVfcSfwiIiu-ujw_0',
      accountingSW: '1IDfsF6gnH4aTdTErb6Z9Kmv6sKsX__H3',
      fortnox: '1MZvYOArT9dwK-eQsUrteU4cvskwzel7W',
      pricingTrackers: '1wej9AMVI9J_H2iPfyZKDwoyhLXvERImg',
      freee: '1Dl3SBfw8anKU4sBbaqxMxoub0YFqL1Ww',
      'bill.com': '1xwBOKj-176Hw37vNZgy6yCkqEyXnPvOA',
      hemnetMunicipalityPrices: '11UTy0akipaxgObE2s0O5B0TRz18DmXsM',
      accountingSWUS: '1nMmmyvJjUdxHyZdAZxJM8pRTsnzpWLCb',
      marketplacesA: '1hyF5jBJfrY6k2Uy1XPNmyN76oer3jtHQ',
      annieNordic: '1Ow0etwdIWGxFfIvqI6DLzWLv_9TNgGXE',
      wisePreArchiveTFees: '1LuVL487myXxEHRnsojH-8jG34oiakX_2',
      wiseTFees: '1Z446Zd5Y6iodyO4AxphSy-a8qUPbqe6S',
      wiseOldTFees: '1mGeryq3TKQg2eyzHCt4nI2c0XzlWgABy',
      wiseNewTFees: '1it41XxyD67pL_eY6usSvSvFSaeRsLl26',
      revolutTFees: '1NP1UVUneVSkOHwO8mPm0IBJ-pW5KA1bH',
      billdotcomTFees: '1By-YVMXK6tJR_OeKjnARbg6DzHYj3cIL',
      trackerSnapshots: '1JRrHR1MawqGetPlfobWYkbQ3rGYeef4O',
      apiIntegrations: '1WnMWvt3D2uLxx81bv8OagmOk_-s9nYX-',
      divvy: '1iBKBmdAOGspXlgVuIlmbSfxsvQFAf0Jk',
      datingApptopia: '1RqZDvWGlHAJawf3OB55pA2PKn35IBe_m',
      vodApptopia: '1NWFaszry2rN96NXwDeSHB2Dqd5O-FpHR',
      frbrdeApptopia: '10hlCjLI4S_9CaOprQaNP6gr2ObdfjQS-',
      nordicApptopia: '1nc6oDYOyM1oj569ej3kWoVgJYRadbpe3',
      zingTFees: '1glOM3uJVCaIHYbEsdBw1XIxZCwEEayrL',
      wiseDataAi: '1Gm-8ZAZ7G2C60p4l-Eeb6EVJ1zH2BAY6',
      wiseSimilarWeb: '10S0x6RsLYY5TVCTS5qms0LELIYkQii4d',
      wiseApptopia: '1iNL0Zbg0JV3w8M5e9TJtEchy_V5et5Bu',
      revolutApptopia: '1Jbew9UTWh-9VJycH-OUmPs8KLNx1fjwq',
      revolutDataAi: '1aVmIL7dgtiRVQBCfGKIdOLLzBfffaPmg',
      revolutSimilarWeb: '1ft4AugtxBX53WB9Si-GpEWFdAMaybdv8',
      exchangeRatesComparators: '15UY9dm32673n7t9F0in3OEWcWNJlDXRQ',
      schibstedExports: '1ANfuFANiZiML40NrF9KbrL4h30eNg9SA',
      adevintaExports: '1K3pOpi2dw-iKdc4bGjHjxB2TP0IRzD9M',
      nordic: '1xxV-vhtC-ULSCsjMyFZEeNCYmf9CuG4x',
      bcgExports: '1gvfPy4lw5CKYm49fCc2zhgnepsqcLOTK',
      adevinta: '1NoiPPCqfXr7CU5o0x6idoL7ji4dgwdOS',
      costar: '1p-ZEh2yw1iey1xCB4e3jWW7gic8V-B9M',
      'finn.no': '1_U5X2S7o60VPNSwSbxcoS_DWtoiYFFY1',
      smg: '1NeNbPTQCSDJVpkrXxIp9bL10u3bGHwc5',
      accountingBookkeeping: '1PRXNAFZZASS8e75lf0VEVrmlsf2Vu_HI',
      realEstateSE: '1eN_OzRh1bX9PEgHzj76GbQwzg_GSMexr',
      wiseSensortower: '15iGEowEyAB-w2lioV2ZNeCCnkJygwia6',
      revolutSensortower: '1Fjewx_85zY8UJtIaEJ_wdGJTLu-51dXX',
      datingSensortower: '1DhLH8XGeLP7CvsSYu7G4KxthABAUKDai',
      vodSensortower: '1pXRmZxnbe0fmG4xG7Rd9HTQi5VFSNWns',
      frbrdeSensortower: '14EREJo2IofE-Wh-lIkoG-S3yY4xtYgv-',
      nordicSensortower: '10ahgFFF7ukoFQTIYapagaP-XJTN4jxvV',
      wiseOldRoutes: '15A4Yv_ile-_6m9ONJ9zqDdG4e8naK8oO',
      wiseOldLfl: '1H7xbSgw9Ws0vU9ehTqlZgO5bD2ZXgnU0',
      wiseNewRoutes: '1QPIF_9gXP3lC6vkAbhhSoe4s4V1UWnGd',
      wiseNewLfl: '1XqKLX-Bi9BQySQutDyJvSeXvtTYBiYIZ',
      xero: '1xjJZwR12IbUvTcqVANQ3LY9vHbyJp-fD',
      sidetrade: '12uTi6pZV_VN5Z79x7lH2Frug4ud1Dv3g',
      karnov: '11Uj-U0W03LNApSUNzAUlk8fO-L87f_Cf',
      wiseOldBreakdown: '1PWbb88s3jIXCAfKvzqR829UdIQidi4lT',
      hemnet_booli_aggregations: '1uYsIvn18NeseXIZOUq1Tzo5qkPNiMzZP',
    },
    oAuthConfig: {
      client_secret: '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',
      refresh_token:
        '1//047sjtbuE7fSFCgYIARAAGAQSNwF-L9IrnYv74-SrP4Wz_SeUmtVXs3MJBRs8w8oIZEsEbZqmw0ZYMQPkw2Gcon3VfngdoOmlLtY',
      client_id: '************-bre78in6cfs79su9mkj2m2g6ltvnv27u.apps.googleusercontent.com',
      redirect_uri: 'https://developers.google.com/oauthplayground',
    },
  },
  test: {
    accountName: 'stub.development',
    defaultFileType: 'text/csv',
    folders: {
      default: 'root',
      base: 'root',
      vor: '1rIrlweQFLEWJjRf223FMWD9H2re5HPA_',
      test: '1AuhZAAq503NzlWM1iMEyjo_gsaOaOYnq',
      csvExports: '1HM3cini2KrckeQmKagwyIrqetD_GMBae',
      archive: '1hiizpHwIn9pOUPGd_2G33nyJ1Jdgaa1y',
      nuComSW: '1kNxaHsPri1VyGgeyNuVyQmG67VSrxSGi',
      datingA: '1dRgCz7o-QB_SVePrj5C_5bziNIYyRLy3',
      datingSW: '1aGgRAr5Z13UnKuQ-1qp1PCG77CmhagMp',
      vodA: '1hM0TnLEsqxxJglwaJzhar6m_iZPU3Kk6',
      vodSW: '17cSpZh85zEFy9fW3LxS1db1O4tsCfKPs',
      mobileDE: '1FcTq96CPNPVsKZBnMrOrYj1qMhDxfIHf',
      wiseTFees: '13LdAnKXlqAXwlCpvlmTPk8nt-nujQTvL',
      revolutTFees: '1E2cChWWhbOdG296C2sQEn2pk8qIiPoBm',
      liveauctioneersGMV: '1QbaE6OymRXxdMalcS5DnOIxyrvh1ipEL',
      accountingSW: '1ikIYmnMEu_msx_rm8li6CyydVHZKtxDW',
      fortnox: '1tBXICkIGWPl_Qd8AWnVTX1Cd6uQKzsKr',
      freee: '1vELYr2dmbnYbXa8qG3Xp-t_03tomaIvl',
      'bill.com': '1_aDUhoRN0sIW00PLiWzIWkMFgvseXB7y',
      hemnetMunicipalityPrices: '1wO_NJnv-ArBKTsOgvp7gtPvK840Mc36T',
      crawlerInspection: '1Cuv2-ICk5Y-kEPyLO7qsW8vk9P9hqYoy',
      accountingSWUS: '1THMwWabeCERATPk0vSQwd0gnsyRr8QaA',
      tFees: '1jymiPt8V94jaFvQ_E15392cMH2nNX2vq',
      marketplacesA: '1A57DbjH0wUBafkWHG7iChEmPKjdv1xai',
      annieNordic: '1e2CAWDtf_iS-IuQjemD10GTiVXzRhNUe',
      wisePreArchiveTFees: '1JHNAUkwEn1zlXCvJ90XVCq5hayXWfjsX',
      trackerSnapshots: '15_S0k5O3RBgMAHj8VFXCSZI7RC5mJGsI',
      apiIntegrations: '19WD9BfjnG5ebX6NzF_NPFbTStpOxJzsP',
      divvy: '15FeOT6ugYkEDGupz3DZERvddVnxvdCwN',
      datingApptopia: '15UKc8hWbVDWtxRn_lBXQ1tZz0mLUkY35',
      vodApptopia: '1WeRKJ1AWohwjjr10tXKgRXZ0TtQzeZOl',
      frbrdeApptopia: '1PbmBN9cOUnfKQM3gmVkrig8TWQ89dnr8',
      nordicApptopia: '1CoSZnITptK-DtjLLDmg1mT0A_jr9C102',
      zingTFees: '1CYf2AJjuzwk5cFGxW1oowdhDwR0qQtOC',
      wiseDataAi: '1SR5J0F98utPRmQgLB4ZOui6M3VUoq3rp',
      wiseSimilarWeb: '1EOqotbzMgUit-Aju7tAmrBtPDNs1odfj',
      wiseApptopia: '1hWvIzDzKCU_-ltcGAbFauHn9PF0elG6o',
      revolutApptopia: '1WY4qonAG-0xWQupf4l8_T8X9yspW6M1N',
      revolutDataAi: '1vCzLmaa96S0XC-bylrpnD8JNaT_Hz3YV',
      revolutSimilarWeb: '1uGfHy_fcYqKo_qG-YEs5WDu2Ld0HVbIP',
      exchangeRatesComparators: '10rve-548gDAcs3xSy3_ToxJWYib-Y0B4',
      schibstedExports: '1WV-gci6lJf4TqzidIFFjWpy_mP0-phBo',
      adevintaExports: '17sTON1kk-qNx6icBxmpColFmmUeiHBmD',
      nordic: '1H_bX9nGuhu1MRNvuCkvXceJO5qxYCBtv',
      bcgExports: '1HYNIf29U4aPUVZf2rOSivWKaxPUtD7h1',
      adevinta: '1D4Jlm0QQ4Kuq5Vrp4P5L_Y1RxfxiPu80',
      costar: '1ybmoE4a8WXwSQOZRVWi8zGeX2VYU_Lqo',
      'finn.no': '10ygtZAtcjWhRB_MHdiyJkv-rjT-sFZEc',
      smg: '12PwWKSZa2_FhaUYzh_FKq5f-77cHad47',
      accountingBookkeeping: '1K-f6a9t0HlqAdwEJr2s5pPOe5jN3T3Te',
      realEstateSE: '1G5luESB2--i68m5797ZbcU4cv3hvkEw9',
      wiseSensortower: '1j3JFhmDbI7UjCPjrXtq5bfC6So4A7rcD',
      revolutSensortower: '1GhxitEGY7GikGEzACcaBpC5u9kFakxwB',
      datingSensortower: '1SDuGmQfg8SME1oPL--qLFjn1I2R-1K5d',
      vodSensortower: '1XGD7iGdmn8VCgxeqS7r8PyfzyU_Ppb0V',
      frbrdeSensortower: '1OgyFzaG2Rzmdlx7hGQm-m6Pcb6NbVom4',
      nordicSensortower: '1dtBM5Z-zlG5fHUiay2YuH9myrn9Kjk6n',
      xero: '1Jg7x1ImGOJnMpJjWFLtZ4MRcKJMExsPi',
      sidetrade: '1_eLUCufVlNI1ZDkBFd2DL6XxGCdVaioU',
      karnov: '1s1BAgQjoRv-dFeDeOIpzdCGNF6XRoILX',
      hemnet_booli_aggregations: '1d0h8xHwuOd67C-WRQbIdPcWBA2K3JDT4',
    },
    oAuthConfig: {
      client_secret: '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',
      refresh_token:
        '1//04_OEmF79in0sCgYIARAAGAQSNwF-L9Ir-9f_LQUAV0Y34JJ7bZHG4D5Swnt-Sb5GVRH_w96vMwKczg00duHsk6f2UHj6LOBkcwo',
      client_id: '************-lr3muguhv4ooiq60hq06td8k84khe8q3.apps.googleusercontent.com',
      redirect_uri: 'https://developers.google.com/oauthplayground',
    },
  },
}

export async function GDrive(account: string, maxRetries = 5, delay = 2_000): Promise<GDriveUtil> {
  let lastError: Error | undefined
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const gdrive = new GDriveUtil()
      await gdrive.init(account)
      return gdrive
    } catch (error) {
      lastError = error as Error
      if (attempt < maxRetries) {
        await wait(delay * attempt) // Exponential backoff
      }
    }
  }
  throw lastError
}

export class GDriveUtil {
  static searchQueries = {
    folder: "mimeType = 'application/vnd.google-apps.folder'",
    notFolder: "mimeType != 'application/vnd.google-apps.folder'",
    shared: "not ('me' in owners)",
    notShared: "'me' in owners",
    modifiedBefore: (date: Date) => `modifiedTime < '${date.toISOString()}'`,
    modifiedAfter: (date: Date) => `modifiedTime >= '${date.toISOString()}'`,
    parentFolderId: (parentFolderId: string) => `'${parentFolderId}' in parents`,
    notArchiveFolder: (archiveFolderId: string) => `'${archiveFolderId}' not in parents`,
  }

  declare account: string
  declare private authentication: InstanceType<typeof auth.OAuth2>
  declare private driveInstance: drive_v3.Drive
  declare private gmailInstance: gmail_v1.Gmail
  declare private files: drive_v3.Schema$File[]

  async init(account: string) {
    this.account = account

    await this.setAuthentication()
    //if (!this.authentication) throw new Error('GDriveUtil: Authentication failed')

    await this.setGoogleDriveInstance()
    //if (!this.driveInstance) throw new Error('GDriveUtil: Google Drive instance failed')

    // if (!this.gmailInstance) {
    //   await this.setGMailInstance()
    // }

    await this.getFileList()
    // if (!this.files) throw new Error('GDriveUtil: File list failed')
  }

  // getOAuth2Client() {
  //   if(!this.authentication) {
  //   const googleOAuth2Client = new auth.OAuth2(
  //     accountConfigs[this.account].oAuthConfig.client_id,
  //     accountConfigs[this.account].oAuthConfig.client_secret,
  //     accountConfigs[this.account].oAuthConfig.redirect_uri,
  //   )

  //   googleOAuth2Client.setCredentials({ refresh_token: accountConfigs[this.account].oAuthConfig.refresh_token })

  //   this.authentication =  googleOAuth2Client
  // }
  // }

  async setAuthentication() {
    if (!this.authentication) {
      const { client_id, client_secret, redirect_uri, refresh_token } = accountConfigs[this.account].oAuthConfig

      //for (let retry = 1; retry <= 3; retry++) {
      try {
        const oAuth2Client = new auth.OAuth2(client_id, client_secret, redirect_uri)
        oAuth2Client.setCredentials({ refresh_token }) // Set a timeout of 20 seconds for all requests made by this client
        //oAuth2Client.setRequestOptions({ timeout: 20000, retryConfig: { retry: 5, retryDelay: 1500 } })
        this.authentication = oAuth2Client
        //if (this.authentication) break
      } catch (error) {
        //empty
        throw new Error(
          'GDriveUtil: Set Authentication Failed\nReason:\nCode: ' +
            (error.error?.code ?? error.code) +
            '\nMessage: ' +
            (error.error?.message ?? error.message) +
            '\nError array: ' +
            (JSON.stringify(error.error?.errors) ?? '(no errors associated)') +
            '\n' +
            error,
        )
      }
      // if (this.authentication) break
      // await wait(retry * 2000)
      // }
    }
  }

  async setGoogleDriveInstance() {
    if (!this.driveInstance) {
      //for (let retry = 1; retry <= 3; retry++) {
      // try {
      //   await this.authentication.getAccessToken()
      // } catch (error) {
      //   throw new Error('OAuth2 token fetch failed:', error)
      //   //empty
      // }
      try {
        this.driveInstance = new drive_v3.Drive({
          auth: this.authentication,
          timeout: 30_000,
          retryConfig: { retry: 10, retryDelay: 2000 },
        })
      } catch (error) {
        throw new Error(
          'GDriveUtil: Google Drive Instance Failed\nReason:\nCode: ' +
            (error.error?.code ?? error.code) +
            '\nMessage: ' +
            (error.error?.message ?? error.message) +
            '\nError array: ' +
            (JSON.stringify(error.error?.errors) ?? '(no errors associated)') +
            '\n' +
            error,
        )
        //empty
      }
      //   if (this.driveInstance) break
      //   await wait(retry * 2000)
      // }
    }
  }

  async setGMailInstance() {
    if (!this.gmailInstance) {
      // await this.authentication.getAccessToken()
      this.gmailInstance = new gmail_v1.Gmail({
        auth: this.authentication,
        timeout: 30_000,
        retryConfig: { retry: 10, retryDelay: 2000 },
      })
    }
  }

  async getFileList(searchQuery = GDriveUtil.searchQueries.notShared) {
    //await this.setGoogleDriveInstance()

    // if (this.driveInstance) {
    const fileList = await this.driveInstance.files?.list({ q: searchQuery }).then((res) => res.data.files)
    if (!fileList) throw new Error('GDriveUtil: File list is undefined')

    this.files = fileList
    return fileList
    // }
  }

  async insert(
    file: string,
    fileName: string,
    fileType = accountConfigs[this.account].defaultFileType,
    parentFolderId = accountConfigs[this.account].folders.default,
  ) {
    // await this.setGoogleDriveInstance()

    const fileMetadata = {
      name: fileName,
      parents: [parentFolderId],
    }

    const media = {
      mimeType: fileType,
      body: file,
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any

    try {
      res = await this.driveInstance.files.create({
        requestBody: fileMetadata,
        media: media,
        fields: 'id',
      })
      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Insert Request Failed\nReason::\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }

    return res?.data
  }

  async createFolder(fileName: string, parentFolderId = accountConfigs[this.account].folders.default) {
    // await this.setGoogleDriveInstance()

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any
    const fileMetadata = {
      name: fileName,
      mimeType: 'application/vnd.google-apps.folder',
      parents: [parentFolderId],
    }

    try {
      res = await this.driveInstance.files.create({
        requestBody: fileMetadata,
        fields: 'id',
      })
      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Create Folder Request Failed\nReason::\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }
    return res?.data
  }

  async update(fileId: string, file: string, fileType = accountConfigs[this.account].defaultFileType) {
    // await this.setGoogleDriveInstance()

    const fileMetadata = {}

    const media = {
      mimeType: fileType,
      body: file,
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any

    try {
      res = await this.driveInstance.files.update({
        fileId: fileId,
        requestBody: fileMetadata,
        media: media,
        fields: 'id',
      })
      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Update Request Failed\nReason:\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }

    return res?.data
  }

  async rename(fileId: string, newName: string) {
    // await this.setGoogleDriveInstance()

    const fileMetadata = {
      name: newName,
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any
    try {
      res = await this.driveInstance.files.update({
        fileId: fileId,
        requestBody: fileMetadata,
        fields: 'id',
      })
      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Request Failed\nReason:\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }

    return res?.data
  }

  async move(fileId: string, targetFolder = accountConfigs[this.account].folders.default) {
    // await this.setGoogleDriveInstance()

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any
    try {
      // Retrieve the existing parents to remove
      const file = await this.driveInstance.files.get({
        fileId: fileId,
        fields: 'parents',
      })

      const previousParents = file.data.parents?.join(',')

      // Move the file to the new folder
      res = await this.driveInstance.files.update({
        fileId: fileId,
        addParents: targetFolder,
        removeParents: previousParents,
        fields: 'id, parents',
      })

      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Move File Request Failed\nReason:\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }
    return res?.data
  }

  async delete(fileId: string) {
    // await this.setGoogleDriveInstance()

    try {
      await this.driveInstance.files.delete({
        fileId: fileId,
      })
      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Delete Request Failed\nReason:\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }
  }

  async upsert(
    file: string,
    fileName: string,
    fileType = accountConfigs[this.account].defaultFileType,
    parentFolderId = accountConfigs[this.account].folders.default,
  ) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any
    const filteredList = this.fileList.filter((file) => file.name === fileName)
    if (filteredList?.length) {
      res = await this.update(filteredList[0].id, file, fileType)
    } else {
      res = await this.insert(file, fileName, fileType, parentFolderId)
    }
    await this.getFileList()
    return res
  }

  async copy(fileId: string, parentFolderId = accountConfigs[this.account].folders.default, newName?: string) {
    // await this.setGoogleDriveInstance()

    const fileMetadata: drive_v3.Schema$File = {
      name: newName,
      parents: [parentFolderId],
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let res: any

    try {
      res = await this.driveInstance.files.copy({
        fileId: fileId,
        requestBody: fileMetadata,
        fields: 'id, parents',
      })
      await this.getFileList()
    } catch (error) {
      throw new Error(
        'GDriveUtil: Copy Request Failed\nReason:\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }
    return res?.data
  }

  async sendMail(subject: string, message: string, ...recipients: string[]) {
    if (!this.gmailInstance) {
      await this.setGMailInstance()
    }

    const email = [
      `From: ${accountConfigs[this.account].accountName + '@gmail.com'}`,
      `To: ${this.formatRecipients(...recipients)}`,
      `Subject: ${subject}`,
      '',
      `${message}`,
    ].join('\r\n')

    const encodedMessage = Buffer.from(email)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '')

    try {
      const res = await this.gmailInstance.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedMessage,
        },
      })
      return res.data
    } catch (error) {
      throw new Error(
        'GDriveUtil: Request Failed\nReason:\nCode: ' +
          (error.error?.code ?? error.code) +
          '\nMessage: ' +
          (error.error?.message ?? error.message) +
          '\nError array: ' +
          (JSON.stringify(error.error?.errors) ?? '(no errors associated)'),
      )
    }
  }

  async notifyDevs(subject: string, message: string) {
    return await this.sendMail(
      subject,
      message,
      emailDirectory.dgDev,
      emailDirectory.sebastiaoSlack,
      emailDirectory.marcSlack,
      emailDirectory.joaoSlack,
    )
  }

  formatRecipients(...recipients: string[]) {
    if (!recipients?.length) throw new Error('No email recipients provided')
    if (!recipients.map((recipient) => emailRegex.test(recipient)).reduce((acc, value) => acc && value))
      throw new Error('Invalid email address format')
    return recipients.reduce((acc, recipient) => (acc ? acc + ', ' + recipient : recipient))
  }

  printState() {
    return {
      accountConfig: accountConfigs[this.account],
      files: this.files,
    }
  }

  get fileList() {
    return this.files
  }
}
