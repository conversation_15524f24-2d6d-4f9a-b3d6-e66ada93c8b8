import { startOfISOWeek, startOfMonth, addWeeks, addMonths, isEqual } from 'date-fns'
import { ReadPreference } from 'mongodb'

import { DataType, TimeUnit } from '@datagatherers/datagatherers'

import { isLastCrawlOfMonth, getProjectProviderDataType } from '../../utils'
import { SnapshotResolution } from '../interfaces'
import { getSnapTime, hasPreviousSnap, getControllerId } from '../utils'

import type {
  Iso3166Alpha2,
  ControllerConfig,
  ControllerRuntime,
  Item,
  Snapshot,
  SnapStats,
} from '@datagatherers/datagatherers'

export async function cars(
  iso: Iso3166Alpha2,
  config: ControllerConfig,
  runtime: ControllerRuntime,
  snapshotResolution: SnapshotResolution = SnapshotResolution.Full,
  date?: Date,
  customProvider?: string,
  flags?: Record<string, unknown>,
) {
  if ((!date || isEqual(startOfISOWeek(date), startOfISOWeek(new Date()))) && !customProvider) {
    await setVehicleTypesToDealers(config, runtime, iso)
  }

  if (snapshotResolution === SnapshotResolution.Week || snapshotResolution === SnapshotResolution.Full) {
    await snapCars(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, customProvider, flags)
    await snapCars(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date, customProvider, flags)
  }

  if (
    snapshotResolution === SnapshotResolution.Month ||
    (snapshotResolution === SnapshotResolution.Full && isLastCrawlOfMonth())
  ) {
    await snapCars(iso, DataType.dealers, TimeUnit.months, config, runtime, date, customProvider, flags)
    await snapCars(iso, DataType.inventory, TimeUnit.months, config, runtime, date, customProvider, flags)
  }
}

export async function snapCars(
  iso: Iso3166Alpha2,
  dataType: DataType,
  snapTimeUnit: TimeUnit,
  config: ControllerConfig,
  runtime: ControllerRuntime,
  date?: Date,
  customProvider?: string,
  flags?: Record<string, unknown>,
) {
  if (config && !runtime.skipStoreData) {
    const itemCollection = runtime.collection<Item>('item')
    const startDate =
      snapTimeUnit === TimeUnit.weeks
        ? startOfISOWeek(date ? new Date(date) : new Date())
        : startOfMonth(date ? new Date(date) : new Date())
    const endDate = snapTimeUnit === TimeUnit.weeks ? addWeeks(startDate, 1) : addMonths(startDate, 1)
    const snapTime = getSnapTime(startDate, snapTimeUnit)
    const setIsNew = await hasPreviousSnap(iso, dataType, snapTimeUnit, config, runtime, date, customProvider)
    const controllerId = getControllerId(config)
    const snapMap: Map<string, Omit<Snapshot, '_id' | 'createdAt' | 'updatedAt'>> = new Map()
    const project = config.project
    const provider = config.provider
    const aggregation = [
      {
        $match: {
          projectProviderDataType: getProjectProviderDataType(project, provider, dataType),
          iso,
          updatedAt: { $gte: startDate },
          createdAt: { $lt: endDate },
          ...(dataType === DataType.dealers && { isActive: true }),
          ...flags,
        },
      },
      {
        $group: {
          _id: {
            stockType: '$stockType',
            dealerType: '$dealerType',
            productType: '$productType',
            stockTypes: '$stockTypes',
            vehicleType: '$vehicleType',
            vehicleTypes: '$vehicleTypes',
            ...(dataType === DataType.inventory && { hasPrice: { $gt: ['$price', 0] }, priceRange: '$priceRange' }),
            ...(dataType === DataType.dealers && { isPaying: '$isPaying', listingCountRange: '$listingCountRange' }),
            ...(setIsNew && {
              isNew: {
                $and: [{ $gte: ['$createdAt', startDate] }, { $lt: ['$createdAt', endDate] }],
              },
            }),
            ...(['SE', 'NO', 'AT'].includes(iso) &&
              !customProvider?.match(/_domestic/i) &&
              !customProvider?.match(/_abroad/i) && { state: '$state' }),
            crossedProductType: '$crossedProductType',
          },
          count: {
            $sum: 1,
          },
          // ...(['ebk', 'ebk_smb', '2dehands'].includes(config.provider) && {
          advertisingCost: { $sum: '$data.advertisingCost' },
          //}),
          ...(dataType === DataType.inventory && {
            gmvCurrent: {
              $sum: '$price',
            },
          }),
        },
      },
    ]
    const results = await itemCollection
      .aggregate(aggregation, {
        hint: { projectProviderDataType: 1, iso: 1, updatedAt: 1, isActive: 1, dealerType: 1 },
        readPreference: ReadPreference.SECONDARY_PREFERRED,
      })
      .toArray()
    if (!results?.length) {
      return
    }
    results.map((item) => {
      const ident = `${iso}_${
        !customProvider ? provider : customProvider
      }_${snapTime}_${snapTimeUnit}_${dataType}_${controllerId}`

      if (!snapMap.has(ident)) {
        snapMap.set(ident, {
          project,
          controllerId,
          provider: !customProvider ? provider : customProvider,
          iso,
          snapTime,
          snapTimeUnit,
          dataType,
          stats: [],
        })
      }
      const snap = snapMap.get(ident)
      if (snap) {
        const stats: typeof SnapStats = {
          count: item.count,
          ...(item.gmvCurrent && { gmvCurrent: item.gmvCurrent }),
          ...(item.advertisingCost && { advertisingCost: item.advertisingCost }),

          ...(item._id.stockType && { stockType: item._id.stockType }),
          ...(item._id.dealerType && { dealerType: item._id.dealerType }),
          ...(item._id.isPaying && { isPaying: item._id.isPaying }),
          ...(item._id.productType && { productType: item._id.productType }),
          ...(item._id.state && { state: item._id.state }),
          ...(item._id.listingCountRange && { listingCountRange: item._id.listingCountRange }),
          ...(item._id.stockTypes && { stockTypes: item._id.stockTypes }),
          ...(item._id.isNew && { isNew: item._id.isNew }),
          ...(item._id.hasPrice && { hasPrice: item._id.hasPrice }),
          ...(item._id.priceRange && { priceRange: item._id.priceRange }),
          ...(item._id.vehicleType && { vehicleType: item._id.vehicleType }),
          ...(item._id.crossedProductType && { crossedProductType: item._id.crossedProductType }),
          ...(item._id.vehicleTypes && { vehicleTypes: item._id.vehicleTypes }),
        }
        snap.stats?.push(stats)
      }
    })

    const now = new Date()

    for (const snap of snapMap.values()) {
      await runtime.collection<Snapshot>('snapshot').updateOne(
        {
          project: snap.project,
          controllerId,
          provider: snap.provider,
          iso: snap.iso,
          snapTime: snap.snapTime,
          snapTimeUnit: snap.snapTimeUnit,
          ...(snap.dataType && { dataType: snap.dataType }),
        },
        {
          $setOnInsert: {
            project: snap.project,
            controllerId,
            provider: snap.provider,
            iso: snap.iso,
            snapTime: snap.snapTime,
            snapTimeUnit: snap.snapTimeUnit,
            ...(snap.dataType && { dataType: snap.dataType }),
            createdAt: now,
          },
          $set: {
            updatedAt: now,
            stats: snap.stats,
          },
        },
        { upsert: true, writeConcern: { w: 'majority' } },
      )
    }
  }
  return
}

// export const carsSnapshots = {
//   carsWeeklyFull: async (
//     iso: Iso3166Alpha2,
//     config: ControllerConfig,
//     runtime: ControllerRuntime,
//     date?: Date,
//     customProvider?: string,
//     flags?: Record<string, unknown>,
//   ) => {
//     await snapCars(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, customProvider, flags)
//     await snapCars(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date, customProvider, flags)
//   },
//   carsMonthlyFull: async (
//     iso: Iso3166Alpha2,
//     config: ControllerConfig,
//     runtime: ControllerRuntime,
//     date?: Date,
//     customProvider?: string,
//     flags?: Record<string, unknown>,
//   ) => {
//     await snapCars(iso, DataType.dealers, TimeUnit.months, config, runtime, date, customProvider, flags)
//     await snapCars(iso, DataType.inventory, TimeUnit.months, config, runtime, date, customProvider, flags)
//   },
//   carsFull: async (
//     iso: Iso3166Alpha2,
//     config: ControllerConfig,
//     runtime: ControllerRuntime,
//     date?: Date,
//     customProvider?: string,
//     flags?: Record<string, unknown>,
//   ) => {
//     await snapCars(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, customProvider, flags)
//     await snapCars(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date, customProvider, flags)
//     if (isLastCrawlOfMonth()) {
//       await snapCars(iso, DataType.dealers, TimeUnit.months, config, runtime, date, customProvider, flags)
//       await snapCars(iso, DataType.inventory, TimeUnit.months, config, runtime, date, customProvider, flags)
//     }
//   },
//   snapCars,
// }

// Recommended indexes for optimal performance:
// 1. { projectProviderDataType: 1, iso: 1, dealerId: 1 } - for dealer lookup
// 2. { projectProviderDataType: 1, iso: 1, updatedAt: 1, dealerId: 1 } - for inventory lookup
// 3. { dealerId: 1, projectProviderDataType: 1, iso: 1, updatedAt: 1 } - alternative for inventory
async function setVehicleTypesToDealers(config: ControllerConfig, runtime: ControllerRuntime, iso: Iso3166Alpha2) {
  const dealerProjectProviderDataType = getProjectProviderDataType(config.project, config.provider, DataType.dealers)
  const inventoryProjectProviderDataType = getProjectProviderDataType(
    config.project,
    config.provider,
    DataType.inventory,
  )

  await runtime
    .collection<Item>('item')
    .aggregate([
      // Start with dealer documents
      {
        $match: {
          projectProviderDataType: dealerProjectProviderDataType,
          iso,
        },
      },
      // Lookup inventory items for each dealer (optimized for index usage)
      {
        $lookup: {
          from: 'item',
          let: { dealerId: '$dealerId' },
          pipeline: [
            {
              $match: {
                // Use direct field comparisons for better index usage
                projectProviderDataType: inventoryProjectProviderDataType,
                iso: iso,
                updatedAt: { $gte: startOfISOWeek(new Date()) },
                $expr: { $eq: ['$dealerId', '$$dealerId'] },
              },
            },
            {
              $group: {
                _id: null,
                vehicleTypes: { $addToSet: '$vehicleType' },
              },
            },
          ],
          as: 'inventoryData',
        },
      },
      // Only process dealers that have inventory data
      {
        $match: {
          'inventoryData.0.vehicleTypes': { $exists: true },
        },
      },
      // Merge the vehicleTypes back into the dealer documents
      {
        $merge: {
          into: 'item',
          on: '_id',
          whenMatched: {
            $set: {
              vehicleTypes: { $arrayElemAt: ['$inventoryData.vehicleTypes', 0] },
            },
          },
          whenNotMatched: 'discard',
        },
      },
    ])
    .toArray()
}
