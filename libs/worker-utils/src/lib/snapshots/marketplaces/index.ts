import { startOfISOWeek, startOfMonth, addWeeks, addMonths, subWeeks, subMonths } from 'date-fns'
import { ReadPreference } from 'mongodb'

import { DataType, TimeUnit } from '@datagatherers/datagatherers'

import { getProjectProviderDataType, isLastCrawlOfMonth } from '../../utils'
import { SnapshotResolution } from '../interfaces'
import { getSnapTime, hasPreviousSnap, getControllerId } from '../utils'

import type {
  Iso3166Alpha2,
  ControllerConfig,
  ControllerRuntime,
  Item,
  Snapshot,
  SnapStats,
} from '@datagatherers/datagatherers'

export async function marketplaces(
  iso: Iso3166Alpha2,
  config: ControllerConfig,
  runtime: ControllerRuntime,
  snapshotResolution: SnapshotResolution = SnapshotResolution.Full,
  date?: Date,
  customProvider?: string,
  flags?: Record<string, unknown>,
) {
  if (snapshotResolution === SnapshotResolution.Week || snapshotResolution === SnapshotResolution.Full) {
    await snapMarketplaces(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, customProvider, flags)
    await snapMarketplaces(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date, customProvider, flags)
  }
  if (
    snapshotResolution === SnapshotResolution.Month ||
    (snapshotResolution === SnapshotResolution.Full && isLastCrawlOfMonth())
  ) {
    await snapMarketplaces(iso, DataType.dealers, TimeUnit.months, config, runtime, date, customProvider, flags)
    await snapMarketplaces(iso, DataType.inventory, TimeUnit.months, config, runtime, date, customProvider, flags)
  }
}

async function snapMarketplaces(
  iso: Iso3166Alpha2,
  dataType: DataType,
  snapTimeUnit: TimeUnit,
  config: ControllerConfig,
  runtime: ControllerRuntime,
  date?: Date,
  customProvider?: string,
  flags?: Record<string, unknown>,
) {
  if (config && !runtime.skipStoreData) {
    const itemCollection = runtime.collection<Item>('item')
    const startDate =
      snapTimeUnit === TimeUnit.weeks
        ? startOfISOWeek(date ? new Date(date) : new Date())
        : startOfMonth(date ? new Date(date) : new Date())
    const endDate = snapTimeUnit === TimeUnit.weeks ? addWeeks(startDate, 1) : addMonths(startDate, 1)
    const olderDate = snapTimeUnit === TimeUnit.weeks ? subWeeks(startDate, 1) : subMonths(startDate, 1)
    const snapTime = getSnapTime(startDate, snapTimeUnit)
    const setIsNew = await hasPreviousSnap(iso, dataType, snapTimeUnit, config, runtime, date, customProvider)
    const controllerId = getControllerId(config)
    const snapMap: Map<string, Snapshot> = new Map()

    const project = config.project
    const provider = config.provider

    const aggregationCurrent = [
      {
        $match: {
          projectProviderDataType: getProjectProviderDataType(project, provider, dataType),
          iso,
          updatedAt: { $gte: startDate },
          createdAt: { $lt: endDate },
          ...(dataType === DataType.dealers && { isActive: true }),
          ...flags,
        },
      },
      {
        $group: {
          _id: {
            stockType: '$stockType',
            dealerType: '$dealerType',
            condition: '$condition',
            productType: '$productType',
            ...(setIsNew && {
              isNew: {
                $and: [{ $gte: ['$createdAt', startDate] }, { $lt: ['$createdAt', endDate] }],
              },
            }),
            ...(dataType === DataType.inventory && { hasPrice: { $gt: ['$price', 0] } }),
            ...(dataType === DataType.dealers && { isPaying: '$isPaying', listingCountRange: '$listingCountRange' }),
            ...(config.provider === 'enjoei' && { state: '$geo.adminAreas.level1' }),
            ...(['NO', 'AT'].includes(iso) &&
              !customProvider?.match(/_domestic/i) &&
              !customProvider?.match(/_abroad/i) && { state: '$state' }),
            petsType: '$petsType',
            businessType: '$businessType',
            crossedProductType: '$crossedProductType',
            vehicleType: '$vehicleType',
          },
          count: {
            $sum: 1,
          },
          //...(['ebk', 'ebk_smb'].includes(config.provider) && {
          advertisingCost: { $sum: '$data.advertisingCost' },
          //}),
          ...(dataType === DataType.inventory && {
            gmvCurrent: {
              $sum: '$price',
            },
          }),
        },
      },
    ]
    const resultsCurrent = await itemCollection
      .aggregate(aggregationCurrent, {
        hint: { projectProviderDataType: 1, iso: 1, updatedAt: 1, isActive: 1, dealerType: 1 },
        readPreference: ReadPreference.SECONDARY_PREFERRED,
      })
      .toArray()
    if (!resultsCurrent?.length) {
      return
    }

    resultsCurrent.map((item) => {
      const ident = `${iso}_${
        !customProvider ? provider : customProvider
      }_${snapTime}_${snapTimeUnit}_${dataType}_${controllerId}`
      if (!snapMap.has(ident)) {
        snapMap.set(ident, {
          project,
          controllerId,
          provider: !customProvider ? provider : customProvider,
          iso,
          snapTime,
          snapTimeUnit,
          dataType,
          stats: [],
        } as Snapshot)
      }
      const snap = snapMap.get(ident)
      if (snap) {
        const stats: typeof SnapStats = {
          count: item.count,
          ...(item.gmvCurrent && { gmvCurrent: item.gmvCurrent }),
          ...(item.advertisingCost && { advertisingCost: item.advertisingCost }),

          ...(item._id.stockType && { stockType: item._id.stockType }),
          ...(item._id.dealerType && { dealerType: item._id.dealerType }),
          ...(item._id.productType && { productType: item._id.productType }),
          ...(item._id.isPaying && { isPaying: item._id.isPaying }),
          ...(item._id.condition && { condition: item._id.condition }),
          ...(item._id.listingCountRange && { listingCountRange: item._id.listingCountRange }),
          ...(item._id.state && { state: item._id.state }),
          ...(item._id.isNew && { isNew: item._id.isNew }),
          ...(item._id.hasPrice && { hasPrice: item._id.hasPrice }),

          ...(item._id.petsType && { petsType: item._id.petsType }),
          ...(item._id.businessType && { businessType: item._id.businessType }),
          ...(item._id.crossedProductType && { crossedProductType: item._id.crossedProductType }),
          ...(item._id.vehicleType && { vehicleType: item._id.vehicleType }),
        }
        snap.stats?.push(stats)
      }
    })

    if (dataType === DataType.inventory) {
      const aggregationSold = [
        {
          $match: {
            projectProviderDataType: getProjectProviderDataType(project, provider, dataType),
            iso,
            updatedAt: { $gte: olderDate, $lt: startDate },
            ...flags,
          },
        },
        {
          $group: {
            _id: {
              stockType: '$stockType',
              dealerType: '$dealerType',
              condition: '$condition',
              hasPrice: { $gt: ['$price', 0] },
            },
            count: {
              $sum: 1,
            },
            gmvSold: {
              $sum: '$price',
            },
          },
        },
      ]
      const resultsSold = await itemCollection
        .aggregate(aggregationSold, {
          hint: { projectProviderDataType: 1, iso: 1, updatedAt: 1, isActive: 1, dealerType: 1 },
          readPreference: ReadPreference.SECONDARY_PREFERRED,
        })
        .toArray()
      if (resultsSold?.length !== 0) {
        resultsSold.map((item) => {
          const ident = `${iso}_${
            !customProvider ? provider : customProvider
          }_${snapTime}_${snapTimeUnit}_${dataType}_${controllerId}`
          if (!snapMap.has(ident)) {
            snapMap.set(ident, {
              project,
              controllerId,
              provider: !customProvider ? provider : customProvider,
              iso,
              snapTime,
              snapTimeUnit,
              dataType,
              stats: [],
            } as Snapshot)
          }
          const snap = snapMap.get(ident)
          if (snap) {
            const stats: typeof SnapStats = {
              gmvSold: item.gmvSold,
              ...(item._id.stockType && { stockType: item._id.stockType }),
              ...(item._id.dealerType && { dealerType: item._id.dealerType }),
              ...(item._id.isPaying && { isPaying: item._id.isPaying }),
              ...(item._id.condition && { condition: item._id.condition }),
              ...(item._id.auctionType && { auctionType: item._id.auctionType }),
              ...(item._id.hasPrice && { hasPrice: item._id.hasPrice }),
            }
            snap.stats?.push(stats)
          }
        })
      }
    }

    const now = new Date()

    for (const snap of snapMap.values()) {
      await runtime.collection<Snapshot>('snapshot').updateOne(
        {
          project: snap.project,
          controllerId: snap.controllerId,
          provider: snap.provider,
          iso: snap.iso,
          snapTime: snap.snapTime,
          snapTimeUnit: snap.snapTimeUnit,
          dataType: snap.dataType,
        },
        {
          $setOnInsert: {
            project: snap.project,
            controllerId: snap.controllerId,
            provider: snap.provider,
            iso: snap.iso,
            snapTime: snap.snapTime,
            snapTimeUnit: snap.snapTimeUnit,
            dataType: snap.dataType,
            createdAt: now,
          },
          $set: {
            updatedAt: now,
            stats: snap.stats,
          },
        },
        { upsert: true, writeConcern: { w: 'majority' } },
      )
    }
  }
  return
}

export const marketplacesSnapshots = {
  marketplacesWeeklyFull: async (
    iso: Iso3166Alpha2,
    config: ControllerConfig,
    runtime: ControllerRuntime,
    date?: Date,
    customProvider?: string,
    flags?: Record<string, unknown>,
  ) => {
    await snapMarketplaces(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, customProvider, flags)
    await snapMarketplaces(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date, customProvider, flags)
  },
  marketplacesMonthlyFull: async (
    iso: Iso3166Alpha2,
    config: ControllerConfig,
    runtime: ControllerRuntime,
    date?: Date,
    customProvider?: string,
    flags?: Record<string, unknown>,
  ) => {
    await snapMarketplaces(iso, DataType.dealers, TimeUnit.months, config, runtime, date, customProvider, flags)
    await snapMarketplaces(iso, DataType.inventory, TimeUnit.months, config, runtime, date, customProvider, flags)
  },
  marketplacesFull: async (
    iso: Iso3166Alpha2,
    config: ControllerConfig,
    runtime: ControllerRuntime,
    date?: Date,
    customProvider?: string,
    flags?: Record<string, unknown>,
  ) => {
    await snapMarketplaces(iso, DataType.dealers, TimeUnit.weeks, config, runtime, date, customProvider, flags)
    await snapMarketplaces(iso, DataType.inventory, TimeUnit.weeks, config, runtime, date, customProvider, flags)
    if (isLastCrawlOfMonth()) {
      await snapMarketplaces(iso, DataType.dealers, TimeUnit.months, config, runtime, date, customProvider, flags)
      await snapMarketplaces(iso, DataType.inventory, TimeUnit.months, config, runtime, date, customProvider, flags)
    }
  },
  snapMarketplaces: async (
    iso: Iso3166Alpha2,
    dataType: DataType,
    snapTimeUnit: TimeUnit,
    config: ControllerConfig,
    runtime: ControllerRuntime,
    date?: Date,
    customProvider?: string,
    flags?: Record<string, unknown>,
  ) => {
    await snapMarketplaces(iso, dataType, snapTimeUnit, config, runtime, date, customProvider, flags)
  },
}
