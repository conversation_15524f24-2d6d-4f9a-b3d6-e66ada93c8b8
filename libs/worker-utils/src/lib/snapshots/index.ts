import { TimeUnit, ProjectType } from '@datagatherers/datagatherers'

import { accounting, accountingSnapshots } from './accounting'
import { animals, animalsSnapshots } from './animals'
import { auctions, auctionsSnapshots } from './auctions'
import { cars } from './cars'
import { delivery, deliverySnapshots } from './delivery'
import { SnapshotResolution } from './interfaces'
import { jobs, jobsSnapshots } from './jobs'
import { marketplaces, marketplacesSnapshots } from './marketplaces'
import { realestates, realestateSnapshots } from './realestates'
import { snapForTime } from './shared'
import { vodSnapshots } from './vod'

import type { Iso3166Alpha2, ControllerConfig, DataType, ControllerRuntime } from '@datagatherers/datagatherers'

export * from './interfaces'
export * from './shared'
export * from './utils'

export * from './accounting'
export * from './animals'
export * from './auctions'
export * from './cars'
export * from './jobs'
export * from './marketplaces'
export * from './realestates'

export async function snapshot(
  iso: Iso3166Alpha2,
  config: ControllerConfig,
  runtime: ControllerRuntime,
  snapshotResolution: SnapshotResolution = SnapshotResolution.Full,
  date?: Date,
  customProvider?: string,
  flags?: Record<string, unknown>,
) {
  switch (config.project) {
    case ProjectType.realestates:
      await realestates(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.marketplaces:
      await marketplaces(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.auctions:
      await auctions(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.jobs:
      await jobs(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.cars:
      await cars(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.delivery:
      await delivery(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.animals:
      await animals(iso, config, runtime, snapshotResolution, date, customProvider, flags)
      break
    case ProjectType.accounting:
      await accounting(iso, config, runtime, date, customProvider, flags)
      break
    default:
      break
  }
}

export const snapshots = {
  snapForWeek: async (
    iso: Iso3166Alpha2,
    dataType: DataType,
    config: ControllerConfig,
    runtime: ControllerRuntime,
    date?: Date,
    customProvider?: string,
    flags?: Record<string, unknown>,
  ) => {
    return await snapForTime(iso, dataType, TimeUnit.weeks, config, runtime, date, customProvider, flags)
  },
  snapForMonth: async (
    iso: Iso3166Alpha2,
    dataType: DataType,
    config: ControllerConfig,
    runtime: ControllerRuntime,
    date?: Date,
    customProvider?: string,
    flags?: Record<string, unknown>,
  ) => {
    await snapForTime(iso, dataType, TimeUnit.months, config, runtime, date, customProvider, flags)
  },
  ...accountingSnapshots,
  ...auctionsSnapshots,
  //...carsSnapshots,
  ...deliverySnapshots,
  ...jobsSnapshots,
  ...marketplacesSnapshots,
  ...realestateSnapshots,
  ...vodSnapshots,
  ...animalsSnapshots,
}
