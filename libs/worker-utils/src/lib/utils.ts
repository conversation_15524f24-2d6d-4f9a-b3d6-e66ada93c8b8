import { createHash } from 'crypto'
import fs from 'fs'
import { serialize } from 'node:v8'
import readline from 'readline'

import { isSameMonth, startOfISOWeek, subMinutes, subWeeks } from 'date-fns'
import isoCountryCurrency, { getAllInfoByISO } from 'iso-country-currency'
import { JSDOM } from 'jsdom'
import { sync } from 'rimraf'
import UserAgent from 'user-agents'

import {
  RealEstatesBusinessType,
  RealEstatesPropertyScope,
  RealEstatesStockType,
  TimeUnit,
  parseError,
  ProjectType,
} from '@datagatherers/datagatherers'

import { browser } from './browser'
import { getSnapTime } from './snapshots'

import type {
  CollectionResolver,
  ControllerConfig,
  JobInstance,
  JobRuntime,
  ControllerRuntime,
  DataType,
  JobDefinition,
  Iso3166Alpha2,
  Snapshot,
  ClassifiedsStockType,
  DealerType,
  VehicleType,
  Collection,
  TrackerType,
  RealEstatesPropertyType,
  AuctionsAuctionType,
  AuctionsBidType,
} from '@datagatherers/datagatherers'
import type { AxiosError } from '@nestjs/terminus/dist/errors/axios.error'
import type { RequestError, Response as GotResponse } from 'got'
import type { IncomingHttpHeaders } from 'http'
import type { HTTPResponse, Page, PuppeteerLifeCycleEvent } from 'puppeteer-core'

export { round } from '@datagatherers/core'

export async function clearTempStorage(): Promise<void> {
  sync('/tmp/puppeteer*', { glob: true })
}

export async function cleanProcess(): Promise<void> {
  if (browser) {
    let chromeTmpDataDir: string | null = null
    const chromeSpawnArgs = browser.process()?.spawnargs
    if (chromeSpawnArgs) {
      for (const chromeSpawnArg of chromeSpawnArgs) {
        if (chromeSpawnArg.indexOf('--user-data-dir=') === 0) {
          chromeTmpDataDir = chromeSpawnArg.replace('--user-data-dir=', '')
        }
      }
    }

    const existingPages = await browser.pages()

    for (const page of existingPages) {
      if (!page.isClosed()) {
        await page.close()
      }
    }

    await browser.disconnect()
    await browser.close()

    if (chromeTmpDataDir !== null) {
      sync(chromeTmpDataDir)
    }
  }

  return clearTempStorage()
}

export async function wait(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export async function randomWait(msMin: number, msMax: number) {
  const min = Math.ceil(msMin)
  const max = Math.floor(msMax)
  const randVal = Math.floor(Math.random() * (max - min + 1)) + min
  return await wait(randVal)
}

export function removeFields<T, Y extends keyof T>(object: T, keys: Y[]): Omit<T, Y> {
  const tempObj = { ...object }

  for (const key of keys) {
    if (tempObj[key]) {
      delete tempObj[key]
    }
  }

  return tempObj
}

export function keepFields<T, Y extends keyof T>(object: T, keys: Extract<keyof T, string>[]): Pick<T, Y> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const tempObj: any = {}
  for (const key in object) {
    if (keys.includes(key)) {
      tempObj[key] = object[key]
    }
  }
  return tempObj as Pick<T, Y>
}

export function* chunks<T>(arr: T[], n: number) {
  for (let i = 0; i < arr.length; i += n) {
    yield arr.slice(i, i + n)
  }
}

export function arraySlicer<T>(arr: T[], n: number): T[][] {
  return [...chunks(arr, n)]
}

export function hasDupes<T>(array: T[]): boolean {
  return new Set(array).size !== array.length
}

export function countDupes<T>(array: T[]): number {
  return array.length - Array.from(new Set(array)).length
}

export function deDupes<T>(array: T[]): T[] {
  return Array.from(new Set(array))
}

export function deDupesNonNull<T>(array: T[]) {
  return deDupes(array).flatMap((element) => {
    if (!element) return []
    return element
  })
}

export function getDupes<T>(array: T[]): T[] {
  const noDupes = Array.from(new Set(array))
  for (const item of noDupes) {
    const index = array.indexOf(item)
    if (index !== -1) {
      array.splice(index, 1)
    }
  }
  return array
}

export function encodeBase64(string: string): string {
  return Buffer.from(string, 'utf-8').toString('base64')
}

export function decodeBase64(string: string): string {
  return Buffer.from(string, 'base64').toString('utf-8')
}

export function randomIntFromInterval(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1) + min)
}

export type areaUnits = 'sqft' | 'acres' | 'ha' | 'sqm'

export function getREArea(area: number, unit?: areaUnits): number {
  let result = 0
  switch (unit) {
    case 'sqft':
      result = area / 10.764
      break
    case 'acres':
      result = area * 4046.85642
      break
    case 'ha':
      result = area * 10000
      break
    default:
      result = area
      break
  }
  return Math.round(result)
}

export function getCurrencySymbolFromCurrency(currency: string): string | undefined {
  if (currency) {
    return isoCountryCurrency.getParamByParam('currency', currency, 'symbol')
  }
  return undefined
}

export function getIsoCountry2FromCurrency(currency: string): Iso3166Alpha2 | undefined {
  if (currency) {
    return isoCountryCurrency.getISOByParam('currency', currency) as Iso3166Alpha2
  }
  return undefined
}

export function getIso3166Alpha2FromCurrency(currency: string): Iso3166Alpha2 | undefined {
  if (currency) {
    return isoCountryCurrency.getISOByParam('currency', currency) as Iso3166Alpha2
  }
  return undefined
}

export function getAllIso3166Alpha2FromCurrency(currency: string): Iso3166Alpha2[] {
  if (currency) {
    return isoCountryCurrency.getAllISOByCurrencyOrSymbol('currency', currency) as Iso3166Alpha2[]
  }
  return []
}

export function generateId(seed: string): string {
  if (!seed?.length) return ''
  const md5Hash = createHash('md5').update(seed).digest('base64').slice(0, 22)
  const modulo256 = ('000' + (Buffer.from(seed).reduce((acc, value) => acc + value) % 256).toString()).slice(-3)
  return md5Hash.concat(modulo256)
}

export async function pageFetch(page: Page, url: string, request: RequestInit, timeout = 5000) {
  const { res, err } = await page.evaluate(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async ({ url, request, timeout }: any) => {
      const controller = new AbortController()
      const timer = setTimeout(() => controller.abort(), timeout)
      try {
        const res = await window.fetch(url, { ...request, signal: controller.signal })
        const response = await res.text()
        clearTimeout(timer)
        return { res: response }
      } catch (err) {
        return { err }
      }
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    { url, request, timeout } as any,
  )

  return new Promise<string | undefined>((resolve, reject) => {
    if (err) {
      return reject(err)
    }
    return resolve(res)
  })
}

export async function getIsoList(
  config: ControllerConfig,
  runtime: ControllerRuntime,
  dataType: DataType,
  startDate: Date,
  endDate?: Date,
): Promise<Iso3166Alpha2[]> {
  return await getIsoListfromDb(config.project, config.provider, dataType, runtime.collection, startDate, endDate)
}

export async function getIsoListfromDb(
  project: ProjectType,
  provider: string,
  dataType: DataType,
  collection: CollectionResolver,
  startDate: Date,
  endDate?: Date,
): Promise<Iso3166Alpha2[]> {
  const updatedAt = !endDate ? { $gte: startDate } : { $gte: startDate, $lte: endDate }
  const isoList = await collection('item')
    .aggregate([
      {
        $match: {
          projectProviderDataType: getProjectProviderDataType(project, provider, dataType),
          iso: { $exists: true },
          updatedAt,
        },
      },
      {
        $group: {
          _id: '$iso',
        },
      },
    ])
    .toArray()

  return isoList.map((isoObj) => {
    return isoObj._id
  })
}

export function splitRangeDiv(
  rangeMin: number,
  rangeMax: number,
  chunks: number,
): { rangeMin: number; rangeMax: number }[] {
  if (rangeMax < rangeMin || chunks <= 0) throw new Error('SplitRange: Invalid Input Parameters')

  const subRanges: { rangeMin: number; rangeMax: number }[] = []

  const chunkSize = Math.ceil((rangeMax - rangeMin) / chunks)
  for (let i = rangeMin; i < rangeMax; i += chunkSize) {
    subRanges.push({
      rangeMin: i,
      rangeMax: i + chunkSize < rangeMax ? i + chunkSize : rangeMax,
    })
  }
  return subRanges
}

export function splitRangeSize(
  rangeMin: number,
  rangeMax: number,
  size: number,
): { rangeMin: number; rangeMax: number }[] {
  if (isNaN(rangeMin) || isNaN(rangeMax) || isNaN(size) || rangeMax < rangeMin || size <= 0)
    throw new Error('SplitRange: Invalid Input Parameters')

  const subRanges: { rangeMin: number; rangeMax: number }[] = []

  for (let i = rangeMin; i <= rangeMax; i += size) {
    subRanges.push({
      rangeMin: i,
      rangeMax: Math.min(i + size - 1, rangeMax),
    })
  }
  return subRanges
}

export function getProjectProviderDataType(project: ProjectType, provider: string, dataType: DataType): string {
  return `${project}_${provider}_${dataType}`
}

export function getProjectProviderTrackerType(
  project: ProjectType,
  provider: string,
  trackerType: TrackerType,
): string {
  return `${project}_${provider}_${trackerType}`
}

export function getItemId(dataType: DataType, project: ProjectType, id: string): string {
  return `${dataType}_${project}_${id}`
}

export function getTrackerItemId(trackerType: TrackerType, project: ProjectType, id: string): string {
  return `${trackerType}_${project}_${id}`
}

export function getREStockTypeFromTypeScope(
  businessType: RealEstatesBusinessType,
  propertyScope: RealEstatesPropertyScope,
): RealEstatesStockType {
  const typeScopeDic: {
    businessType: RealEstatesBusinessType
    propertyScope: RealEstatesPropertyScope
    stockType: RealEstatesStockType
  }[] = [
    {
      businessType: RealEstatesBusinessType.sale,
      propertyScope: RealEstatesPropertyScope.residential,
      stockType: RealEstatesStockType.residential_sales,
    },
    {
      businessType: RealEstatesBusinessType.sale,
      propertyScope: RealEstatesPropertyScope.commercial,
      stockType: RealEstatesStockType.commercial_sales,
    },
    {
      businessType: RealEstatesBusinessType.rent,
      propertyScope: RealEstatesPropertyScope.residential,
      stockType: RealEstatesStockType.residential_lettings,
    },
    {
      businessType: RealEstatesBusinessType.rent,
      propertyScope: RealEstatesPropertyScope.commercial,
      stockType: RealEstatesStockType.commercial_lettings,
    },
  ]
  return (
    typeScopeDic.find((item) => item.businessType === businessType && item.propertyScope === propertyScope)
      ?.stockType ?? RealEstatesStockType.other
  )
}

export async function getIp(runtime: ControllerRuntime | JobRuntime) {
  if (!runtime) throw new Error('No runtime found')
  return await runtime
    .got('https://api.my-ip.io/v2/ip.json', { method: 'GET' })
    .json<{ ip: string; type: string; timeZone: string; country: { code: Iso3166Alpha2; name: string } }>()
}

export async function sampleIp(runtime: ControllerRuntime | JobRuntime, lib: 'got' | 'axios' | 'browser' = 'got') {
  if (!runtime) throw new Error('No runtime found')

  if (lib === 'browser') {
    if (!runtime.page) throw new Error('No page found in runtime')

    await runtime.page.goto('https://api.my-ip.io/v2/ip.json', { waitUntil: 'networkidle2' })
    const response = await runtime.page.content()
    // eslint-disable-next-line no-console
    console.info(response)

    return response
  } else if (lib === 'axios') {
    return await runtime
      .axios('https://api.my-ip.io/v2/ip.json', { method: 'GET' })
      // eslint-disable-next-line no-console
      .then(console.info)
      .catch((error: AxiosError) => console.error(parseError(error)))
  } else if (lib === 'got') {
    return await runtime
      .got('https://api.my-ip.io/v2/ip.json', { method: 'GET' })
      .json<{ ip: string; type: string; country: { code: Iso3166Alpha2; name: string } }>()
      // eslint-disable-next-line no-console
      .then(console.info)
      .catch((error: RequestError) => console.error(parseError(error)))
  }
}

export function countOccurences(arr: string[] | number[]) {
  const digest: Map<string | number, number> = new Map()
  for (const elementToCompare of arr) {
    let count = 0
    for (const element of arr) {
      if (elementToCompare === element) count++
    }
    if (digest.has(elementToCompare)) continue
    digest.set(elementToCompare, count)
  }
  return digest
}

export function getCurrencyByIso(iso: Iso3166Alpha2): string | undefined {
  if (iso === 'EU') return 'EUR'
  try {
    return getAllInfoByISO(iso).currency
  } catch {
    return
  }
}

export function getMonthlyPrice(weeklyPrice: number) {
  return (weeklyPrice * 52) / 12
}

export function isFirstCrawlOfMonth() {
  const curr = new Date()
  return !isSameMonth(subWeeks(curr, 1), curr)
}

export function isLastCrawlOfMonth() {
  // const curr = new Date()
  // return !isSameMonth(addWeeks(curr, 1), curr)
  return true
}

export async function getJSDOMDocument(
  runtime: ControllerRuntime | JobRuntime,
  url: string,
  oldDocument?: Document,
  cookies?: { name: string; value: string; domain: string }[],
) {
  await randomWait(150, 250)

  if (cookies?.length) {
    await runtime.page.setCookie(...cookies)
  }

  await runtime.page.goto(url, { waitUntil: 'networkidle2' })

  if (oldDocument) {
    oldDocument.body.innerHTML = await runtime.page.content()
    return oldDocument
  } else {
    const { document } = new JSDOM().window
    document.body.innerHTML = await runtime.page.content()
    return document
  }
}

export function capitalizeFirstLetter(string: string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}
export function unCapitalizeFirstLetter(string: string) {
  return string.charAt(0).toLowerCase() + string.slice(1)
}

export function getUTCTimestamp(date: Date) {
  return getUTCDate(date).getTime()
}

export function getUTCDate(date: Date) {
  return subMinutes(date, date.getTimezoneOffset())
}

export function unescapeSlashes(str: string) {
  // add another escaped slash if the string ends with an odd
  // number of escaped slashes which will crash JSON.parse
  let parsedStr = str.replace(/(^|[^\\])(\\\\)*\\$/, '$&\\')

  // escape unescaped double quotes to prevent error with
  // added double quotes in json string
  parsedStr = parsedStr.replace(/(^|[^\\])((\\\\)*")/g, '$1\\$2')
  try {
    parsedStr = JSON.parse(`"${parsedStr}"`)
  } catch {
    parsedStr = str
  }

  return parsedStr
}

export function removeTabNewline(str: string): string {
  // The regex [\r\n\t]+ is used to match all newline, carriage return and tab characters.
  return str.replace(/[\r\n\t]+/g, '')
}

export async function defaultScheduleExistingDealers(
  job: JobDefinition,
  runtime: ControllerRuntime,
  iso: Iso3166Alpha2,
  dataType: DataType,
  mongodbBatchSize = 20000,
) {
  const dealerIds = runtime
    .collection('item')
    .aggregate([
      {
        $match: {
          projectProviderDataType: getProjectProviderDataType(job.project, job.provider, dataType),
          iso,
          updatedAt: { $gte: startOfISOWeek(new Date()) },
        },
      },
      {
        $group: {
          _id: '$dealerId',
        },
      },
    ])
    .batchSize(mongodbBatchSize)
    .stream()

  let dealerIdDocuments = []
  const jobs: JobDefinition[] = []

  for await (const stream of dealerIds) {
    dealerIdDocuments.push(stream)
    if (dealerIdDocuments.length === mongodbBatchSize) {
      jobs.push({
        ...job,
        data: {
          ...job.data,
          iso,
          dealerIds: dealerIdDocuments,
        },
      })
      dealerIdDocuments = []
    }
  }

  if (dealerIdDocuments?.length) {
    jobs.push({
      ...job,
      data: {
        ...job.data,
        iso,
        dealerIds: dealerIdDocuments,
      },
    })
  }
  return jobs
}

export function defaultUpdateExistingDealers(job: JobInstance, iso: Iso3166Alpha2, dataType: DataType) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (job.data as any)?.dealerIds?.flatMap((item: { _id: string }) => {
    if (!item?._id) return []

    return {
      type: 'item' as const,
      data: {
        iso,
        itemId: getItemId(dataType, job.project, item._id),
      },
    }
  })
}

export function writePDFFromBase64(pdf: string, filename = 'base64converted') {
  const pdfBuffer = Buffer.from(pdf, 'base64')
  fs.writeFileSync(filename + '.pdf', pdfBuffer)
}

export function getEnumValues<TEnum>(enumObj: TEnum): Array<TEnum[keyof TEnum]> {
  const values = new Set<TEnum[keyof TEnum]>()
  for (const key in enumObj) {
    if (Object.prototype.hasOwnProperty.call(enumObj, key)) {
      const isKeyNumeric = !isNaN(parseFloat(key))
      if (!isKeyNumeric) {
        const value = enumObj[key]
        values.add(value)
      }
    }
  }
  return Array.from(values)
}

export function getEnumKeys<TEnum>(enumObj: TEnum): Array<keyof TEnum> {
  const keys = new Set<keyof TEnum>()
  for (const key in enumObj) {
    if (Object.prototype.hasOwnProperty.call(enumObj, key)) {
      const isKeyNumeric = !isNaN(parseFloat(key))
      if (!isKeyNumeric) {
        keys.add(key)
      }
    }
  }
  return Array.from(keys)
}

export function camelCase(input: string) {
  return input.replace(/\w\S*/g, (w) => w.replace(/^\w/, (c) => c.toUpperCase()))
}

// ! USE --noSpinner FLAG
export function askQuestion(query: string): Promise<string> {
  const readlineInterface = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  })
  return new Promise((resolve) => {
    readlineInterface.question(query + '\n', (answer) => {
      resolve(answer)
      readlineInterface.close()
    })
  })
}

type UnwrapPromise<T> =
  T extends Promise<infer U>
    ? U
    : T extends (...args: unknown[]) => Promise<infer U>
      ? U
      : T extends (...args: unknown[]) => infer U
        ? U
        : T

class Oncefy<F extends (...args: unknown[]) => ReturnType<F>> {
  pendingConcurrent: ((error?: Error, result?: UnwrapPromise<ReturnType<F>>) => void)[] = []
  isRunningConcurrent = false

  constructor(readonly fn: F) {}

  async invoke(...args: Parameters<F>) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<UnwrapPromise<ReturnType<F>>>(async (resolve, reject) => {
      this.pendingConcurrent.push((error?: Error, result?: UnwrapPromise<ReturnType<F>>) => {
        return error ? reject(error) : resolve(result)
      })

      if (!this.isRunningConcurrent) {
        this.isRunningConcurrent = true

        try {
          const result = await Promise.resolve(this.fn(...args))
          this.resolveConcurrent(undefined, result as UnwrapPromise<ReturnType<F>>)
        } catch (error) {
          this.resolveConcurrent(error)
        }
      }
    })
  }

  resolveConcurrent = (error?: Error, result?: UnwrapPromise<ReturnType<F>>) => {
    this.pendingConcurrent.map((callback) => callback(error, result))
    this.pendingConcurrent = []
    this.isRunningConcurrent = false
  }
}

export function oncefy<
  F extends (...args: unknown[]) => ReturnType<F>,
  T extends (value: UnwrapPromise<ReturnType<F>>) => UnwrapPromise<ReturnType<T>>,
>(fn: F, then?: T) {
  const instance = new Oncefy(fn)
  if (then) {
    return (...args: Parameters<F>) => instance.invoke(...args).then(then)
  }

  return (...args: Parameters<F>) => instance.invoke(...args)
}

class Retryify<F extends (...args: unknown[]) => ReturnType<F>, R extends (n?: number) => void | Promise<void>> {
  pool = 0

  constructor(
    readonly maxRetries: number,
    readonly fn: F,
    readonly filter: <E extends Error>(error: E) => boolean,
    readonly onRetry?: R,
    readonly logOnEachRetry = false,
  ) {}

  async invoke(...args: Parameters<F>): Promise<UnwrapPromise<ReturnType<F>>> {
    try {
      return await Promise.resolve(this.fn(...args) as UnwrapPromise<ReturnType<F>>)
    } catch (error) {
      if (this.pool < this.maxRetries && this.filter(error)) {
        this.pool += 1

        if (this.logOnEachRetry) {
          console.error(parseError(error))
        }

        if (this.onRetry) {
          await this.onRetry(this.pool)
        }

        return this.invoke(...args).then((result) => {
          this.pool -= 1
          return result
        })
      }

      throw error
    }
  }
}

export function retryify<
  F extends (...args: unknown[]) => ReturnType<F>,
  R extends (n?: number) => void | Promise<void>,
>(maxRetries: number, fn: F, filter: <E extends Error>(error: E) => boolean, onRetry?: R, logOnEachRetry = false) {
  const instance = new Retryify(maxRetries, fn, filter, onRetry, logOnEachRetry)
  return (...args: Parameters<F>) => instance.invoke(...args)
}

export function random<T>(arr: T[]) {
  return arr[Math.floor(Math.random() * arr.length)]
}

export function weightedRandom(min: number, max: number) {
  return Math.round(max / (Math.random() * max + min))
}

export function intersection<T>(arr1: T[], arr2: T[]) {
  return arr1.filter((value) => arr2.includes(value))
}

export function parseResponseSize(body: unknown) {
  return serialize(body).byteLength
}

const timeUnitFormatter = (locale: string | string[], unit: string, unitDisplay: Intl.RelativeTimeFormatStyle) =>
  Intl.NumberFormat(locale, { style: 'unit', unit, unitDisplay }).format

const divMod = (n: number, m: number) => [Math.floor(n / m), n % m]

const createDurationFormatter = (locale: string | string[], unitDisplay: Intl.RelativeTimeFormatStyle = 'long') => {
  const fmtDays = timeUnitFormatter(locale, 'day', unitDisplay),
    fmtHours = timeUnitFormatter(locale, 'hour', unitDisplay),
    fmtMinutes = timeUnitFormatter(locale, 'minute', unitDisplay),
    fmtSeconds = timeUnitFormatter(locale, 'second', unitDisplay),
    fmtMilliseconds = timeUnitFormatter(locale, 'millisecond', unitDisplay),
    fmtList = new Intl.ListFormat(locale, { style: 'long', type: 'conjunction' })
  return (milliseconds: number) => {
    let days, hours, minutes, seconds
    ;[days, milliseconds] = divMod(milliseconds, 864e5)
    ;[hours, milliseconds] = divMod(milliseconds, 36e5)
    ;[minutes, milliseconds] = divMod(milliseconds, 6e4)
    ;[seconds, milliseconds] = divMod(milliseconds, 1e3)
    return fmtList.format(
      [
        days ? fmtDays(days) : null,
        hours ? fmtHours(hours) : null,
        minutes ? fmtMinutes(minutes) : null,
        seconds ? fmtSeconds(seconds) : null,
        unitDisplay != 'narrow' && (unitDisplay != 'short' || !seconds) && milliseconds
          ? fmtMilliseconds(milliseconds)
          : null,
      ].filter((v) => v !== null),
    )
  }
}

export const durationFormatter = createDurationFormatter('en-US')
export const durationFormatterShort = createDurationFormatter('en-US', 'short')
export const durationFormatterNarrow = createDurationFormatter('en-US', 'narrow')

export function sizeFormatter(bytes: number, si = false, dp = 1) {
  const thresh = si ? 1000 : 1024

  if (Math.abs(bytes) < thresh) {
    return bytes + ' B'
  }

  const units = si
    ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB']
  let u = -1
  const r = 10 ** dp

  do {
    bytes /= thresh
    ++u
  } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1)

  return bytes.toFixed(dp) + ' ' + units[u]
}

async function gotifyPuppeteerResponse(response: HTTPResponse): Promise<GotResponse<string>> {
  const statusCode = response.status() ?? null
  const statusMessage = response.statusText() ?? null
  const headers = response.headers() as IncomingHttpHeaders
  const url = response.url() ?? null
  const buffer = await response.buffer()
  const body = buffer.toString('utf-8')
  const ok = response.ok()

  return {
    statusCode,
    statusMessage,
    headers,
    url,
    body,
    ok,
  } as GotResponse<string>
}

export async function fetchFingerprintingParams(
  runtime: ControllerRuntime | JobRuntime,
  url: string,
  refreshUserAgent: 'desktop' | 'mobile' | 'tablet' | 'all' = 'all',
  waitUntil: PuppeteerLifeCycleEvent = 'networkidle2',
  timeout = 5000,
) {
  if (!runtime.page) throw new Error('Puppeteer page unavailable, enable BROWSER in provider config')

  if (refreshUserAgent) {
    const newUserAgent = new UserAgent(
      refreshUserAgent !== 'all' ? { deviceCategory: refreshUserAgent } : {},
    ).toString()
    await runtime.page.setUserAgent(newUserAgent)
  }

  let response: HTTPResponse
  try {
    response = await runtime.page.goto(url, { waitUntil, timeout })
  } catch {
    throw new Error(`fetchFingerprintingParams navigation failed: ${url}`)
  }

  const responseDetails = await gotifyPuppeteerResponse(response)
  const cookiesPup = await runtime.page.cookies()
  const cookiesGot = cookiesPup.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ')

  const userAgent = await runtime.page.evaluate(() => navigator.userAgent)
  const viewport = runtime.page.viewport()
  const language = await runtime.page.evaluate(() => navigator.language)
  const timezone = await runtime.page.evaluate(() => Intl.DateTimeFormat().resolvedOptions().timeZone)
  const platform = userAgent.includes('Win')
    ? 'Windows'
    : userAgent.includes('Mac')
      ? 'MacOS'
      : userAgent.includes('Linux')
        ? 'Linux'
        : 'Unknown'

  const screenResolution = await runtime.page.evaluate(() => `${screen.width}x${screen.height}`)
  const hardwareConcurrency = await runtime.page.evaluate(() => navigator.hardwareConcurrency)

  return {
    headersGot: {
      'User-Agent': userAgent,
      'Accept-Language': language,
      Cookie: cookiesGot,
    },
    headersPup: {
      'User-Agent': userAgent,
      'Accept-Language': language,
      Cookie: cookiesPup,
    },
    params: {
      cookiesPup,
      cookiesGot,
      userAgent,
      viewport,
      language,
      timezone,
      platform,
      screenResolution,
      hardwareConcurrency,
    },
    responseDetails,
  }
}

type StatsParams = {
  count: number
  stockType?: ClassifiedsStockType
  dealerType?: DealerType
  vehicleType?: VehicleType
  businessType?: RealEstatesBusinessType
  propertyType?: RealEstatesPropertyType
  propertyScope?: RealEstatesPropertyScope
  isPaying?: boolean
  isNew?: boolean
  auctionType?: AuctionsAuctionType
  bidType?: AuctionsBidType
}

export class DirectSnapshotGenerator {
  private readonly now: Date
  private readonly collection: Collection<Snapshot>
  private readonly project: ProjectType
  private readonly baseFilter: {
    controllerId: string
    project: ProjectType
    provider: string
    dataType: DataType
    iso: Iso3166Alpha2
  }
  private hasResetStats = false

  constructor(job: JobInstance, provider: string, dataType: DataType, iso: Iso3166Alpha2) {
    this.now = new Date()
    this.collection = job.runtime.collection<Snapshot>('snapshot')
    this.project = job.project

    this.baseFilter = {
      controllerId: '-1',
      project: job.project,
      provider,
      dataType,
      iso,
    }
  }

  private createStatsObject(countValue: number, stats: StatsParams) {
    return {
      count: countValue,
      ...(stats?.stockType !== undefined && { stockType: stats.stockType }),
      ...(stats?.dealerType !== undefined && { dealerType: stats.dealerType }),
      ...(stats?.vehicleType !== undefined && { vehicleType: stats.vehicleType }),
      ...(stats?.businessType !== undefined && { businessType: stats.businessType }),
      ...(stats?.propertyType !== undefined && { propertyType: stats.propertyType }),
      ...(stats?.propertyScope !== undefined && { propertyScope: stats.propertyScope }),
      ...(stats?.isPaying !== undefined && { isPaying: stats.isPaying }),
      ...(stats?.isNew !== undefined && { isNew: stats.isNew }),
      ...(stats?.auctionType !== undefined && { auctionType: stats.auctionType }),
      ...(stats?.bidType !== undefined && { bidType: stats.bidType }),
    }
  }

  async save(stats: StatsParams, forceUpdate = false) {
    if (forceUpdate && !this.hasResetStats) {
      await this.resetStats()
      this.hasResetStats = true
    }

    if (this.project === ProjectType.accounting) {
      await this.createSnapshot(TimeUnit.months, stats)
    } else {
      await this.createSnapshot(TimeUnit.weeks, stats)

      if (isFirstCrawlOfMonth()) {
        await this.createSnapshot(TimeUnit.months, stats)
      }
    }
  }

  private async resetStats() {
    const snapTime = getSnapTime(this.now, TimeUnit.weeks)
    await this.collection.updateOne(
      { ...this.baseFilter, snapTimeUnit: TimeUnit.weeks, snapTime },
      {
        $set: {
          updatedAt: this.now,
          stats: [],
        },
      },
      { upsert: true, writeConcern: { w: 'majority' } },
    )

    if (isFirstCrawlOfMonth()) {
      const monthlySnapTime = getSnapTime(this.now, TimeUnit.months)
      await this.collection.updateOne(
        { ...this.baseFilter, snapTimeUnit: TimeUnit.months, snapTime: monthlySnapTime },
        {
          $set: {
            updatedAt: this.now,
            stats: [],
          },
        },
        { upsert: true, writeConcern: { w: 'majority' } },
      )
    }
  }

  private async createSnapshot(snapTimeUnit: TimeUnit, params: StatsParams) {
    const snapTime = getSnapTime(this.now, snapTimeUnit)

    if (!this.hasResetStats) {
      const currentSnapshot = await this.collection
        .find<Snapshot>({
          ...this.baseFilter,
          snapTimeUnit,
          snapTime,
        })
        .sort({ updatedAt: -1 })
        .limit(1)
        .toArray()

      if (currentSnapshot?.[0]?.stats?.length) {
        const matchingExistingStats = currentSnapshot[0].stats.filter((stat) => {
          if (params?.stockType !== undefined && stat.stockType === params.stockType) return true
          if (params?.dealerType !== undefined && stat.dealerType === params.dealerType) return true
          if (params?.vehicleType !== undefined && stat.vehicleType === params.vehicleType) return true
          if (params?.businessType !== undefined && stat.businessType === params.businessType) return true
          if (params?.propertyType !== undefined && stat.propertyType === params.propertyType) return true
          if (params?.propertyScope !== undefined && stat.propertyScope === params.propertyScope) return true
          if (params?.isPaying !== undefined && stat.isPaying === params.isPaying) return true
          if (params?.isNew !== undefined && stat.isNew === params.isNew) return true
          if (params?.auctionType !== undefined && stat.auctionType === params.auctionType) return true
          if (params?.bidType !== undefined && stat.bidType === params.bidType) return true
          return false
        })

        if (matchingExistingStats.length > 0) {
          const requestedCombination = {
            stockType: params?.stockType,
            dealerType: params?.dealerType,
            vehicleType: params?.vehicleType,
            businessType: params?.businessType,
            propertyType: params?.propertyType,
            propertyScope: params?.propertyScope,
            isPaying: params?.isPaying,
            isNew: params?.isNew,
            auctionType: params?.auctionType,
            bidType: params?.bidType,
          }

          const hasIncompatibleChanges = matchingExistingStats.some((existing) => {
            const hasConflict = Object.keys(requestedCombination).some((key) => {
              const existingHasField = key in existing && existing[key] !== undefined
              const requestedHasField = requestedCombination[key] !== undefined
              return existingHasField !== requestedHasField
            })
            return hasConflict
          })

          if (hasIncompatibleChanges) {
            throw new Error(
              'Attempting to modify field structure of existing stats. If you want to change the tracking structure, use forceUpdate=true',
            )
          }
        }
      }
    }

    const stats = []

    stats.push(this.createStatsObject(params.count, params))

    const updateOperations = this.hasResetStats
      ? [
          {
            $setOnInsert: {
              ...this.baseFilter,
              snapTimeUnit,
              snapTime,
              createdAt: this.now,
            },
            $set: {
              updatedAt: this.now,
            },
            $push: { stats: { $each: stats } },
          },
        ]
      : [
          {
            $pull: {
              stats: {
                ...(params?.stockType !== undefined && { stockType: params.stockType }),
                ...(params?.dealerType !== undefined && { dealerType: params.dealerType }),
                ...(params?.vehicleType !== undefined && { vehicleType: params.vehicleType }),
                ...(params?.businessType !== undefined && { businessType: params.businessType }),
                ...(params?.propertyType !== undefined && { propertyType: params.propertyType }),
                ...(params?.propertyScope !== undefined && { propertyScope: params.propertyScope }),
                ...(params?.isPaying !== undefined && { isPaying: params.isPaying }),
                ...(params?.isNew !== undefined && { isNew: params.isNew }),
                ...(params?.auctionType !== undefined && { auctionType: params.auctionType }),
                ...(params?.bidType !== undefined && { bidType: params.bidType }),
              },
            },
            $set: { updatedAt: this.now },
          },
          {
            $setOnInsert: {
              ...this.baseFilter,
              snapTimeUnit,
              snapTime,
              createdAt: this.now,
            },
            $push: { stats: { $each: stats } },
          },
        ]

    for (const operation of updateOperations) {
      await this.collection.updateOne(
        {
          ...this.baseFilter,
          snapTimeUnit,
          snapTime,
        },
        operation,
        { upsert: true, writeConcern: { w: 'majority' } },
      )
    }
  }
}
