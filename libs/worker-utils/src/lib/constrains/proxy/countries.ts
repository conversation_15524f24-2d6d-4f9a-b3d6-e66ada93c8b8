import { random } from '../../utils'

import type { Iso3166Alpha2Code, ConstrainsRegion } from '@datagatherers/datagatherers'

const NorthAmerica: Iso3166Alpha2Code[] = [
  'AG',
  'AI',
  'AW',
  'BB',
  'BL',
  'BM',
  'BQ',
  'BS',
  'BZ',
  'CA',
  'CR',
  'CU',
  'CW',
  'DM',
  'DO',
  'GD',
  'GL',
  'GP',
  'GT',
  'HN',
  'HT',
  'JM',
  'KN',
  'KY',
  'LC',
  'MF',
  'MQ',
  'MS',
  'MX',
  'NI',
  'PA',
  'PM',
  'PR',
  'SV',
  'SX',
  'TC',
  'TT',
  'US',
  'VC',
  'VG',
  'VI',
]
const SouthAmerica: Iso3166Alpha2Code[] = [
  'AR',
  'BO',
  'BR',
  'CL',
  'CO',
  'EC',
  'FK',
  'GF',
  'GY',
  'PE',
  'PY',
  'SR',
  'UY',
  'VE',
]
const EU: Iso3166Alpha2Code[] = [
  'AT',
  'BE',
  'BG',
  'CY',
  'DE',
  'DK',
  'EE',
  'ES',
  'FI',
  'FR',
  'GR',
  'HR',
  'HU',
  'IE',
  'IT',
  'LT',
  'LU',
  'LV',
  'MT',
  'NL',
  'PL',
  'PT',
  'RO',
  'SE',
  'SI',
  'SK',
]

export const regions: {
  [index in ConstrainsRegion]: Iso3166Alpha2Code[]
} = {
  EU,
  Europe: [
    ...EU,
    'AD',
    'AL',
    'AX',
    'BA',
    'BY',
    'CH',
    'CZ',
    'FO',
    'GB',
    'GG',
    'GI',
    'IM',
    'IS',
    'JE',
    'LI',
    'MC',
    'MD',
    'ME',
    'MK',
    'NO',
    'RS',
    'RU',
    'SJ',
    'SM',
    'UA',
    'VA',
  ],
  Asia: [
    'AE',
    'AF',
    'AM',
    'AZ',
    'BD',
    'BH',
    'BN',
    'BT',
    'CC',
    'CN',
    'GE',
    'HK',
    'ID',
    'IL',
    'IN',
    'IO',
    'IQ',
    'IR',
    'JO',
    'JP',
    'KG',
    'KH',
    'KP',
    'KR',
    'KW',
    'KZ',
    'LA',
    'LB',
    'LK',
    'MM',
    'MN',
    'MO',
    'MV',
    'MY',
    'NP',
    'OM',
    'PH',
    'PK',
    'PS',
    'QA',
    'SA',
    'SG',
    'SY',
    'TH',
    'TJ',
    'TM',
    'TR',
    'TW',
    'UZ',
    'VN',
    'YE',
  ],
  Africa: [
    'AO',
    'BF',
    'BI',
    'BJ',
    'BW',
    'CD',
    'CF',
    'CG',
    'CI',
    'CM',
    'CV',
    'DJ',
    'DZ',
    'EG',
    'EH',
    'ER',
    'ET',
    'GA',
    'GH',
    'GM',
    'GN',
    'GQ',
    'GW',
    'KE',
    'KM',
    'LR',
    'LS',
    'LY',
    'MA',
    'MG',
    'ML',
    'MR',
    'MU',
    'MW',
    'MZ',
    'NA',
    'NE',
    'NG',
    'RE',
    'RW',
    'SC',
    'SD',
    'SH',
    'SL',
    'SN',
    'SO',
    'SS',
    'ST',
    'SZ',
    'TD',
    'TG',
    'TN',
    'TZ',
    'UG',
    'YT',
    'ZA',
    'ZM',
    'ZW',
  ],
  NorthAmerica,
  SouthAmerica,
  Americas: [...SouthAmerica, ...NorthAmerica],
  Oceania: [
    'AS',
    'AU',
    'CK',
    'CX',
    'FJ',
    'FM',
    'GU',
    'KI',
    'MH',
    'MP',
    'NC',
    'NF',
    'NR',
    'NU',
    'NZ',
    'PF',
    'PG',
    'PN',
    'PW',
    'SB',
    'TK',
    'TL',
    'TO',
    'TV',
    'UM',
    'VU',
    'WF',
    'WS',
  ],
  Antarctica: ['AQ', 'BV', 'GS', 'HM', 'TF'],
  MiddleEast: ['AE', 'EG', 'SA', 'TR'],
}

export function filterCountriesbyRegion(available: Iso3166Alpha2Code[], region: ConstrainsRegion) {
  const cleaned = available.map((x) => x.toUpperCase())

  return regions[region].filter((x) => cleaned.includes(x))
}

export function filterCountries(available: Iso3166Alpha2Code[]) {
  const cleaned = available.map((x) => x.toUpperCase())

  return {
    EU: regions.EU.filter((x) => cleaned.includes(x)),
    Europe: regions.Europe.filter((x) => cleaned.includes(x)),
    Asia: regions.Asia.filter((x) => cleaned.includes(x)),
    Africa: regions.Africa.filter((x) => cleaned.includes(x)),
    NorthAmerica: regions.NorthAmerica.filter((x) => cleaned.includes(x)),
    SouthAmerica: regions.SouthAmerica.filter((x) => cleaned.includes(x)),
    Americas: regions.Americas.filter((x) => cleaned.includes(x)),
    MiddleEast: regions.MiddleEast.filter((x) => cleaned.includes(x)),
  }
}

export function getRandomCountry(available: Iso3166Alpha2Code[], region: ConstrainsRegion): Iso3166Alpha2Code {
  return random(filterCountriesbyRegion(available, region))
}
