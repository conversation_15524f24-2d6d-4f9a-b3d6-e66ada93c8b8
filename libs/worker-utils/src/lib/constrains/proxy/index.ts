import axios, { type AxiosError } from 'axios'
import axiosRetry, { isIdempotentRequestError, isNetworkError } from 'axios-retry'
import { HttpsProxyAgent } from 'https-proxy-agent'
import UserAgent from 'user-agents'

import { oldRegions, parseError } from '@datagatherers/datagatherers'

import { countriesData } from './countries-data'
import {
  brightdataSharedDatacenter,
  brightdataRotatingResidential,
  brightdataRotatingMobile,
  brightdataRotatingISP,
} from './providers'
import {
  parseProxyServer,
  parseProxyAuthBasic,
  parseProxyServerWithAuth,
  isProxyError,
  proxyErrors,
  isTimeoutError,
  timeoutErrors,
} from './utils'
import { importGotScraping } from '../../got-scraping'
import { parseResponseSize, weightedRandom } from '../../utils'

import type { CountryData } from './countries-data'
import type {
  JobProxyPlan,
  JobProxyPlanFingerprint,
  Region,
  RuntimeConstrains,
  JobProxyStats,
} from '@datagatherers/datagatherers'

const MAX_PROXY_RETRIES_PER_REQUEST = process.env.MAX_PROXY_RETRIES_PER_REQUEST
  ? Number(process.env.MAX_PROXY_RETRIES_PER_REQUEST)
  : 5
const MAX_TIMEOUT_RETRIES_PER_REQUEST = process.env.MAX_TIMEOUT_RETRIES_PER_REQUEST
  ? Number(process.env.MAX_TIMEOUT_RETRIES_PER_REQUEST)
  : 5

function generateExit(constrains?: RuntimeConstrains) {
  switch (constrains.exitType) {
    // case 'cloudflare':
    //   return cloudflarePlanBuilder(constrains)
    case 'residential':
      return brightdataRotatingResidential(constrains)
    case 'isp':
      return brightdataRotatingISP(constrains)
    case 'mobile':
      return brightdataRotatingMobile(constrains)
    default:
      return brightdataSharedDatacenter(constrains)
  }
}

export function fingerprintForBrowser(plan: JobProxyPlan): JobProxyPlanFingerprint {
  const country: CountryData = countriesData[plan.country]

  return {
    timezone: country.timezones?.length
      ? country.timezones[weightedRandom(1, country.timezones.length) - 1]
      : 'America/New_York',
    locale: country.locales?.length ? country.locales[weightedRandom(1, country.locales.length) - 1] : 'en-US',
    locales: country.locales || ['en-US', 'en'],
  }
}

export function generateProxyPlan(constrains?: RuntimeConstrains | false, oldRegion?: Region): JobProxyPlan {
  if (!constrains) {
    if (constrains === false) {
      return undefined
    }
    if (oldRegion) {
      const constrains = { region: oldRegions[oldRegion] }
      return brightdataSharedDatacenter(constrains)
    }
    return brightdataSharedDatacenter()
  }

  if (
    oldRegion &&
    !constrains.region?.length &&
    !constrains.countries?.length &&
    !constrains.cities?.length &&
    !constrains.states?.length
  ) {
    constrains.region = oldRegions[oldRegion]
  }

  return generateExit(constrains)
}

// Puppeteer
export function parseForBrowser(plan: JobProxyPlan) {
  return parseProxyServerWithAuth(plan)
}

// Axios
export async function applyProxyPlanToAxios(plan: JobProxyPlan, stats: JobProxyStats) {
  const instance = axios.create()

  if (!plan) {
    // Do Nothing
  } else if (plan.provider === 'cloudflare') {
    instance.interceptors.request.use(function (config) {
      stats.increment('requests')

      config.url = `${parseProxyServer(plan)}/${config.url}`
      config.headers.set('User-Agent', new UserAgent().toString())
      config.headers.set('Proxy-Authorization', parseProxyAuthBasic(plan))
      config.timeout = 10_000
      return { ...config, meta: { startTime: performance.now() } }
    })
  } else {
    instance.interceptors.request.use(function (config) {
      stats.increment('requests')

      config.httpsAgent = new HttpsProxyAgent(parseProxyServerWithAuth(plan))
      config.httpAgent = config.httpsAgent
      config.headers.set('User-Agent', new UserAgent().toString())
      config.timeout = 10_000
      return { ...config, meta: { startTime: performance.now() } }
    })

    axiosRetry(instance, {
      retries: MAX_PROXY_RETRIES_PER_REQUEST + MAX_TIMEOUT_RETRIES_PER_REQUEST,
      retryCondition: (error: AxiosError) =>
        isNetworkError(error) ||
        isIdempotentRequestError(error) ||
        proxyErrors.some((err) => error.response?.statusText?.includes(err) || error.message?.includes(err)) ||
        timeoutErrors.some((err) => error.response?.statusText?.includes(err) || error.message?.includes(err)),
      onRetry(_retryCount, error, requestConfig) {
        stats.increment('requests')

        if (proxyErrors.some((err) => error.response?.statusText?.includes(err) || error.message?.includes(err))) {
          stats.increment('proxyErrors')

          plan = generateProxyPlan(plan.constrains)

          requestConfig.httpsAgent = new HttpsProxyAgent(parseProxyServerWithAuth(plan))
          requestConfig.httpAgent = requestConfig.httpsAgent
        }

        if (timeoutErrors.some((err) => error.response?.statusText?.includes(err) || error.message?.includes(err))) {
          stats.increment('timeoutErrors')
        }
      },
    })
  }

  instance.interceptors.response.use(
    (response) => {
      stats.increment('dataTransfered', parseResponseSize(response.data) * 0.6)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stats.increment('requestsTime', performance.now() - (response.config as any).meta.startTime)

      return response
    },
    (error: AxiosError) => {
      if (error.response?.data) {
        stats.increment('dataTransfered', parseResponseSize(error.response.data) * 0.6)
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stats.increment('requestsTime', performance.now() - (error.config as any).meta.startTime)

      if (process.env.APP_STAGE !== 'production') {
        console.error(parseError(error))
      }
    },
  )

  if (process.env.DEBUG_PROXY === 'true') {
    instance('https://api.my-ip.io/v2/ip.json', { method: 'GET' })
      // eslint-disable-next-line no-console
      .then((response) => console.info('axios', response?.data))
  }

  return instance
}

// Got

const defaultMethods = ['GET', 'PUT', 'POST', 'HEAD', 'DELETE', 'OPTIONS', 'TRACE']
const defaultStatusCodes = [408, 413, 429, 500, 502, 503, 504, 521, 522, 524]
const defaultErrorCodes = [
  'ETIMEDOUT',
  'ECONNRESET',
  'EADDRINUSE',
  'ECONNREFUSED',
  'EPIPE',
  // 'ENOTFOUND',
  'ENETUNREACH',
  'EAI_AGAIN',
  'ERR_SSL_SSLV3_ALERT_BAD_RECORD_MAC',
  'ERR_HTTP2_STREAM_ERROR',
  'EHOSTUNREACH',
]

export async function applyProxyPlanToGot(plan: JobProxyPlan, stats: JobProxyStats) {
  const { gotScraping, hooks } = await importGotScraping()
  if (!plan) {
    return gotScraping
  }

  let instance: typeof gotScraping

  const extraHooks = {
    beforeRequest: [
      (_options) => {
        stats.increment('requests')
      },
    ],
    afterResponse: [
      (response, _retryWithMergedOptions) => {
        stats.increment('dataTransfered', parseResponseSize(response.rawBody))
        stats.increment('requestsTime', response.timings.phases.total)

        return response
      },
    ],
    beforeError: [
      (error) => {
        if (process.env.APP_STAGE !== 'production') {
          console.error(parseError(error))
        }

        return error
      },
    ],
  }

  if (plan.provider === 'cloudflare') {
    instance = gotScraping.extend({
      prefixUrl: `${parseProxyServer(plan)}/`,
      headers: {
        'Proxy-Authorization': parseProxyAuthBasic(plan),
      },
      hooks: extraHooks,
      timeout: {
        request: 10_000,
      },
    })
  } else {
    instance = gotScraping.extend({
      https: {
        rejectUnauthorized: false,
      },
      timeout: {
        request: 10_000,
      },
      proxyUrl: parseProxyServerWithAuth(plan),
      retry: {
        limit: 0,
        methods: defaultMethods as never,
        backoffLimit: 5_0000,
        maxRetryAfter: 60_0000,
        calculateDelay: ({ attemptCount, error, computedValue, retryOptions }) => {
          // console.log({
          //   attemptCount,
          //   error: { status: error.response?.statusCode, code: error.code },
          //   computedValue,
          //   // retryOptions,
          // })
          if (attemptCount < MAX_PROXY_RETRIES_PER_REQUEST && isProxyError(error)) {
            stats.increment('proxyErrors')

            if (process.env.APP_STAGE !== 'production') {
              console.error(parseError(error))
            }

            plan = generateProxyPlan(plan.constrains)

            const proxyUrl = parseProxyServerWithAuth(plan)
            const updatedOptions = { proxyUrl }

            instance.defaults.options.merge(updatedOptions as never)

            error.options.context.proxyUrl = proxyUrl
            hooks.proxyHook(error.options)

            return 1
          }

          if (attemptCount < MAX_TIMEOUT_RETRIES_PER_REQUEST && isTimeoutError(error)) {
            stats.increment('timeoutErrors')

            if (process.env.APP_STAGE !== 'production') {
              console.error(parseError(error))
            }

            return 1
          }

          if (attemptCount > retryOptions.limit) {
            return 0
          }

          const hasMethod = defaultMethods.includes(error.options.method)
          const hasErrorCode = defaultErrorCodes.includes(error.code)
          const hasStatusCode = error.response && defaultStatusCodes.includes(error.response.statusCode)
          if (hasMethod && (hasErrorCode || hasStatusCode)) {
            const noise = Math.random() * retryOptions.noise
            return Math.min(2 ** (attemptCount - 1) * 1000, retryOptions.backoffLimit) + noise
          }

          return computedValue
        },
      },
      hooks: extraHooks,
    })
  }

  if (process.env.DEBUG_PROXY === 'true') {
    instance('https://api.my-ip.io/v2/ip.json', { method: 'GET' })
      .json()
      // eslint-disable-next-line no-console
      .then((res) => console.info('got', res))
  }

  return instance
}
